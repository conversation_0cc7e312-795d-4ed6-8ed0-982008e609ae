import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";

export enum AppNotificationSettingEnum {
  TRANSACTIONAL = "app_transactional",
  LEARNING_GUIDE = "app_learning_guide",
  ANALYST_INSIGHT = "app_analyst_insight",
  QUICK_TAKE = "app_quick_take",
  WEEKLY_REVIEW = "app_weekly_review",
  DAILY_RECAP = "app_daily_recap",
  PROMOTIONAL = "app_promotional"
}

export enum EmailNotificationSettingEnum {
  TRANSACTIONAL = "email_transactional",
  PROMOTIONAL = "email_promotional",
  WEALTHYBITES = "email_wealthybites"
}

export type NotificationSettingEnum = AppNotificationSettingEnum | EmailNotificationSettingEnum;

export type AppNotificationSettings = Map<AppNotificationSettingEnum, boolean>;
export type EmailNotificationSettings = Map<EmailNotificationSettingEnum, boolean>;

/**
 * INTERFACES
 */
export interface NotificationSettingsDTOInterface {
  owner: mongoose.Types.ObjectId;
  app: {
    deviceNotificationsEnabled: boolean;
    settings: AppNotificationSettings;
  };
  email: {
    settings: EmailNotificationSettings;
  };
}

export interface NotificationSettingsInterface extends Omit<NotificationSettingsDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationSettingsDocument extends NotificationSettingsInterface, Document {}

/**
 * SCHEMA
 */
const notificationSettingsSchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    app: {
      deviceNotificationsEnabled: {
        type: Boolean,
        required: true
      },
      settings: {
        type: Map,
        of: Boolean,
        required: true
      }
    },
    email: {
      settings: {
        type: Map,
        of: Boolean,
        required: true
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);
notificationSettingsSchema.index({ owner: 1 });

export const NotificationSettings = mongoose.model<NotificationSettingsDocument>(
  "NotificationSettings",
  notificationSettingsSchema
);
