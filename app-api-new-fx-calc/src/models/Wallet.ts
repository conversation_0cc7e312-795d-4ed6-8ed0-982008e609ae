import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";
import { ProviderEnum } from "../configs/providersConfig";
import { AccountStatusArray, AccountStatusType } from "../external-services/devengoService";

export interface WalletDTOInterface {
  owner: mongoose.Types.ObjectId;
  activeProviders: ProviderEnum[];
  createdAt?: Date;
  iban?: string;
  providers?: {
    [ProviderEnum.DEVENGO]?: {
      id: string;
      status: AccountStatusType;
    };
  };
}

export interface WalletInterface extends Omit<WalletDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;

  // virtuals
  readonly hasTerminalDevengoStatus: boolean;
}

export interface WalletDocument extends WalletInterface, Document {}

const walletSchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true
    },
    iban: { type: String },
    activeProviders: { type: Object.values(ProviderEnum) },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.DEVENGO]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: AccountStatusArray }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

walletSchema.virtual("hasTerminalDevengoStatus").get(function () {
  const wallet = this as unknown as WalletDocument;

  return ["active", "deactivated", "closed"].includes(wallet.providers?.devengo?.status);
});

export const Wallet = mongoose.model<WalletDocument>("Wallet", walletSchema);
