import mongoose, { Document } from "mongoose";
import { PortfolioDocument } from "./Portfolio";
import { UserDocument } from "./User";
import {
  AccountStatusArray,
  AccountStatusType,
  PortfolioWrapperArray,
  PortfolioWrapperTypeEnum
} from "../external-services/wealthkernelService";
import { ProviderEnum } from "../configs/providersConfig";

export enum AccountPopulationFieldsEnum {
  OWNER = "owner"
}

export interface AccountDTOInterface {
  wrapperType: PortfolioWrapperTypeEnum;
  name: string;
  owner: mongoose.Types.ObjectId;
  activeProviders?: ProviderEnum[];
  providers?: {
    wealthkernel?: {
      id: string;
      status: AccountStatusType;
    };
  };
}

interface AccountInterface extends Omit<AccountDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;

  // virtuals
  readonly portfolios: PortfolioDocument[];

  // Added by mongoose timestamps
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

export interface AccountDocument extends AccountInterface, Document {}

const accountSchema = new mongoose.Schema(
  {
    wrapperType: { type: String, enum: PortfolioWrapperArray, default: PortfolioWrapperTypeEnum.GIA },
    name: { type: String, required: true },
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: AccountStatusArray }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

/**
 * INDEXES
 */
accountSchema.index({ owner: 1 });

/**
 * VIRTUALS
 */
accountSchema.virtual("portfolios", {
  ref: "Portfolio",
  localField: "_id",
  foreignField: "account",
  justOne: false
});

export const Account = mongoose.model<AccountDocument>("Account", accountSchema);
