import { UserDocument } from "./User";
import mongoose, { Document } from "mongoose";

export enum MonitorType {
  AGGREGATE_AMOUNT_FOR_LOW_RISK_USER = "AGGREGATE_AMOUNT_FOR_LOW_RISK_USER",
  AGGR<PERSON>ATE_AMOUNT_FOR_HIGH_RISK_USER = "AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER",
  WITHDRAWAL_AFTER_DEPOSIT_FOR_HIGH_RISK_USER = "WITHDRAWAL_AFTER_DEPOSIT_FOR_HIGH_RISK_USER",
  HIGH_VOLUME_DEPOSITS = "HIGH_VOLUME_DEPOSITS",
  TRANSACTION_AFTER_1_YEAR = "TRANSACTION_AFTER_1_YEAR",
  NET_AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER = "NET_AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER"
}

/**
 * INTERFACES
 */
export interface TransactionMonitorDTOInterface {
  owner: mongoose.Types.ObjectId;
  type: MonitorType;
  createdAt?: Date;
}

export interface TransactionMonitorInterface extends Omit<TransactionMonitorDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;
}

export interface TransactionMonitorDocument extends TransactionMonitorInterface, Document {}

/**
 * SCHEMA
 */

const transactionMonitorSchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    type: {
      type: String,
      enum: Object.values(MonitorType),
      required: true
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);
transactionMonitorSchema.index({ owner: 1, type: 1, createdAt: -1 });

export const TransactionMonitor = mongoose.model<TransactionMonitorDocument>(
  "TransactionMonitor",
  transactionMonitorSchema
);
