import mongoose, { Document, Schema } from "mongoose";
import { plansConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "./User";
import { MandateDocument } from "./Mandate";
import { SubscriptionStatusArray, SubscriptionStatusType } from "../external-services/stripeService";
import { ProviderEnum } from "../configs/providersConfig";
import { PaymentMethodDocument } from "./PaymentMethod";
import ConfigUtil from "../utils/configUtil";

/**
 * DOCUMENTS
 */
export interface SubscriptionDTOInterface {
  active: boolean;
  owner: mongoose.Types.ObjectId;
  price: plansConfig.PriceType;
  category: plansConfig.SubscriptionCategoryType;
  mandate?: mongoose.Types.ObjectId;
  paymentMethod?: mongoose.Types.ObjectId;
  nextChargeAt?: Date;
  expiration?: {
    date: Date;
    downgradesTo: plansConfig.PriceType;
  };
  hasUsedFreeTrial?: boolean;
  providers?: {
    stripe: {
      id?: string;
      status?: SubscriptionStatusType;
    };
  };
}

export interface SubscriptionInterface
  extends Omit<SubscriptionDTOInterface, "owner" | "mandate" | "paymentMethod"> {
  owner: mongoose.Types.ObjectId | UserDocument;
  mandate?: mongoose.Types.ObjectId | MandateDocument;
  paymentMethod?: mongoose.Types.ObjectId | PaymentMethodDocument;
  createdAt: Date;

  // VIRTUALS
  readonly isCardBasedProviderActive: boolean;
  readonly cardBasedProviderId: string;
  readonly plan: plansConfig.PlanType;
  readonly isPaidPlan: boolean;
}

export interface SubscriptionDocument extends SubscriptionInterface, Document {}

/**
 * SCHEMA
 */
const subscriptionSchema: Schema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    price: {
      type: String,
      enum: plansConfig.PriceArrayConst,
      required: true
    },
    mandate: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Mandate",
      required: false
    },
    paymentMethod: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PaymentMethod",
      required: false
    },
    nextChargeAt: {
      type: Date,
      required: false
    },
    expiration: {
      _id: false,
      type: {
        date: { type: Date },
        downgradesTo: { type: String, enum: plansConfig.PriceArrayConst }
      },
      required: false
    },
    hasUsedFreeTrial: { type: Boolean, required: false },
    active: { type: Boolean, default: true },
    category: { type: String, enum: plansConfig.SubscriptionCategoryArray, required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.STRIPE]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: SubscriptionStatusArray }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

subscriptionSchema.virtual("plan").get(function (): plansConfig.PlanType {
  const subscription = this as SubscriptionDocument;

  return ConfigUtil.getPricing()[subscription.price].plan;
});

subscriptionSchema.virtual("isCardBasedProviderActive").get(function (): boolean {
  const subscription = this as SubscriptionDocument;

  // For now, we only have Stripe as a card-based subscription service.
  if (subscription?.providers?.stripe?.id) {
    return ["active", "trialing"].includes(subscription?.providers?.stripe?.status);
  } else return false;
});

subscriptionSchema.virtual("cardBasedProviderId").get(function (): string {
  const subscription = this as SubscriptionDocument;

  // For now, we only have Stripe as a card-based subscription service.
  return subscription?.providers?.stripe?.id;
});

subscriptionSchema.virtual("isPaidPlan").get(function (): boolean {
  const subscription = this as SubscriptionDocument;
  return subscription.active && subscription.price !== "free_monthly";
});

export const Subscription = mongoose.model<SubscriptionDocument>("Subscription", subscriptionSchema);
