import mongoose, { Document } from "mongoose";
import { investmentUniverseConfig, publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";

const { PublicAssetArrayConst } = publicInvestmentUniverseConfig;
const { AssetArrayConst } = investmentUniverseConfig;

/**
 * TYPES
 */
export const CorporateEventCategoryArray = ["StockSplitCorporateEvent", "AssetIsinChangeCorporateEvent"] as const;
export type CorporateEventCategoryType = (typeof CorporateEventCategoryArray)[number];

/**
 * INTERFACES
 */
export interface CorporateEventDTOInterface {
  asset: publicInvestmentUniverseConfig.PublicAssetType;
  date: Date;
}

export interface StockSplitCorporateEventDTOInterface extends CorporateEventDTOInterface {
  splitRatio: string;
}

export interface AssetIsinChangeCorporateEventDTOInterface extends CorporateEventDTOInterface {
  oldISIN: string;
  newISIN: string;
}

export interface CorporateEventInterface extends CorporateEventDTOInterface {
  createdAt: Date;
}

export interface StockSplitCorporateEventInterface
  extends CorporateEventInterface,
    StockSplitCorporateEventDTOInterface {
  // virtuals
  readonly divider: number;
  readonly multiplier: number;
}

export interface AssetIsinChangeCorporateEventInterface
  extends CorporateEventInterface,
    AssetIsinChangeCorporateEventDTOInterface {}

export interface CorporateEventDocument extends CorporateEventInterface, Document {}
export interface StockSplitCorporateEventDocument extends StockSplitCorporateEventInterface, Document {}
export interface AssetIsinChangeCorporateEventDocument extends AssetIsinChangeCorporateEventInterface, Document {}

/**
 * SCHEMA
 */
const corporateEventSchema = new mongoose.Schema(
  {
    asset: {
      type: String,
      enum: []
        .concat(AssetArrayConst)
        .concat(PublicAssetArrayConst) as publicInvestmentUniverseConfig.PublicAssetType[],
      required: true
    },
    date: { type: Date, required: true }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true, discriminatorKey: "category" }
);

const stockSplitCorporateEventSchema = new mongoose.Schema(
  {
    splitRatio: { type: String, required: true }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const assetIsinChangeCorporateEventSchema = new mongoose.Schema(
  {
    oldISIN: { type: String, required: true },
    newISIN: { type: String, required: true }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

/**
 * VIRTUALS
 */
stockSplitCorporateEventSchema.virtual("divider").get(function (): number {
  return new Decimal(this.splitRatio.split("/")[0]).toNumber();
});

stockSplitCorporateEventSchema.virtual("multiplier").get(function (): number {
  return new Decimal(this.splitRatio.split("/")[1]).toNumber();
});

/**
 * INDEXES
 */
stockSplitCorporateEventSchema.index({ asset: 1, date: -1 });
assetIsinChangeCorporateEventSchema.index({ asset: 1, oldISIN: 1, newISIN: 1 });

export const CorporateEvent = mongoose.model<CorporateEventDocument>("CorporateEvent", corporateEventSchema);

export const StockSplitCorporateEvent = CorporateEvent.discriminator<StockSplitCorporateEventDocument>(
  "StockSplitCorporateEvent",
  stockSplitCorporateEventSchema
);

export const AssetIsinChangeCorporateEvent = CorporateEvent.discriminator<AssetIsinChangeCorporateEventDocument>(
  "AssetIsinChangeCorporateEvent",
  assetIsinChangeCorporateEventSchema
);
