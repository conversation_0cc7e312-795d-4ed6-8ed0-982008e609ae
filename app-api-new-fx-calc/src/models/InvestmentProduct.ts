import mongoose, { Document, Schema } from "mongoose";
import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import InvestmentProductService from "../services/investmentProductService";
import { IntraDayAssetTickerDocument } from "./IntraDayTicker";

const { ASSET_CONFIG } = investmentUniverseConfig;

/**
 * TYPES
 */
export type LineType = {
  active: boolean;

  // generated by MongoDB
  readonly updatedAt: Date;
};

/**
 * INTERFACES
 */
export interface InvestmentProductInterface {
  commonId: investmentUniverseConfig.AssetType;
  listed?: boolean;
  buyLine: LineType;
  sellLine: LineType;
  currentTicker: IntraDayAssetTickerDocument;

  // VIRTUALS
  readonly tradedCurrency: currenciesConfig.MainCurrencyType;
  readonly tradedPrice: number;
  readonly isStock: boolean;
  readonly isETF: boolean;
  readonly formalTicker: string;
}

/**
 * DOCUMENTS
 */
export interface InvestmentProductDocument extends InvestmentProductInterface, Document {}

/**
 * SCHEMA
 */

const lineSchema: Schema = new mongoose.Schema(
  {
    active: { type: Boolean, default: true }
  },
  { _id: false, timestamps: { createdAt: false, updatedAt: true } }
);

const investmentProductSchema: Schema = new mongoose.Schema(
  {
    commonId: {
      type: String,
      required: [true, "You need to specify a global identifier for the investment product"],
      unique: true
    },
    listed: { type: Boolean, default: false },
    buyLine: lineSchema,
    sellLine: lineSchema
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

/**
 * Returns the latest intra-day ticker of the investment product.
 */
investmentProductSchema.virtual("currentTicker", {
  ref: "IntraDayAssetTicker",
  localField: "_id",
  foreignField: "investmentProduct",
  justOne: true,
  perDocumentLimit: 1,
  options: { sort: { timestamp: -1 } }
});

investmentProductSchema.virtual("tradedCurrency").get(function (): currenciesConfig.MainCurrencyType {
  const investmentProduct = this as InvestmentProductDocument;
  return InvestmentProductService.getMainTradedCurrency(investmentProduct.commonId);
});

investmentProductSchema.virtual("tradedPrice").get(function (): number {
  const investmentProduct = this as InvestmentProductDocument;
  return investmentProduct.currentTicker?.pricePerCurrency?.[investmentProduct.tradedCurrency];
});

investmentProductSchema.virtual("isStock").get(function (): boolean {
  const investmentProduct = this as InvestmentProductDocument;
  return ASSET_CONFIG[investmentProduct.commonId as investmentUniverseConfig.AssetType].category === "stock";
});

investmentProductSchema.virtual("isETF").get(function (): boolean {
  const investmentProduct = this as InvestmentProductDocument;
  return ASSET_CONFIG[investmentProduct.commonId as investmentUniverseConfig.AssetType].category === "etf";
});

investmentProductSchema.virtual("formalTicker").get(function (): string {
  const investmentProduct = this as InvestmentProductDocument;
  return ASSET_CONFIG[investmentProduct.commonId as investmentUniverseConfig.AssetType].formalTicker;
});

export const InvestmentProduct = mongoose.model<InvestmentProductDocument>(
  "InvestmentProduct",
  investmentProductSchema
);
