import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";
import { MandateDocument } from "./Mandate";
import { PortfolioDocument } from "./Portfolio";
import { PortfolioAllocationMethodEnum } from "../services/portfolioService";
import { currenciesConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";

const { MainCurrencies } = currenciesConfig;

/**
 * ENUMS
 */
export enum AutomationPopulationFieldsEnum {
  OWNER = "owner",
  MANDATE = "mandate",
  PORTFOLIO = "portfolio"
}

/**
 * TYPES
 */
export const AutomationCategoryArray = [
  "RebalanceAutomation",
  "TopUpAutomation",
  "SavingsTopUpAutomation"
] as const;
export type AutomationCategoryType = (typeof AutomationCategoryArray)[number];

export const AutomationStatusArray = ["Pending", "Active", "Inactive"] as const;
export type AutomationStatusType = (typeof AutomationStatusArray)[number];

export const FrequencySettingArray = ["monthly"] as const;
export type FrequencySettingType = (typeof FrequencySettingArray)[number];

/**
 * INTERFACES
 */
export interface AutomationDTOInterface {
  // At a first glance this fields seems to have an overlap with 'active'.
  // However, the two properties should be treated differently. InitialiseAt is
  // used for scenarios where repeating investments are created after converting
  // a user from a one-off investment. In this case we want the repeating investment to
  // *be able* to be initialised after a month and this is not correlated to whether the
  // automation is active or not (which is defined by the user toggle).
  initialiseAt?: Date;
  category: AutomationCategoryType;
  owner: mongoose.Types.ObjectId;
  portfolio: mongoose.Types.ObjectId;
  frequency: FrequencySettingType;
}

export interface TopUpAutomationDTOInterface extends AutomationDTOInterface {
  dayOfMonth: number; // 1-28 or -1 (if the last day of the month)
  mandate: mongoose.Types.ObjectId;
  allocationMethod: PortfolioAllocationMethodEnum;
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    // value of the recurring top-up - stored in cents
    amount: number;
  };
}

export interface SavingsTopUpAutomationDTOInterface extends AutomationDTOInterface {
  dayOfMonth: number; // 1-28 or -1 (if the last day of the month)
  mandate: mongoose.Types.ObjectId;
  savingsProduct: savingsUniverseConfig.SavingsProductType;
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    // value of the recurring top-up - stored in cents
    amount: number;
  };
}

export interface AutomationInterface extends Omit<AutomationDTOInterface, "owner" | "portfolio"> {
  active: boolean;
  owner: mongoose.Types.ObjectId | UserDocument;
  portfolio: mongoose.Types.ObjectId | PortfolioDocument;

  // VIRTUALS
  status: AutomationStatusType;
}

interface TopUpAutomationInterface extends Omit<TopUpAutomationDTOInterface, "owner" | "mandate" | "portfolio"> {
  owner: mongoose.Types.ObjectId | UserDocument;
  portfolio: mongoose.Types.ObjectId | PortfolioDocument;
  mandate: mongoose.Types.ObjectId | MandateDocument;
}

interface SavingsTopUpAutomationInterface
  extends Omit<SavingsTopUpAutomationDTOInterface, "owner" | "mandate" | "portfolio"> {
  owner: mongoose.Types.ObjectId | UserDocument;
  portfolio: mongoose.Types.ObjectId | PortfolioDocument;
  mandate: mongoose.Types.ObjectId | MandateDocument;
}

export interface AutomationDocument extends AutomationInterface, Document {}

export interface TopUpAutomationDocument extends TopUpAutomationInterface, AutomationDocument {}

export interface SavingsTopUpAutomationDocument extends SavingsTopUpAutomationInterface, AutomationDocument {}

export interface RebalanceAutomationDocument extends AutomationInterface, AutomationDocument {}

/**
 * SCHEMA
 */

const automationSchema = new mongoose.Schema(
  {
    active: { type: Boolean, default: true },
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User"
    },
    portfolio: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Portfolio"
    },
    frequency: {
      type: String,
      enum: FrequencySettingArray,
      default: "monthly"
    }
  },
  { discriminatorKey: "category", timestamps: true }
);

const topUpAutomationSchema = new mongoose.Schema(
  {
    initialiseAt: { type: Date, required: false },
    dayOfMonth: {
      type: Number,
      required: true,
      validate: [
        function (dayOfMonth: number) {
          return dayOfMonth === -1 || (dayOfMonth >= 1 && dayOfMonth <= 28);
        },
        "Invalid day of month"
      ]
    },
    mandate: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Mandate",
      required: true
    },
    consideration: {
      _id: false,
      type: {
        currency: {
          type: String,
          enum: MainCurrencies
        },
        amount: { type: Number }
      },
      required: true
    },
    allocationMethod: {
      type: String,
      enum: PortfolioAllocationMethodEnum,
      default: "holdings" as PortfolioAllocationMethodEnum,
      required: true
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const savingsTopUpAutomationSchema = new mongoose.Schema(
  {
    dayOfMonth: {
      type: Number,
      required: true,
      validate: [
        function (dayOfMonth: number) {
          return dayOfMonth === -1 || (dayOfMonth >= 1 && dayOfMonth <= 28);
        },
        "Invalid day of month"
      ]
    },
    savingsProduct: { type: String, enum: savingsUniverseConfig.SavingsProductArray, required: true },
    mandate: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Mandate",
      required: true
    },
    consideration: {
      _id: false,
      type: {
        currency: {
          type: String,
          enum: MainCurrencies
        },
        amount: { type: Number }
      },
      required: true
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const rebalanceAutomationSchema = new mongoose.Schema(
  {},
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

/**
 * INDEXES
 */
automationSchema.index({ owner: 1, category: 1 });

/**
 * VIRTUALS
 */
automationSchema.virtual("status").get(function (): AutomationStatusType {
  if (!this.active) {
    return "Inactive";
  } else return "Active";
});

export const Automation = mongoose.model<AutomationDocument>("Automation", automationSchema);

export const TopUpAutomation = Automation.discriminator<TopUpAutomationDocument>(
  "TopUpAutomation",
  topUpAutomationSchema
);

export const SavingsTopUpAutomation = Automation.discriminator<SavingsTopUpAutomationDocument>(
  "SavingsTopUpAutomation",
  savingsTopUpAutomationSchema
);

export const RebalanceAutomation = Automation.discriminator<RebalanceAutomationDocument>(
  "RebalanceAutomation",
  rebalanceAutomationSchema
);
