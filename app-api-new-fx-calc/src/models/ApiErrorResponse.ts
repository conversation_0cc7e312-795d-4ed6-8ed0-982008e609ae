import { nanoid } from "nanoid";

export type ApiErrorResponseType = {
  status?: number;
  message: string;
  description?: string;
};

export class ApiErrorResponse {
  status: number;
  error: { message: string; description?: string };
  responseId: string;

  constructor(errorResponse: ApiErrorResponseType) {
    const { message, description, status } = errorResponse;
    this.status = status ?? 500; // default 500 for other than implementations of BaseError
    this.error = { message, description: description ?? "Internal Error" };
    this.responseId = nanoid();
  }
}
