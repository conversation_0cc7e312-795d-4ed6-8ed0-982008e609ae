import mongoose, { Document } from "mongoose";
import { countriesConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "./User";
import {
  EmploymentStatusArray,
  EmploymentStatusType,
  SourceOfWealthArray,
  SourceOfWealthType
} from "../external-services/wealthkernelService";
import { AmlScreeningResultEnum } from "../configs/riskAssessmentConfig";

export enum RiskScoreClassificationEnum {
  LowRisk = "Low risk",
  MediumRisk = "Medium risk",
  HighRisk = "High risk",
  Prohibited = "Prohibited"
}

export enum SourceOfFundsEnum {
  LinkedUKBankAccount = "LinkedUKBankAccount"
}

export type RiskAssessmentNationality = {
  value: countriesConfig.CountryCodesType;
  score: number;
};

export type RiskAssessmentSourcesOfFunds = {
  value: SourceOfFundsEnum[];
  score: number;
};

export type RiskAssessmentEmploymentStatus = {
  value: EmploymentStatusType;
  score: number;
};

export type RiskAssessmentVolumeOfTransactions = {
  value: number; // stored in pounds
  score: number;
};

export type RiskAssessmentAmlScreening = {
  value: AmlScreeningResultEnum;
  score: number;
};

export type RiskAssessmentSourcesOfWealth = {
  value: SourceOfWealthType[];
  score: number;
};

export interface RiskAssessmentDTOInterface {
  owner: mongoose.Types.ObjectId;
  createdAt: Date;
  nationality: RiskAssessmentNationality;
  sourcesOfFunds: RiskAssessmentSourcesOfFunds;
  employmentStatus: RiskAssessmentEmploymentStatus;
  volumeOfTransactions: RiskAssessmentVolumeOfTransactions;
  amlScreening: RiskAssessmentAmlScreening;
  sourcesOfWealth: RiskAssessmentSourcesOfWealth;
  totalScore: number;
}

export interface RiskAssessmentInterface extends Omit<RiskAssessmentDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;

  // virtuals
  readonly classification: RiskScoreClassificationEnum;
}

export interface RiskAssessmentDocument extends RiskAssessmentInterface, Document {}

const riskAssessmentSchema = new mongoose.Schema(
  {
    nationality: {
      value: { type: String, enum: countriesConfig.countryCodesArray, required: true },
      score: { type: Number, required: true }
    },
    sourcesOfFunds: {
      value: { type: [String], enum: Object.values(SourceOfFundsEnum), required: true },
      score: { type: Number, required: true }
    },
    sourcesOfWealth: {
      value: { type: [String], enum: SourceOfWealthArray, required: true },
      score: { type: Number, required: true }
    },
    employmentStatus: {
      value: { type: String, enum: EmploymentStatusArray, required: true },
      score: { type: Number, required: true }
    },
    volumeOfTransactions: {
      value: { type: Number, required: true },
      score: { type: Number, required: true }
    },
    amlScreening: {
      value: { type: String, enum: Object.values(AmlScreeningResultEnum), required: true },
      score: { type: Number, required: true }
    },
    totalScore: { type: Number, required: true },
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);
riskAssessmentSchema.index({ owner: 1, createdAt: -1 });

riskAssessmentSchema.virtual("classification").get(function (): RiskScoreClassificationEnum {
  const score = this.totalScore;
  if (score <= 5) {
    return RiskScoreClassificationEnum.LowRisk;
  } else if (score <= 10) {
    return RiskScoreClassificationEnum.MediumRisk;
  } else if (score < 100) {
    return RiskScoreClassificationEnum.HighRisk;
  } else if (score > 100) {
    return RiskScoreClassificationEnum.Prohibited;
  }
});

export const RiskAssessment = mongoose.model<RiskAssessmentDocument>("RiskAssessment", riskAssessmentSchema);
