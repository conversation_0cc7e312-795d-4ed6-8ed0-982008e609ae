import mongoose, { Document, Schema } from "mongoose";
import { AssetTransactionDocument, RebalanceTransactionDocument, TransactionDocument } from "./Transaction";
import {
  OrderSideArray,
  OrderSideType,
  WealthkernelOrderStatusArray,
  WealthkernelOrderStatusType
} from "../external-services/wealthkernelService";
import Decimal from "decimal.js";
import { ProviderEnum } from "../configs/providersConfig";
import {
  currenciesConfig,
  investmentUniverseConfig,
  savingsUniverseConfig,
  fees
} from "@wealthyhood/shared-configs";
import { ForeignCurrencyRatesType } from "currencies";
import CurrencyUtil from "../utils/currencyUtil";
import { UserDocument } from "./User";
import { ExecutionTypeEnum, ExecutionWindowType } from "../configs/executionWindowConfig";
import ExecutionWindowUtil from "../utils/executionWindowUtil";
import { InvestmentProductDocument } from "./InvestmentProduct";
import { plansConfig } from "@wealthyhood/shared-configs";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;
const { MainCurrencies } = currenciesConfig;
const { REAL_TIME_ETF_EXECUTION_FEES } = fees;

const FALLBACK_EXCHANGE_RATE = 1;

export type FeesType = {
  fx: FeeType;
  realtimeExecution?: FeeType; // Older transactions/rewards do not have real time execution
  commission?: FeeType; // Older transactions/rewards do not have commission
  executionSpread?: FeeType; // Older transactions/rewards do not have execution spread
};

export type FeeType = {
  currency: currenciesConfig.MainCurrencyType;
  amount: number; // Stored in whole currency (not cents)
};

export enum OrderPopulationFieldsEnum {
  TRANSACTION = "transaction"
}

export type UnitPriceType = {
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
};

type DisplayExchangeRateType = {
  rate: number;
  currency: currenciesConfig.MainCurrencyType;
};

export const OrderStatusArray = [
  "Pending",
  "Matched",
  "Rejected",
  "Settled",
  "Cancelled",
  /**
   * This status is used for orders that internally filled, meaning they got matched with another order of the opposite side.
   * Currently, this status is used only for Savings Product orders.
   */
  "InternallyFilled"
] as const;
export type OrderStatusType = (typeof OrderStatusArray)[number];

type CommonIdType = investmentUniverseConfig.AssetType | savingsUniverseConfig.SavingsProductType;

export enum OrderSubmissionIntentEnum {
  REAL_TIME = "REAL_TIME",
  AGGREGATE = "AGGREGATE"
}

export interface OrderDTOInterface {
  status?: OrderStatusType;
  consideration: {
    originalAmount?: number; // amount before fees applied, stored in cents
    amountSubmitted?: number; // amount submitted to WK, after fees applied, stored in cents
    amount?: number; // final settlement amount (initially equal to amountSubmitted)
    currency: currenciesConfig.MainCurrencyType;
  };
  fees?: FeesType;
  quantity?: number;
  createdAt?: Date;
  updatedAt?: Date;
  filledAt?: Date;
  // This represents the actual order settlement in the market which usually happens in T+1 where T is the
  // execution date. This is **NOT** related to us marking the parent transaction as 'Settled'.
  marketSettledAt?: Date;
  // The exchange rate is our provider exchange rate **modified** by our FX spread.
  exchangeRate?: number;
  // The unit price is the price of the asset at the time of settlement, in its traded currency.
  unitPrice?: UnitPriceType;
  isin: string;
  rejectionReason?: string;
  settlementCurrency: currenciesConfig.MainCurrencyType;
  side: OrderSideType;
  // Is being used for debugging to associate a pending order with a transaction if it fails during creation
  transaction: mongoose.Types.ObjectId;
  submissionIntent: OrderSubmissionIntentEnum;
  activeProviders: ProviderEnum[];
  providers?: {
    wealthkernel?: {
      status: WealthkernelOrderStatusType;
      id: string;
      submittedAt: Date;
      brokerFxRate?: number; // Raw FX rate from WealthKernel
      baseExchangeRate?: number; // Base exchange rate from WealthKernel
      accountingBrokerFxFee?: number; // FX fee charged by WealthKernel (in cents)
    };
  };
}

export interface OrderInterface extends Omit<OrderDTOInterface, "transaction"> {
  transaction: mongoose.Types.ObjectId | AssetTransactionDocument | RebalanceTransactionDocument;

  // VIRTUALS
  displayAmount?: number;
  displayQuantity?: number;
  displayExchangeRate?: DisplayExchangeRateType;
  executionWindow?: ExecutionWindowType;
  isCancellable?: boolean;
  displayDate?: Date;
  hasTerminalStatus?: boolean;
  estimatedRealTimeCommission?: number;

  readonly amountForReturnsAndUpBy?: number;
  readonly getDisplayQuantity?: (
    userCurrency: currenciesConfig.MainCurrencyType,
    investmentProduct: InvestmentProductDocument,
    transaction: TransactionDocument
  ) => number;
  readonly getDisplayAmount?: (
    userCurrency: currenciesConfig.MainCurrencyType,
    investmentProduct: InvestmentProductDocument,
    transaction: TransactionDocument
  ) => number;
  readonly getIsCancellable?: (
    user: UserDocument,
    investmentProduct: InvestmentProductDocument,
    transaction: TransactionDocument
  ) => boolean;
  readonly getExecutionWindow?: (
    user: UserDocument,
    investmentProduct: InvestmentProductDocument,
    transaction: TransactionDocument
  ) => boolean;
  readonly getDisplayExchangeRate?: (
    user: UserDocument,
    investmentProduct: InvestmentProductDocument,
    exchangeRatesWithSpread: ForeignCurrencyRatesType
  ) => DisplayExchangeRateType;
  readonly getEstimatedRealTimeCommission?: (plan: plansConfig.PlanType) => number;
  readonly assetCategory?: investmentUniverseConfig.AssetCategoryType;
  readonly displayUnitPrice?: UnitPriceType;
  readonly wealthyhoodUnitPrice?: UnitPriceType;
  readonly isSubmittedToBroker?: boolean;
  readonly commonId?: CommonIdType;
  readonly isInvestmentOrder?: boolean;
  readonly isSavingsOrder?: boolean;
  readonly isMatched?: boolean;
}

export interface OrderDocument extends OrderInterface, Document {}

const feesSchema: Schema = new mongoose.Schema(
  {
    fx: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"],
        default: "GBP"
      },
      amount: { type: Number }
    },
    commission: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"],
        default: "GBP"
      },
      amount: { type: Number }
    },
    executionSpread: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"],
        default: "GBP"
      },
      amount: { type: Number }
    },
    realtimeExecution: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"],
        default: "GBP"
      },
      amount: { type: Number }
    }
  },
  { _id: false }
);

const orderSchema: Schema = new mongoose.Schema(
  {
    status: {
      type: String,
      enum: OrderStatusArray,
      default: "Pending"
    },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    fees: feesSchema,
    consideration: {
      type: {
        currency: {
          type: String,
          enum: MainCurrencies,
          default: "GBP"
        },
        amount: { type: Number },
        amountSubmitted: { type: Number },
        originalAmount: { type: Number }
      },
      _id: false,
      required: false
    },
    quantity: { type: Number, required: false },
    isin: { type: String, required: true },
    filledAt: { type: Date, required: false },
    marketSettledAt: { type: Date, required: false },
    exchangeRate: { type: Number, required: false },
    rejectionReason: { type: String, required: false },
    settlementCurrency: {
      type: String,
      enum: ["EUR", "GBP", "USD"],
      default: "GBP"
    },
    unitPrice: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"]
      },
      amount: { type: Number }
    },
    side: { type: String, enum: OrderSideArray, required: true },
    transaction: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Transaction",
      required: true
    },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          status: { type: String, enum: WealthkernelOrderStatusArray },
          id: { type: String },
          submittedAt: { type: Date },
          brokerFxRate: { type: Number },
          baseExchangeRate: { type: Number },
          accountingBrokerFxFee: { type: Number }
        }
      }
    },
    submissionIntent: { type: String, enum: Object.values(OrderSubmissionIntentEnum), required: true }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

/**
 * @description
 * The display amount we're showing users in orders depends on the order side and status.
 *  1. Buy: we're showing the amount the user paid (e.g. £10, even if some of that was fees or a cash
 * remainder that will be sent to our portfolio)
 *  2. Sell:
 *     a) If the order is pending, we show an estimation of what the user will receive (ticker * quantity)
 *     b) If the order is settled, and therefore has a consideration amount, we show that instead
 *     c) If the order is rejected sell we don't show an amount
 *  The amount is returned in cents and is up to clients to convert to GBP.
 */
orderSchema.virtual("getDisplayAmount").get(function (): (
  userCurrency: currenciesConfig.MainCurrencyType,
  investmentProduct: InvestmentProductDocument,
  transaction: TransactionDocument
) => number {
  return (userCurrency, investmentProduct, transaction) => {
    if (this.side === "Sell") {
      if (["Rejected", "Cancelled", "DepositFailed"].includes(transaction?.status)) return undefined;
      else if (!this.consideration?.amount) {
        return Decimal.mul(investmentProduct.currentTicker.getPrice(userCurrency), this.quantity)
          .mul(100)
          .toNumber();
      } else return this.consideration.amount;
    } else if (this.side === "Buy") {
      return this.consideration.originalAmount ?? this.consideration.amountSubmitted ?? this.consideration.amount;
    }
  };
});

/**
 * @description Is similar to getDisplayAmount but is intended for calculations in the API, not
 * for display purposes. It returns the amount fields that we should use for xirr & up-by calculations.
 *
 * NOTE1: we exclude the real time commission fee from the amount for returns & up-by calculations:
 * 1) For buys we use the amount pre-FX (and other spreads), but remove the real time commission fee
 * 2) For sells we use the amount post-FX (and other spreads), but we add back the real time commission fee
 *
 * Example: If somebody pays for 10 euros of a stock and we charge him 1 EUR for real time execution and 0.1 EUR for FX
 * they actually bought 8.9 EUR worth of stock. Because we exclude the real time execution fee, we calculate as follows:
 * 8.9 (current value) - 9 (amount paid after real time execution fee) = -0.1 up-by (which is the FX fee that we don't remove
 * from the calculation)
 *
 * NOTE2: the getter should be used only on matched orders, because only these are taken into account
 * on returns & up-by calculations.
 *
 * NOTE3: this should be as close as possible to the display amount, otherwise for metrics such as the
 * up-by, the numbers that the user sees, won't add up.
 */
orderSchema.virtual("amountForReturnsAndUpBy").get(function (): number {
  const order = this as OrderDocument;
  const realtimeExecutionFee = Decimal.mul(order.fees?.realtimeExecution?.amount ?? 0, 100); // Make it cents

  if (order.side === "Buy") {
    const amountPreSpreads =
      order.consideration.originalAmount ?? order.consideration.amountSubmitted ?? order.consideration.amount;
    return Decimal.sub(amountPreSpreads, realtimeExecutionFee).toNumber();
  } else if (order.side === "Sell") {
    return Decimal.sum(order.consideration?.amount ?? 0, realtimeExecutionFee).toNumber();
  }
});

/**
 * @description
 * The display quantity we're showing users in orders depends on the order side and status.
 *  1. Buy:
 *      a) If the order is settled, we display the settled quantity
 *      b) If the order is pending, we show an estimation of what the user will get (amount user paid / ticker price)
 *      c) If the order is rejected we don't display quantity
 *  2. Sell:
 *     a) we are showing the quantity that user sold
 */
orderSchema.virtual("getDisplayQuantity").get(function (): (
  userCurrency: currenciesConfig.MainCurrencyType,
  investmentProduct: InvestmentProductDocument,
  transaction: TransactionDocument
) => number {
  return (userCurrency, investmentProduct, transaction) => {
    if (this.side === "Buy") {
      if (["Rejected", "Cancelled", "DepositFailed"].includes(transaction.status)) return undefined;
      if (!this.quantity) {
        return Decimal.div(
          this.consideration?.amount ?? this.consideration?.originalAmount,
          investmentProduct.currentTicker.getPrice(userCurrency)
        )
          .div(100)
          .toNumber();
      } else return this.quantity;
    }
    if (this.side === "Sell") {
      return this.quantity;
    }
  };
});

/**
 * @description
 * The display exchange rate we're showing users in orders depends on the order status.
 * - If the order is pending, then we show the live FX rate we take from EOD modified by our spread.
 * - If the order is matched, then we show the stored exchange rate, rounded at two decimal points.
 */
orderSchema.virtual("getDisplayExchangeRate").get(function (): (
  user: UserDocument,
  investmentProduct: InvestmentProductDocument,
  exchangeRatesWithSpread: ForeignCurrencyRatesType
) => DisplayExchangeRateType {
  return (user, investmentProduct, exchangeRatesWithSpread) => {
    const order = this as OrderDocument;
    const tradedCurrency = investmentProduct.tradedCurrency;

    if (order.isMatched && order.exchangeRate) {
      return {
        rate: new Decimal(order.exchangeRate).toDecimalPlaces(3).toNumber(),
        currency: order.unitPrice.currency as currenciesConfig.MainCurrencyType
      };
    } else if (order.status === "Pending" && CurrencyUtil.isForeignCurrency(user.currency, tradedCurrency)) {
      return {
        rate: exchangeRatesWithSpread[tradedCurrency as currenciesConfig.MainCurrencyType],
        currency: tradedCurrency
      };
    }
  };
});

/**
 * @description
 * We get an estimated real-time commission for a pending sell to show to the users, because
 * we get the actual fees for a sell order only after the order is matched.
 * For the time being we only charge real-time commission fee for ETFs.
 */
orderSchema.virtual("getEstimatedRealTimeCommission").get(function (): (plan: plansConfig.PlanType) => number {
  return (plan) => {
    const order = this as OrderDocument;

    if (order.side !== "Sell" || order.status !== "Pending") {
      return undefined;
    }

    const isEtf = order.assetCategory === "etf";
    if (isEtf && order.submissionIntent === OrderSubmissionIntentEnum.REAL_TIME) {
      return new Decimal(REAL_TIME_ETF_EXECUTION_FEES[plan]).toDecimalPlaces(2).toNumber();
    }

    return 0;
  };
});

/**
 * @description This is used for display purposes by the clients. It may seem as if there is
 * overlap with the unitPrice in the document, but the document field is the source of truth for
 * the actual unit price at the time of execution. This virtual field is the unit price that
 * we *want* to display to the users.
 *
 * The main idea is to show the user a unit price as close to the real one as possible. This means
 * that for buy orders we will have to subtract the realtime fee from the original amount and for
 * sell orders we will have to add it because the consideration.amount is the WK amount - real time fee.
 *
 * Example 1: Buy Order
 * - User initiates an ETF real-time buy for €10.
 * - Real-time fee is €1.
 * - Amount submitted to WealthKernel (WK) is €9 (€10 - €1).
 * - Assuming the ETF price is €9, the user acquires 1 unit.
 * - In the receipt, we display:
 *   - Amount: €10 (original user intent)
 *   - Quantity: 1
 *   - Unit Price: €9 (because originalAmount €10 - €1 fee = €9 / 1 unit = €9)
 *
 * Example 2: Sell Order
 * - User initiates an ETF real-time sell for approximately €10.
 * - Real-time fee is €1.
 * - WealthKernel (WK) returns cash of €10 from the sale.
 * - Cash credited to the user is €9 (€10 - €1).
 * - Assuming the ETF price was €10, the user sold 1 unit.
 * - In the receipt, we display:
 *   - Amount: €9 (net amount to user)
 *   - Quantity: 1
 *   - Unit Price: €10 (because consideration.amount €10 / 1 unit = €10, before our fees)
 *
 * In pounds (or user's currency, after FX).
 */
orderSchema.virtual("displayUnitPrice").get(function (): UnitPriceType {
  const order = this as OrderDocument;
  if (order.consideration?.amount && order.quantity) {
    let orderAmount: number;
    const realtimeExecutionFee = Decimal.mul(order.fees?.realtimeExecution?.amount ?? 0, 100); // Make it cents
    if (order.side === "Buy") {
      orderAmount = Decimal.sub(
        order.consideration.originalAmount ?? order.consideration.amount,
        realtimeExecutionFee
      ).toNumber();
    } else {
      orderAmount = Decimal.add(order.consideration.amount, realtimeExecutionFee).toNumber();
    }
    return {
      amount: Decimal.div(orderAmount, order.quantity)
        .mul(order.exchangeRate ?? FALLBACK_EXCHANGE_RATE)
        .round()
        .div(100)
        .toNumber(),
      currency: order.unitPrice?.currency
    };
  } else {
    return order.unitPrice;
  }
});

/**
 * @description This field just returns the displayUnitPrice for now. The reason why we do that
 * is because the 'displayUnitPrice' is intended for display purposes by the clients only
 * and should not be used for calculations. So in the future we may make changes on how we calc
 * the wealthyhood unit price and that may be different than the unit price we display in the client
 * so let's keep those fields separate for flexibility.
 */
orderSchema.virtual("wealthyhoodUnitPrice").get(function (): UnitPriceType {
  return (this as OrderDocument).displayUnitPrice;
});

orderSchema.virtual("displayExchangeRate").get(function (): DisplayExchangeRateType {
  const order = this as OrderDocument;

  if (order.exchangeRate) {
    return {
      rate: new Decimal(order.exchangeRate).toDecimalPlaces(2).toNumber(),
      currency: order.unitPrice.currency
    };
  }
});

orderSchema.virtual("hasExecutionStarted").get(function (): boolean {
  const order = this as OrderDocument;

  return order.isSubmittedToBroker;
});

orderSchema.virtual("assetCategory").get(function (): investmentUniverseConfig.AssetCategoryType {
  const order = this as OrderDocument;

  if (order.isInvestmentOrder) {
    return ASSET_CONFIG[order.commonId as investmentUniverseConfig.AssetType].category;
  }
});

/**
 * For asset transactions, we calculate the execution window for an order on-demand.
 *
 * For rebalance transactions, because we need to know in which phase of the rebalance we are, we look at the
 * execution window of the parent transaction.
 */
orderSchema.virtual("getExecutionWindow").get(function (): (
  user: UserDocument,
  investmentProduct: InvestmentProductDocument,
  transactionLinkedToOrder: TransactionDocument
) => ExecutionWindowType {
  return (user, investmentProduct, transactionLinkedToOrder) => {
    const order = this as OrderDocument;

    if (!order.hasTerminalStatus) {
      if (transactionLinkedToOrder.category === "AssetTransaction") {
        return ExecutionWindowUtil.getOrderExecutionWindow(order, user.currency, investmentProduct);
      } else if (transactionLinkedToOrder.category === "RebalanceTransaction") {
        const rebalanceTransaction = transactionLinkedToOrder as RebalanceTransactionDocument;

        if (order.side === "Buy") {
          return rebalanceTransaction.buyExecutionWindow;
        } else {
          return rebalanceTransaction.sellExecutionWindow;
        }
      }
    }
  };
});

/**
 * @description Returns if an order is cancellable or not.
 *
 * For an order to be Cancellable the following must apply:
 * 1. The order must be pending
 * 2. The order must NOT be submitted to WK.
 * 3. The parent transaction must be an asset transaction.
 * 4. The order must not have real-time execution window.
 */
orderSchema.virtual("getIsCancellable").get(function (): (
  user: UserDocument,
  investmentProduct: InvestmentProductDocument,
  transaction: TransactionDocument
) => boolean {
  return (user, investmentProduct, transaction) => {
    if (["PendingDeposit", "PendingGift", "DepositFailed"].includes(transaction.status)) {
      return this.status === "Pending" && !this.isSubmittedToBroker;
    }

    const executionWindow = this.getExecutionWindow(user, investmentProduct, transaction);

    return (
      this.status === "Pending" &&
      !this.isSubmittedToBroker &&
      transaction.category === "AssetTransaction" &&
      executionWindow.executionType !== ExecutionTypeEnum.REALTIME
    );
  };
});

orderSchema.virtual("wealthkernel").get(function (): any {
  return this?.providers?.wealthkernel;
});

orderSchema.virtual("isSubmittedToBroker").get(function (): any {
  const activeProvider = this.activeProviders[0];

  return !!this?.providers?.[activeProvider]?.id;
});

orderSchema.virtual("hasTerminalStatus").get(function (): any {
  return ["Settled", "Matched", "Cancelled", "Rejected"].includes(this.status);
});

const isinToCommonIdDict: Record<string, CommonIdType> = Object.fromEntries(
  Object.entries({ ...ASSET_CONFIG, ...SAVINGS_PRODUCT_CONFIG_GLOBAL }).map(([key, config]) => [
    config.isin,
    key as CommonIdType
  ])
);
orderSchema.virtual("commonId").get(function (): CommonIdType {
  const order = this as OrderDocument;

  return isinToCommonIdDict[order.isin];
});
orderSchema.virtual("isInvestmentOrder").get(function (): boolean {
  const order = this as OrderDocument;

  return !!ASSET_CONFIG[order.commonId as investmentUniverseConfig.AssetType];
});
orderSchema.virtual("isSavingsOrder").get(function (): boolean {
  const order = this as OrderDocument;

  return !!SAVINGS_PRODUCT_CONFIG_GLOBAL[order.commonId as savingsUniverseConfig.SavingsProductType];
});

orderSchema.virtual("displayDate").get(function () {
  const order = this as OrderDocument;

  if (order.isMatched) {
    return order.filledAt;
  } else return order.createdAt;
});

orderSchema.virtual("isMatched").get(function (): boolean {
  const order = this as OrderDocument;

  return order.status === "Matched" || order.status === "Settled";
});

/**
 * INDEXES
 */
orderSchema.index({ transaction: 1 });
orderSchema.index({ "providers.wealthkernel.id": 1 });
// For OrderService.createFallbackRealtimeWealthkernelOrders
orderSchema.index({
  activeProviders: 1,
  status: 1,
  submissionIntent: 1,
  "providers.wealthkernel.id": 1,
  createdAt: 1
});

export const Order = mongoose.model<OrderDocument>("Order", orderSchema);
