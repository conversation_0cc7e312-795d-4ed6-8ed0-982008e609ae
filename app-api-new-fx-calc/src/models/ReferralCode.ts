import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";
import { customAlphabet } from "nanoid";

const alphabet = "0123456789abcdefghijklmnopqrstuvwxyz";
const nanoidShort = customAlphabet(alphabet, 8);

/**
 * ENUMS
 */
export enum LifetimeEnum {
  EXPIRING = "expiring",
  NON_EXPIRING = "non-expiring"
}

/**
 * TYPES
 */
const LifetimeArray = ["expiring", "non-expiring"] as const;
type LifetimeType = (typeof LifetimeArray)[number];

/**
 * INTERFACES
 */

export interface ReferralCodeDTOInterface {
  // boolean that indicates whether the link is valid to be used
  // always true for non-expiring links
  active?: boolean;
  // indicates whether the link can be used specific amount of times or indefinitely
  lifetime?: LifetimeType;
  // the user id for the user that owns the link
  owner: mongoose.Types.ObjectId;
}

export interface ReferralCodeInterface extends Omit<ReferralCodeDTOInterface, "active" | "owner"> {
  active: boolean;
  // the referral code - similar to the wlthdId of the participant
  code: string;
  owner: mongoose.Types.ObjectId | UserDocument;

  // virtual getters
  readonly isExpiring: boolean;
}

export interface ReferralCodeDocument extends ReferralCodeInterface, Document {}

/**
 * SCHEMA
 */
const referralCodeSchema = new mongoose.Schema(
  {
    active: { type: Boolean, default: true },
    code: {
      type: String,
      default: () => nanoidShort(),
      unique: true
    },
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    lifetime: {
      type: String,
      enum: LifetimeArray,
      default: LifetimeEnum.EXPIRING
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

referralCodeSchema.virtual("isExpiring").get(function (): boolean {
  return this.lifetime === LifetimeEnum.EXPIRING;
});

export const ReferralCode = mongoose.model<ReferralCodeDocument>("ReferralCode", referralCodeSchema);
