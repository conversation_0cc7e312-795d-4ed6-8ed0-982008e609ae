import mongoose, { Document, Schema } from "mongoose";
import { indexesConfig } from "@wealthyhood/shared-configs";
import { envIsDev } from "../utils/environmentUtil";

const { IndexArrayConst } = indexesConfig;

/**
 * DTO INTERFACES
 */
export interface IndexPriceDTOInterface {
  date: Date;
  index: indexesConfig.IndexType;
  price: number;
  dailyReturnPercentage: number;
}

type IndexPriceInterface = IndexPriceDTOInterface;

/**
 * DOCUMENTS
 */
export interface IndexPriceDocument extends IndexPriceInterface, Document {}

/**
 * SCHEMAS
 */
const indexPriceSchema: Schema = new mongoose.Schema(
  {
    date: {
      type: Date,
      required: true
    },
    index: {
      type: String,
      enum: IndexArrayConst
    },
    price: Number,
    dailyReturnPercentage: Number
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    timeseries: !envIsDev()
      ? {
          timeField: "date",
          granularity: "hours"
        }
      : undefined,
    autoCreate: true,
    timestamps: true
  }
);

/**
 * MODELS
 */
export const IndexPrice = mongoose.model<IndexPriceDocument>("IndexPrice", indexPriceSchema);
