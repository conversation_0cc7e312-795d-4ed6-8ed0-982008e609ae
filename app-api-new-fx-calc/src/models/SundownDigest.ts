import mongoose, { Document, Schema } from "mongoose";
import { publicInvestmentUniverseConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../configs/providersConfig";
import { envIsDev } from "../utils/environmentUtil";

const { PublicAssetArrayConst } = publicInvestmentUniverseConfig;
const { AssetArrayConst } = investmentUniverseConfig;

/**
 * DTO INTERFACES
 */
interface SundownDigestSectionInterface {
  companyName?: string;
  companyTicker?: string;
  assetId?: publicInvestmentUniverseConfig.PublicAssetType;
  assetReturnPercentage?: number;
  title?: string;
  content?: string;
  tag?: string;
}

export interface SundownDigestDTOInterface {
  date: Date;
  content: string;
  formattedContent?: {
    overview?: string;
    sections?: Array<SundownDigestSectionInterface>;
  };
  providers?: {
    stockNews: {
      id: string;
    };
  };
}

type SundownDigestInterface = SundownDigestDTOInterface;

/**
 * DOCUMENTS
 */
export interface SundownDigestDocument extends SundownDigestInterface, Document {}

/**
 * SCHEMAS
 */
const sundownDigestSchema: Schema = new mongoose.Schema(
  {
    date: {
      type: Date,
      required: true
    },
    content: {
      type: String,
      required: true
    },
    formattedContent: {
      type: {
        _id: false,
        overview: { type: String },
        sections: [
          {
            _id: false,
            companyName: { type: String },
            companyTicker: { type: String },
            assetId: {
              type: String,
              enum: [...PublicAssetArrayConst, ...AssetArrayConst]
            },
            assetReturnPercentage: { type: Number },
            title: { type: String },
            content: { type: String },
            tag: { type: String }
          }
        ]
      },
      required: false
    },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.STOCK_NEWS]: {
          _id: false,
          id: { type: String }
        }
      }
    }
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    timeseries: !envIsDev()
      ? {
          timeField: "date",
          granularity: "hours"
        }
      : undefined,
    autoCreate: true,
    timestamps: true
  }
);

/**
 * MODELS
 */
export const SundownDigest = mongoose.model<SundownDigestDocument>("SundownDigest", sundownDigestSchema);
