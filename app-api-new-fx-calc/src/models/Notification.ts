import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";
import { ProviderEnum } from "../configs/providersConfig";
import { NotificationEventType } from "../configs/notificationSettingsConfig";
import { EmailType } from "../external-services/mailerService";
import DbUtil from "../utils/dbUtil";
import NotificationService from "../services/notificationService";

/**
 * TYPES
 */
export const NotificationStatusArray = ["Pending", "Sent", "Skipped"] as const;
export type NotificationStatusType = (typeof NotificationStatusArray)[number];

export const NotificationMethodArray = ["email", "app"] as const;
export type NotificationMethodType = (typeof NotificationMethodArray)[number];

export enum NotificationMetadataTypeEnum {
  LEARNING_GUIDE = "learningGuide",
  ANALYST_INSIGHT = "analystInsight",
  OTHER = "other"
}

export type CustomNotificationProperties = Map<string, string>;

/**
 * INTERFACES
 */
export interface NotificationDTOInterface {
  owner: mongoose.Types.ObjectId;
  method: NotificationMethodType;
  status: NotificationStatusType;
  sentAt?: Date;
  createdAt?: Date;
  notifyAt?: Date;
  providers: {
    intercom?: {
      notificationId: NotificationEventType;
      properties?: CustomNotificationProperties;
    };
    onesignal?: {
      notificationId: NotificationEventType;
      // properties are variables for the notification template (title, body, etc)
      properties?: CustomNotificationProperties;
      // metadata is additional information for the notification (e.g. the documentId of the asset that was sold or the id of the analyst insight
      // that was just published) - can be considered as background data send to the mobile client
      metadata?: {
        notificationType: NotificationMetadataTypeEnum;
        documentId: string;
      };
    };
    postmark?: {
      notificationId: EmailType;
      properties?: CustomNotificationProperties;
    };
  };
}

export interface NotificationInterface extends Omit<NotificationDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationDocument extends NotificationInterface, Document {}

/**
 * SCHEMA
 */
const notificationSchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    method: { type: String, enum: NotificationMethodArray, required: true },
    status: { type: String, enum: NotificationStatusArray, default: "Pending" },
    sentAt: { type: Date },
    notifyAt: { type: Date, default: Date.now },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.INTERCOM]: {
          _id: false,
          notificationId: { type: String },
          properties: { type: Map, of: String }
        },
        [ProviderEnum.ONESIGNAL]: {
          _id: false,
          // notification id is a wealthyhood human-readable id and different from the onesignal id
          notificationId: { type: String },
          properties: { type: Map, of: String },
          metadata: {
            _id: false,
            notificationType: { type: String, enum: Object.values(NotificationMetadataTypeEnum) },
            documentId: { type: String }
          }
        },
        [ProviderEnum.POSTMARK]: {
          _id: false,
          notificationId: { type: String },
          properties: { type: Map, of: mongoose.Schema.Types.Mixed }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);
notificationSchema.index({ owner: 1 });
notificationSchema.index({ owner: 1, "providers.onesignal.properties.title": 1 });

export const Notification = mongoose.model<NotificationDocument>("Notification", notificationSchema);

/**
 * CHANGE STREAMS
 */
DbUtil.listenToChangeStream<NotificationDocument>(
  Notification,
  "update",
  async (notification: NotificationDocument) => {
    await NotificationService.markSameTypeAndDayContentNotificationsAsSkipped(notification);
  }
);
