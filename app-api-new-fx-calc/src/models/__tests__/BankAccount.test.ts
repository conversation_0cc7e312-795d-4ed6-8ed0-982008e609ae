import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildBankAccount } from "../../tests/utils/generateModels";

describe("BankAccount", () => {
  beforeAll(async () => await connectDb("BankAccount"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("isAvailableForDirectDebit", () => {
    it("should return isAvailableForDirectDebit set to false if IBAN starts with GB", async () => {
      const bankAccount = await buildBankAccount({ iban: "GB********90" });

      expect(bankAccount.isAvailableForDirectDebit).toEqual(false);
    });

    it("should return isAvailableForDirectDebit set to true if IBAN does not start with GB", async () => {
      const bankAccount = await buildBankAccount({ iban: "GR********90" });

      expect(bankAccount.isAvailableForDirectDebit).toEqual(true);
    });

    it("should return isAvailableForDirectDebit set to true if bank account has no IBAN", async () => {
      const bankAccount = await buildBankAccount({ sortCode: "00-00-00", number: "********" });

      expect(bankAccount.isAvailableForDirectDebit).toEqual(true);
    });
  });
});
