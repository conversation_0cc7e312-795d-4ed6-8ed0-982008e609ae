import { DailySavingsProductTickerDocument } from "./DailyTicker";
import mongoose, { Document, Schema } from "mongoose";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";

/**
 * TYPES
 */
export type LineType = {
  active: boolean;

  // generated by MongoDB
  readonly updatedAt: Date;
};

/**
 * INTERFACES
 */
export interface SavingsProductInterface {
  commonId: savingsUniverseConfig.SavingsProductType;
  buyLine: LineType;
  sellLine: LineType;

  // VIRTUAL
  readonly currentTicker: DailySavingsProductTickerDocument;
  // generated by MongoDB
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

/**
 * DOCUMENTS
 */
export interface SavingsProductDocument extends SavingsProductInterface, Document {}

/**
 * SCHEMA
 */

const lineSchema: Schema = new mongoose.Schema(
  {
    active: { type: Boolean, default: true }
  },
  { _id: false, timestamps: { createdAt: false, updatedAt: true } }
);

const savingsProductSchema: Schema = new mongoose.Schema(
  {
    commonId: {
      type: String,
      enum: savingsUniverseConfig.SavingsProductArray,
      required: [true, "You need to specify a global identifier for the savings product"],
      unique: true
    },
    buyLine: lineSchema,
    sellLine: lineSchema
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

savingsProductSchema.virtual("currentTicker", {
  ref: "DailySavingsProductTicker",
  localField: "_id",
  foreignField: "savingsProduct",
  justOne: true,
  perDocumentLimit: 1,
  options: { sort: { date: -1 } }
});

export const SavingsProduct = mongoose.model<SavingsProductDocument>("SavingsProduct", savingsProductSchema);
