import mongoose, { Document, Schema } from "mongoose";
import { UserDocument } from "./User";

/**
 * ENUMS
 */
export enum UserDataRequestReasonEnum {
  INACTIVE_USER = "inactive-user",
  USER_REQUEST = "user-request",
  ADMIN_REQUEST = "admin-request"
}

/**
 * TYPES
 */
export const UserDataRequestTypeArray = ["gdpr-delete", "disassociation"] as const;
export type UserDataRequestTypeType = (typeof UserDataRequestTypeArray)[number];

export const UserDataRequestStatusArray = ["Created", "Completed"] as const;
export type UserDataRequestStatusType = (typeof UserDataRequestStatusArray)[number];

/**
 * DOCUMENTS
 */
export interface UserDataRequestDTOInterface {
  owner: mongoose.Types.ObjectId;
  requestType: UserDataRequestTypeType;
  reason: UserDataRequestReasonEnum;
}

export interface UserDataRequestInterface extends Omit<UserDataRequestDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;
  status: UserDataRequestStatusType;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserDataRequestDocument extends UserDataRequestInterface, Document {}

/**
 * SCHEMA
 */
const userDataRequestSchema: Schema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    requestType: {
      type: String,
      enum: UserDataRequestTypeArray,
      required: true
    },
    reason: {
      type: String,
      enum: UserDataRequestReasonEnum,
      required: false
    },
    status: { type: String, enum: UserDataRequestStatusArray, default: "Created" }
  },
  { timestamps: true }
);

export const UserDataRequest = mongoose.model<UserDataRequestDocument>("UserDataRequest", userDataRequestSchema);
