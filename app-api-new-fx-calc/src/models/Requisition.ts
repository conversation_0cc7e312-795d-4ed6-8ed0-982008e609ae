import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";
import { ProviderEnum } from "../configs/providersConfig";

/**
 * A requisition represents the **intent** for a bank account linking.
 *
 * Specifically, in the context of linking a bank account with GoCardless data, the requisition document holds the
 * mapping between the reference string that is included in the redirection URL and the actual GoCardless requisition
 * entity that holds account information for every account that was enabled by the user during the flow.
 */
export interface RequisitionDTOInterface {
  owner: mongoose.Types.ObjectId;
  reference: string;
  providers: {
    [ProviderEnum.GOCARDLESS_DATA]: {
      id: string;
    };
  };
}

export interface RequisitionInterface extends Omit<RequisitionDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;
}

export interface RequisitionDocument extends RequisitionInterface, Document {}

const requisitionSchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true
    },
    reference: {
      type: String,
      unique: true,
      required: true,
      index: true
    },
    providers: {
      [ProviderEnum.GOCARDLESS_DATA]: {
        id: String
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

export const Requisition = mongoose.model<RequisitionDocument>("Requisition", requisitionSchema);
