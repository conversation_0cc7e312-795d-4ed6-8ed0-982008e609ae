export abstract class BaseError extends Error {
  public status: 400 | 403 | 404 | 409 | 422 | 500 | 501;
  public description: string;
  constructor(message: string) {
    super(message);
    this.name = "BaseError";
  }
}

export class NotFoundError extends BaseError {
  constructor(message: string, description?: string) {
    super(message);
    this.name = "NotFoundError";
    this.description = description ?? "Resource not found";
    this.status = 404;
  }
}
export class BadRequestError extends BaseError {
  constructor(message: string, description?: string) {
    super(message);
    this.name = "BadRequestError";
    this.description = description ?? "Operation failed";
    this.status = 400;
  }
}

export class InternalServerError extends BaseError {
  constructor(message: string, description?: string) {
    super(message);
    this.name = "InternalServerError";
    this.description = description ?? "An error occurred";
    this.status = 500;
  }
}

export class ForbiddenError extends BaseError {
  constructor(message: string, description?: string) {
    super(message);
    this.name = "ForbiddenError";
    this.description = description ?? "Inaccessible resource";
    this.status = 403;
  }
}

export class NotImplementedError extends BaseError {
  constructor(message: string, description?: string) {
    super(message);
    this.name = "NotImplementedError";
    this.description = description ?? "Not Implemented";
    this.status = 501;
  }
}

export class ConflictError extends BaseError {
  constructor(message: string, description?: string) {
    super(message);
    this.name = "ConflictError";
    this.description = description ?? "Conflict";
    this.status = 409;
  }
}

export class UnprocessableEntityError extends BaseError {
  constructor(message: string, description?: string) {
    super(message);
    this.name = "UnprocessableEntityError";
    this.description = description ?? "Unprocessable Entity";
    this.status = 422;
  }
}
