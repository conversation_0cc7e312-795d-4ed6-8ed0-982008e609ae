import mongoose, { Document } from "mongoose";
import { countriesConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "./User";
import { ProviderEnum } from "../configs/providersConfig";

export interface AddressDTOInterface {
  owner: mongoose.Types.ObjectId | UserDocument;
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  region?: string;
  countryCode: countriesConfig.CountryCodesType;
  postalCode: string;
  activeProviders: ProviderEnum[];
  providers?: {
    wealthkernel?: {
      id: string;
    };
  };
}

export interface AddressInterface extends Omit<AddressDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;
}

export interface AddressDocument extends AddressInterface, Document {}

const addressSchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    line1: { type: String, required: true },
    line2: String,
    line3: String,
    city: { type: String, required: true },
    region: String,
    countryCode: { type: String, enum: countriesConfig.countryCodesArray, required: true },
    postalCode: { type: String, required: true },
    activeProviders: {
      type: Object.values(ProviderEnum),
      required: true
    },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

/**
 * INDEXES
 */
addressSchema.index({ owner: 1 });

export const Address = mongoose.model<AddressDocument>("Address", addressSchema);
