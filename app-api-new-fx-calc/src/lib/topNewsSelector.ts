import { generateObject } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";

const ANTHROPIC_MODEL_NAME = "claude-3-7-sonnet-20250219";

const SECTION_SCHEMA = z.object({
  companyName: z.string(),
  ticker: z.string().optional(),
  assetId: z.string(),
  title: z.string(),
  content: z.string()
});

const TOP_NEWS_SCHEMA = z.object({
  topNews: z.array(SECTION_SCHEMA).max(5)
});

export type TopNewsSection = z.infer<typeof SECTION_SCHEMA>;

const TOP_NEWS_SYSTEM_PROMPT =
  "You are an AI assistant for Wealthyhood, an investing app. You will be given a list of news items (each with companyName, ticker, assetId, title, content). Select the 5 most interesting, impactful, or relevant news items for investors this week. Prioritize diversity by company, avoid duplicates, and focus on actionable or high-signal news. Do not invent news. Return only the top 5 as an array in the required format.";

export default class TopNewsSelector {
  /**
   * @description Selects the top 5 most interesting news items from the week's market news sections.
   * @param sections Array of news sections with assetId
   * @returns Array of up to 5 top news items
   */
  public static async select(sections: TopNewsSection[]): Promise<TopNewsSection[]> {
    if (!sections || sections.length === 0) return [];
    try {
      const { object: rawData } = await generateObject({
        model: anthropic(ANTHROPIC_MODEL_NAME),
        system: TOP_NEWS_SYSTEM_PROMPT,
        prompt: JSON.stringify(sections),
        schema: TOP_NEWS_SCHEMA,
        temperature: 0.7
      });
      return TOP_NEWS_SCHEMA.parse(rawData).topNews;
    } catch (err) {
      captureException(err);
      logger.error("Error selecting top news", {
        module: "TopNewsSelector",
        method: "select",
        data: { err, sections }
      });
      return [];
    }
  }
}
