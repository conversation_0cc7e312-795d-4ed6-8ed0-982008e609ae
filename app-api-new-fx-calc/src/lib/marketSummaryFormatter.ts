import { generateObject } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";

const ANTHROPIC_MODEL_NAME = "claude-3-7-sonnet-20250219";

const MARKET_SUMMARY_SCHEMA = z
  .object({
    overview: z.string().nonempty().describe("Overview text for the market summary article"),
    sections: z
      .array(
        z.object({
          companyName: z.string().nonempty().describe("Full name of the company"),
          companyTicker: z.string().optional().describe("Stock ticker symbol"),
          title: z.string().nonempty().describe("Short title of the section"),
          content: z.string().nonempty().describe("Summary of news content related to this stock")
        })
      )
      .optional()
  })
  .strict();

export type FormattedMarketSummaryType = z.infer<typeof MARKET_SUMMARY_SCHEMA>;

const MARKET_SUMMARY_SYSTEM_PROMPT = `You are a financial content formatter specializing in organizing market summaries for Wealthyhood, an investing app.
  Your task is to extract information about companies mentioned in the text and format it into structured data.
  Include the company name, stock ticker, and a concise summary of the news content for each company.

  The text is a market summary for the day. It is a collection of news items for each company.

  THIS IS IMPORTANT:
  If you see a paragraph that mentions two companies and it's for the same news, DO NOT create two sections. Create one and highlight
  the company that you can find the ticker symbol. For some news sections, such as political news, there won't be a ticker symbol mentioned
  That's ok still include the section. Omit it only if you can't find a relevant company name.

  Regarding your tone:
  - Use a neutral style similar to the tone provided by the input text
  - DO NOT use your own humor, memes or slang - try to remain as close to the original text
  - Focus on providing as much of the information given to you - do not make assumptions or try to add your own facts
  - The beginning of the section SHOULD NOT have any words used for connectivity between sentences that are provided in the input

  Regarding the overview:
  - You will be asked to create a short overview of the article
  - This should be a short summary of the whole article with less than 200 characters
  - Avoid jargon and unnecessary information
  - Try to include as many highlights as possible from the provided news
  - Connect the highlights properly, don't just add titles with ','
  - Avoid adding in the beginning any intro words such as "Wealthyhood Market Summary:" or "Market moves:"

  Regarding the title of each news section:
  - Should be in lowercase EXCEPT FOR company names, countries, and other proper nouns.
  - IMPORTANT: company names, countries, and other proper nouns should be Capitalised or in UPPERCASE (depending on the original text).

  Regarding the ticker:
  - (GOOG, GOOGL) should be extracted as GOOGL

  If you see any mentions to 'sundown digest' or similar, replace with 'Wealthyhood Market Summary'.

  The output should be a JSON object with the following fields:
  - overview: A short summary of the whole article with less than 200 characters
  - sections: An array of objects with the following fields:
    - companyName: Full name of the company
    - companyTicker: Stock ticker symbol
    - title: A section title with less than 40 characters
    - content: Summary of news content related to this stock
`;

export default class MarketSummaryFormatter {
  /**
   * @description Formats a market summary text using AI to extract structured data about companies.
   * @param inputText The raw market summary text
   * @returns A formatted market summary object and markdown content
   */
  public static async format(inputText: string): Promise<FormattedMarketSummaryType> {
    try {
      // Call the language model to generate structured data
      const { object: rawData } = await generateObject({
        model: anthropic(ANTHROPIC_MODEL_NAME),
        system: MARKET_SUMMARY_SYSTEM_PROMPT,
        prompt: inputText,
        schema: MARKET_SUMMARY_SCHEMA,
        temperature: 0.7
      });

      return MARKET_SUMMARY_SCHEMA.parse(rawData);
    } catch (err) {
      captureException(err);
      logger.error("Error formatting market summary:", {
        module: "MarketSummaryFormatter",
        method: "format",
        data: {
          inputText,
          err
        }
      });
    }
  }
}
