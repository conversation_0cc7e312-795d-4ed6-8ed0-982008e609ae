import { generateObject } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";

const ANTHROPIC_MODEL_NAME = "claude-3-7-sonnet-20250219";

const TITLE_FORMATTER_SCHEMA = z
  .object({
    formattedTitle: z.string().nonempty().describe("The properly formatted title")
  })
  .strict();

export type FormattedTitleType = z.infer<typeof TITLE_FORMATTER_SCHEMA>;

const TITLE_FORMATTER_SYSTEM_PROMPT = `You are a title formatter for Wealthyhood, an investing app.
Your task is to properly format titles for analyst insights, ensuring they follow sentence case rules:

1. Make words lowercase by default
2. Preserve proper capitalization for:
   - Proper nouns (names, companies, countries, etc.)
   - Acronyms (e.g., "AI", "GDP", "FAANG", "ESG")
   - First word of the title
   - First word after a colon or dash
3. Keep lowercase:
   - Articles (a, an, the)
   - Coordinating conjunctions (and, but, for, or, nor)
   - Prepositions with fewer than 5 letters (in, on, at, by, for, etc.)
   - "to" in infinitives
4. Special cases:
   - Preserve hyphenated words as they appear in the original
   - Preserve special formatting like emojis at the beginning of titles

The output should be a JSON object with a single field:
- formattedTitle: The properly formatted title
`;

export default class AnalystInsightTitleFormatter {
  /**
   * @description Formats a title using AI to ensure proper title case with correct capitalization
   * of proper nouns, acronyms, and other special cases.
   * @param title The original title to format
   * @returns The formatted title
   */
  public static async format(title: string): Promise<string> {
    try {
      // If title is empty or undefined, return as is
      if (!title) {
        return title;
      }

      // Call the language model to format the title
      const { object: rawData } = await generateObject({
        model: anthropic(ANTHROPIC_MODEL_NAME),
        system: TITLE_FORMATTER_SYSTEM_PROMPT,
        prompt: title,
        schema: TITLE_FORMATTER_SCHEMA,
        temperature: 0.2 // Lower temperature for more consistent results
      });

      const result = TITLE_FORMATTER_SCHEMA.parse(rawData);
      return result.formattedTitle;
    } catch (err) {
      captureException(err);
      logger.error("Error formatting analyst insight title:", {
        module: "AnalystInsightTitleFormatter",
        method: "format",
        data: {
          originalTitle: title,
          error: err
        }
      });

      // Return the original title if formatting fails
      return title;
    }
  }
}
