import { generateObject } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import { captureException } from "@sentry/node";
import logger from "../external-services/loggerService";

const ANTHROPIC_MODEL_NAME = "claude-3-7-sonnet-********";

const SCHEMA = z.object({
  decision: z.string().nonempty().describe("The decision")
});

export enum BankAccountNameCheckResultEnum {
  MATCH = "MATCH",
  NO_MATCH = "NO_MATCH"
}

const BANK_ACCOUNT_NAME_CHECKER_SYSTEM_PROMPT = `You are an AI assistant specializing in name matching and verification.
Your task is to determine if two names likely refer to the same person, accounting for common variations and differences in name representation.

When comparing names, consider the following scenarios:
1. Nicknames and full names (e.g., <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>)
2. Middle names or initials that may be present in one name but not the other
3. Different order of names (e.g., "<PERSON>" vs "<PERSON>")
4. Common spelling variations
5. Extra or missing spaces between names
6. Different capitalization

Guidelines for matching:
- If the names are clearly the same person despite minor variations, return "MATCH"
- If the names are significantly different and likely refer to different people, return "NO_MATCH"
- Be conservative in matching - if there's significant uncertainty, prefer "NO_MATCH"
- Consider cultural naming conventions and common name variations
- Ignore differences in capitalization and extra spaces
- Consider that middle names/initials may be optional

Your response should be either "MATCH" or "NO_MATCH" based on whether the names likely refer to the same person.`;

export default class BankAccountNameChecker {
  public static async checkBankAccountName(
    bankAccountName: string,
    userFullName: string
  ): Promise<BankAccountNameCheckResultEnum | undefined> {
    try {
      // If we have an exact match already, we don't need to use the AI check.
      if (bankAccountName.trim().toLowerCase() === userFullName.trim().toLowerCase()) {
        return BankAccountNameCheckResultEnum.MATCH;
      }

      const prompt = this._generatePrompt(bankAccountName, userFullName);

      const { object: resolvedData } = await generateObject({
        model: anthropic(ANTHROPIC_MODEL_NAME),
        system: BANK_ACCOUNT_NAME_CHECKER_SYSTEM_PROMPT,
        prompt,
        schema: SCHEMA,
        temperature: 0.2
      });

      return resolvedData.decision as BankAccountNameCheckResultEnum;
    } catch (err) {
      captureException(err);
      logger.error("Error checking bank account name:", {
        module: "BankAccountNameChecker",
        method: "checkBankAccountName",
        data: {
          bankAccountName,
          userFullName,
          err
        }
      });

      return undefined;
    }
  }

  /**
   * Generates a prompt for the model to decide whether the name on the user's bank account matches the user name.
   * @returns A formatted prompt string
   * @private
   * @param bankAccountName
   * @param userFullName
   */
  private static _generatePrompt(bankAccountName: string, userFullName: string): string {
    return `Decide whether this bank account name matches the user full name:
      Name on bank account: ${bankAccountName}
      User's full name: ${userFullName}`;
  }
}
