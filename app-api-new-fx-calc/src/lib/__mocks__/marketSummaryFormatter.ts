import { generateObject } from "ai";
import { marketSummaryModelMock } from "../../tests/utils/aiModelMocks";
import { z } from "zod";

const MARKET_SUMMARY_SCHEMA = z
  .object({
    overview: z.string().nonempty().describe("Overview text for the market summary article"),
    sections: z
      .array(
        z.object({
          companyName: z.string().nonempty().describe("Full name of the company"),
          companyTicker: z.string().optional().describe("Stock ticker symbol"),
          title: z.string().nonempty().describe("Short title of the section"),
          content: z.string().nonempty().describe("Summary of news content related to this stock")
        })
      )
      .optional()
  })
  .strict();

export default class MarketSummaryFormatter {
  public static async format(inputText: string) {
    const { object: rawData } = await generateObject({
      model: marketSummaryModelMock,
      prompt: inputText,
      schema: MARKET_SUMMARY_SCHEMA,
      temperature: 0.7
    });

    return MARKET_SUMMARY_SCHEMA.parse(rawData);
  }
}
