import { generateObject } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";

const ANTHROPIC_MODEL_NAME = "claude-3-7-sonnet-20250219";

const MARKET_ROUNDUP_SCHEMA = z.object({
  overview: z.string().describe("Aggregated market overview, max 280 chars")
});

const MARKET_ROUNDUP_SYSTEM_PROMPT =
  "You are a financial content summarizer for Wealthyhood, an investing app. Your job is to take several daily market summary overviews and combine them into a single, concise, engaging, and informative weekly market overview for a newsletter.\n\nGuidelines:\n- Make the summary max 280 characters.\n- Use neutral, millennial-friendly tone.\n- Focus on the most important and recurring trends or highlights.\n- Avoid repetition.\n- Do not introduce new facts or data.\n- Avoid generic intros/outros.\n- Do not mention specific days.\n- Connect the highlights smoothly.\n";

export default class MarketRoundupCreator {
  /**
   * @description Aggregates an array of market summary overviews into a single weekly overview (max 280 chars).
   * @param overviews Array of daily market summary overviews
   * @returns Aggregated overview string
   */
  public static async aggregate(overviews: string[]): Promise<string> {
    if (!overviews || overviews.length === 0) return "";
    try {
      const prompt = overviews.join("\n");
      const { object: rawData } = await generateObject({
        model: anthropic(ANTHROPIC_MODEL_NAME),
        system: MARKET_ROUNDUP_SYSTEM_PROMPT,
        prompt,
        schema: MARKET_ROUNDUP_SCHEMA,
        temperature: 0.7
      });
      return MARKET_ROUNDUP_SCHEMA.parse(rawData).overview;
    } catch (err) {
      captureException(err);
      logger.error("Error aggregating market overviews", {
        module: "MarketRoundupCreator",
        method: "aggregate",
        data: { overviews, err }
      });
      return "";
    }
  }
}
