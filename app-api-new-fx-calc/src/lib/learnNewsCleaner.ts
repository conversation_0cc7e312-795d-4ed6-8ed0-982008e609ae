import { generateObject } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import { captureException } from "@sentry/node";
import logger from "../external-services/loggerService";
import { delay } from "../utils/scriptUtil";

const ANTHROPIC_MODEL_NAME = "claude-3-7-sonnet-20250219";

const LEARN_NEWS_CLEANER_OUTPUT_SCHEMA = z.object({
  htmlContent: z.string().describe("Cleaned HTML content")
});

const LEARN_NEWS_CLEANER_SYSTEM_PROMPT = `You are an AI assistant specializing in cleaning financial news content for publication.

Clean the following HTML financial content by applying these rules:

1. Remove any paragraphs that contain calls-to-action or promote Financial Modeling Prep tools/websites
2. For companies mentioned in the content:
  - Remove all <a> tags and their href attributes
  - Preserve important financial metrics like current prices, target prices, and percentage changes
3. Remove any sentences that reference clicking links, visiting websites, or would lose meaning after link removal

Return only the cleaned HTML content ready for publication.`;

export default class LearnNewsCleaner {
  /**
   * Cleans the given HTML content by removing FMP promos and links.
   * @returns The cleaned HTML content
   * @param content
   */
  public static async clean(content: string): Promise<string> {
    try {
      const prompt = this._generatePrompt(content);

      const { object: cleanedContent } = await generateObject({
        model: anthropic(ANTHROPIC_MODEL_NAME),
        system: LEARN_NEWS_CLEANER_SYSTEM_PROMPT,
        prompt,
        schema: LEARN_NEWS_CLEANER_OUTPUT_SCHEMA,
        temperature: 0.2
      });

      // We add a delay of 60 seconds after after every prompt execution to avoid hitting Anthropic API rate limits.
      await delay(60000);

      return cleanedContent.htmlContent;
    } catch (err) {
      captureException(err);
      logger.error("Error cleaning news content HTML:", {
        module: "LearnNewsCleaner",
        method: "cleanLearnNewsArticle",
        data: {
          content,
          err
        }
      });
      return undefined;
    }
  }

  /**
   * Generates a prompt for the model to clean news content from FMP.
   * @param content HTML content for a news article
   * @returns A formatted prompt string
   * @private
   */
  private static _generatePrompt(content: string): string {
    return `Clean the following HTML content:
      ${content}
      `;
  }
}
