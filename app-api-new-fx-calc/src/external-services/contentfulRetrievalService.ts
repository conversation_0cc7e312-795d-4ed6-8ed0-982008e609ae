import { ContentfulClientApi, createClient, Entry, EntryCollection, EntrySkeletonType } from "contentful";
import { ContentfulContentTypeEnum, ContentfulAccountEnum } from "../configs/contentfulConfig";

export type RequestOptions = {
  limit: number;
};

class ContentfulRetrievalService {
  private _client: ContentfulClientApi<undefined>;
  private static _instances: Record<ContentfulAccountEnum, ContentfulRetrievalService> = {
    [ContentfulAccountEnum.LANDING_PAGE]: null,
    [ContentfulAccountEnum.LEARN_HUB]: null
  };

  constructor(options: { account: ContentfulAccountEnum }) {
    const space = process.env[`CONTENTFUL_SPACE_${options?.account}`];
    const environment = process.env[`CONTENTFUL_ENVIRONMENT_${options?.account}`];
    const accessToken = process.env[`CONTENTFUL_RETRIEVAL_ACCESS_TOKEN_${options?.account}`] as string;

    this._client = createClient({
      accessToken,
      space,
      environment,
      host: "cdn.contentful.com"
    });
  }

  // ====================
  // Statics - Utils
  // ====================
  public static get LandingPageInstance(): ContentfulRetrievalService {
    return (
      this._instances[ContentfulAccountEnum.LANDING_PAGE] ||
      (this._instances[ContentfulAccountEnum.LANDING_PAGE] = new this({
        account: ContentfulAccountEnum.LANDING_PAGE
      }))
    );
  }

  public static get LearnHubInstance(): ContentfulRetrievalService {
    return (
      this._instances[ContentfulAccountEnum.LEARN_HUB] ||
      (this._instances[ContentfulAccountEnum.LEARN_HUB] = new this({ account: ContentfulAccountEnum.LEARN_HUB }))
    );
  }

  public async getEntries(
    contentType: ContentfulContentTypeEnum,
    options: RequestOptions = { limit: 100 }
  ): Promise<EntryCollection<EntrySkeletonType>> {
    return this._client.getEntries({ content_type: contentType, limit: options.limit });
  }

  public async getEntriesByIds(
    contentType: ContentfulContentTypeEnum,
    ids: string[]
  ): Promise<EntryCollection<EntrySkeletonType>> {
    return this._client.getEntries({ content_type: contentType, "sys.id[in]": ids });
  }

  public async getEntry(id: string): Promise<Entry<EntrySkeletonType>> {
    return this._client.getEntry(id);
  }

  public async getEntryBySlug(
    slug: string,
    contentType: ContentfulContentTypeEnum
  ): Promise<Entry<EntrySkeletonType>> {
    const entries = await this._client.getEntries({
      content_type: contentType,
      "fields.slug": slug,
      limit: 1,
      include: 2 // this may need to be increased for deeper relationships
    });

    if (entries.items.length > 0) {
      return entries.items[0];
    } else {
      return null;
    }
  }
}

export default ContentfulRetrievalService;
