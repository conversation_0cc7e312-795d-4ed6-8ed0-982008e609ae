import * as configcat from "configcat-node";
import { FeatureFlagEnum } from "../configs/featuresConfig";

class ConfigCatService {
  private _client: configcat.IConfigCatClient;

  constructor() {
    this._client = configcat.getClient(process.env.CONFIG_CAT_API_KEY, configcat.PollingMode.AutoPoll);
  }

  public isPriceMomentumSentimentEnabled(email: string): boolean {
    return this._client.snapshot().getValue(FeatureFlagEnum.PRICE_MOMENTUM_SENTIMENT, false, {
      identifier: email
    });
  }
}

const configCatService = new ConfigCatService();

export default configCatService;
