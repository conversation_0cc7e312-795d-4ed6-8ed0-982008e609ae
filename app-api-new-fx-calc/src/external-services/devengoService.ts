import HttpUtil from "../utils/httpUtil";
import axios, { Method } from "axios";
import { captureException } from "@sentry/node";
import logger from "./loggerService";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import { ForbiddenError } from "../models/ApiErrors";
import { CustomRequest } from "custom";
import { hashSHA256utf8 } from "../utils/cryptoUtil";

/**
 * WEBHOOKS
 */
type DevengoWebhookSignaturePartKeyType = "v1" | "t";

export const IncomingPaymentStatusArray = ["created", "rejected", "confirmed"] as const;
export type IncomingPaymentStatusType = (typeof IncomingPaymentStatusArray)[number];

export const OutgoingPaymentStatusArray = [
  "created",
  "validating",
  "blocked",
  "denied",
  "pending",
  "delayed",
  "processing",
  "retrying",
  "confirmed",
  "rejected",
  "canceled",
  "reversed"
] as const;
export type OutgoingPaymentStatusType = (typeof IncomingPaymentStatusArray)[number];

export type ThirdPartyType = {
  name: string;
  account: { identifiers: IdentifierType[]; bank: BankType };
};

type BankType = { name: string; bic: string };
export type IdentifierType = { type: "iban"; iban: string };

type DevengoEventPayloadType = {
  id: string;
  type: string;
  created_at: string;
};

export type DevengoPaymentEventPayloadType = DevengoEventPayloadType & {
  data: {
    object: {
      id: string;
      status: IncomingPaymentStatusType;
      account_id: IncomingPaymentStatusType;
      company_reference: string;
      description: string;
      third_party: ThirdPartyType;
      amount: {
        cents: number;
        currency: currenciesConfig.MainCurrencyType;
      };
    };
  };
};

export type DevengoAccountEventPayloadType = DevengoEventPayloadType & {
  data: {
    object: {
      id: string;
      status: AccountStatusType;
      identifiers: IdentifierType[];
    };
  };
};

export type CreatePaymentDataType = {
  destination: {
    iban: string;
  };
  amount: {
    cents: number;
    currency: "EUR";
  };
  account_id: string;
  company_reference: string;
  recipient: string;
  description: string;
};

export type CreateAccountDataType = {
  name: string;
  currency: string;
  metadata: {
    wealthyhoodId: string;
  };
};

export const AccountStatusArray = ["created", "delayed", "active", "deactivated", "closed"] as const;
export type AccountStatusType = (typeof AccountStatusArray)[number];

type PaymentResponseType = {
  payment: { id: string };
};

type AccountResponseType = {
  account: {
    id: string;
    status: AccountStatusType;
    identifiers: IdentifierType[];
  };
};

export class DevengoService {
  private static _authenticationUrl = process.env.DEVENGO_AUTH_URL;
  private static _serviceUrl = process.env.DEVENGO_API_URL;
  private static _wealthyhoodAccountHolderId = process.env.DEVENGO_WEALTHYHOOD_ACCOUNT_HOLDER_ID;
  private static _instance: DevengoService;

  // E-mail and password are used to generate authentication tokens.
  private static _email = process.env.DEVENGO_EMAIL;
  private static _password = process.env.DEVENGO_PASSWORD;

  private _accessToken: string;
  private _authPromise: Promise<any>;

  private constructor() {
    DevengoService._verifyCredentialsExist();
    this._requestAccessToken();
  }

  // ===============
  // PUBLIC METHODS
  // ===============
  public static get Instance(): DevengoService {
    return this._instance || (this._instance = new this());
  }

  public static validateWebhookSignature(req: CustomRequest) {
    const signatureHeader = req.headers["x-devengo-webhooks-sig"] as string;

    const parts = Object.fromEntries(signatureHeader.split(",").map((part) => part.split("="))) as Record<
      DevengoWebhookSignaturePartKeyType,
      string
    >;

    const signedPayload = `${parts.t}.${JSON.stringify(req.body)}`;
    const secretDecoded = Buffer.from(process.env.DEVENGO_WEBHOOK_SECRET, "base64");

    const contentHmac = hashSHA256utf8(signedPayload, secretDecoded);

    if (contentHmac !== parts.v1) {
      throw new ForbiddenError("Received webhook from Devengo with invalid signature!");
    }
  }

  public async createPayment(paymentData: CreatePaymentDataType): Promise<PaymentResponseType> {
    return this._fetch({
      method: "POST",
      url: `${DevengoService._serviceUrl}/payments`,
      data: paymentData,
      headers: { "X-Devengo-Idempotency-Key": `${paymentData.description}-${paymentData.account_id}` }
    });
  }

  public async createAccount(accountData: CreateAccountDataType): Promise<AccountResponseType> {
    return this._fetch({
      method: "POST",
      url: `${DevengoService._serviceUrl}/accounts`,
      data: { ...accountData, account_holder_id: DevengoService._wealthyhoodAccountHolderId },
      headers: { "X-Devengo-Idempotency-Key": accountData.metadata.wealthyhoodId }
    });
  }

  public async getAccount(id: string): Promise<AccountResponseType> {
    return this._fetch({
      method: "GET",
      url: `${DevengoService._serviceUrl}/accounts/${id}`
    });
  }

  // ===============
  // PRIVATE METHODS
  // ===============
  private async _fetch(config: { method: Method; url: string; data?: any; headers?: any }): Promise<any> {
    await this._isReady();

    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${this._accessToken}`,
      ...config.headers
    };

    return await HttpUtil.fetch(
      { ...config, headers },
      {
        throwError: false,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "DevengoService",
          method: "_fetch"
        }
      }
    );
  }

  /**
   * @description Returns a promise to indicate whether the API authentication is complete
   */
  private async _isReady(): Promise<boolean> {
    await this._authPromise;
    return Boolean(this._accessToken);
  }

  private async _requestAccessToken(): Promise<void> {
    try {
      this._authPromise = axios.post(
        `${DevengoService._authenticationUrl}`,
        {
          email: DevengoService._email,
          password: DevengoService._password
        },
        {
          headers: {
            "Content-Type": "application/json"
          }
        }
      );

      const response = await this._authPromise;

      this._accessToken = response.data.token;

      setTimeout(() => this._requestAccessToken(), 60000);
    } catch (err) {
      logger.error("Retrieval of Devengo access token failed", {
        module: "DevengoService",
        method: "_requestAccessToken"
      });
      captureException(err);
      setTimeout(() => this._requestAccessToken(), 5000);
    }
  }

  private static _verifyCredentialsExist(): void {
    if (!DevengoService._email || !DevengoService._password) {
      throw new Error("DEVENGO_EMAIL or DEVENGO_PASSWORD env variable has not been set");
    }
    if (!DevengoService._serviceUrl) {
      throw new Error("DEVENGO_API_URL env variable is not set");
    }
    if (!DevengoService._authenticationUrl) {
      throw new Error("DEVENGO_AUTH_URL env variable is not set");
    }
    if (!DevengoService._wealthyhoodAccountHolderId) {
      throw new Error("DEVENGO_WEALTHYHOOD_ACCOUNT_HOLDER_ID env variable is not set");
    }
  }
}
