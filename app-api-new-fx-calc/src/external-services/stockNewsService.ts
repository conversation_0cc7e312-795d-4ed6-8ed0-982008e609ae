import logger from "./loggerService";
import HttpUtil from "../utils/httpUtil";
import { ETF_TICKER_TO_STOCK_NEWS_CONFIG } from "../configs/assetNewsConfig";
import { Method } from "axios";

/**
 * CONFIG
 */
const STOCK_NEWS_API_URL = "https://stocknewsapi.com/api/v1";
const DEFAULT_SUNDOWN_DIGESTS_LIMIT = 5;

export type StockNewsSentimentAPIResponse = {
  total: {
    [ticker: string]: {
      "Total Positive": number;
      "Total Negative": number;
      "Total Neutral": number;
      "Sentiment Score": number;
    };
  };
  data: {
    [date: string]: {
      [ticker: string]: {
        Neutral: number;
        Positive: number;
        Negative: number;
        sentiment_score: number;
      };
    };
  };
  total_pages: number;
};

type StockNewsAPIResponse = {
  data: StockNewsItem[];
  total_pages: number;
};

export type StockNewsItem = {
  news_url: string;
  image_url: string;
  title: string;
  text: string;
  source_name: string;
  date: string;
  topics: string[];
  sentiment: "Positive" | "Negative" | "Neutral";
  type: "Article";
  tickers: string[];
  news_id: string;
};

type SundownDigestItem = {
  id: number;
  text: string;
  date: string; // ISO full date in UTC
};

export enum StockNewsDaysEnum {
  LAST_7_DAYS = "last7days",
  LAST_30_DAYS = "last30days"
}

/**
 * Documentation for Stock News API can be found here: https://stocknewsapi.com/documentation
 */
export default class StockNewsService {
  private static _accessToken?: string = process.env.STOCK_NEWS_API_TOKEN;

  /**
   * Fetches stock news items for a given ticker.
   * @param ticker The stock ticker symbol.
   * @param limit The number of news items to retrieve.
   * @param date
   * @returns Promise that resolves to an array of StockNewsItem objects.
   */
  public static async getStockNews(
    ticker: string,
    limit: number,
    date?: StockNewsDaysEnum
  ): Promise<StockNewsItem[]> {
    const response: StockNewsAPIResponse = await StockNewsService._fetch({
      method: "GET",
      url: STOCK_NEWS_API_URL,
      params: {
        tickers: ticker,
        "extra-fields": "id",
        items: limit,
        page: 1,
        date,
        token: StockNewsService._accessToken
      }
    });

    return response.data;
  }

  public static async getEtfNews(
    ticker: string,
    limit: number,
    date?: StockNewsDaysEnum
  ): Promise<StockNewsItem[] | undefined> {
    const params = ETF_TICKER_TO_STOCK_NEWS_CONFIG[ticker];

    if (!params) {
      logger.warn(`No params for etf: ${ticker} `, {
        module: "StockNewsService",
        method: "getEtfNews"
      });
      return;
    }

    const response: StockNewsAPIResponse = await StockNewsService._fetch({
      method: "GET",
      url: `${STOCK_NEWS_API_URL}/category`,
      params: {
        section: "alltickers",
        "extra-fields": "id",
        items: limit,
        page: 1,
        date,
        token: StockNewsService._accessToken,
        ...params
      }
    });

    return response.data;
  }

  public static async getSundownDigests(
    limit: number = DEFAULT_SUNDOWN_DIGESTS_LIMIT,
    page = 1
  ): Promise<SundownDigestItem[] | undefined> {
    const response = await StockNewsService._fetch({
      method: "GET",
      url: `${STOCK_NEWS_API_URL}/sundown-digest`,
      params: {
        items: limit,
        page,
        token: StockNewsService._accessToken
      }
    });

    return response.data;
  }

  private static async _fetch(config: { method: Method; url: string; params: any }): Promise<any> {
    return await HttpUtil.fetch(
      { ...config },
      {
        throwError: true,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "stockNewsService",
          method: "_fetch"
        }
      }
    );
  }
}
