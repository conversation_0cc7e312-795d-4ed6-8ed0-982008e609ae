import { Method } from "axios";
import HttpUtil from "../utils/httpUtil";
import { JSDOM } from "jsdom";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";

export type BlackrockFundDataType = {
  /**
   * The amount that Blackrock distributes daily based on the holded amount inside the Fund.
   * This is amount multiplied by 365 equal the annual interest rate.
   * --
   * Note:
   * In some cases, like Fridays, the distribution factor includeds the weekend days
   * this means that the amount could be 3x than usual, or even more if a holiday is included.
   */
  dailyDistributionFactor: number;
  /**
   * Interest rate of the fund for the given fixed date.
   */
  oneDayYield: number;
  /**
   * The date when the data was fixed. Updated every day (excluding weekends and bank holidays)
   */
  fixingDate: Date;
};

export const SavingsProductURLConfig: Record<savingsUniverseConfig.SavingsProductType, string> = {
  mmf_dist_eur: "https://www.blackrock.com/cash/en-fi/products/329901/",
  mmf_dist_gbp:
    "https://www.blackrock.com/cash/en-fi/products/229256/blackrock-ics-sterling-liquidity-premier-dis-fund"
};

export default class BlackrockService {
  public static async getDistributingMoneyMarketFundData(
    savingsProductCommonId: savingsUniverseConfig.SavingsProductType
  ): Promise<BlackrockFundDataType> {
    const fundDataHtml: string = await BlackrockService._fetch({
      method: "GET",
      url: SavingsProductURLConfig[savingsProductCommonId]
    });

    const dom = new JSDOM(fundDataHtml);
    const document = dom.window.document;
    const dailyDistributionFactorDiv = document.querySelector(".col-dailyDistributionFactor");
    const fixingDateStr = dailyDistributionFactorDiv
      .querySelector(".as-of-date")
      .textContent.trim()
      .replace("\nas of ", "");
    const dailyDistributionFactor = dailyDistributionFactorDiv.querySelector(".data").textContent.trim();
    const oneDayYieldDiv = document.querySelector(".col-oneDayYield");
    const oneDayYield = oneDayYieldDiv.querySelector(".data").textContent.trim();

    return {
      dailyDistributionFactor: parseFloat(dailyDistributionFactor.replace(",", ".")),
      oneDayYield: parseFloat(oneDayYield.replace(",", ".")),
      fixingDate: new Date(fixingDateStr)
    };
  }

  private static async _fetch(config: { method: Method; url: string }): Promise<any> {
    return await HttpUtil.fetch(
      { ...config },
      {
        throwError: true,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "BlackrockService",
          method: "_fetch"
        }
      }
    );
  }
}
