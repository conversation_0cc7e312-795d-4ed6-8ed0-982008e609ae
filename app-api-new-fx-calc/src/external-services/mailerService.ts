import { ServerClient, TemplatedMessage, Message } from "postmark";
import { captureException } from "@sentry/node";
import { UserDocument } from "../models/User";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import logger from "../external-services/loggerService";

const { CURRENCY_SYMBOLS } = currenciesConfig;

export type EmailType =
  | "wealthyhoodDividendCreation"
  | "referrerRewardCreation"
  | "referralRewardCreation"
  | "rewardSuccess"
  | "gifterGiftCreation"
  | "unverifiedTargetUserGiftCreation"
  | "verifiedTargetUserGiftCreation"
  | "notExistingTargetUserGiftCreation"
  | "userInvitation"
  | "userVerification"
  | "repeatingInvestment"
  | "automatedRebalance"
  | "deletionCreation"
  | "deletionSuccess"
  | "deletionSuccessForInactiveUser";

export const EMAIL_TEMPLATE_IDS: Record<"development" | "staging" | "production", { [key in EmailType]: number }> =
  {
    development: {
      wealthyhoodDividendCreation: 32757688,
      referrerRewardCreation: 29942124,
      referralRewardCreation: 29942125,
      rewardSuccess: 27719937,
      gifterGiftCreation: 30640918,
      unverifiedTargetUserGiftCreation: 30641182,
      verifiedTargetUserGiftCreation: 30640953,
      notExistingTargetUserGiftCreation: 30641037,
      userInvitation: 31989062,
      userVerification: 24618719,
      repeatingInvestment: 32423929,
      automatedRebalance: 32424279,
      deletionCreation: 33376199,
      deletionSuccess: 33376351,
      deletionSuccessForInactiveUser: 35623426
    },
    staging: {
      wealthyhoodDividendCreation: 32757688,
      referrerRewardCreation: 29942124,
      referralRewardCreation: 29942125,
      rewardSuccess: 27719937,
      gifterGiftCreation: 30640918,
      unverifiedTargetUserGiftCreation: 30641182,
      verifiedTargetUserGiftCreation: 30640953,
      notExistingTargetUserGiftCreation: 30641037,
      userInvitation: 31989062,
      userVerification: 24618719,
      repeatingInvestment: 32423929,
      automatedRebalance: 32424279,
      deletionCreation: 33376199,
      deletionSuccess: 33376351,
      deletionSuccessForInactiveUser: 35623426
    },
    production: {
      wealthyhoodDividendCreation: 32757688,
      referrerRewardCreation: 29942124,
      referralRewardCreation: 29942125,
      rewardSuccess: 27719937,
      gifterGiftCreation: 30640918,
      unverifiedTargetUserGiftCreation: 30641182,
      verifiedTargetUserGiftCreation: 30640953,
      notExistingTargetUserGiftCreation: 30641037,
      userInvitation: 31989062,
      userVerification: 24618719,
      repeatingInvestment: 32423929,
      automatedRebalance: 32424279,
      deletionCreation: 33376199,
      deletionSuccess: 33376351,
      deletionSuccessForInactiveUser: 35623426
    }
  } as const;

const nodeEnv = process.env.NODE_ENV as "development" | "staging" | "production";
const postmarkServerClient = new ServerClient(process.env.POSTMARK_SERVER_API_KEY, { useHttps: true });

class MailerService {
  /**
   * PUBLIC METHODS
   */

  /**
   * The below method is used when we want to send an e-mail to an **existing** user.
   *
   * @param user
   * @param id
   * @param properties
   */
  public static sendEmail(user: UserDocument, id: EmailType, properties: object): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: user.email,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv][id],
      TemplateModel: {
        // eslint-disable-next-line camelcase
        user_first_name: user.firstName,
        user_currency: CURRENCY_SYMBOLS[user.currency],
        ...properties
      }
    };

    MailerService._sendEmailWithTemplate(msg);
  }

  public static sendNotExistingTargetUserGiftCreation(
    userEmail: string,
    gifter: UserDocument,
    message: string
  ): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: userEmail,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv].notExistingTargetUserGiftCreation,
      TemplateModel: {
        gifter_full_name: `${gifter.firstName} ${gifter.lastName}`,
        gifter_currency: CURRENCY_SYMBOLS[gifter.currency],
        message
      }
    };

    MailerService._sendEmailWithTemplate(msg);
  }

  public static sendUserInvitation(userEmail: string, referrer: UserDocument): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: userEmail,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv].userInvitation,
      TemplateModel: {
        referrer_full_name: `${referrer.firstName} ${referrer.lastName}`
      }
    };

    MailerService._sendEmailWithTemplate(msg);
  }

  /**
   * Postmark API allows up to 500 emails in a single batch call.
   * The postmark library's sendEmailBatch method should handle this array directly.
   * If larger batches are needed and the library doesn't chunk, manual chunking would be required here.
   *
   * @param messages
   * @returns
   */
  public static async batchEmails(messages: Message[]): Promise<void> {
    if (!messages || messages.length === 0) {
      return;
    }

    if (messages.length > 500) {
      logger.error(
        `Attempted to send ${messages.length} emails in a single batch but the Postmark API limit is 500.`,
        {
          module: "MailerService",
          method: "sendBatchEmails",
          data: {
            count: messages.length
          }
        }
      );
      return;
    }

    try {
      await postmarkServerClient.sendEmailBatch(messages);
    } catch (err) {
      captureException(err);
      logger.error("Error sending batch emails via Postmark:", {
        module: "MailerService",
        method: "sendBatchEmails",
        data: {
          error: err
        }
      });
    }
  }

  /**
   * PRIVATE METHODS
   */
  private static async _sendEmailWithTemplate(msg: TemplatedMessage): Promise<void> {
    (async (): Promise<void> => {
      try {
        await postmarkServerClient.sendEmailWithTemplate(msg);
      } catch (err) {
        captureException(err);
      }
    })();
  }
}

export default MailerService;
