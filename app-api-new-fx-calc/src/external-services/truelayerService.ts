import axios from "axios";
import { addBreadcrumb, captureException } from "@sentry/node";
import { IResult } from "truelayer-client";
import * as tlSigning from "truelayer-signing";
import qs from "qs";
import { BadRequestError, InternalServerError } from "../models/ApiErrors";
import logger from "./loggerService";
import { envIsProd } from "../utils/environmentUtil";
import { Request } from "express";
import { PartialRecord } from "utils";
import { getCachedDataWithFallback } from "../utils/cacheUtil";

const TRUELAYER_DOMAIN_CONFIG: Record<"development" | "staging" | "production", string> = {
  development: "truelayer-sandbox.com",
  staging: "truelayer-sandbox.com",
  production: "truelayer.com"
};

// =========================================
// =========================================
// ********* TRUELAYER PAY CLIENT **********
// =========================================
// =========================================

const WK_BENEFICIARY_NAME = process.env.WEALTHKERNEL_BENEFICIARY_NAME;
const WK_BENEFICIARY_ACCOUNT_NUMBER = process.env.WEALTHKERNEL_BENEFICIARY_ACCOUNT_NUMBER;
const WK_BENEFICIARY_SORT_CODE = process.env.WEALTHKERNEL_BENEFICIARY_SORT_CODE;
if (!WK_BENEFICIARY_NAME || !WK_BENEFICIARY_ACCOUNT_NUMBER || !WK_BENEFICIARY_SORT_CODE) {
  throw new Error("WK beneficiary bank details in env vars are undefined");
}

const WEALTHYHOOD_BENEFICIARY_NAME = process.env.WEALTHYHOOD_BENEFICIARY_NAME;
const WEALTHYHOOD_BENEFICIARY_ACCOUNT_NUMBER = process.env.WEALTHYHOOD_BENEFICIARY_ACCOUNT_NUMBER;
const WEALTHYHOOD_BENEFICIARY_SORT_CODE = process.env.WEALTHYHOOD_BENEFICIARY_SORT_CODE;
if (
  !WEALTHYHOOD_BENEFICIARY_NAME ||
  !WEALTHYHOOD_BENEFICIARY_ACCOUNT_NUMBER ||
  !WEALTHYHOOD_BENEFICIARY_SORT_CODE
) {
  throw new Error("Wealthyhood beneficiary bank details in env vars are undefined");
}

/**
 * ENUMS
 */
enum HttpMethodEnum {
  GET = "GET",
  POST = "POST"
}
enum TruelayerEndpointEnum {
  PAYMENTS = "payments"
}

export enum BeneficiaryEnum {
  WEALTHKERNEL = "wealthkernel",
  WEALTHYHOOD = "wealthyhood"
}

/**
 * TYPES
 */

export const FailureStatusArray = [
  "authorization_failed", // The PSU failed to authorise the payment successfully.
  "blocked", // The payment has been blocked due to a regulatory requirement. This may happen if the PSU fails a sanctions check.
  "canceled", // The PSU cancelled the payment on the hosted payment page or the payment was cancelled using Cancel Payment API.
  "constraint_violation", // The constraints set up for the mandate were breached by this recurring payment.
  "expired", // The token of the payment expired before the payment got authorised, thus it won't be possible to execute the payment.
  "expired_at_provider", // The payment failed because the token or exchange code used to communicate with the bank expired.
  "insufficient_funds", // The PSU did not have the required balance in their account to complete this payment.
  "internal_server_error", // An error has occurred within TrueLayer when processing the payment.
  "invalid_account_details", // The payment failed because either the creditor's or debtor's account details were invalid.
  "invalid_account_holder_name", // The payment failed because the account holder's name details were invalid.
  "invalid_credentials", // The banking credentials provided by the PSU to log into their bank were incorrect.
  "invalid_creditor_account", // The payment failed because an invalid creditor account reference was provided.
  "invalid_mandate_state", // The mandate was not in a valid status to create a recurring payment.
  "invalid_otp", // The PSU submitted an incorrect one-time password during the authorisation of the payment.
  "invalid_remitter_account", // The account details of the remitter bank account provided during the payment flow were incorrect.
  "invalid_request", // The payment failed due to invalid data in the request.
  "invalid_sort_code", // The payment failed due to an invalid sort code being provided.
  "mandate_revoked", // The mandate for a recurring payment is revoked.
  "not_authorized", // The PSU cancelled the payment or wasn't able to successfully authenticate on the provider's UI.
  "payment_limit_exceeded", // The PSU's payment limit amount with their bank was breached.
  "provider_error", // The provider has unexpectedly failed when creating the payment.
  "provider_rejected", // The payment was rejected by the provider for an unspecified reason.
  "rejected", // The payment was rejected for an unspecified reason.
  "scheme_unavailable", // There is no scheme available given the provider selection configuration.
  "unknown_error", // The payment failed for an unknown reason.
  "user_canceled", // The payment failed because the user cancelled the authorisation during the payment flow.
  "user_canceled_at_provider", // This is not documented - but showed up in one payment
  "verification_declined"
] as const;
export type FailureStatusType = (typeof FailureStatusArray)[number];

export const TruelayerPaymentVersionArray = ["v1", "v3"] as const;
export type TruelayerPaymentVersionType = (typeof TruelayerPaymentVersionArray)[number];

const TruelayerPayProvidersArray = [
  "ob-starling",
  "ob-barclays",
  "ob-boi",
  "ob-bos",
  "ob-danske",
  "ob-first-direct",
  "ob-halifax",
  "ob-hsbc",
  "ob-lloyds",
  "ob-monzo",
  "ob-nationwide",
  "ob-natwest",
  "ob-rbs",
  "ob-revolut",
  "ob-santander",
  "ob-tesco",
  "ob-tsb",
  "ob-ulster"
] as const;
export type TruelayerPayProvidersType = (typeof TruelayerPayProvidersArray)[number] | "mock-payments-gb-redirect";

type AccountIdentifierType = {
  type: "sort_code_account_number";
  sort_code: string; // 560029
  account_number: string; // ********
};

/* eslint-disable camelcase */
type AuthorizationFlowType = {
  configuration: {
    provider_selection: ProviderSelectionUserSelectedType | ProviderSelectionPreselectedType;
    redirect: {
      return_uri: string;
    };
    form: {
      input_types: ("text" | "select" | "text_with_image")[];
    };
  };
};

type ProviderSelectionUserSelectedType = {
  type: "user_selected";
  filter?: {
    countries?: (
      | "AT"
      | "BE"
      | "DE"
      | "DK"
      | "ES"
      | "FI"
      | "FR"
      | "GB"
      | "IE"
      | "IT"
      | "LT"
      | "NL"
      | "NO"
      | "PL"
      | "PT"
      | "RO"
    )[];
    release_channel?: "general_availability" | "public_beta" | "private_beta";
    customer_segments?: ("retail" | "business" | "corporate")[];
    provider_ids?: TruelayerPayProvidersType[];
    excludes?: {
      provider_ids?: TruelayerPayProvidersType[];
    };
  };
  // Payments in the UK use Faster Payments (faster_payments_service), which is free and instant.
  // If you only plan on making payments within the UK, you do not need to set scheme_selection for instant payments
  scheme_selection?:
    | {
        type: "instant_only";
        allow_remitter_fee?: boolean;
      }
    | {
        type: "instant_preferred";
        allow_remitter_fee?: boolean;
      };
};
type ProviderSelectionPreselectedType = {
  type: "preselected";
  provider_id: TruelayerPayProvidersType;
  // use Faster payments in the UK
  // https://docs.truelayer.com/docs/select-a-provider-for-a-payment#scheme_ids-for-payments
  scheme_id:
    | "provider_determined"
    | "sepa_credit_transfer"
    | "sepa_credit_transfer_instant"
    | "faster_payments_service";
  remitter?: {
    account_holder_name: string;
    account_identifier: AccountIdentifierType;
  };
  data_access_token?: string;
};
type BeneficiaryExternalAccountType = {
  type: "external_account";
  account_holder_name: string;
  account_identifier: AccountIdentifierType;
  reference: string; // pattern: ^[a-zA-Z0-9-:()\.,'\+ \?\/]+$
};

type PaymentMethodType = {
  type: "bank_transfer";
  provider_selection: ProviderSelectionUserSelectedType | ProviderSelectionPreselectedType;
  beneficiary: BeneficiaryExternalAccountType;
};
type UserType = {
  id?: string; // f9b48c9d-176b-46dd-b2da-fe1a2b77350c
  name?: string; // Remi Terr
  email?: string; // <EMAIL>
  phone?: string; // +************
  date_of_birth?: Date; // in YYYY-MM-DD format - 1990-01-31
  address?: {
    address_line1: string;
    address_line2?: string;
    city: string;
    state: string;
    zip: string;
    country_code: string; // https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
  };
};

type PaymentOptionsType = {
  amount_in_minor: number; // in minor units (cents)
  currency: "GBP" | "EUR" | "PLN" | "NOK";
  payment_method: PaymentMethodType;
  user: UserType;
  metadata?: object;
};
type PaymentSourceType = {
  account_identifies: AccountIdentifierType[];
  account_holder_name: string;
};

type PaymentCreationResponseType = {
  id: string;
  user: {
    id: string;
  };
  resource_token: string;
  status: PaymentStatusTypeV3;
};
/* eslint-enable camelcase */

export const PaymentStatusArrayV1 = [
  "new",
  "authorised",
  "cancelled",
  "failed",
  "rejected",
  "submitted",
  "executed"
] as const;
export type PaymentStatusTypeV1 = (typeof PaymentStatusArrayV1)[number];

export const PaymentStatusArrayV3 = [
  "authorization_required",
  "authorizing",
  "authorized",
  "executed", // terminal state for external accounts
  "failed", // terminal for all payments
  "settled" // terminal for payments into merchant accounts
] as const;
export type PaymentStatusTypeV3 = (typeof PaymentStatusArrayV3)[number];
export type PaymentTypeV3 = {
  id: string;
  created_at: Date;
  executed_at?: Date;
  status: PaymentStatusTypeV3;
  authorization_flow: AuthorizationFlowType;
  payment_source?: PaymentSourceType;
  failure_reason?: FailureStatusType;
} & PaymentOptionsType;
export type ProviderTypeV1 = {
  id: string;
  logo: string;
  icon: string;
  displayable_name: string;
  main_bg_color: string;
  supports_app_to_app: boolean;
  country_code: string;
  divisions: string[];
};
export type ProviderTypeV2 = {
  provider_id: string;
  logo_url: string;
  icon_url: string;
  display_name: string;
  country: string;
  divisions: ("retail" | "business")[];
  single_immediate_payment_schemes: { scheme_id: "faster_payments_service"; requirements: object[] }[];
};

const TRUELAYER_API_URL = process.env.TRUELAYER_API_URL;
const TRUELAYER_AUTH_URL = process.env.TRUELAYER_AUTH_URL;
const TRUELAYER_PAY_URL = process.env.TRUELAYER_PAY_URL;

/**
 * OTHER TYPES
 */
type RequestHeadersType = {
  Authorization?: string;
  "Idempotency-Key"?: string;
  "Tl-signature"?: string;
};

type BeneficiaryConfigType = {
  holderName: string;
  accountNumber: string;
  sortCode: string;
};
const BENEFICIARY_CONFIG: Record<BeneficiaryEnum, BeneficiaryConfigType> = {
  [BeneficiaryEnum.WEALTHKERNEL]: {
    holderName: WK_BENEFICIARY_NAME,
    accountNumber: WK_BENEFICIARY_ACCOUNT_NUMBER,
    sortCode: WK_BENEFICIARY_SORT_CODE
  },
  [BeneficiaryEnum.WEALTHYHOOD]: {
    holderName: WEALTHYHOOD_BENEFICIARY_NAME,
    accountNumber: WEALTHYHOOD_BENEFICIARY_ACCOUNT_NUMBER,
    sortCode: WEALTHYHOOD_BENEFICIARY_SORT_CODE
  }
};

type AuthResponseType = {
  access_token: string;
  expires_in: number; // in seconds
};

// In seconds
const TOKEN_CACHE_EXPIRATION_BUFFER = 10 * 60; // 10 minutes
const TOKEN_POLLING_INTERVAL = 5 * 60; // 5 minutes
const TOKEN_ERROR_INTERVAL = 60; // 1 minute

export class TruelayerPaymentsClient {
  private static _clientId = process.env.TRUELAYER_CLIENT_ID;
  private static _clientSecret = process.env.TRUELAYER_CLIENT_SECRET;
  private _accessToken: string;
  private _authPromise: Promise<AuthResponseType>;

  constructor() {
    TruelayerPaymentsClient._verifyCredentialsExist();
    this._requestAccessToken();
  }

  // ==========
  // PUBLIC METHODS
  // ==========

  public async createPayment({
    amount,
    bankReference,
    redirectUri,
    beneficiary,
    remitter,
    user
  }: {
    amount: number;
    bankReference: string;
    redirectUri: string;
    beneficiary: BeneficiaryEnum;
    remitter: {
      name: string;
      number: string;
      sortCode: string;
      providerId: TruelayerPayProvidersType;
    };
    user: {
      email: string;
      name: string;
    };
    clientInfo: {
      platform?: string;
      version?: string;
    };
  }): Promise<{ paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 }> {
    return this._createPayment({
      amount,
      bankReference,
      redirectUri,
      user,
      beneficiary,
      remitter: envIsProd()
        ? {
            holderName: remitter.name,
            accountNumber: remitter.number,
            sortCode: remitter.sortCode,
            providerId: remitter.providerId as TruelayerPayProvidersType
          }
        : null
    });
  }

  public async getPayment(paymentId: string): Promise<{
    status: PaymentStatusTypeV3;
    failureReason?: FailureStatusType | string;
    executedAt?: Date;
  }> {
    const payment = await this._getPayment(paymentId);

    if (!payment || !payment.status) {
      // No results exist => payment was not retrieved from truelayer API
      throw new InternalServerError(`Payment id ${paymentId} could not be found on truelayer`);
    }

    return {
      status: payment.status,
      failureReason: payment.status === "failed" ? payment.failure_reason : "",
      executedAt: payment.status === "executed" ? new Date(payment.executed_at) : null
    };
  }

  public async getProviders(): Promise<ProviderTypeV2[]> {
    const response = await axios({
      method: HttpMethodEnum.GET,
      url: `${TRUELAYER_PAY_URL}/v2/single-immediate-payments-providers`,
      params: {
        currency: "GBP",
        auth_flow_type: "redirect",
        account_type: "sort_code_account_number",
        client_id: process.env.TRUELAYER_CLIENT_ID
      }
    });

    return response.data?.results;
  }

  /**
   * @description
   * This function validate status updates, by checking that new statuses
   * -won't revert current status in the context of payments lifecycle.
   * For example We cannot update newStatus: "authorization_required" when currentStatus is "authorized".
   * Read more about payments statuses here: https://docs.truelayer.com/docs/single-payment-statuses#payment-statuses
   */
  public static validatePaymentStatusUpdate(
    currentStatus: PaymentStatusTypeV3,
    newStatus: PaymentStatusTypeV3
  ): boolean {
    if (newStatus === "failed" && !["failed", "executed"].includes(currentStatus)) return true;
    else if (
      newStatus === "authorization_required" &&
      !["failed", "executed", "authorized", "authorizing"].includes(currentStatus)
    )
      return true;
    else if (newStatus === "authorizing" && !["failed", "executed", "authorized"].includes(currentStatus))
      return true;
    else if (newStatus === "authorized" && !["failed", "executed"].includes(currentStatus)) return true;
    else if (newStatus === "executed" && !["failed", "executed"].includes(currentStatus)) return true;
    else return false;
  }

  // ==========
  // PRIVATE METHODS
  // ==========

  private _buildHostedPageUrl({
    payment_id,
    resource_token,
    return_uri
  }: {
    payment_id: string;
    resource_token: string;
    return_uri: string;
  }): string {
    return `https://payment.${
      TRUELAYER_DOMAIN_CONFIG[process.env.NODE_ENV as "development" | "staging" | "production"]
    }/payments#payment_id=${payment_id}&resource_token=${resource_token}&return_uri=${return_uri}`;
  }

  /**
   * @description Creates a truelayer payments and gets as a result a uri that will redirect the user to their bank
   * to auth & approve the payment.
   * @param options
   */
  private async _createPayment({
    amount,
    bankReference,
    remitter,
    beneficiary,
    redirectUri,
    user
  }: {
    amount: number;
    bankReference: string;
    beneficiary: BeneficiaryEnum;
    remitter?: {
      holderName: string;
      accountNumber: string;
      sortCode: string;
      providerId: TruelayerPayProvidersType;
    };
    redirectUri: string;
    user: {
      name?: string;
      email?: string;
    };
  }): Promise<{ paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 }> {
    const provider_selection: ProviderSelectionUserSelectedType | ProviderSelectionPreselectedType = envIsProd()
      ? {
          type: "preselected",
          provider_id: remitter.providerId,
          scheme_id: remitter.providerId === "ob-revolut" ? "provider_determined" : "faster_payments_service",
          remitter: {
            account_holder_name: remitter.holderName,
            account_identifier: {
              type: "sort_code_account_number",
              // sort codes are being stored in DB with '-'
              // in node v18 this can be refactored with replaceAll method
              sort_code: remitter.sortCode.trim().split("-").join(""),
              account_number: remitter.accountNumber
            }
          }
        }
      : {
          type: "user_selected"
        };

    const data: PaymentOptionsType = {
      amount_in_minor: amount,
      currency: "GBP",
      payment_method: {
        type: "bank_transfer",
        provider_selection,
        beneficiary: {
          type: "external_account",
          account_holder_name: BENEFICIARY_CONFIG[beneficiary].holderName,
          reference: bankReference,
          account_identifier: {
            type: "sort_code_account_number",
            sort_code: BENEFICIARY_CONFIG[beneficiary].sortCode,
            account_number: BENEFICIARY_CONFIG[beneficiary].accountNumber
          }
        }
      },
      user
    };

    const idempotencyKey = bankReference;
    const signature = this._signRequest(TruelayerEndpointEnum.PAYMENTS, data, idempotencyKey);

    const { id, resource_token, status }: PaymentCreationResponseType = await this._fetch({
      method: HttpMethodEnum.POST,
      url: `${TRUELAYER_API_URL}/${TruelayerEndpointEnum.PAYMENTS}`,
      headers: {
        "Tl-signature": signature,
        "Idempotency-Key": idempotencyKey
      },
      data
    });
    const paymentUri = this._buildHostedPageUrl({ payment_id: id, resource_token, return_uri: redirectUri });

    return { paymentId: id, paymentUri, status };
    /* eslint-enable camelcase */
  }

  private async _getPayment(paymentId: string): Promise<PaymentTypeV3> {
    const url = `${TRUELAYER_API_URL}/${TruelayerEndpointEnum.PAYMENTS}/${paymentId}`;
    return await this._fetch({ method: HttpMethodEnum.GET, url });
  }

  /**
   * @description Returns a promise to indicate whether the API authentication is complete
   */
  private async _isReady(): Promise<boolean> {
    await this._authPromise;
    return Boolean(this._accessToken);
  }

  /**
   * @description Throws an error if any of env variables required to use the Truelayer API, are not set.
   */
  private static _verifyCredentialsExist(): void {
    if (!TruelayerPaymentsClient._clientId) {
      throw new Error("TRUELAYER_CLIENT_ID env variable is not set");
    } else if (!TruelayerPaymentsClient._clientSecret) {
      throw new Error("TRUELAYER_CLIENT_SECRET env variable is not set");
    }
  }

  /**
   * @description Method to fetch the access token that's used to authenticate TL API requests.
   *
   * 1. Retrieve the access token from the cache.
   * 1.5 If it doesn't exist, make a request to the TL API to get the access token.
   * 2. Poll every 5 minutes to get the latest access token.
   *
   * If the request fails, we retry after 1 minute.
   */
  private async _requestAccessToken(): Promise<void> {
    try {
      this._authPromise = getCachedDataWithFallback<AuthResponseType>(
        "truelayer:payments:auth",
        async () => {
          logger.info("Making http request", {
            module: "truelayerService",
            method: "_requestAccessToken"
          });

          return (
            await axios.post(
              `${TRUELAYER_AUTH_URL}/connect/token`,
              qs.stringify({
                // eslint-disable-next-line camelcase
                grant_type: "client_credentials",
                // eslint-disable-next-line camelcase
                client_id: TruelayerPaymentsClient._clientId,
                // eslint-disable-next-line camelcase
                client_secret: TruelayerPaymentsClient._clientSecret,
                scope: "payments"
              })
            )
          ).data;
        },
        (response: AuthResponseType) => response.expires_in - TOKEN_CACHE_EXPIRATION_BUFFER
      );

      const authResponse = await this._authPromise;

      this._accessToken = authResponse.access_token;

      setTimeout(() => this._requestAccessToken(), TOKEN_POLLING_INTERVAL * 1000);
    } catch (err) {
      logger.error("Retrieval of truelayer access token failed", {
        module: "truelayerService",
        method: "_requestAccessToken"
      });
      captureException(err);
      setTimeout(() => this._requestAccessToken(), TOKEN_ERROR_INTERVAL * 1000);
    }
  }

  private _signRequest(path: TruelayerEndpointEnum, data: object, idempotencyKey: string): string {
    const kid = process.env.TRUELAYER_SIGN_KID;
    /**
     * It is encoded with the following method:
     * const privateKey= [private key pem string value]
     * const buff = Buffer.from(privateKey).toString('base64');
     *
     * And the buff value is set to the env file
     */
    const privateKeyPem = Buffer.from(process.env.TRUELAYER_PRIVATE_KEY_PEM_BASE64, "base64").toString("ascii");

    return tlSigning.sign({
      kid,
      privateKeyPem,
      method: HttpMethodEnum.POST as unknown as tlSigning.HttpMethod,
      path: `/${path}`,
      headers: { "Idempotency-Key": idempotencyKey },
      body: JSON.stringify(data)
    });
  }

  /**
   * @description This is the method for making any requests to access the TL PAY API, other than authentication.
   * It is configured to have the Bearer token in the header of the request.
   * @param method get or post
   * @param url the Truelayer endpoint that we want to access as defined in the endpoint enum
   * @param headers
   * @param data any data that may be posted with the request
   */
  private async _fetch({
    method,
    url,
    headers,
    data
  }: {
    method: HttpMethodEnum;
    url: string;
    headers?: RequestHeadersType;
    data?: any;
  }): Promise<any> {
    try {
      await this._isReady();

      const response = await axios({
        method,
        url,
        headers: {
          ...headers,
          Authorization: `Bearer ${this._accessToken}`
        },
        data
      });
      return response.data;
    } catch (err) {
      logger.error("http request failed", {
        module: "truelayerService",
        method: "_fetch",
        data: {
          url,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status: err.response && err.response.status,
          reason: err.response ? JSON.stringify(err.response.data, null, 4) : "",
          requestData: {
            data: JSON.stringify(data, null, 4)
          },
          error: err
        }
      });
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4),
          requestData: {
            data: JSON.stringify(data, null, 4)
          }
        }
      });
      captureException(err);
    }
  }
}

// =========================================
// =========================================
// ********* TRUELAYER DATA CLIENT *********
// =========================================
// =========================================

const TRUELAYER_DATA_REDIRECT_URL = `${process.env.DOMAIN_URL}/truelayer-data-callback`;

/**
 * ENUMS
 */

enum TruelayerDataEndpointEnum {
  ACCOUNTS = "accounts"
}

/**
 * TYPES
 */

export type AccountNumberType = {
  iban?: string;
  swift_bic: string;
  number?: string;
  sort_code?: string;
};
export type AccountType = {
  update_timestamp: string;
  account_id: string;
  account_type: "TRANSACTION" | "SAVINGS";
  display_name: string;
  currency: "GBP" | "EUR" | "CHF" | "USD";
  account_number: AccountNumberType;
  provider: {
    display_name: string;
    provider_id: TruelayerPayProvidersType;
    logo_uri: string;
  };
};

export class TruelayerDataClient {
  private static _clientId = process.env.TRUELAYER_CLIENT_ID;
  private static _clientSecret = process.env.TRUELAYER_CLIENT_SECRET;
  private _accessToken: string;
  private _authPromise: Promise<any>;

  constructor(code: string) {
    TruelayerDataClient._verifyCredentialsExist();
    this._requestAccessTokenWithCode(code);
  }

  // ==========
  // PUBLIC METHODS
  // ==========

  /**
   * @description Finds the first UK account that shows up in the list of accounts
   * @returns
   */
  public async getUkAccount(): Promise<AccountType> {
    const accountResponse = await this._getAccounts();
    return accountResponse.results.find(
      (account) => account.currency === "GBP" && Boolean(account.account_number.sort_code)
    );
  }

  // ==========
  // PRIVATE METHODS
  // ==========

  private async _getAccounts(): Promise<IResult<AccountType>> {
    return await this._fetch({ method: HttpMethodEnum.GET, endpoint: TruelayerDataEndpointEnum.ACCOUNTS });
  }

  /**
   * @description Returns a promise to indicate whether the API authentication is complete
   */
  private async _isReady(): Promise<boolean> {
    await this._authPromise;
    return Boolean(this._accessToken);
  }

  /**
   * @description Method to fetch the access token that's used to authenticate TL API requests. Once the access
   * token & the expiration date of that token are fetched, a timeout is set (a minute faster than the expiration)
   * to renew the access token
   *
   */
  private async _requestAccessTokenWithCode(code: string): Promise<void> {
    try {
      // Use client id, secret & code to obtain access token
      this._authPromise = axios.post(
        `${TRUELAYER_AUTH_URL}/connect/token`,
        qs.stringify({
          // eslint-disable-next-line camelcase
          grant_type: "authorization_code",
          // eslint-disable-next-line camelcase
          client_id: TruelayerDataClient._clientId,
          // eslint-disable-next-line camelcase
          client_secret: TruelayerDataClient._clientSecret,
          redirect_uri: TRUELAYER_DATA_REDIRECT_URL,
          code
        })
      );
      const response = await this._authPromise;
      this._accessToken = response.data.access_token;
      return;
    } catch (err) {
      logger.error("Retrieval of truelayer access token failed", {
        module: "truelayerDataService",
        method: "_requestAccessTokenWithCode"
      });
      captureException(err);
    }
  }

  /**
   * @description Throws an error if any of env variables required to use the Truelayer API, are not set.
   */
  private static _verifyCredentialsExist(): void {
    if (!TruelayerDataClient._clientId) {
      throw new Error("TRUELAYER_CLIENT_ID env variable is not set");
    } else if (!TruelayerDataClient._clientSecret) {
      throw new Error("TRUELAYER_CLIENT_SECRET env variable is not set");
    }
  }

  /**
   * @description This is the method for making any requests to access the TL PAY API, other than authentication.
   * It is configured to have the Bearer token in the header of the request.
   * @param method get or post
   * @param url the Truelayer endpoint that we want to access as defined in the endpoint enum
   * @param headers
   * @param data any data that may be posted with the request
   */
  private async _fetch({
    method,
    endpoint,
    headers,
    data
  }: {
    method: HttpMethodEnum;
    endpoint: string;
    headers?: RequestHeadersType;
    data?: any;
  }): Promise<any> {
    try {
      await this._isReady();

      const response = await axios({
        method,
        url: `https://api.${
          TRUELAYER_DOMAIN_CONFIG[process.env.NODE_ENV as "development" | "staging" | "production"]
        }/data/v1/${endpoint}`,
        headers: {
          ...headers,
          Authorization: `Bearer ${this._accessToken}`
        },
        data
      });
      return response.data;
    } catch (err) {
      logger.error("http request failed", {
        module: "truelayerDataService",
        method: "_fetch",
        data: {
          url: endpoint,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status: err.response && err.response.status,
          reason: err.response ? JSON.stringify(err.response.data, null, 4) : "",
          requestData: {
            data: JSON.stringify(data, null, 4)
          },
          error: err
        }
      });
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url: endpoint,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4),
          requestData: {
            data: JSON.stringify(data, null, 4)
          }
        }
      });
      captureException(err);
    }
  }
}

// =========================================
// =========================================
// *** TRUELAYER PAYMENTS WEBHOOK CLIENT ****
// =========================================
// =========================================

/**
 * TYPES
 */

export enum TruelayerWebhookEnum {
  PAYMENT_EXECUTED = "payment_executed",
  PAYMENT_FAILED = "payment_failed",
  PAYMENT_AUTHORIZED = "payment_authorized"
}

type EventPaymentMethodType = {
  type: "bank_transfer";
  provider_id: TruelayerPayProvidersType;
  // use Faster payments in the UK
  // https://docs.truelayer.com/docs/select-a-provider-for-a-payment#scheme_ids-for-payments
  scheme_id:
    | "provider_determined"
    | "sepa_credit_transfer"
    | "sepa_credit_transfer_instant"
    | "faster_payments_service";
};

type EventPaymentSourceType = {
  account_holder_name: string;
  account_identifiers: AccountIdentifierType[];
};

export type TruelayerPaymentEventPayloadType = {
  event_id: string;
  event_version: number;
  payment_id: string;
  metadata?: object;
};

export type TruelayerPaymentExecutedEventPayloadType = TruelayerPaymentEventPayloadType & {
  type: "payment_executed";
  executed_at: string;
  payment_method: EventPaymentMethodType;
  payment_source?: EventPaymentSourceType;
  // Exeperimentary field from Truelayer as of 18th July 2023
  settlement_risk?: {
    category: "low_risk" | "high_risk ";
  };
};

export type TruelayerPaymentFailedEventPayloadType = TruelayerPaymentEventPayloadType & {
  type: "payment_failed";
  failed_at: string;
  payment_method: EventPaymentMethodType;
  failure_stage: "authorization_required" | "authorizing" | "authorized";
  failure_reason: FailureStatusType;
};

export type TruelayerPaymentAuthorizedEventPayloadType = TruelayerPaymentEventPayloadType & {
  type: "payment_authorized";
  authorized_at: string;
  payment_source?: EventPaymentSourceType;
};

type JwkType = {
  [key: string]: any;
} & {
  alg: string;
  kty: string;
  kid: string;
};
const PaymentNotificationToPaymentStatus: PartialRecord<string, PaymentStatusTypeV3> = {
  [TruelayerWebhookEnum.PAYMENT_EXECUTED]: "executed",
  [TruelayerWebhookEnum.PAYMENT_FAILED]: "failed",
  [TruelayerWebhookEnum.PAYMENT_AUTHORIZED]: "authorized"
};

export class TruelayerPaymentWebhookClient {
  // Json web Key Url
  private _jkUrl: string;

  // global JWK cache
  private _cachedJwk: {
    keys: JwkType[];
  };

  constructor() {
    this._jkUrl = `https://webhooks.${
      TRUELAYER_DOMAIN_CONFIG[process.env.NODE_ENV as "development" | "staging" | "production"]
    }/.well-known/jwks`;
    this._cachedJwk = { keys: [] };
  }

  // ==========
  // Static Utils
  // ==========

  public static getPayloadFromPaymentEvent(requestBody: any): {
    status: PaymentStatusTypeV3;
    failureReason?: FailureStatusType | string;
    executedAt?: Date;
  } {
    const type = requestBody.type as string;
    const payload: {
      status: PaymentStatusTypeV3;
      failureReason?: FailureStatusType | string;
      executedAt?: Date;
    } = {
      status: TruelayerPaymentWebhookClient._getStatusFromPaymentEvent(type)
    };

    switch (type) {
      case TruelayerWebhookEnum.PAYMENT_EXECUTED:
        payload.executedAt = new Date((requestBody as TruelayerPaymentExecutedEventPayloadType).executed_at);
        break;
      case TruelayerWebhookEnum.PAYMENT_FAILED:
        payload.failureReason = (requestBody as TruelayerPaymentFailedEventPayloadType).failure_reason;
        break;
      case TruelayerWebhookEnum.PAYMENT_AUTHORIZED:
        break;
      default:
        throw new BadRequestError(`Event payload had unknown type ${type}`);
    }

    return payload;
  }

  private static _getStatusFromPaymentEvent(eventType: string): PaymentStatusTypeV3 {
    return PaymentNotificationToPaymentStatus[eventType];
  }

  // ==========
  // PUBLIC METHODS
  // ==========

  /**
   * We only verify requests for production, due to an issue with signature validation on sandbox for the fixed
   * 'mock-gb-payments-redirect account that has a pre-defined account holder name by Truelayer (containing special
   * characters).
   *
   * @param req
   */
  public async verifyRequest(req: Request): Promise<void> {
    if (!envIsProd()) {
      return;
    }

    // extract the Tl-Signature from the headers
    const sig = req.headers["tl-signature"] as string;
    tlSigning.SignatureError.ensure(!!sig, "Missing Tl-Signature header");

    // get the (cached) JWKs for this request
    const jwks = await this._getJwks(sig);

    // verify the request (will throw on failure)
    tlSigning.verify({
      signature: sig,
      method: req.method,
      path: req.path,
      headers: req.headers,
      body: JSON.stringify(req.body),
      jwks: JSON.stringify(jwks)
    } as tlSigning.JwkVerifyParameters);
  }

  // ==========
  // PRIVATE METHODS
  // ==========

  /**
   * @description
   * Tries to retrive the JWKs from a cache,
   * otherwises, gets the JWKs from the endpoint.
   * JWKs are unique by JKU+KID,
   * which is how the cache is determined to be up to date
   */
  private async _getJwks(sig: string): Promise<{
    keys: JwkType[];
  }> {
    const kid = tlSigning.extractKid(sig);
    tlSigning.SignatureError.ensure(!!kid, "Tl-Signature has missing key id");

    const jku = tlSigning.extractJku(sig);
    tlSigning.SignatureError.ensure(this._jkUrl === jku, `Tl-Signature has invalid jku: ${jku}`);

    // check if we have this KID/JKU pair stored
    const jwks = this._cachedJwk;
    if (jwks) {
      for (let i = 0; i < jwks.keys.length; i++) {
        if (jwks.keys[i].kid == kid) {
          return jwks;
        }
      }
    }

    // otherwise, fetch the JWKs from the server
    this._cachedJwk = (await axios.get(jku)).data;
    return this._cachedJwk;
  }
}

export const truelayerPaymentWebhookClient = new TruelayerPaymentWebhookClient();
