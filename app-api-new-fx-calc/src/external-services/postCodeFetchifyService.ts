import axios, { AxiosRequestConfig } from "axios";
import { BadRequestError, InternalServerError } from "../models/ApiErrors";
import logger from "./loggerService";
import { captureException } from "@sentry/node";

enum HttpMethodEnum {
  GET = "get",
  POST = "post"
}

type FetchifyPostCodeValidationResponseType = {
  error_code: string;
  error_msg: string;
};

export default class PostCodeFetchifyService {
  /**
   * Throws an exception if the given post code (lowercase without empty spaces) does not exist.
   * If any other error occurs (e.g communication with Fetchify API), catches (and logs) the exception.
   *
   * @param owner string representation of the owner's UserDocument ID
   * @param postCode
   */
  public static async validateUKPostCode(owner: string, postCode: string) {
    const config: AxiosRequestConfig = {
      params: {
        response: "data_formatted",
        postcode: postCode
      }
    };

    const res = (await this._fetch(
      HttpMethodEnum.GET,
      "json/basicaddress",
      {},
      config
    )) as FetchifyPostCodeValidationResponseType;

    // 0001 and 0002 error codes mean a non-existing or misformatted post code.
    if (["0001", "0002"].includes(res?.error_code)) {
      logger.warn(
        `Validation of post code failed on fetchify with error code ${res.error_code} and message "${res.error_msg}`,
        {
          module: "PostCodeFetchifyService",
          method: "validateUKPostCode",
          data: {
            owner,
            postCode
          }
        }
      );

      throw new BadRequestError(`Post code ${postCode} could not be validated`);
    }
  }

  private static async _fetch(
    method: HttpMethodEnum,
    url: string,
    data?: any,
    config: AxiosRequestConfig = {}
  ): Promise<any> {
    logger.info("making http request", {
      module: "PostCodeFetchifyService",
      method: "_fetch",
      data: {
        method,
        url,
        params: config.params,
        data
      }
    });

    try {
      const response = await axios({
        method,
        url: `${process.env.POSTCODE_FETCHIFY_API}/${url}`,
        data,
        headers: { ...config.headers },
        params: {
          ...config.params,
          key: `${process.env.FETCHIFY_API_KEY}`
        }
      });

      const responseData = response.data as FetchifyPostCodeValidationResponseType;
      // If the status code is not 0001 or 00002, it could mean we're doing something wrong when hitting the API
      // e.g. JSON syntax error, Invalid or no access token, account suspended, etc. In those cases, we don't want to
      // fail the user's request.
      if (responseData.error_code && !["0001", "0002"].includes(responseData.error_code)) {
        throw new InternalServerError(`${responseData.error_code}: ${responseData.error_msg}`);
      }
      return responseData;
    } catch (err) {
      logger.error("http request failed", {
        module: "PostCodeFetchifyService",
        method: "_fetch",
        data: {
          error: err
        }
      });
      captureException(err);
    }
  }
}
