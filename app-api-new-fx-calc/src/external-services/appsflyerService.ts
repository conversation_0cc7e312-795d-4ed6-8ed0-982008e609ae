import { addBreadcrumb, captureException } from "@sentry/node";
import logger from "./loggerService";
import { GoogleAdsMetadataType, TrackingSourceType } from "../models/Participant";

const SOURCE_MAPPING: Record<MediaSourceType, TrackingSourceType> = {
  "Apple Search Ads": "apple",
  landing_page_reward_source: "organic",
  // when using this media source we will first check whether any utm source params exist
  // if not we will use the organic value
  main_onelink_source: "organic",
  googleadwords_int: "google",
  Email: "organic"
};

const MediaSourceArray = [
  "Apple Search Ads",
  "Email",
  "googleadwords_int",
  "landing_page_reward_source",
  "main_onelink_source"
] as const;
type MediaSourceType = (typeof MediaSourceArray)[number];

type AttributionObjDataType = {
  // appsflyer params
  orig_cost?: string;
  is_first_launch: false;
  af_click_lookback?: string;
  click_time?: string;
  campaign_id?: string;
  lat?: string;
  install_time: string;
  media_source?: MediaSourceType;
  ad_event_id?: string;
  af_message?: string;
  af_status: "Non-organic" | "Organic";
  af_ad_id?: string;
  af_reengagement_window?: string;
  af_sub1?: string;
  // used for Facebook Click ID
  af_sub2?: string;
  af_sub3?: string;
  // used for Google Click ID
  af_sub4?: string;
  af_sub5?: string;
  "click-timestamp"?: string;
  campaign?: string;
  af_ad_type?: "ClickToDownload";
  af_adset_id?: string;
  retargeting_conversion_type?: "none";
  network?: "Display";
  iscache?: boolean;
  external_account_id?: number;
  match_type?: "srn";
  af_channel?: "ACI_Display";
  af_viewthrough_lookback?: string;
  af_c_id?: string;
  cost_cents_USD?: string;
  referrer_gclid?: string;
  af_adset?: string;
  af_ad?: string;

  // custom params
  anonymousId?: string;
  gaClientId?: string;
  pageUserLanded?: string;
  grsf?: string;
  wlthd?: string;
  sid?: string;
  submissiontech_click_id?: string;
};

export default class AppsflyerService {
  /**
   * PUBLIC METHODS
   */
  public static parseAttributionParams(headers: any): {
    anonymousId: string;
    appsflyerId?: string;
    gaClientId?: string;
    pageUserLanded?: string;
    wlthd?: string;
    grsf?: string;
    sid?: string;
    submissionTechClickId?: string;
  } {
    const { appsflyer_id, appsflyer_attribution_data } = headers;

    let anonymousId, gaClientId, pageUserLanded, grsf, wlthd, sid, submissionTechClickId: string;

    // we add error handling to make sure that signup/login won't fail in case there is an error
    // parsing attribution data
    try {
      const attributionDataObj = AppsflyerService._getAppsflyerAttributionObject(headers);
      anonymousId = attributionDataObj.anonymousId;
      gaClientId = attributionDataObj.gaClientId;
      pageUserLanded = attributionDataObj.pageUserLanded;
      grsf = attributionDataObj.grsf;
      wlthd = attributionDataObj.wlthd;
      sid = attributionDataObj.sid;
      submissionTechClickId = attributionDataObj.submissiontech_click_id;
    } catch (err) {
      addBreadcrumb({
        type: "default",
        category: "AppsflyerService.parseAttributionParams",
        level: "info",
        data: {
          appsflyerId: appsflyer_id,
          attributionData: appsflyer_attribution_data
        }
      });
      captureException(err);
      logger.error("Failed to parse appsflyer attribution data", {
        module: "AppsflyerService",
        method: "parseAttributionParams",
        data: {
          appsflyerId: appsflyer_id,
          attributionData: appsflyer_attribution_data
        }
      });
    }

    return {
      appsflyerId: appsflyer_id,
      anonymousId,
      wlthd,
      gaClientId,
      pageUserLanded,
      grsf,
      sid,
      submissionTechClickId
    };
  }

  public static parseGoogleAdMetadata(headers: any): GoogleAdsMetadataType {
    let googleAdsMetadata: GoogleAdsMetadataType;
    const trackingSource = AppsflyerService.parseSource(headers);
    if (trackingSource === "google") {
      const attributionDataObj = AppsflyerService._getAppsflyerAttributionObject(headers);
      googleAdsMetadata = {
        adSetId: attributionDataObj.af_adset_id,
        campaign: attributionDataObj.campaign,
        campaignId: attributionDataObj.campaign_id,
        gclid: attributionDataObj.referrer_gclid ?? attributionDataObj.af_sub4
      };
    }

    return googleAdsMetadata;
  }

  public static parseSource(headers: any): TrackingSourceType {
    const { appsflyer_id, appsflyer_attribution_data } = headers;

    let trackingSource: TrackingSourceType = "organic";

    // we add error handling to make sure that signup/login won't fail in case there is an error
    // parsing attribution data
    try {
      const attributionDataObj = AppsflyerService._getAppsflyerAttributionObject(headers);

      // sometimes appsflyer_attribution_data field can be empty
      if (attributionDataObj?.af_status === "Non-organic") {
        if (AppsflyerService._isGoogleAttributed(attributionDataObj)) {
          // we have set af_sub4 (gclid) and campaign so source is google
          trackingSource = "google";
        } else {
          const appsflyerMediaSource = SOURCE_MAPPING[attributionDataObj.media_source as MediaSourceType];
          if (appsflyerMediaSource) {
            trackingSource = appsflyerMediaSource;
            logger.info(`Will set tracking source to ${trackingSource}`, {
              module: "AppsflyerService",
              method: "parseSource",
              data: {
                appsflyerId: appsflyer_id,
                attributionData: appsflyer_attribution_data
              }
            });
          } else {
            logger.warn(`Could not identify media source for key ${attributionDataObj.media_source}`, {
              module: "AppsflyerService",
              method: "parseSource",
              data: {
                appsflyerId: appsflyer_id,
                attributionData: appsflyer_attribution_data
              }
            });
          }
        }
      } else {
        // We have the var to organic already but we want to be more explicit at this point
        trackingSource = "organic";
      }
    } catch (err) {
      addBreadcrumb({
        type: "default",
        category: "AppsflyerService.parseSource",
        level: "info",
        data: {
          appsflyerId: appsflyer_id,
          attributionData: appsflyer_attribution_data
        }
      });
      captureException(err);
      logger.error("Failed to parse appsflyer source and ad data", {
        module: "AppsflyerService",
        method: "parseSource",
        data: {
          appsflyerId: appsflyer_id,
          attributionData: appsflyer_attribution_data
        }
      });
    }

    return trackingSource;
  }

  /**
   * PRIVATE METHODS
   */
  private static _isGoogleAttributed(attributionDataObj: AttributionObjDataType): boolean {
    const appsflyerMediaSource = SOURCE_MAPPING[attributionDataObj.media_source as MediaSourceType];

    return (
      appsflyerMediaSource === "google" ||
      (Boolean(attributionDataObj.af_sub4) && Boolean(attributionDataObj.campaign))
    );
  }

  private static _getAppsflyerAttributionObject(headers: any): AttributionObjDataType {
    const { appsflyer_attribution_data } = headers;

    let attributionDataObj: AttributionObjDataType;
    if (typeof appsflyer_attribution_data === "string") {
      attributionDataObj = JSON.parse(appsflyer_attribution_data as string) as AttributionObjDataType;
    } else {
      // The appsflyer_attribution_data can be undefined sometimes from the client (due to appsflyer error)
      // In that case we set the object to the headers object. The appsflyer conversion data will be missing
      // but we'll be able to retrieve fields like wlthd, grsf, sid.
      attributionDataObj = headers;
    }

    return attributionDataObj;
  }
}
