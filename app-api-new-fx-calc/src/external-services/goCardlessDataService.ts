import axios, { Method } from "axios";
import { captureException } from "@sentry/node";
import logger from "./loggerService";
import { countriesConfig } from "@wealthyhood/shared-configs";
import HttpUtil from "../utils/httpUtil";
import { PlatformEnum } from "../configs/platformConfig";
import { customAlphabet } from "nanoid";

const nanoid = customAlphabet("0123456789abcdefghijklmnopqrstuvwxyz", 6);

export type InstitutionType = {
  id: string;
  name: string;
  bic: string;
  countries: countriesConfig.CountryCodesType[];
  transaction_total_days: string;
  logo: string;
};

export type AccountType = {
  id: string;
  iban: string;
  institution_id: string;
  owner_name: string;
};

export type AccountDetailsType = {
  account: {
    currency: string;
  };
};

export type EndUserAgreementType = {
  id: string;
};

const RequisitionStatusArray = [
  "CREATED",
  "GIVING_CONSENT",
  "UNDERGOING_AUTHENTICATION",
  "REJECTED",
  "SELECTING_ACCOUNTS",
  "GRANTING_ACCESS",
  "LINKED",
  "EXPIRED"
] as const;
export type RequisitionStatusType = (typeof RequisitionStatusArray)[number];

export type RequisitionType = {
  id: string;
  status: {
    long: RequisitionStatusType;
    description: string;
  };
  reference: string;
  accounts: string[];
  link: string; // Example: "https://ob.gocardless.com/psd2/start/3fa85f64-5717-4562-b3fc-2c963f66afa6/{$INSTITUTION_ID}"
};

const GOCARDLESS_DATA_REDIRECT_URL = `${process.env.DOMAIN_URL}/gocardless-data-callback`;

export class GoCardlessDataService {
  private static _id = process.env.GOCARDLESS_DATA_SECRET_ID;
  private static _secret = process.env.GOCARDLESS_DATA_SECRET_KEY;
  private static _openBankingUrl = process.env.GOCARDLESS_DATA_URL;

  private static _instance: GoCardlessDataService;

  private _accessToken: string;
  private _authPromise: Promise<any>;

  private constructor() {
    GoCardlessDataService._verifyCredentialsExist();
    this._requestAccessToken();
  }

  // ===============
  // PUBLIC METHODS
  // ===============
  public static get Instance(): GoCardlessDataService {
    return this._instance || (this._instance = new this());
  }

  // Creates an end user agreement which specifies what details we are going to collect from the user's bank
  // account if they choose to link one.
  public async createEndUserAgreement(institutionId: string): Promise<EndUserAgreementType> {
    return this._fetch({
      method: "POST",
      url: `${GoCardlessDataService._openBankingUrl}/api/v2/agreements/enduser/`,
      data: {
        access_valid_for_days: 1,
        access_scope: ["details"],
        institution_id: institutionId
      }
    });
  }

  // Creates a requisition which is a bank account linking request. Clients need to redirect to the redirect URL
  // returned from this API to complete the linking process.
  public async createRequisition(
    ownerId: string,
    platform: PlatformEnum,
    institutionId: string,
    agreementId: string,
    redirectUriState?: string
  ): Promise<RequisitionType> {
    return this._fetch({
      method: "POST",
      url: `${GoCardlessDataService._openBankingUrl}/api/v2/requisitions/`,
      data: {
        redirect: `${GOCARDLESS_DATA_REDIRECT_URL}?state=${redirectUriState}`,
        institution_id: institutionId,
        reference: `${ownerId}--${platform}--${nanoid()}`,
        user_language: "EN",
        agreement: agreementId
      }
    });
  }

  public async getRequisition(requisitionId: string): Promise<RequisitionType> {
    return this._fetch({
      method: "GET",
      url: `${GoCardlessDataService._openBankingUrl}/api/v2/requisitions/${requisitionId}`
    });
  }

  public async getAccount(accountId: string): Promise<AccountType> {
    return this._fetch({
      method: "GET",
      url: `${GoCardlessDataService._openBankingUrl}/api/v2/accounts/${accountId}`
    });
  }
  public async getAccountDetails(accountId: string): Promise<AccountDetailsType> {
    return this._fetch({
      method: "GET",
      url: `${GoCardlessDataService._openBankingUrl}/api/v2/accounts/${accountId}/details`
    });
  }

  // ===============
  // PRIVATE METHODS
  // ===============

  /**
   * @description Returns a promise to indicate whether the API authentication is complete
   */
  private async _isReady(): Promise<boolean> {
    await this._authPromise;
    return Boolean(this._accessToken);
  }

  private async _requestAccessToken(): Promise<void> {
    try {
      this._authPromise = axios.post(
        `${GoCardlessDataService._openBankingUrl}/api/v2/token/new/`,
        {
          secret_id: GoCardlessDataService._id,
          secret_key: GoCardlessDataService._secret
        },
        {
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json"
          }
        }
      );

      const response = await this._authPromise;

      this._accessToken = response.data.access;

      setTimeout(() => this._requestAccessToken(), 60000);
    } catch (err) {
      logger.error("Retrieval of GoCardless access token failed", {
        module: "GoCardlessDataService",
        method: "_requestAccessToken"
      });
      captureException(err);
      setTimeout(() => this._requestAccessToken(), 5000);
    }
  }

  private static _verifyCredentialsExist(): void {
    if (!GoCardlessDataService._id || !GoCardlessDataService._secret) {
      throw new Error("GOCARDLESS_DATA_SECRET_ID or GOCARDLESS_DATA_SECRET_KEY env variable has not been set");
    } else if (!GoCardlessDataService._openBankingUrl) {
      throw new Error("GOCARDLESS_DATA_URL env variable has not been set");
    }
  }

  private async _fetch(config: { method: Method; url: string; data?: any; headers?: any }): Promise<any> {
    await this._isReady();

    const headers = {
      Authorization: `Bearer ${this._accessToken}`,
      ...config.headers
    };

    return await HttpUtil.fetch(
      { ...config, headers },
      {
        throwError: true,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "GoCardlessDataService",
          method: "_fetch"
        }
      }
    );
  }
}
