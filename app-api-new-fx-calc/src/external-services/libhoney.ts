import { NextFunction, Response } from "express";
import lib<PERSON>oney from "libhoney";
import { CustomRequest } from "custom";
import { SENSITIVE_KEYS } from "../configs/headerConfig";

export const hcCaptureXhr = (options: any): ((req: CustomRequest, res: Response, next: NextFunction) => void) => {
  const honey: any = new libHoney(options);
  return function (req: CustomRequest, res: Response, next: NextFunction): void {
    const filteredHeaders = Object.fromEntries(
      Object.entries(req.headers).filter(([key]) => !SENSITIVE_KEYS.includes(key))
    );

    honey.sendNow({
      app: req.app,
      baseUrl: req.baseUrl,
      body: req.body,
      environment: process.env.NODE_ENV,
      fresh: req.fresh,
      hostname: req.hostname,
      ip: req.ip,
      method: req.method,
      originalUrl: req.originalUrl,
      params: req.params,
      path: req.path,
      protocol: req.protocol,
      query: req.query,
      route: req.route,
      secure: req.secure,
      xhr: req.xhr,
      userId: req.user && req.user._id,
      userEmail: req.user && req.user.email,
      headers: filteredHeaders
    });
    next();
  };
};
