import { Method } from "axios";
import HttpUtil from "../utils/httpUtil";
import { addBreadcrumb } from "@sentry/node";

export type QuoteType = {
  symbol: string;
  price: number;
  changePercentage: number; // e.g. 0.22 for 0.22%
  timestamp: number; // In seconds
};

enum FmpEndpointEnum {
  BATCH_QUOTE = "BATCH_QUOTE",
  HISTORICAL_INDEX_PRICES = "HISTORICAL_INDEX_PRICES",
  NEWS_ARTICLES = "NEWS_ARTICLES"
}

/**
 * CONFIG
 */

const FMP_BASE_URL = "https://financialmodelingprep.com";
type FMPNewsArticleType = {
  title: string;
  date: string; // yyyy-MM-dd HH:mm:ss in EST timezone
  content: string;
  tickers: string; // e.g. STOCK:AAPL
  image: string;
};

export class FMPService {
  private static _apiKey = process.env.FMP_API_KEY;
  private static _instance: FMPService;

  private constructor() {
    FMPService._verifyCredentialsExist();
  }

  // ===============
  // PUBLIC METHODS
  // ===============
  public static get Instance(): FMPService {
    return this._instance || (this._instance = new this());
  }

  /**
   * Fetches real-time data for one or more assets/indexes using the batch quote API.
   *
   * @returns array of FMP quotes.
   * @param symbols
   */
  public async getBatchQuote(symbols: string[]): Promise<QuoteType[]> {
    addBreadcrumb({
      type: "default",
      category: "FMPService.getBatchQuote",
      level: "info",
      data: {
        symbols
      }
    });

    const endpoint = FMPService._getEndpoint(FmpEndpointEnum.BATCH_QUOTE, { symbols });
    return this._fetch(endpoint);
  }

  /**
   * @description Fetches daily close prices for an index symbol between two dates.
   * @param symbol e.g. '^GSPC'
   * @param from   'YYYY-MM-DD'
   * @param to     'YYYY-MM-DD'
   * @returns An array of { date, close } sorted by date ascending
   */
  public async getHistoricalIndexPrices(
    symbol: string,
    options: { from: string; to: string }
  ): Promise<
    {
      date: string;
      close: number;
    }[]
  > {
    const endpoint = FMPService._getEndpoint(FmpEndpointEnum.HISTORICAL_INDEX_PRICES, { symbol });
    const { from, to } = options;
    const response = await this._fetch(endpoint, { from, to });
    return response.historical
      .map((item: { date: string; close: number }): { date: string; close: number } => ({
        date: item.date,
        close: item.close
      }))
      .sort((a: { date: string }, b: { date: string }): number => a.date.localeCompare(b.date));
  }

  /**
   * Fetches news articles from FMP API.
   * @param options Additional options (e.g. pagination & to include only stock tickers)
   * @returns array of news articles
   */
  public async getNewsArticles(
    options: { page?: number; limit?: number; includeOnlyStockTickers?: boolean } = {
      page: 0,
      limit: 200,
      includeOnlyStockTickers: false
    }
  ): Promise<FMPNewsArticleType[]> {
    addBreadcrumb({
      type: "default",
      category: "FMPService.getNewsArticles",
      level: "info",
      data: {
        page: options?.page,
        limit: options?.limit,
        includeOnlyStockTickers: options?.includeOnlyStockTickers
      }
    });

    const endpoint = FMPService._getEndpoint(FmpEndpointEnum.NEWS_ARTICLES);
    const articles = await this._fetch(endpoint, { page: options?.page, limit: options?.limit });

    if (options?.includeOnlyStockTickers) {
      return articles.filter((article: FMPNewsArticleType) => article.tickers.includes("STOCK"));
    }

    return articles;
  }

  // ===============
  // PRIVATE METHODS
  // ===============
  private static _getEndpoint(type: FmpEndpointEnum, options?: { symbol?: string; symbols?: string[] }): string {
    switch (type) {
      case FmpEndpointEnum.BATCH_QUOTE:
        if (!options?.symbols?.length) {
          throw new Error("Symbols are required for BATCH_QUOTE endpoint");
        }
        return `/stable/batch-quote?symbols=${options.symbols.join(",")}`;
      case FmpEndpointEnum.HISTORICAL_INDEX_PRICES:
        if (!options?.symbol) {
          throw new Error("Symbol is required for HISTORICAL_INDEX_PRICES endpoint");
        }
        return `/api/v3/historical-price-full/index/${encodeURIComponent(options.symbol)}`;
      case FmpEndpointEnum.NEWS_ARTICLES:
        return "/stable/fmp-articles";
      default:
        throw new Error(`Unknown FMP endpoint type: ${type}`);
    }
  }

  private async _fetch(endpointPath: string, params: Record<string, any> = {}): Promise<any> {
    const url = `${FMP_BASE_URL}${endpointPath}`;

    return HttpUtil.fetch(
      {
        method: "GET" as Method,
        url,
        params: {
          ...params,
          apikey: FMPService._apiKey
        }
      },
      {
        throwError: false,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "FMPService",
          method: "_fetch"
        }
      }
    );
  }

  private static _verifyCredentialsExist(): void {
    if (!FMPService._apiKey) {
      throw new Error("FMP_API_KEY env variable has not been set");
    }
  }
}
