import axios from "axios";
import { addBreadcrumb, captureException } from "@sentry/node";
import logger from "./loggerService";

const FINANCE_ADS_URL = "https://www.financeads.net/tl.php";
const PROGRAM_ID = 4318;

enum HttpMethodEnum {
  GET = "get",
  POST = "post"
}

type OrderCategoryType = "lead" | "KYC" | "deposit";
type ParamsType = {
  p: number; // program id
  oid: string; // order id
  ocategory: OrderCategoryType; // order category
  ovalue?: number; // order value
  s_id: string; // affiliate id
};

export default class FinanceAdsService {
  public static async notify(options: {
    influencerId: string;
    category: OrderCategoryType;
    orderId: string;
    orderValue?: number;
  }): Promise<void> {
    const { category, influencerId, orderId, orderValue } = options;

    const params: ParamsType = {
      p: PROGRAM_ID,
      oid: `${orderId}-${category}`,
      ocategory: category,
      ovalue: orderValue,
      s_id: influencerId
    };
    await FinanceAdsService._fetch(HttpMethodEnum.GET, params);
  }

  private static async _fetch(method: HttpMethodEnum, params: any = {}): Promise<void> {
    try {
      logger.info("Making http request", {
        module: "FinanceAdsService",
        method: "_fetch",
        data: {
          params
        }
      });
      const response = await axios({
        method,
        url: FINANCE_ADS_URL,
        params
      });
      return response.data;
    } catch (err) {
      logger.error("http request failed", {
        module: "FinanceAdsService",
        method: "_fetch",
        data: {
          error: err
        }
      });
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4)
        }
      });
      captureException(err);
    }
  }
}
