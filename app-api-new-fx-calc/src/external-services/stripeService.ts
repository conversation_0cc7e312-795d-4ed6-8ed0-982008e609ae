import Stripe from "stripe";

export type CustomerDataType = {
  email: string;
  name: string;
  metadata: {
    wealthyhoodId: string;
  };
};

export type CustomerType = {
  id: string;
};

export const PaymentIntentStatusArray = [
  "canceled",
  "processing",
  "requires_action",
  "requires_capture",
  "requires_confirmation",
  "requires_payment_method",
  "succeeded"
] as const;
export type PaymentIntentStatusType = (typeof PaymentIntentStatusArray)[number];

export const SubscriptionStatusArray = [
  "active",
  "canceled",
  "incomplete",
  "incomplete_expired",
  "past_due",
  "paused",
  "trialing",
  "unpaid"
] as const;
export type SubscriptionStatusType = (typeof SubscriptionStatusArray)[number];

const API_VERSION = "2023-10-16";

const FREE_TRIAL_DAYS = 7;

export class StripeService {
  private _stripe: Stripe;
  private static _instance: StripeService;

  constructor() {
    this._stripe = new Stripe(process.env.STRIPE_API_KEY);
  }

  public static get Instance(): StripeService {
    return StripeService._instance || (StripeService._instance = new StripeService());
  }

  public constructEvent(payload: string, header: string, secret: string): Stripe.Event {
    return this._stripe.webhooks.constructEvent(payload, header, secret);
  }

  public async createCustomer(customerData: CustomerDataType): Promise<CustomerType> {
    return this._stripe.customers.create(customerData);
  }

  public async createSetupIntent(customerId: string): Promise<Stripe.SetupIntent> {
    return this._stripe.setupIntents.create({
      customer: customerId,
      automatic_payment_methods: {
        enabled: true
      }
    });
  }

  public async retrieveSetupIntent(setupIntentId: string): Promise<Stripe.SetupIntent> {
    return this._stripe.setupIntents.retrieve(setupIntentId);
  }

  public async retrievePaymentIntent(
    paymentIntentId: string,
    expand: string[] = []
  ): Promise<Stripe.PaymentIntent> {
    return this._stripe.paymentIntents.retrieve(paymentIntentId, { expand });
  }

  public async createEphemeralKey(customerId: string): Promise<Stripe.EphemeralKey> {
    return this._stripe.ephemeralKeys.create(
      {
        customer: customerId
      },
      { apiVersion: API_VERSION }
    );
  }

  public async createSubscription(
    customerId: string,
    priceId: string,
    paymentMethodId: string,
    options: {
      withFreeTrial: boolean;
      isApplePay?: boolean;
    }
  ): Promise<Stripe.Subscription> {
    return this._stripe.subscriptions.create({
      customer: customerId,
      items: [
        {
          price: priceId
        }
      ],
      // Based on feedback from Stripe, when our payment method is Apple Pay, to advance the first invoice, we
      // want the subscription to have been set as 'payment_behaviour: allow_incomplete'
      payment_behavior: options?.isApplePay && !options.withFreeTrial ? "allow_incomplete" : "default_incomplete",
      default_payment_method: paymentMethodId,
      payment_settings: {
        save_default_payment_method: "on_subscription"
      },
      expand: ["latest_invoice.payment_intent", "pending_setup_intent"],
      trial_period_days: options?.withFreeTrial ? FREE_TRIAL_DAYS : undefined
    });
  }

  public async retrieveSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    return this._stripe.subscriptions.retrieve(subscriptionId);
  }

  public async cancelSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    return this._stripe.subscriptions.cancel(subscriptionId);
  }

  public async updateSubscription(subscriptionId: string, data: any): Promise<Stripe.Subscription> {
    return this._stripe.subscriptions.update(subscriptionId, data);
  }

  public async retrievePaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod> {
    return this._stripe.paymentMethods.retrieve(paymentMethodId);
  }
}
