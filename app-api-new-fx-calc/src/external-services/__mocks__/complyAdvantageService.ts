import { buildSearch, buildSearchWithDetails } from "../../tests/utils/generateComplyAdvantage";

export type HitType = {
  doc: {
    id: string;
    types: string[];
  };
  is_whitelisted: boolean;
};

export type SearchWithDetailsType = {
  hits: HitType[];
};

export type SearchType = {
  ref: string;
};

type UpdateSearchEntitiesDataType = {
  entities: string[];
  is_whitelisted: true;
};

export enum HitCategoryType {
  PEP = "pep",
  AdverseMedia = "adverse-media",
  Warning = "warning",
  FitnessAndProbity = "fitness-probity",
  Sanction = "sanction"
}

export class ComplyAdvantageService {
  public static async retrieveSearches(clientReference: string): Promise<SearchType[]> {
    return [buildSearch()];
  }

  public static async retrieveSearchWithDetails(id: string): Promise<SearchWithDetailsType> {
    return buildSearchWithDetails();
  }

  public static async updateSearchEntities(searchId: string, data: UpdateSearchEntitiesDataType): Promise<void> {
    return;
  }
}
