import { ContentfulAccountEnum } from "../../configs/contentfulConfig";
import { ContentEntryCategoryEnum, ContentEntryContentTypeEnum } from "../../models/ContentEntry";
import { buildContentfulContentEntryCreationResponse } from "../../tests/utils/generateContentful";

export type ContentEntryContentfulType = {
  slug: string;
  title: string;
  publishedAt: string;
  subtitle: string;
  readingTime: string;
  summary: string;
  headerImage: string;
  bannerImage: string;
  content: string;
  category: ContentEntryCategoryEnum;
  contentType: ContentEntryContentTypeEnum;
  notificationTitle?: string;
  notificationBody?: string;
};

class ContentfulManagementService {
  private static _instances: Record<ContentfulAccountEnum, ContentfulManagementService> = {
    [ContentfulAccountEnum.LANDING_PAGE]: null,
    [ContentfulAccountEnum.LEARN_HUB]: null
  };

  constructor(options: { account: ContentfulAccountEnum }) {}

  public static get LandingPageInstance(): ContentfulManagementService {
    return (
      this._instances[ContentfulAccountEnum.LANDING_PAGE] ||
      (this._instances[ContentfulAccountEnum.LANDING_PAGE] = new this({
        account: ContentfulAccountEnum.LANDING_PAGE
      }))
    );
  }

  public static get LearnHubInstance(): ContentfulManagementService {
    return (
      this._instances[ContentfulAccountEnum.LEARN_HUB] ||
      (this._instances[ContentfulAccountEnum.LEARN_HUB] = new this({ account: ContentfulAccountEnum.LEARN_HUB }))
    );
  }

  public async createContentEntry(
    data: ContentEntryContentfulType,
    options: { publishAfterCreation: boolean }
  ): Promise<any> {
    return buildContentfulContentEntryCreationResponse();
  }
}

export default ContentfulManagementService;
