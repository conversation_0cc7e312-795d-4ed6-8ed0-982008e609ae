import { cloudflareConfig } from "@wealthyhood/shared-configs";
import { PartialRecord } from "../../types/utils";

export enum FilesEnum {
  POPULAR_ASSETS_OVERRIDE = "CLOUDFLARE_POPULAR_ASSETS_OVERRIDE_URL"
}

export enum BucketsEnum {
  ACCOUNT_STATEMENTS = "account-statements",
  ETF_HOLDING_LOGOS = "etf-holdings-logos",
  POPULAR_ASSETS = "server-config"
}

export enum ContentTypeEnum {
  IMAGE_PNG = "image/png",
  APPLICATION_PDF = "application/pdf"
}

const IMAGES_URL = "https://images.wealthyhood.cloud";

export const BUCKET_URL_CONFIG: PartialRecord<BucketsEnum, string> = {
  [BucketsEnum.ACCOUNT_STATEMENTS]: "https://pub-383ae354f347441493b02868f3167f7b.r2.dev",
  [BucketsEnum.POPULAR_ASSETS]: "https://pub-b466be118e5b4ea19a3eebd3c7c6bad7.r2.dev",
  [BucketsEnum.ETF_HOLDING_LOGOS]: "https://etf-holdings-logos.wealthyhood.dev"
};

const KV_STORAGE = "KV_STORAGE";
// Initialising kv storage
global[KV_STORAGE] = {};

export default class CloudflareService {
  private static _instance: CloudflareService;
  public static get Instance(): CloudflareService {
    return CloudflareService._instance || (CloudflareService._instance = new CloudflareService());
  }

  public getResizedImageURL(imageURL: string, options: { width: number; height: number }): string {
    return `${IMAGES_URL}/cdn-cgi/image/width=${options.width},height=${options.height},fit=cover/${imageURL}`;
  }

  /**
   * This mocks the remote cloudflare kv storage
   * --
   * Use this in test suites, in order to asset if the data was uploaded with expected format
   */
  public static get kvStorage(): Record<cloudflareConfig.KvNamespaceKeys, any> {
    return global[KV_STORAGE];
  }

  public async updateKeyValuePair(kvNamespceKey: cloudflareConfig.KvNamespaceKeys, data: string): Promise<void> {
    global[KV_STORAGE][kvNamespceKey] = JSON.parse(data);
  }

  public async getDataFromFile<T>(file: FilesEnum): Promise<T> {
    return [] as T;
  }

  public async uploadObject(
    bucket: BucketsEnum,
    key: string,
    object: NodeJS.ReadableStream,
    options: { contentType: ContentTypeEnum }
  ): Promise<{ fileUri: string }> {
    return { fileUri: "" };
  }

  public async doesObjectExists(bucket: BucketsEnum, objectKey: string): Promise<boolean> {
    return true;
  }
}
