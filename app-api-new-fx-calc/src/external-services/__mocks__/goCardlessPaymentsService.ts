import { countriesConfig } from "@wealthyhood/shared-configs";
import {
  buildGoCardlessBankAccount,
  buildGoCardlessBillingRequest,
  buildGoCardlessCustomer,
  buildGoCardlessFulfiledBillingRequest,
  buildGoCardlessMandate,
  buildGoCardlessPayment,
  buildGoCardlessPayout,
  buildGoCardlessPayoutItem
} from "../../tests/utils/generateGoCardless";

/**
 * DATA TYPES
 */
export const PaymentStatusArray = [
  "pending_customer_approval",
  "pending_submission",
  "submitted",
  "confirmed",
  "paid_out",
  "cancelled",
  "customer_approval_denied",
  "failed",
  "charged_back"
] as const;
export type PaymentStatusType = (typeof PaymentStatusArray)[number];

type PaymentDataType = {
  amount: number; // in cents
  currency: string;
  links: {
    mandate: string;
  };
  metadata: {
    wealthyhoodId: string;
  };
};

export type PaymentType = {
  amount: number; // in cents
  id: string;
  status: PaymentStatusType;
  reference: string;
  metadata: any;
};

type CustomerBankAccountDataType = {
  iban: string;
  account_holder_name: string;
  metadata: {
    wealthyhoodId: string;
  };
  links: {
    customer: string;
  };
};

type CustomerDataType = {
  email: string;
  given_name: string;
  family_name: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  postal_code: string;
  country_code: countriesConfig.CountryCodesType;
  metadata: {
    wealthyhoodId: string;
  };
};

type MandateOnlyBillingRequestDataType = {
  customerId: string;
  bankAccountId: string;
  metadata: {
    wealthyhoodId: string;
  };
};

type CollectCustomerDetailsActionType = {
  customer_billing_detail: {
    address_line1: string;
    city: string;
    postal_code: string;
    country_code: countriesConfig.CountryCodesType;
  };
};

export type CustomerBankAccountType = {
  id: string;
  metadata?: {
    wealthyhoodId: string;
  };
};

export type BillingRequestType = {
  id: string;
};

export type FulfiledBillingRequestType = BillingRequestType & {
  mandate_request: {
    links: { mandate: string };
  };
};

export type BillingRequestFlowType = {
  authorisation_url: string;
};

export type CustomerType = {
  id: string;
};

export const EventLinkKeyArray = ["mandate", "payment", "payout"] as const;
type EventLinkKeyType = (typeof EventLinkKeyArray)[number];

export type EventsType = {
  events: EventType[];
};

export const ResourceTypeArray = [
  "billing_requests",
  "creditors",
  "instalment_schedules",
  "mandates",
  "payer_authorisations",
  "payments",
  "payouts",
  "refunds",
  "scheme_identifiers",
  "subscriptions"
] as const;
export type ResourceTypeType = (typeof ResourceTypeArray)[number];

export type EventType = {
  id: string;
  resource_type: ResourceTypeType;
  action: string;
  links: {
    [key in EventLinkKeyType]?: string;
  };
  details?: {
    cause: string;
    description: string;
  };
};
export const PayoutStatusArray = ["pending", "paid", "bounced"] as const;
export type PayoutStatusType = (typeof PayoutStatusArray)[number];

export type PayoutType = {
  id: string;
  status: PayoutStatusType;
  reference: string;
};

export const PayoutItemTypeArray = [
  "payment_paid_out",
  "payment_failed",
  "payment_charged_back",
  "payment_refunded",
  "refund",
  "refund_funds_returned",
  "gocardless_fee",
  "app_fee",
  "revenue_share",
  "surcharge_fee"
] as const;
export type PayoutItemTypeType = (typeof PayoutItemTypeArray)[number];
export type PayoutItemType = {
  type: PayoutItemTypeType;
  links: {
    payment: string;
  };
};

export class GoCardlessPaymentsService {
  private static _instance: GoCardlessPaymentsService;

  // ===============
  // PUBLIC METHODS
  // ===============
  public static get Instance(): GoCardlessPaymentsService {
    return this._instance || (this._instance = new this());
  }

  public static formatDate(date: Date): string {
    const day = new Date(date).toLocaleDateString("en-GB", { day: "2-digit" });
    const month = new Date(date).toLocaleDateString("en-GB", { month: "2-digit" });
    const year = new Date(date).toLocaleDateString("en-GB", { year: "numeric" });
    return `${year}-${month}-${day}`;
  }

  public async createCustomer(customerData: CustomerDataType): Promise<CustomerType> {
    return buildGoCardlessCustomer();
  }

  public async createCustomerBankAccount(
    bankAccountData: CustomerBankAccountDataType
  ): Promise<CustomerBankAccountType> {
    return buildGoCardlessBankAccount();
  }

  public async retrieveMandate(id: string): Promise<any> {
    return buildGoCardlessMandate();
  }

  public async cancelMandate(id: string): Promise<void> {
    return;
  }

  public async createMandateOnlyBillingRequest(
    data: MandateOnlyBillingRequestDataType
  ): Promise<BillingRequestType> {
    return buildGoCardlessBillingRequest();
  }

  public async collectCustomerDetails(
    billingRequestId: string,
    data: CollectCustomerDetailsActionType
  ): Promise<void> {
    return;
  }

  public async confirmPayerDetails(billingRequestId: string): Promise<void> {
    return;
  }

  public async fulfilBillingRequest(billingRequestId: string): Promise<FulfiledBillingRequestType> {
    return buildGoCardlessFulfiledBillingRequest();
  }

  public async createPayment(data: PaymentDataType, idempotencyKey: string): Promise<PaymentType> {
    return buildGoCardlessPayment();
  }

  public async retrievePayout(payoutId: string): Promise<PayoutType> {
    return buildGoCardlessPayout();
  }

  public async retrievePayoutItems(payoutId: string): Promise<PayoutItemType[]> {
    return [buildGoCardlessPayoutItem()];
  }

  public static checkSignature(requestSignature: string, requestBody: any): void {
    return;
  }
}
