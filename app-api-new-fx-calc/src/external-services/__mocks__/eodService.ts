/* eslint-disable @typescript-eslint/no-empty-function */
import { faker } from "@faker-js/faker";

/**
 * CONFIG
 */
export const EOD_BASE_URL = "https://eodhistoricaldata.com";

export class EodService {
  public currencyRatesAreValid(): boolean {
    return true;
  }

  public async getLatestFXRates(): Promise<any> {
    return {
      USD: {
        GBP: faker.number.int({ min: 1, max: 5 }),
        EUR: faker.number.int({ min: 1, max: 5 }),
        USD: 1
      },
      EUR: {
        GBP: faker.number.int({ min: 1, max: 5 }),
        USD: faker.number.int({ min: 1, max: 5 }),
        EUR: 1
      },
      GBP: {
        EUR: faker.number.int({ min: 1, max: 5 }),
        USD: faker.number.int({ min: 1, max: 5 }),
        GBP: 1
      }
    };
  }

  public async getAssetSentiments(): Promise<void> {}

  public async getStockSplits(): Promise<void> {}

  public async getRealTimeAssetsData(): Promise<void> {}

  public async getRealTimeIndexData(): Promise<void> {}

  public async getAssetFundamentalsData(): Promise<void> {}

  public async getAssetFundamentalsDataByFundamentalsTicker(): Promise<void> {}

  public async getHistoricalPrices(): Promise<void> {}

  public async getHistoricalIndexPrices(): Promise<void> {}

  public async getIntradayPrices(): Promise<void> {}

  public async getHistoricalVolume(): Promise<void> {}
}

const eodService = new EodService();

export default eodService;
