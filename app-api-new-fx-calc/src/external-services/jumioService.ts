import qs from "qs";
import { captureException } from "@sentry/node";
import logger from "./loggerService";
import axios, { Method } from "axios";
import HttpUtil from "../utils/httpUtil";
import { countriesConfig } from "@wealthyhood/shared-configs";
import { BadRequestError } from "../models/ApiErrors";

/**
 * TYPES
 */
export const WorkflowStatusArray = [
  "INITIATED",
  "ACQUIRED",
  "PROCESSED",
  "SESSION_EXPIRED",
  "TOKEN_EXPIRED"
] as const;
export type WorkflowStatusType = (typeof WorkflowStatusArray)[number];

export const WorkflowExecutionDecisionArray = ["NOT_EXECUTED", "PASSED", "REJECTED", "WARNING"] as const;
export type WorkflowExecutionDecisionType = (typeof WorkflowExecutionDecisionArray)[number];

type ConfigType = {
  tokenUrl: string;
  accountsUrl: string;
  retrievalUrl: string;
  apiUrl: string;
};

export class JumioService {
  private static _instance: JumioService;
  private static _clientId = process.env.JUMIO_CLIENT_ID;
  private static _clientSecret = process.env.JUMIO_CLIENT_SECRET;
  private static _config: ConfigType;
  private _accessToken: string;
  private _authPromise: Promise<any>;

  private constructor() {
    JumioService._verifyCredentialsExist();
    JumioService._populateConfiguration();
    this._requestAccessToken();
  }

  // ===============
  // PUBLIC METHODS
  // ===============
  public static get Instance(): JumioService {
    return this._instance || (this._instance = new this());
  }

  public async generateTransactionPDF(
    accountId: string,
    workflowExecutionId: string
  ): Promise<{ presignedUrl: string }> {
    const res = await this._fetch({
      method: "GET",
      url:
        JumioService._config.retrievalUrl +
        `/accounts/${accountId}/workflow-executions/${workflowExecutionId}/generate`
    });

    return res as { presignedUrl: string };
  }

  // ===============
  // PRIVATE METHODS
  // ===============

  /**
   * @description Returns a promise to indicate whether the API authentication is complete
   */
  private async _isReady(): Promise<boolean> {
    await this._authPromise;
    return Boolean(this._accessToken);
  }

  private async _requestAccessToken(): Promise<void> {
    try {
      //1. Use client id, secret to obtain access token
      this._authPromise = axios.post(
        `${JumioService._config.tokenUrl}`,
        qs.stringify({
          // eslint-disable-next-line camelcase
          grant_type: "client_credentials"
        }),
        {
          headers: {
            Accept: "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
          },
          auth: {
            username: JumioService._clientId as string,
            password: JumioService._clientSecret as string
          }
        }
      );
      const response = await this._authPromise;
      this._accessToken = response.data.access_token;

      // 2. Check when it expires to renew it
      setTimeout(() => this._requestAccessToken(), (response.data.expires_in - 60) * 1000);
    } catch (err) {
      logger.error("Retrieval of jumio access token failed", {
        module: "JumioService",
        method: "requestAccessToken"
      });
      captureException(err);
      setTimeout(() => this._requestAccessToken(), 1000);
    }
  }

  private static _verifyCredentialsExist() {
    if (!JumioService._clientId) {
      throw new Error("JUMIO_CLIENT_ID env variable is not set");
    } else if (!JumioService._clientSecret) {
      throw new Error("JUMIO_CLIENT_SECRET env variable is not set");
    }
  }

  private static _populateConfiguration() {
    this._config = {
      tokenUrl: "https://auth.emea-1.jumio.ai/oauth2/token",
      accountsUrl: "https://account.emea-1.jumio.ai/api/v1/accounts",
      retrievalUrl: "https://retrieval.emea-1.jumio.ai/api/v1",
      apiUrl: "https://api.emea-1.jumio.ai/api/v1"
    };
  }

  /**
   * @description This is the method for making any requests to access the Jumio API, other than authentication.
   * It is configured to have the Bearer token in the header of the request.
   * @param method get or post
   * @param url the jumio endpoint that we want to access as defined in the endpoint enum
   * @param headers
   * @param data any data that may be posted with the request
   */
  private async _fetch(config: {
    method: Method;
    url: string;
    overrideAccessToken?: string;
    data?: any;
    headers?: any;
  }): Promise<any> {
    await this._isReady();
    const { overrideAccessToken, ...requestConfig } = config;

    const headers = {
      Authorization: `Bearer ${overrideAccessToken || this._accessToken}`,
      "User-Agent": "Wealthyhood-app-api",
      ...config.headers
    };

    return await HttpUtil.fetch(
      { ...requestConfig, headers },
      {
        throwError: true,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "JumioService",
          method: "_fetch"
        }
      }
    );
  }
}
