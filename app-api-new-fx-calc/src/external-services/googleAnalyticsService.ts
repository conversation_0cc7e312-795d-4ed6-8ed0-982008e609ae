import axios from "axios";
import logger from "./loggerService";
import { ParticipantDocument } from "../models/Participant";
import { UserDocument } from "../models/User";
import { hashSHA256 } from "../utils/cryptoUtil";
import { envIsProd } from "../utils/environmentUtil";

export enum EventActionEnum {
  EMAIL_SUBMITTED = "email_submitted",
  SIGNED_UP = "signed_up",
  VIRTUAL_PORTFOLIO = "virtual_portfolio_created",
  BANK = "bank_account_linked",
  PERSONAL_DETAILS = "personal_details_submitted",
  PASSPORT_DETAILS = "passport_details_submitted",
  PASSPORT_DETAILS_OVER_24 = "passport_details_submitted_over_24",
  ADDRESS = "address_submitted",
  TAX_DETAILS = "tax_details_submitted",
  VERIFIED = "verified",
  EMAIL_VERIFIED = "email_verified",

  WELCOME = "welcome",
  FIRST_DEPOSIT_CREATED = "first_deposit_created",
  DEPOSIT_CREATED = "deposit_created",
  FIRST_INVESTMENT_CREATED = "first_investment_created",
  INVESTMENT_CREATED = "investment_created"
}

class GoogleAnalyticsService {
  public static async trackEventGA4({
    event,
    user,
    participant
  }: {
    event: EventActionEnum;
    user?: UserDocument;
    participant?: ParticipantDocument;
  }): Promise<void> {
    let trackedParticipant: ParticipantDocument;
    if (user) {
      if (!user.participant) {
        await user.populate("participant");
      }
      trackedParticipant = user.participant;
    } else if (participant) {
      trackedParticipant = participant;
    }

    if (!trackedParticipant) {
      logger.error(`No participant found for user ${user?.email ?? participant?.email}.`, {
        module: "GoogleAnalyticsService",
        method: "trackEventGA4",
        data: { event }
      });
      return;
    }

    const params = {
      measurement_id: process.env.GA_TRACKING_ID,
      api_secret: process.env.GA4_MP_API_SECRET
    };

    const data = {
      user_id: trackedParticipant.anonymousId ?? hashSHA256(trackedParticipant.email),
      client_id: trackedParticipant.gaClientId,
      events: [
        {
          name: event,
          params: {
            page_location: trackedParticipant.pageUserLanded
          }
        }
      ]
    };

    if (envIsProd()) {
      logger.info(`Tracking data for user ${trackedParticipant.email}.`, {
        module: "GoogleAnalyticsService",
        method: "trackEventGA4",
        data
      });

      await axios({
        method: "POST",
        url: "https://www.google-analytics.com/mp/collect",
        params,
        data
      });
    }
  }
}

export default GoogleAnalyticsService;
