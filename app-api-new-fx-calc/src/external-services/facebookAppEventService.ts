import axios from "axios";
import { captureException } from "@sentry/node";
import { hashSHA256 } from "../utils/cryptoUtil";
import { envIsProd } from "../utils/environmentUtil";

const APP_DATASET_ID = "650461146724845";
const APP_EVENT_URL = `https://graph.facebook.com/v15.0/${APP_DATASET_ID}/events`;

type ExtInfoType = {
  extInfoVersion: "i2" | "a2";
  appPackageName: string; // com.some.app
  shortVersion: string; // 771
  longVersion: string; // Version 7.7.1
  osVersion: string; // 10.1.1
  deviceModelName: string; //iPhone
  locale: string; // en_GB
  timezoneAbbreviation: string; // GMT-1
  carrier: string; // TMobile
  screenWidth: string; // 1920
  screenHeight: string; // 1080
  screenDensity: string; // 2.00
  cpuCores: string; // 2
  externalStorage: string; // 128
  freeSpaceExternalStorage: string; // 8
  deviceTimezone: string; // USA/New York
};
type AppDataType = {
  advertiser_tracking_enabled: boolean;
  application_tracking_enabled: boolean;
  extinfo: string[];
};
type UserDataType = {
  em: [string];
};
type EventDataType = {
  event_name: string;
  event_time: number;
  action_source: "app" | "website" | "other";
  app_data: AppDataType;
  user_data: UserDataType;
};

export default class FacebookAppEventService {
  public static async trackEvent(eventName: string, platform: "ios" | "android", { email }: { email: string }) {
    if (!platform) {
      // for users coming from web the platform will be empty - skipping the tracking
      return;
    }

    // Access token is sent as a param
    const params = {
      access_token: process.env.FACEBOOK_CONVERSION_API_ACCESS_TOKEN
    };

    const userData: UserDataType = {
      // User email needs to be SHA256 hashed
      em: [hashSHA256(email)]
    };

    const extInfoData = FacebookAppEventService._getPlatformExtInfo(platform);
    const eventData: EventDataType = {
      event_name: eventName,
      event_time: Math.floor(Date.now() / 1000),
      action_source: "app",
      app_data: {
        advertiser_tracking_enabled: true,
        application_tracking_enabled: true,
        extinfo: Object.values(extInfoData)
      },
      user_data: userData
    };

    // Track events only in production env
    if (envIsProd()) {
      try {
        await axios({
          method: "POST",
          url: APP_EVENT_URL,
          params,
          data: {
            data: [eventData]
          }
        });
      } catch (err) {
        captureException(err);
      }
      return;
    }
  }

  private static _getPlatformExtInfo(platform: "ios" | "android"): ExtInfoType {
    const EXT_INFO_CONFIG: Record<"ios" | "android", ExtInfoType> = {
      ios: {
        extInfoVersion: "i2",
        appPackageName: "com.wealthyhood.client-ios",
        shortVersion: "771",
        longVersion: "Version 7.7.1",
        osVersion: "10.1.1",
        deviceModelName: "iPhone",
        locale: "en_GB",
        timezoneAbbreviation: "GMT",
        carrier: "TMobile",
        screenWidth: "1920",
        screenHeight: "1080",
        screenDensity: "2.00",
        cpuCores: "2",
        externalStorage: "128",
        freeSpaceExternalStorage: "8",
        deviceTimezone: "Europe/Athens"
      },
      // TODO: update for android
      android: {
        extInfoVersion: "a2",
        appPackageName: "com.some.app",
        shortVersion: "771",
        longVersion: "Version 7.7.1",
        osVersion: "10.1.1",
        deviceModelName: "iPhone",
        locale: "en_GB",
        timezoneAbbreviation: "GMT",
        carrier: "TMobile",
        screenWidth: "1920",
        screenHeight: "1080",
        screenDensity: "2.00",
        cpuCores: "2",
        externalStorage: "128",
        freeSpaceExternalStorage: "8",
        deviceTimezone: "Europe/Athens"
      }
    };

    return EXT_INFO_CONFIG[platform];
  }
}
