import { Method } from "axios";
import { countriesConfig } from "@wealthyhood/shared-configs";
import HttpUtil from "../utils/httpUtil";
import crypto from "crypto";

/**
 * DATA TYPES
 */
export const PaymentStatusArray = [
  "pending_customer_approval",
  "pending_submission",
  "submitted",
  "confirmed",
  "paid_out",
  "cancelled",
  "customer_approval_denied",
  "failed",
  "charged_back"
] as const;
export type PaymentStatusType = (typeof PaymentStatusArray)[number];

type PaymentDataType = {
  amount: number; // in cents
  currency: string;
  links: {
    mandate: string;
  };
  charge_date: string; // YYYY-MM-DD
  metadata: {
    wealthyhoodId: string;
  };
};

export type PaymentType = {
  amount: number; // in cents
  id: string;
  status: PaymentStatusType;
  reference: string;
  metadata: any;
};

type CustomerBankAccountDataType = {
  iban: string;
  account_holder_name: string;
  metadata: {
    wealthyhoodId: string;
  };
  links: {
    customer: string;
  };
};

type CustomerDataType = {
  email: string;
  given_name: string;
  family_name: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  postal_code: string;
  country_code: countriesConfig.CountryCodesType;
  metadata: {
    wealthyhoodId: string;
  };
};

type MandateOnlyBillingRequestDataType = {
  customerId: string;
  bankAccountId: string;
  metadata: {
    wealthyhoodId: string;
  };
};

type CollectCustomerDetailsActionType = {
  customer_billing_detail: {
    address_line1: string;
    city: string;
    postal_code: string;
    country_code: countriesConfig.CountryCodesType;
  };
};

export type CustomerBankAccountType = {
  id: string;
  metadata?: {
    wealthyhoodId: string;
  };
};

export type BillingRequestType = {
  id: string;
};

export type FulfiledBillingRequestType = BillingRequestType & {
  mandate_request: {
    links: { mandate: string };
  };
};

export type CustomerType = {
  id: string;
};

export const MandateStatusArray = [
  "pending_customer_approval",
  "pending_submission",
  "submitted",
  "active",
  "suspended_by_payer",
  "failed",
  "cancelled",
  "expired",
  "consumed",
  "blocked"
] as const;
export type MandateStatusType = (typeof MandateStatusArray)[number];

export type MandateType = {
  id: string;
  status: MandateStatusType;
  next_possible_charge_date: string;
};

export const EventLinkKeyArray = ["mandate", "payment", "payout"] as const;
type EventLinkKeyType = (typeof EventLinkKeyArray)[number];

export type EventsType = {
  events: EventType[];
};

export const ResourceTypeArray = [
  "billing_requests",
  "creditors",
  "instalment_schedules",
  "mandates",
  "payer_authorisations",
  "payments",
  "payouts",
  "refunds",
  "scheme_identifiers",
  "subscriptions"
] as const;
export type ResourceTypeType = (typeof ResourceTypeArray)[number];

export type EventType = {
  id: string;
  resource_type: ResourceTypeType;
  action: string;
  links: {
    [key in EventLinkKeyType]?: string;
  };
  details?: {
    cause: string;
    description: string;
  };
};

export const PayoutStatusArray = ["pending", "paid", "bounced"] as const;
export type PayoutStatusType = (typeof PayoutStatusArray)[number];

export type PayoutType = {
  id: string;
  status: PayoutStatusType;
  reference: string;
};

export const PayoutItemTypeArray = [
  "payment_paid_out",
  "payment_failed",
  "payment_charged_back",
  "payment_refunded",
  "refund",
  "refund_funds_returned",
  "gocardless_fee",
  "app_fee",
  "revenue_share",
  "surcharge_fee"
] as const;
export type PayoutItemTypeType = (typeof PayoutItemTypeArray)[number];
export type PayoutItemType = {
  type: PayoutItemTypeType;
  links: {
    payment: string;
  };
};

export class GoCardlessPaymentsService {
  private static _url = process.env.GOCARDLESS_PAY_URL;
  private static _accessToken = process.env.GOCARDLESS_PAY_ACCESS_TOKEN;

  private static _instance: GoCardlessPaymentsService;

  private constructor() {
    GoCardlessPaymentsService._verifyCredentialsExist();
  }

  // ===============
  // PUBLIC METHODS
  // ===============
  public static get Instance(): GoCardlessPaymentsService {
    return this._instance || (this._instance = new this());
  }

  /**
   * @description Takes a date as an input and modifies it to the format <yyyy-mm-dd> expected by the GoCardless API
   * @param date
   */
  public static formatDate(date: Date): string {
    const day = new Date(date).toLocaleDateString("en-GB", { day: "2-digit" });
    const month = new Date(date).toLocaleDateString("en-GB", { month: "2-digit" });
    const year = new Date(date).toLocaleDateString("en-GB", { year: "numeric" });
    return `${year}-${month}-${day}`;
  }

  public async createCustomer(customerData: CustomerDataType): Promise<CustomerType> {
    const response = await this._fetch({
      method: "POST",
      url: `${GoCardlessPaymentsService._url}/customers`,
      data: {
        customers: customerData
      }
    });

    return response.customers;
  }

  public async createCustomerBankAccount(
    bankAccountData: CustomerBankAccountDataType
  ): Promise<CustomerBankAccountType> {
    const response = await this._fetch({
      method: "POST",
      url: `${GoCardlessPaymentsService._url}/customer_bank_accounts`,
      data: {
        customer_bank_accounts: bankAccountData
      }
    });

    return response.customer_bank_accounts;
  }

  public async retrieveMandate(id: string): Promise<MandateType> {
    const mandate = await this._fetch({
      method: "GET",
      url: `${GoCardlessPaymentsService._url}/mandates/${id}`
    });

    return mandate.mandates;
  }

  public async cancelMandate(id: string): Promise<void> {
    await this._fetch({
      method: "POST",
      url: `${GoCardlessPaymentsService._url}/mandates/${id}/cancel`
    });
  }

  public async createMandateOnlyBillingRequest(
    data: MandateOnlyBillingRequestDataType
  ): Promise<BillingRequestType> {
    const billingRequest = await this._fetch({
      method: "POST",
      url: `${GoCardlessPaymentsService._url}/billing_requests`,
      data: {
        billing_requests: {
          mandate_request: {
            scheme: "sepa_core",
            metadata: {
              wealthyhoodId: data.metadata.wealthyhoodId
            }
          },
          links: { customer: data.customerId, customer_bank_account: data.bankAccountId }
        }
      }
    });

    return billingRequest.billing_requests;
  }

  public async collectCustomerDetails(
    billingRequestId: string,
    data: CollectCustomerDetailsActionType
  ): Promise<void> {
    await this._fetch({
      method: "POST",
      url: `${GoCardlessPaymentsService._url}/billing_requests/${billingRequestId}/actions/collect_customer_details`,
      data: {
        data
      }
    });
  }

  public async confirmPayerDetails(billingRequestId: string): Promise<void> {
    await this._fetch({
      method: "POST",
      url: `${GoCardlessPaymentsService._url}/billing_requests/${billingRequestId}/actions/confirm_payer_details`
    });
  }

  public async fulfilBillingRequest(billingRequestId: string): Promise<FulfiledBillingRequestType> {
    const billingRequestFlow = await this._fetch({
      method: "POST",
      url: `${GoCardlessPaymentsService._url}/billing_requests/${billingRequestId}/actions/fulfil`
    });

    return billingRequestFlow.billing_requests;
  }

  public async createPayment(data: PaymentDataType, idempotencyKey: string): Promise<PaymentType> {
    const payment = await this._fetch({
      method: "POST",
      url: `${GoCardlessPaymentsService._url}/payments`,
      data: {
        payments: data
      },
      headers: {
        "Idempotency-Key": idempotencyKey
      }
    });

    return payment.payments;
  }

  public async retrievePayout(payoutId: string): Promise<PayoutType> {
    const payout = await this._fetch({
      method: "GET",
      url: `${GoCardlessPaymentsService._url}/payouts/${payoutId}`
    });

    return payout.payouts;
  }

  public async retrievePayoutItems(payoutId: string): Promise<PayoutItemType[]> {
    const payoutItems = await this._fetch({
      method: "GET",
      url: `${GoCardlessPaymentsService._url}/payout_items?payout=${payoutId}`
    });

    return payoutItems.payout_items;
  }

  public static checkSignature(requestSignature: string, requestBody: any): void {
    const calculatedSignature = crypto
      .createHmac("sha256", process.env.GOCARDLESS_WEBHOOK_SECRET)
      .update(JSON.stringify(requestBody))
      .digest("hex");

    if (requestSignature !== calculatedSignature) {
      throw new Error("Invalid GoCardless signature");
    }
  }

  // ===============
  // PRIVATE METHODS
  // ===============

  private static _verifyCredentialsExist(): void {
    if (!GoCardlessPaymentsService._accessToken) {
      throw new Error("GOCARDLESS_PAY_ACCESS_TOKEN env variable has not been set");
    } else if (!GoCardlessPaymentsService._url) {
      throw new Error("GOCARDLESS_PAY_URL env variable has not been set");
    }
  }

  /**
   * @description This is the method for making any requests to access the GoCardless API.
   * @param config
   */
  private async _fetch(config: { method: Method; url: string; data?: any; headers?: any }): Promise<any> {
    const headers = {
      Authorization: `Bearer ${GoCardlessPaymentsService._accessToken}`,
      "GoCardless-Version": "2015-07-06",
      ...config.headers
    };

    return await HttpUtil.fetch(
      { ...config, headers },
      {
        throwError: true,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "GoCardlessPaymentsService",
          method: "_fetch"
        }
      }
    );
  }
}
