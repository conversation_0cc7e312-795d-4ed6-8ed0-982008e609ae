import { InternalServerError } from "../models/ApiErrors";
import {
  LifetimeEnum,
  ReferralCode,
  ReferralCodeDTOInterface,
  ReferralCodeDocument
} from "../models/ReferralCode";
import { UserDocument } from "../models/User";

export default class ReferralCodeService {
  /**
   * @description Generates a new expiring referral code for a user
   * @param owner The user who will own the referral code
   * @returns The newly created referral code document
   */
  public static async generateExpiringCode(owner: UserDocument): Promise<ReferralCodeDocument> {
    const referralCodeData: ReferralCodeDTOInterface = {
      lifetime: LifetimeEnum.EXPIRING,
      owner: owner.id
    };
    return await new ReferralCode(referralCodeData).save();
  }

  /**
   * @description Replaces an expiring referral code with a new one
   * @param oldCode The old referral code to replace
   */
  public static async replaceExpiringCode(oldCode: ReferralCodeDocument): Promise<void> {
    if (!oldCode.isExpiring) {
      throw new InternalServerError(`Cannot replace non expiring code ${oldCode.code}`);
    }

    // deactivate old code
    await ReferralCode.findOneAndUpdate({ _id: oldCode.id }, { active: false });

    // generate new code
    let ownerId = oldCode.owner;
    if (oldCode.populated("owner")) {
      ownerId = oldCode.owner.id;
    }
    await new ReferralCode({
      lifetime: LifetimeEnum.EXPIRING,
      owner: ownerId
    }).save();
  }
}
