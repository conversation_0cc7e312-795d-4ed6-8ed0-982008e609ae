import mongoose, { QueryOptions } from "mongoose";
import logger from "../external-services/loggerService";
import { AutomationsFilter } from "filters";
import DbUtil from "../utils/dbUtil";
import {
  Automation,
  AutomationDocument,
  AutomationDTOInterface,
  AutomationPopulationFieldsEnum,
  RebalanceAutomationDocument,
  SavingsTopUpAutomation,
  SavingsTopUpAutomationDocument,
  SavingsTopUpAutomationDTOInterface,
  TopUpAutomation,
  TopUpAutomationDocument,
  TopUpAutomationDTOInterface
} from "../models/Automation";
import { TransactionService } from "./transactionService";
import DateUtil from "../utils/dateUtil";
import { DepositCashTransactionDocument, RebalanceTransactionDocument } from "../models/Transaction";
import { captureException } from "@sentry/node";
import { BadRequestError } from "../models/ApiErrors";
import PortfolioService from "./portfolioService";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import UserService from "./userService";
import Decimal from "decimal.js";
import { UserDocument } from "../models/User";
import CurrencyUtil from "../utils/currencyUtil";
import ConfigUtil from "../utils/configUtil";
import { WORK_DAYS_TO_CREATE_DEPOSITS_BEFORE_AUTOMATION_DATE } from "../configs/directDebitConfig";

const PORTFOLIO_VALUE_THRESHOLD_FOR_AUTOMATED_REBALANCE = 10;
const DAYS_TO_CHECK_FOR_EXISTING_REPEATING_TOP_UPS = 24;

export default class AutomationService {
  /**
   * PUBLIC METHODS
   */

  /**
   * Creates a new automation of the given category, or, in the case of top-up automations, if the user already has one,
   * overwrites it with the new configuration provided.
   *
   * @param automationData
   */
  public static async createOrUpdateAutomation(
    automationData: AutomationDTOInterface
  ): Promise<AutomationDocument> {
    switch (automationData.category) {
      case "RebalanceAutomation":
        return AutomationService._createRebalanceAutomation(automationData as AutomationDTOInterface);
      case "SavingsTopUpAutomation": {
        const automation = await AutomationService._createOrUpdateSavingsTopUpAutomation(
          automationData as SavingsTopUpAutomationDTOInterface
        );

        // Clients expect automation to have populated mandate and not populated owner
        await DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.MANDATE);
        DbUtil.depopulateIfAlreadyPopulated(automation, AutomationPopulationFieldsEnum.OWNER);

        return automation;
      }
      case "TopUpAutomation": {
        const automation = await AutomationService._createOrUpdateTopUpAutomation(
          automationData as TopUpAutomationDTOInterface
        );

        // Clients expect automation to have populated mandate and not populated owner
        await DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.MANDATE);
        DbUtil.depopulateIfAlreadyPopulated(automation, AutomationPopulationFieldsEnum.OWNER);

        return automation;
      }
      default:
        throw new BadRequestError("Not supported type automation category");
    }
  }

  public static async getAutomations(
    filter: AutomationsFilter = {},
    populate: {
      mandate: boolean;
      owner: boolean;
    } = {
      mandate: true,
      owner: false
    },
    sort = "-createdAt"
  ): Promise<{ data: AutomationDocument[] }> {
    const dbFilter = this._createAutomationsDbFilter(filter);

    // We are setting strict to 'false' to be able to query automations based on fields that are only present in
    // some types of automations e.g. mandates for top up automations.
    const options: QueryOptions = { strict: false };
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    const populationString = DbUtil.getPopulationString(populate);

    const automations = await Automation.find(dbFilter, null, options).populate(populationString);

    return { data: automations };
  }

  public static async cancelAutomation(automation: AutomationDocument): Promise<AutomationDocument> {
    logger.info(`Cancelling automation ${automation.id}`, {
      module: "AutomationService",
      method: "cancelAutomation",
      data: {
        automation: automation.id
      }
    });

    const cancelledAutomation = await Automation.findByIdAndUpdate(
      automation.id,
      {
        active: false
      },
      { new: true }
    );

    const user = await UserService.getUser(cancelledAutomation.owner.toString());

    if (cancelledAutomation.category == "TopUpAutomation") {
      eventEmitter.emit(events.automation.recurringInvestmentCancellation.eventId, user);
    } else if (cancelledAutomation.category === "SavingsTopUpAutomation") {
      eventEmitter.emit(events.automation.recurringSavingsCancellation.eventId, user);
    } else if (cancelledAutomation.category == "RebalanceAutomation") {
      eventEmitter.emit(events.automation.recurringRebalanceCancellation.eventId, user);
    }

    return cancelledAutomation;
  }

  public static async getAutomation(
    id: string,
    populate: {
      owner: boolean;
      mandate: boolean;
      portfolio: boolean;
    } = {
      owner: true,
      mandate: true,
      portfolio: true
    }
  ): Promise<AutomationDocument> {
    const populationString = DbUtil.getPopulationString(populate);

    return Automation.findById(id).populate(populationString);
  }

  /**
   * @description Creates top-ups (deposits & asset transactions pending those deposits) for all automations that
   * are active and their recurrence date (dayOfMonth) is eligible.
   */
  public static async createAllRecurringTopUps(): Promise<void> {
    const activeAutomations = await TopUpAutomation.find({
      active: true,
      $or: [{ initialiseAt: { $exists: false } }, { initialiseAt: { $lte: new Date(Date.now()) } }]
    }).populate("owner");

    for (let i = 0; i < activeAutomations.length; i++) {
      const automation = activeAutomations[i];

      logger.info(`Checking automation ${automation.id} to create a new top-up`, {
        module: "AutomationService",
        method: "createAllRecurringTopUps",
        data: { automationId: automation._id }
      });

      const topUpCollectionDate = await AutomationService._getTopUpCollectionDate(automation);
      if (!topUpCollectionDate) {
        continue;
      }

      const topUpsExist = await AutomationService._topUpsExistForThisMonthsAutomation(automation);
      if (!topUpsExist) {
        await TransactionService.createAssetTransactionPendingDepositForRecurringTopUp(
          automation,
          topUpCollectionDate
        );
      }
    }
  }

  /**
   * @description Creates savings top-ups (deposits & savings top-up transactions pending those deposits) for all
   * automations that are active and their recurrence date (dayOfMonth) is eligible.
   */
  public static async createAllRecurringSavingsTopUps(): Promise<void> {
    const activeAutomations = await SavingsTopUpAutomation.find({
      active: true
    }).populate("owner");

    for (let i = 0; i < activeAutomations.length; i++) {
      const automation = activeAutomations[i];

      logger.info(`Checking automation ${automation.id} to create a new savings top-up`, {
        module: "AutomationService",
        method: "createAllRecurringSavingsTopUps",
        data: { automationId: automation._id }
      });

      const topUpCollectionDate = await AutomationService._getTopUpCollectionDate(automation);
      if (!topUpCollectionDate) {
        continue;
      }

      const topUpsExist = await AutomationService._topUpsExistForThisMonthsAutomation(automation);
      if (!topUpsExist) {
        await TransactionService.createSavingsTransactionPendingDepositForRecurringTopUp(
          automation,
          topUpCollectionDate
        );
      }
    }
  }

  public static async createAllAutomatedRebalances(): Promise<void> {
    // Get all automations that are active whose recurrence day is four work days or less from now.
    const activeAutomations = (
      await AutomationService.getAutomations({
        categories: ["RebalanceAutomation"],
        activeOnly: true
      })
    ).data;

    for (let i = 0; i < activeAutomations.length; i++) {
      const automation = activeAutomations[i];
      await AutomationService._createRebalanceForAutomation(automation);
    }
  }

  /**
   * PRIVATE METHODS
   */
  private static async _createRebalanceAutomation(
    automationData: AutomationDTOInterface
  ): Promise<RebalanceAutomationDocument> {
    const existingAutomation: AutomationDocument = await Automation.findOne({
      owner: automationData.owner,
      category: automationData.category
    });

    const automation = !existingAutomation
      ? await new Automation(automationData).save()
      : await Automation.findByIdAndUpdate(existingAutomation._id, { active: true }, { new: true });

    const user = await UserService.getUser(automation.owner.toString());
    eventEmitter.emit(events.automation.recurringRebalanceCreation.eventId, user);

    return automation;
  }

  private static async _createOrUpdateTopUpAutomation(
    automationData: TopUpAutomationDTOInterface
  ): Promise<TopUpAutomationDocument> {
    const existingAutomation: AutomationDocument = await TopUpAutomation.findOne({
      owner: automationData.owner
    });

    if (!existingAutomation) {
      logger.info(`Creating ${automationData.category} automation for user ${automationData.owner}`, {
        module: "AutomationService",
        method: "_createOrUpdateTopUpAutomation",
        data: {
          automationData
        }
      });

      // To support both old and new clients, we default to the current date when creating new automations.
      const dayOfMonth = automationData.dayOfMonth ?? DateUtil.convertIntoRecurrenceDate(new Date(Date.now()));

      const automation = await new TopUpAutomation({
        ...automationData,
        dayOfMonth
      }).save();

      const user = await UserService.getUser(automationData.owner.toString());
      const datesEligibleForImmediateTopUp = AutomationService._getDatesToCreateTopUpsFor(user);
      const eligibleCollectionDate = datesEligibleForImmediateTopUp.find(
        (date) => DateUtil.convertIntoRecurrenceDate(date) === automation.dayOfMonth
      );

      if (eligibleCollectionDate) {
        await TransactionService.createAssetTransactionPendingDepositForRecurringTopUp(
          automation,
          eligibleCollectionDate
        );
      }

      eventEmitter.emit(events.automation.recurringInvestmentCreation.eventId, user, {
        amount: new Decimal(automation.consideration.amount).div(100).toNumber(),
        currency: automation.consideration.currency
      });

      return automation;
    } else if (existingAutomation.active) {
      logger.info(`Updating ${automationData.category} automation for user ${automationData.owner}`, {
        module: "AutomationService",
        method: "_createOrUpdateTopUpAutomation",
        data: {
          automationData
        }
      });

      // If there is already an active automation for the user, we can only update the consideration and the mandate
      // (and therefore bank account) used, contrary to day of month, which is not updated.
      return TopUpAutomation.findByIdAndUpdate(
        existingAutomation._id,
        {
          consideration: automationData.consideration,
          mandate: automationData.mandate,
          dayOfMonth: automationData.dayOfMonth
        },
        {
          new: true
        }
      );
    } else {
      // Re-enable the automation
      logger.info(`Replacing ${automationData.category} automation for user ${automationData.owner}`, {
        module: "AutomationService",
        method: "_createOrUpdateTopUpAutomation",
        data: {
          automationData
        }
      });

      // To support both old and new clients, we default to the current date when re-enabling automations.
      const dayOfMonth = automationData.dayOfMonth ?? DateUtil.convertIntoRecurrenceDate(new Date(Date.now()));

      const automation = await TopUpAutomation.findOneAndReplace(
        { _id: existingAutomation._id },
        { ...automationData, dayOfMonth, active: true },
        {
          returnDocument: "after",
          overwriteDiscriminatorKey: true,
          new: true
        }
      );

      await DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.OWNER);
      const user = automation.owner as UserDocument;
      const topUpCollectionDate = await AutomationService._getTopUpCollectionDate(automation);
      if (!topUpCollectionDate) {
        return automation;
      }

      const topUpsExist = await AutomationService._topUpsExistForThisMonthsAutomation(automation);
      if (!topUpsExist) {
        await TransactionService.createAssetTransactionPendingDepositForRecurringTopUp(
          automation,
          topUpCollectionDate
        );

        eventEmitter.emit(events.automation.recurringInvestmentCreation.eventId, user, {
          amount: new Decimal(automation.consideration.amount).div(100).toNumber(),
          currency: automation.consideration.currency
        });
      }

      return automation;
    }
  }

  private static async _createOrUpdateSavingsTopUpAutomation(
    automationData: SavingsTopUpAutomationDTOInterface
  ): Promise<SavingsTopUpAutomationDocument> {
    const existingAutomation: AutomationDocument = await SavingsTopUpAutomation.findOne({
      owner: automationData.owner,
      savingsProduct: automationData.savingsProduct
    });

    if (!existingAutomation) {
      return AutomationService._createSavingsTopUpAutomation(automationData);
    } else if (existingAutomation.active) {
      return AutomationService._updateSavingsTopUpAutomation(existingAutomation, automationData);
    } else {
      return AutomationService._reactivateSavingsTopUpAutomation(existingAutomation, automationData);
    }
  }

  private static async _createRebalanceForAutomation(automation: AutomationDocument): Promise<void> {
    try {
      // If there are any rebalances created within the last 10 days, it means we have already created a rebalance for this
      // month, and we shouldn't create another one.
      const existingRebalances = (await TransactionService.getRebalanceTransactions({
        linkedAutomation: automation._id,
        creationDate: {
          startDate: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 10),
          endDate: new Date(Date.now())
        }
      })) as RebalanceTransactionDocument[];

      if (existingRebalances.length > 0) {
        logger.info(`Not creating a new rebalance for automation ${automation.id} as a rebalance exists already`, {
          module: "AutomationService",
          method: "_createRebalanceForAutomation",
          data: { automationId: automation._id, existingRebalance: existingRebalances[0].id }
        });
        return;
      }

      const portfolio = await PortfolioService.getPortfolio(automation.portfolio.toString(), true);
      const user = portfolio.owner as UserDocument;

      const isPortfolioImbalanced = await PortfolioService.isPortfolioImbalanced(portfolio);
      if (!isPortfolioImbalanced) {
        logger.info(
          `Not creating a new rebalance for automation ${automation.id} as portfolio is already balanced!`,
          {
            module: "AutomationService",
            method: "_createRebalanceForAutomation",
            data: { automationId: automation._id, portfolio: portfolio._id }
          }
        );
        return;
      }

      const isPortfolioValueLowerThanThreshold = new Decimal(
        portfolio.currentTicker.getPrice(user.currency)
      ).lessThan(PORTFOLIO_VALUE_THRESHOLD_FOR_AUTOMATED_REBALANCE);

      if (isPortfolioValueLowerThanThreshold) {
        logger.info(
          `Not creating a new rebalance for automation ${automation.id} as portfolio has value less than ${CurrencyUtil.formatCurrency(PORTFOLIO_VALUE_THRESHOLD_FOR_AUTOMATED_REBALANCE, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}!`,
          {
            module: "AutomationService",
            method: "_createRebalanceForAutomation",
            data: { automationId: automation._id, portfolio: portfolio._id }
          }
        );
        return;
      }

      await PortfolioService.rebalancePortfolio(portfolio, portfolio.initialHoldingsAllocation, {
        linkedAutomation: automation
      });
    } catch (err) {
      captureException(err);
      logger.error(`Failed to create rebalance for automation ${automation._id}`, {
        module: "AutomationService",
        method: "_createRebalanceForAutomation",
        data: { automationId: automation._id, error: err }
      });
    }
  }

  /**
   * PRIVATE METHODS
   */
  private static _createAutomationsDbFilter(filter: AutomationsFilter) {
    const dbFilter = {} as any;

    if (filter.owner) {
      dbFilter["owner"] = new mongoose.Types.ObjectId(filter.owner);
    }

    if (filter.mandate) {
      dbFilter["mandate"] = new mongoose.Types.ObjectId(filter.mandate);
    }

    if (filter.activeOnly) {
      dbFilter["active"] = true;
    }

    if (filter.daysOfMonth) {
      dbFilter["dayOfMonth"] = {
        $in: [...filter.daysOfMonth]
      };
    }

    if (filter.categories) {
      dbFilter["category"] = {
        $in: [...filter.categories]
      };
    }

    if (filter.isInitialised === true) {
      dbFilter["initialiseAt"] = {
        $exists: false
      };
    }

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }

  /**
   * This method returns all dates between now and WORK_DAYS_TO_CREATE_DEPOSITS_BEFORE_AUTOMATION_DATE into the future.
   * @private
   */
  private static _getDatesToCreateTopUpsFor(automationOwner: UserDocument): Date[] {
    const maxDateToCreateTopUpsFor = DateUtil.getDateAfterNthUKWorkDays(
      new Date(Date.now()),
      WORK_DAYS_TO_CREATE_DEPOSITS_BEFORE_AUTOMATION_DATE[automationOwner.companyEntity]
    );

    return DateUtil.getAllDatesBetweenTwoDates(new Date(Date.now()), maxDateToCreateTopUpsFor);
  }

  private static async _createSavingsTopUpAutomation(
    automationData: SavingsTopUpAutomationDTOInterface
  ): Promise<SavingsTopUpAutomationDocument> {
    logger.info(`Creating ${automationData.category} automation for user ${automationData.owner}`, {
      module: "AutomationService",
      method: "_createSavingsTopUpAutomation",
      data: {
        automationData
      }
    });

    const automation = await new SavingsTopUpAutomation(automationData).save();

    const user = await UserService.getUser(automationData.owner.toString());
    const datesEligibleForImmediateTopUp = AutomationService._getDatesToCreateTopUpsFor(user);
    const eligibleCollectionDate = datesEligibleForImmediateTopUp.find(
      (date) => DateUtil.convertIntoRecurrenceDate(date) === automation.dayOfMonth
    );

    if (eligibleCollectionDate) {
      await TransactionService.createSavingsTransactionPendingDepositForRecurringTopUp(
        automation,
        eligibleCollectionDate
      );
    }

    eventEmitter.emit(events.automation.recurringSavingsCreation.eventId, user, {
      amount: new Decimal(automation.consideration.amount).div(100).toNumber(),
      currency: automation.consideration.currency
    });

    return automation;
  }

  private static async _getTopUpCollectionDate(
    automation: SavingsTopUpAutomationDocument | TopUpAutomationDocument
  ): Promise<Date> {
    await DbUtil.populateIfNotAlreadyPopulated(automation, AutomationPopulationFieldsEnum.OWNER);

    const owner = automation.owner as UserDocument;
    const datesEligibleForImmediateTopUp = AutomationService._getDatesToCreateTopUpsFor(owner);
    const topUpCollectionDate = datesEligibleForImmediateTopUp.find(
      (date) => DateUtil.convertIntoRecurrenceDate(date) === automation.dayOfMonth
    );

    if (!topUpCollectionDate) {
      logger.info(
        `Not creating a new top-up for automation ${automation.id}, because day of month is not eligible`,
        {
          module: "AutomationService",
          method: "createAllRecurringTopUps",
          data: {
            automationId: automation._id,
            dayOfMonth: automation.dayOfMonth,
            datesEligibleForImmediateTopUp: datesEligibleForImmediateTopUp
          }
        }
      );
    }

    return topUpCollectionDate;
  }

  private static async _topUpsExistForThisMonthsAutomation(automation: AutomationDocument): Promise<boolean> {
    // If there are any deposits created within the last 25 days, it means we have already created a top-up for this
    // month, and we shouldn't create another one.
    const existingDeposits = (await TransactionService.getDepositCashTransactions({
      linkedAutomation: automation._id,
      creationDate: {
        startDate: DateUtil.getDateOfDaysAgo(new Date(Date.now()), DAYS_TO_CHECK_FOR_EXISTING_REPEATING_TOP_UPS),
        endDate: new Date(Date.now())
      }
    })) as DepositCashTransactionDocument[];

    const topUpsExist = existingDeposits.length > 0;
    if (topUpsExist) {
      logger.info(`Not creating a new top-up for automation ${automation.id} as one exists already`, {
        module: "AutomationService",
        method: "_topUpsExistForThisMonthsAutomation",
        data: { automationId: automation._id, existingDeposit: existingDeposits[0].id }
      });
    }

    return topUpsExist;
  }

  private static async _updateSavingsTopUpAutomation(
    existingAutomation: AutomationDocument,
    automationData: SavingsTopUpAutomationDTOInterface
  ): Promise<SavingsTopUpAutomationDocument> {
    logger.info(`Updating ${automationData.category} automation for user ${automationData.owner}`, {
      module: "AutomationService",
      method: "_updateSavingsTopUpAutomation",
      data: {
        automationData
      }
    });

    return SavingsTopUpAutomation.findByIdAndUpdate(
      existingAutomation._id,
      {
        consideration: automationData.consideration,
        mandate: automationData.mandate,
        dayOfMonth: automationData.dayOfMonth
      },
      {
        new: true
      }
    );
  }

  private static async _reactivateSavingsTopUpAutomation(
    existingAutomation: AutomationDocument,
    automationData: SavingsTopUpAutomationDTOInterface
  ): Promise<SavingsTopUpAutomationDocument> {
    logger.info(`Replacing ${automationData.category} automation for user ${automationData.owner}`, {
      module: "AutomationService",
      method: "_reactivateSavingsTopUpAutomation",
      data: {
        automationData
      }
    });

    const automation = await SavingsTopUpAutomation.findOneAndReplace(
      { _id: existingAutomation._id },
      { ...automationData, active: true },
      {
        returnDocument: "after",
        overwriteDiscriminatorKey: true,
        new: true
      }
    );

    const user = await UserService.getUser(automation.owner.toString());

    const datesEligibleForImmediateTopUp = AutomationService._getDatesToCreateTopUpsFor(user);
    const eligibleCollectionDate = datesEligibleForImmediateTopUp.find(
      (date) => DateUtil.convertIntoRecurrenceDate(date) === automation.dayOfMonth
    );
    if (!eligibleCollectionDate) {
      return automation;
    }

    const topUpsExist = await AutomationService._topUpsExistForThisMonthsAutomation(automation);
    if (!topUpsExist) {
      await TransactionService.createSavingsTransactionPendingDepositForRecurringTopUp(
        automation,
        eligibleCollectionDate
      );

      eventEmitter.emit(events.automation.recurringSavingsCreation.eventId, user, {
        amount: new Decimal(automation.consideration.amount).div(100).toNumber(),
        currency: automation.consideration.currency
      });
    }

    return automation;
  }
}
