import logger from "../external-services/loggerService";
import StockNewsService, { StockNewsDaysEnum, StockNewsItem } from "../external-services/stockNewsService";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import { AssetNews, AssetNewsDocument } from "../models/AssetNews";

const { ASSET_CONFIG } = investmentUniverseConfig;
export default class AssetNewsService {
  /**
   * @description Returns news items for an investment product sorted from newest to oldest
   * @param investmentProductId
   * @param limit The number of news items to retrieve
   * for more info https://stocknewsapi.com/documentation
   */
  public static async getAssetNews(investmentProductId: string, limit?: number): Promise<AssetNewsDocument[]> {
    const assetNews: AssetNewsDocument[] = await AssetNews.find({
      investmentProducts: investmentProductId
    })
      .sort({ date: -1 }) // Sort by date in descending order
      .limit(limit || 50); // Limit the number of documents returned

    return assetNews;
  }

  /**
   * @description Fetches the news for the given investment product, and stores it in the database if not already present.
   * @param investmentProduct The investmentProduct document
   * @param limit {number}The number of news items to retrieve. Takes values from 1 to 100.
   * @param date
   * for more info https://stocknewsapi.com/documentation
   */
  public static async retrieveAndStoreAssetNews(
    investmentProduct: InvestmentProductDocument,
    limit: number,
    date?: StockNewsDaysEnum
  ): Promise<void> {
    const asset = ASSET_CONFIG[investmentProduct.commonId] as investmentUniverseConfig.AssetConfigType;

    let news: StockNewsItem[];
    if (asset.category === "etf") {
      news = await StockNewsService.getEtfNews(asset.formalTicker, limit, date);
    } else {
      news = await StockNewsService.getStockNews(asset.formalTicker, limit, date);
    }

    if (!news) {
      logger.warn(`No news were found for asset ${asset.formalTicker} `, {
        module: "AssetNewsService",
        method: "retrieveAndStoreAssetNews"
      });
      return;
    }

    const operations = news.map((newsItem) => ({
      updateOne: {
        filter: { "providers.stockNews.id": newsItem.news_id }, // Filter by the unique news identifier
        update: {
          $setOnInsert: {
            newsUrl: newsItem.news_url,
            imageUrl: newsItem.image_url,
            title: newsItem.title,
            text: newsItem.text,
            source: newsItem.source_name,
            date: newsItem.date,
            sentiment: newsItem.sentiment,
            type: newsItem.type,
            topics: newsItem.topics,
            tickers: newsItem.tickers
          },
          $addToSet: {
            investmentProducts: investmentProduct._id
          }
        },
        upsert: true // Insert the document if it does not exist
      }
    }));

    await AssetNews.bulkWrite(operations, { ordered: false });
  }
}
