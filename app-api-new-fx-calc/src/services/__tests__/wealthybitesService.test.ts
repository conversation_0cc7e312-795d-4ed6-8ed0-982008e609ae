import { faker } from "@faker-js/faker";
import { clearDb, connectDb, closeDb } from "../../tests/utils/db";
import {
  buildUser,
  buildPortfolio,
  buildHoldingDTO,
  buildIntraDayPortfolioTicker,
  buildSavingsProduct,
  buildDailyPortfolioSavingsTicker,
  buildDailySavingsProductTicker,
  buildReferralCode,
  buildSubscription,
  buildSundownDigest,
  buildContentEntry,
  buildNotificationSettings
} from "../../tests/utils/generateModels";
import { buildContentfulContentEntryResponse } from "../../tests/utils/generateContentful";
import WealthybitesService from "../wealthybitesService";
import logger from "../../external-services/loggerService";
import { RedisClientService } from "../../loaders/redis";
import { UserDocument } from "../../models/User";
import { PortfolioDocument } from "../../models/Portfolio";
import DateUtil from "../../utils/dateUtil";
import MailerService from "../../external-services/mailerService";
import { Message } from "postmark";
import { render } from "@react-email/components";
import React from "react";
import { FMPService } from "../../external-services/fmpService";
import MarketRoundupCreator from "../../lib/marketRoundupCreator";
import TopNewsSelector from "../../lib/topNewsSelector";
import { ReferralCodeDocument } from "../../models/ReferralCode";
import {
  ContentEntryCategoryEnum,
  ContentEntryContentTypeEnum,
  ContentEntryDocument
} from "../../models/ContentEntry";
import { ProviderEnum } from "../../configs/providersConfig";
import { FinimizeContentTypeEnum } from "../../external-services/finimizeService";
import ContentfulRetrievalService from "../../external-services/contentfulRetrievalService";
import { Entry, EntryCollection, EntrySkeletonType } from "contentful";
import { ContentfulContentTypeEnum } from "../../configs/contentfulConfig";
import { EmailNotificationSettingEnum, EmailNotificationSettings } from "../../models/NotificationSettings";

// Mock dependencies
jest.mock("../../external-services/mailerService", () => ({
  batchEmails: jest.fn().mockResolvedValue(true)
}));

jest.mock("@react-email/components", () => ({
  render: jest.fn().mockImplementation(() => "<html><body>Test Email</body></html>")
}));

jest.mock("react", () => ({
  createElement: jest.fn()
}));

jest.mock("../../external-services/fmpService", () => ({
  FMPService: {
    Instance: {
      getHistoricalIndexPrices: jest.fn()
    }
  }
}));

jest.mock("../../lib/marketRoundupCreator", () => ({
  aggregate: jest
    .fn()
    .mockResolvedValue("Markets showed strong performance this week with tech stocks leading gains.")
}));

jest.mock("../../lib/topNewsSelector", () => ({
  select: jest.fn()
}));

describe("WealthybitesService", () => {
  beforeAll(async () => {
    await connectDb("WealthybitesServiceTest");
  });

  afterAll(async () => {
    await closeDb();
  });

  describe("precalculateSnapshotsForAllUsers", () => {
    describe("when the method executes successfully", () => {
      let userWithHoldings: UserDocument;
      let userWithEmptyPortfolio: UserDocument;
      let userWithSavings: UserDocument;
      let portfolioWithHoldings: PortfolioDocument;
      let emptyPortfolio: PortfolioDocument;
      let portfolioWithSavings: PortfolioDocument;
      const TODAY = new Date("2024-01-01");
      const WEEK_AGO = DateUtil.getDateOfDaysAgo(TODAY, 7);
      const SAVINGS_PRODUCT_ID = "mmf_dist_gbp";
      const SAVINGS_AMOUNT = 2000;
      const DAILY_INTEREST = 0.5; // 0.5% daily interest

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => TODAY.getTime());

        // Create test users
        [userWithHoldings, userWithEmptyPortfolio, userWithSavings] = await Promise.all([
          buildUser({
            email: "<EMAIL>",
            kycStatus: "passed",
            portfolioConversionStatus: "completed"
          }),
          buildUser({
            email: "<EMAIL>",
            kycStatus: "passed",
            portfolioConversionStatus: "completed"
          }),
          buildUser({
            email: "<EMAIL>",
            kycStatus: "passed",
            portfolioConversionStatus: "completed"
          })
        ]);

        await Promise.all([
          // userWithHoldings
          buildSubscription({ owner: userWithHoldings.id }),
          buildNotificationSettings({
            owner: userWithHoldings.id,
            email: {
              settings: new Map(
                Object.entries({
                  [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                  [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                  [EmailNotificationSettingEnum.WEALTHYBITES]: true
                })
              ) as EmailNotificationSettings
            }
          }),
          // userWithEmptyPortfolio
          buildSubscription({ owner: userWithEmptyPortfolio.id }),
          buildNotificationSettings({
            owner: userWithEmptyPortfolio.id,
            email: {
              settings: new Map(
                Object.entries({
                  [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                  [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                  [EmailNotificationSettingEnum.WEALTHYBITES]: true
                })
              ) as EmailNotificationSettings
            }
          }),
          // userWithSavings
          buildSubscription({ owner: userWithSavings.id }),
          buildNotificationSettings({
            owner: userWithSavings.id,
            email: {
              settings: new Map(
                Object.entries({
                  [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                  [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                  [EmailNotificationSettingEnum.WEALTHYBITES]: true
                })
              ) as EmailNotificationSettings
            }
          })
        ]);

        // Create savings product
        const savingsProduct = await buildSavingsProduct(true, { commonId: SAVINGS_PRODUCT_ID });

        // Create daily savings product ticker
        await buildDailySavingsProductTicker({
          savingsProduct: savingsProduct.id,
          date: TODAY,
          oneDayYield: DAILY_INTEREST
        });

        // Create portfolios
        const holdings = await Promise.all([
          buildHoldingDTO(true, "equities_apple", 10, { price: 130 }, {}),
          buildHoldingDTO(true, "equities_microsoft", 5, { price: 300 }, {}),
          buildHoldingDTO(true, "equities_amazon", 2, { price: 3000 }, {})
        ]);

        [portfolioWithHoldings, emptyPortfolio, portfolioWithSavings] = await Promise.all([
          buildPortfolio({
            owner: userWithHoldings.id,
            holdings,
            cash: { GBP: { available: 1000, reserved: 0, settled: 1000 } }
          }),
          buildPortfolio({
            owner: userWithEmptyPortfolio.id,
            holdings: [],
            cash: { GBP: { available: 5000, reserved: 0, settled: 5000 } }
          }),
          buildPortfolio({
            owner: userWithSavings.id,
            holdings: [],
            cash: { GBP: { available: 500, reserved: 0, settled: 500 } },
            savings: new Map([[SAVINGS_PRODUCT_ID, { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
          })
        ]);

        // Create daily portfolio savings ticker
        await buildDailyPortfolioSavingsTicker({
          portfolio: portfolioWithSavings.id,
          savingsProduct: savingsProduct.id,
          date: TODAY,
          dailyAccrual: DAILY_INTEREST
        });

        // Create portfolio tickers for calculating returns
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolioWithHoldings._id,
            timestamp: WEEK_AGO,
            pricePerCurrency: { GBP: 8000 }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolioWithHoldings._id,
            timestamp: TODAY,
            pricePerCurrency: { GBP: 8800 }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: emptyPortfolio._id,
            timestamp: WEEK_AGO,
            pricePerCurrency: { GBP: 0 }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: emptyPortfolio._id,
            timestamp: TODAY,
            pricePerCurrency: { GBP: 0 }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolioWithSavings._id,
            timestamp: WEEK_AGO,
            pricePerCurrency: { GBP: 0 }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolioWithSavings._id,
            timestamp: TODAY,
            pricePerCurrency: { GBP: 0 }
          })
        ]);

        // Cache asset weekly returns
        await Promise.all([
          RedisClientService.Instance.set("asset:weeklyReturn:equities_apple", 0.1),
          RedisClientService.Instance.set("asset:weeklyReturn:equities_microsoft", -0.05),
          RedisClientService.Instance.set("asset:weeklyReturn:equities_amazon", 0.15)
        ]);

        // Run the method
        await WealthybitesService.precalculateSnapshotsForAllUsers();
      });

      afterAll(async () => {
        await Promise.all([
          RedisClientService.Instance.del("asset:weeklyReturn:equities_apple"),
          RedisClientService.Instance.del("asset:weeklyReturn:equities_microsoft"),
          RedisClientService.Instance.del("asset:weeklyReturn:equities_amazon"),
          RedisClientService.Instance.del(`wealthybites:snapshot:${userWithHoldings.id}`),
          RedisClientService.Instance.del(`wealthybites:snapshot:${userWithEmptyPortfolio.id}`),
          RedisClientService.Instance.del(`wealthybites:snapshot:${userWithSavings.id}`),
          RedisClientService.Instance.del(`wealthybites:movers:${userWithHoldings.id}`),
          RedisClientService.Instance.del(`wealthybites:movers:${userWithEmptyPortfolio.id}`),
          RedisClientService.Instance.del(`wealthybites:movers:${userWithSavings.id}`)
        ]);
        await clearDb();
      });

      it("should cache investment snapshot for user with holdings", async () => {
        const cachedSnapshot = await RedisClientService.Instance.get(
          `wealthybites:snapshot:${userWithHoldings.id}`
        );

        expect(cachedSnapshot).toMatchObject(
          expect.objectContaining({
            cashValue: 1000,
            investmentsValue: 8800,
            savingsDailyInterest: 0,
            savingsValue: 0,
            totalValue: 9800,
            upByValue: 800
          })
        );

        // Portfolio value increased from 8000 to 8800, which is a 10% return
        expect(cachedSnapshot.weeklyReturns).toBeCloseTo(0.1);
      });

      it("should cache top portfolio movers for user with portfolio and holdings", async () => {
        const cachedMovers = await RedisClientService.Instance.get(`wealthybites:movers:${userWithHoldings.id}`);

        expect(cachedMovers).not.toBeNull();
        expect(cachedMovers).toHaveProperty("winners");
        expect(cachedMovers).toHaveProperty("losers");

        // Amazon has the highest return (15%)
        expect(cachedMovers.winners).toMatchObject([
          { assetId: "equities_amazon", weeklyReturns: 0.15 },
          { assetId: "equities_apple", weeklyReturns: 0.1 }
        ]);

        // Microsoft has the lowest return (-5%)
        expect(cachedMovers.losers).toMatchObject([{ assetId: "equities_microsoft", weeklyReturns: -0.05 }]);
      });

      it("should cache investment snapshot for user with empty portfolio", async () => {
        const cachedSnapshot = await RedisClientService.Instance.get(
          `wealthybites:snapshot:${userWithEmptyPortfolio.id}`
        );

        expect(cachedSnapshot).not.toBeNull();
        expect(cachedSnapshot).toMatchObject(
          expect.objectContaining({
            cashValue: 5000,
            investmentsValue: 0,
            savingsDailyInterest: 0,
            savingsValue: 0,
            totalValue: 5000,
            upByValue: 0
          })
        );

        // Empty portfolio with only cash, no return
        expect(cachedSnapshot.weeklyReturns).toBeCloseTo(0);
      });

      it("should not cache top portfolio movers for user with empty portfolio", async () => {
        const cachedMovers = await RedisClientService.Instance.get(
          `wealthybites:movers:${userWithEmptyPortfolio.id}`
        );

        expect(cachedMovers.winners).toHaveLength(0);
        expect(cachedMovers.losers).toHaveLength(0);
      });

      it("should cache investment snapshot for user with portfolio with savings", async () => {
        const cachedSnapshot = await RedisClientService.Instance.get(
          `wealthybites:snapshot:${userWithSavings.id}`
        );

        expect(cachedSnapshot).not.toBeNull();
        expect(cachedSnapshot).toMatchObject(
          expect.objectContaining({
            cashValue: 500,
            investmentsValue: 0,
            savingsDailyInterest: 0.01,
            savingsValue: 20,
            totalValue: 520,
            upByValue: 0
          })
        );

        // Portfolio with only savings, no return
        expect(cachedSnapshot.weeklyReturns).toBeCloseTo(0);
      });

      it("should not cache top portfolio movers for user with portfolio with savings", async () => {
        const cachedMovers = await RedisClientService.Instance.get(`wealthybites:movers:${userWithSavings.id}`);

        expect(cachedMovers.winners.length).toBe(0);
        expect(cachedMovers.losers.length).toBe(0);
      });
    });

    describe("when there are errors processing some users", () => {
      let successfulUser: UserDocument;
      let errorUser: UserDocument;
      const TODAY = new Date("2024-01-01");
      const SAVINGS_PRODUCT_ID = "mmf_dist_gbp";
      const DAILY_INTEREST = 0.05;

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => TODAY.getTime());

        jest.spyOn(logger, "error");

        const savingsProduct = await buildSavingsProduct(true, { commonId: SAVINGS_PRODUCT_ID });
        // Create daily savings product ticker
        await buildDailySavingsProductTicker({
          savingsProduct: savingsProduct.id,
          date: TODAY,
          oneDayYield: DAILY_INTEREST
        });

        // Create test users
        successfulUser = await buildUser({
          email: "<EMAIL>",
          kycStatus: "passed",
          portfolioConversionStatus: "completed"
        });
        await buildSubscription({ owner: successfulUser.id });
        await buildNotificationSettings({
          owner: successfulUser.id,
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                [EmailNotificationSettingEnum.WEALTHYBITES]: true
              })
            ) as EmailNotificationSettings
          }
        });

        errorUser = await buildUser({
          email: "<EMAIL>",
          kycStatus: "passed",
          portfolioConversionStatus: "completed"
        });
        await buildNotificationSettings({
          owner: errorUser.id,
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                [EmailNotificationSettingEnum.WEALTHYBITES]: true
              })
            ) as EmailNotificationSettings
          }
        });

        // Create portfolio for successful user
        const portfolio = await buildPortfolio({
          owner: successfulUser.id,
          holdings: [await buildHoldingDTO(true, "equities_apple", 1, { price: 2750 })],
          cash: { GBP: { available: 1000, reserved: 0, settled: 1000 } }
        });

        // Create portfolio ticker for calculating returns
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: DateUtil.getDateOfDaysAgo(TODAY, 7),
            pricePerCurrency: { GBP: 2500 }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: TODAY,
            pricePerCurrency: { GBP: 2750 }
          })
        ]);

        // Cache asset weekly returns
        await RedisClientService.Instance.set("asset:weeklyReturn:equities_apple", 0.1);

        // Create an invalid state for the error user that will cause an error
        // Instead of mocking, we'll create a situation that will naturally cause an error
        // For example, create a portfolio without required data
        await buildPortfolio({
          owner: errorUser.id,
          // Missing required fields that will cause an error during processing
          holdings: []
          // Intentionally missing cash field to cause an error
        });

        // Run the method
        await WealthybitesService.precalculateSnapshotsForAllUsers();
      });

      afterAll(async () => {
        await Promise.all([
          RedisClientService.Instance.del("asset:weeklyReturn:equities_apple"),
          RedisClientService.Instance.del(`wealthybites:snapshot:${successfulUser.id}`),
          RedisClientService.Instance.del(`wealthybites:snapshot:${errorUser.id}`),
          RedisClientService.Instance.del(`wealthybites:movers:${successfulUser.id}`),
          RedisClientService.Instance.del(`wealthybites:movers:${errorUser.id}`)
        ]);
        await clearDb();
      });

      it("should cache data for successful users", async () => {
        const cachedSnapshot = await RedisClientService.Instance.get(`wealthybites:snapshot:${successfulUser.id}`);

        expect(cachedSnapshot).toMatchObject(
          expect.objectContaining({
            cashValue: 1000,
            investmentsValue: 2750,
            savingsDailyInterest: 0,
            savingsValue: 0,
            totalValue: 3750,
            upByValue: 250
          })
        );
        expect(cachedSnapshot.weeklyReturns).toBeCloseTo(0.1);
      });

      it("should not cache data for users with errors", async () => {
        const cachedSnapshot = await RedisClientService.Instance.get(`wealthybites:snapshot:${errorUser.id}`);

        expect(cachedSnapshot).toBeUndefined();
      });

      it("should log errors for failed users", () => {
        expect(logger.error).toHaveBeenCalledWith(
          expect.stringContaining(`Failed to precalculate snapshots for user: ${errorUser.id}`),
          expect.objectContaining({
            module: "WealthybitesService",
            method: "precalculateSnapshotsForAllUsers"
          })
        );
      });
    });
  });

  describe("processAllWealthybitesEmails", () => {
    describe("when the method executes successfully", () => {
      let investedUser: UserDocument;
      let uninvestedUser: UserDocument;
      let investedUserWithSavings: UserDocument;
      let deletedUser: UserDocument;
      let userWithWealthybitesDisabled: UserDocument;
      let analysisEntry: ContentEntryDocument;
      let quickTakeEntry: ContentEntryDocument;
      let investedUserReferralCode: ReferralCodeDocument;
      let uninvestedUserReferralCode: ReferralCodeDocument;
      let savingsUserReferralCode: ReferralCodeDocument;
      let createElementCalls: any[];

      const TODAY = new Date("2024-01-01");

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => TODAY.getTime());

        // Create test users
        [investedUser, uninvestedUser, investedUserWithSavings, deletedUser, userWithWealthybitesDisabled] =
          await Promise.all([
            buildUser({
              email: "<EMAIL>",
              firstName: "Invested",
              lastName: "User",
              kycStatus: "passed",
              portfolioConversionStatus: "completed",
              currency: "GBP",
              residencyCountry: "GB"
            }),
            buildUser({
              email: "<EMAIL>",
              firstName: "Uninvested",
              lastName: "User",
              kycStatus: "passed",
              portfolioConversionStatus: "notStarted",
              currency: "GBP",
              residencyCountry: "GB"
            }),
            buildUser({
              email: "<EMAIL>",
              firstName: "Savings",
              lastName: "User",
              kycStatus: "passed",
              portfolioConversionStatus: "completed",
              currency: "GBP",
              residencyCountry: "GB"
            }),
            buildUser({
              email: "<EMAIL>",
              firstName: "Deleted",
              lastName: "User",
              kycStatus: "passed",
              portfolioConversionStatus: "completed",
              currency: "GBP",
              residencyCountry: "GB",
              isDeleted: true
            }),
            buildUser({
              email: "<EMAIL>",
              firstName: "WealthybitesOptOut",
              kycStatus: "passed",
              portfolioConversionStatus: "completed"
            })
          ]);

        // Create subscriptions for all users
        await Promise.all([
          buildSubscription({ owner: investedUser.id }),
          buildNotificationSettings({
            owner: investedUser.id,
            email: {
              settings: new Map(
                Object.entries({
                  [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                  [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                  [EmailNotificationSettingEnum.WEALTHYBITES]: true
                })
              ) as EmailNotificationSettings
            }
          }),
          buildPortfolio({
            owner: investedUser.id,
            holdings: [await buildHoldingDTO(true, "equities_apple", 10, { price: 150 }, {})],
            cash: { GBP: { available: 1000, reserved: 0, settled: 1000 } }
          }),
          buildSubscription({ owner: uninvestedUser.id }),
          buildNotificationSettings({
            owner: uninvestedUser.id,
            email: {
              settings: new Map(
                Object.entries({
                  [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                  [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                  [EmailNotificationSettingEnum.WEALTHYBITES]: true
                })
              ) as EmailNotificationSettings
            }
          }),
          buildSubscription({ owner: investedUserWithSavings.id }),
          buildNotificationSettings({
            owner: investedUserWithSavings.id,
            email: {
              settings: new Map(
                Object.entries({
                  [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                  [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                  [EmailNotificationSettingEnum.WEALTHYBITES]: true
                })
              ) as EmailNotificationSettings
            }
          }),
          buildPortfolio({
            owner: investedUserWithSavings.id,
            cash: { GBP: { available: 500, reserved: 0, settled: 500 } },
            savings: new Map([["mmf_dist_gbp", { amount: 2000, currency: "GBX" }]])
          }),
          buildSubscription({ owner: deletedUser.id }),
          buildNotificationSettings({
            owner: deletedUser.id,
            email: {
              settings: new Map(
                Object.entries({
                  [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                  [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                  [EmailNotificationSettingEnum.WEALTHYBITES]: true
                })
              ) as EmailNotificationSettings
            }
          }),
          buildSubscription({ owner: userWithWealthybitesDisabled.id }),
          buildNotificationSettings({
            owner: userWithWealthybitesDisabled.id,
            email: {
              settings: new Map([
                [EmailNotificationSettingEnum.TRANSACTIONAL, true],
                [EmailNotificationSettingEnum.PROMOTIONAL, true],
                [EmailNotificationSettingEnum.WEALTHYBITES, false]
              ]) as EmailNotificationSettings
            }
          }),
          // Ensure wealthybites unsubscribed user would otherwise be eligible (has a portfolio)
          buildPortfolio({
            owner: userWithWealthybitesDisabled.id,
            holdings: [await buildHoldingDTO(true, "equities_tesla", 2, { price: 200 }, {})],
            cash: { GBP: { available: 300, reserved: 0, settled: 300 } }
          })
        ]);

        // Create sundown digests for the past week
        for (let i = 1; i <= 5; i++) {
          await buildSundownDigest({
            date: DateUtil.getDateOfDaysAgo(TODAY, i),
            content: `Raw content for day ${i}`,
            formattedContent: {
              overview: `Day ${i} market summary: ${i % 2 === 0 ? "Markets up" : "Markets mixed"}.`,
              sections: [
                {
                  assetId: "equities_apple",
                  companyName: "Apple Inc.",
                  companyTicker: "AAPL",
                  title: `Apple news day ${i}`,
                  content: `Apple content for day ${i}`
                },
                {
                  assetId: "equities_microsoft",
                  companyName: "Microsoft Corp.",
                  companyTicker: "MSFT",
                  title: `Microsoft news day ${i}`,
                  content: `Microsoft content for day ${i}`
                }
              ]
            }
          });
        }

        // Mock MarketRoundupCreator
        (MarketRoundupCreator.aggregate as jest.Mock).mockResolvedValue(
          "Markets showed strong performance this week with tech stocks leading gains."
        );

        // Mock FMPService
        (FMPService.Instance.getHistoricalIndexPrices as jest.Mock).mockImplementation(async (symbol: string) => {
          if (symbol === "^GSPC")
            return [
              { date: "2023-12-25", close: 4800 },
              { date: "2023-12-29", close: 4850 },
              { date: "2024-01-01", close: 4900 }
            ];
          if (symbol === "^DJI")
            return [
              { date: "2023-12-25", close: 37000 },
              { date: "2023-12-29", close: 37150 },
              { date: "2024-01-01", close: 37300 }
            ];
          return [];
        });

        // Mock TopNewsSelector
        (TopNewsSelector.select as jest.Mock).mockResolvedValue([
          {
            companyName: "Apple Inc.",
            ticker: "AAPL",
            assetId: "equities_apple",
            title: "Apple news headline",
            content: "Apple news content"
          },
          {
            companyName: "Microsoft Corp.",
            ticker: "MSFT",
            assetId: "equities_microsoft",
            title: "Microsoft news headline",
            content: "Microsoft news content"
          }
        ]);

        // Analyst insights & quick takes
        const fiveDaysAgo = DateUtil.getDateOfDaysAgo(new Date(), 5);
        const threeDaysAgo = DateUtil.getDateOfDaysAgo(new Date(), 3);
        const yesterday = DateUtil.getDateOfDaysAgo(new Date(), 1);

        // Create ANALYSIS type content entry
        analysisEntry = await buildContentEntry({
          title: "Market Analysis: Tech Sector Outlook",
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          contentType: ContentEntryContentTypeEnum.ANALYSIS,
          activeProviders: [ProviderEnum.FINIMIZE, ProviderEnum.CONTENTFUL],
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.INSIGHT,
              publishedAt: fiveDaysAgo
            },
            contentful: {
              id: "contentful-analysis-id-1",
              spaceId: "space-id",
              environmentId: "env-id"
            }
          }
        });

        // Create QUICK_TAKE type content entry
        quickTakeEntry = await buildContentEntry({
          title: "Quick Take: Fed Interest Rate Decision",
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          contentType: ContentEntryContentTypeEnum.QUICK_TAKE,
          activeProviders: [ProviderEnum.FINIMIZE, ProviderEnum.CONTENTFUL],
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.QUICK_TAKE,
              publishedAt: threeDaysAgo
            },
            contentful: {
              id: "contentful-quicktake-id-1",
              spaceId: "space-id",
              environmentId: "env-id"
            }
          }
        });

        // Create WEEKLY_REVIEW type content entry
        const weeklyReviewEntry = await buildContentEntry({
          title: "Weekly Market Recap: Global Economic Updates",
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          contentType: ContentEntryContentTypeEnum.WEEKLY_REVIEW,
          activeProviders: [ProviderEnum.FINIMIZE, ProviderEnum.CONTENTFUL],
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.WEEKLY_BRIEF,
              publishedAt: yesterday
            },
            contentful: {
              id: "contentful-weekly-id-1",
              spaceId: "space-id",
              environmentId: "env-id"
            }
          }
        });

        // Create older content entries that shouldn't be included (outside 7-day window)
        await buildContentEntry({
          title: "Older Analysis: Should Not Be Included",
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          contentType: ContentEntryContentTypeEnum.ANALYSIS,
          activeProviders: [ProviderEnum.FINIMIZE, ProviderEnum.CONTENTFUL],
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.INSIGHT,
              publishedAt: DateUtil.getDateOfDaysAgo(new Date(), 10)
            },
            contentful: {
              id: "contentful-older-id-1",
              spaceId: "space-id",
              environmentId: "env-id"
            }
          }
        });

        // Mock ContentfulRetrievalService to return appropriate responses for our entries
        const contentfulResponses = {
          [analysisEntry.providers.contentful.id]: buildContentfulContentEntryResponse({
            id: analysisEntry.providers.contentful.id,
            title: analysisEntry.title,
            slug: "tech-sector-outlook",
            content: "Detailed analysis of the tech sector's performance and future outlook.",
            readingTime: "5 minutes",
            publishedAt: new Date(analysisEntry.providers?.finimize?.publishedAt),
            analystInsightType: ContentEntryContentTypeEnum.ANALYSIS
          }),
          [quickTakeEntry.providers.contentful.id]: buildContentfulContentEntryResponse({
            id: quickTakeEntry.providers.contentful.id,
            title: quickTakeEntry.title,
            slug: "fed-interest-rate-decision",
            content: "Quick analysis of the Federal Reserve's latest interest rate decision.",
            readingTime: "2 minutes",
            publishedAt: new Date(quickTakeEntry.providers?.finimize?.publishedAt),
            analystInsightType: ContentEntryContentTypeEnum.QUICK_TAKE
          }),
          [weeklyReviewEntry.providers.contentful.id]: buildContentfulContentEntryResponse({
            id: weeklyReviewEntry.providers.contentful.id,
            title: weeklyReviewEntry.title,
            slug: "weekly-market-recap",
            content: "A comprehensive review of this week's market movements and economic updates.",
            readingTime: "7 minutes",
            publishedAt: new Date(weeklyReviewEntry.providers?.finimize?.publishedAt),
            analystInsightType: ContentEntryContentTypeEnum.WEEKLY_REVIEW
          })
        };

        // Mock ContentfulRetrievalService.LearnHubInstance.getEntry
        jest
          .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntry")
          .mockImplementation(async (id: string): Promise<Entry<EntrySkeletonType>> => {
            if (contentfulResponses[id]) {
              return contentfulResponses[id];
            }
            throw new Error(`Entry with ID ${id} not found`);
          });

        // Mock ContentfulRetrievalService.LearnHubInstance.getEntriesByIds
        jest
          .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntriesByIds")
          .mockImplementation(
            async (
              contentType: ContentfulContentTypeEnum,
              ids: string[]
            ): Promise<EntryCollection<EntrySkeletonType>> => {
              const items = ids.filter((id) => contentfulResponses[id]).map((id) => contentfulResponses[id]);
              return { items, total: items.length, skip: 0, limit: items.length };
            }
          );

        jest
          .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntries")
          .mockImplementation(
            async (contentType: ContentfulContentTypeEnum): Promise<EntryCollection<EntrySkeletonType>> => {
              // Create mock learning guide entries
              const learningGuideItems = [
                {
                  sys: {
                    id: "guide-1",
                    type: "Entry",
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z",
                    environment: { sys: { id: "master", type: "Link", linkType: "Environment" } },
                    contentType: { sys: { id: "learningGuide", type: "Link", linkType: "ContentType" } },
                    locale: "en-US"
                  },
                  fields: {
                    title: "Investing Basics",
                    order: 1,
                    key: "investing-basics",
                    slug: "investing-basics",
                    description: "Learn the fundamentals of investing",
                    backgroundColor: "#F5F5F5",
                    guideIcon: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/icon-1.svg"
                        }
                      }
                    },
                    mobileCoverImage: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/mobile-cover-1.jpg"
                        }
                      }
                    },
                    webCoverImage: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/web-cover-1.jpg"
                        }
                      }
                    },
                    chapters: [{ title: "Chapter 1: Getting Started" }, { title: "Chapter 2: Investment Types" }]
                  },
                  metadata: { tags: [] }
                },
                {
                  sys: {
                    id: "guide-2",
                    type: "Entry",
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z",
                    environment: { sys: { id: "master", type: "Link", linkType: "Environment" } },
                    contentType: { sys: { id: "learningGuide", type: "Link", linkType: "ContentType" } },
                    locale: "en-US"
                  },
                  fields: {
                    title: "Understanding Risk",
                    order: 2,
                    key: "understanding-risk",
                    slug: "understanding-risk",
                    description: "Master the concept of investment risk",
                    backgroundColor: "#E8F5E9",
                    guideIcon: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/icon-2.svg"
                        }
                      }
                    },
                    mobileCoverImage: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/mobile-cover-2.jpg"
                        }
                      }
                    },
                    webCoverImage: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/web-cover-2.jpg"
                        }
                      }
                    },
                    chapters: [
                      { title: "Chapter 1: Risk vs Reward" },
                      { title: "Chapter 2: Diversification" },
                      { title: "Chapter 3: Risk Management" }
                    ]
                  },
                  metadata: { tags: [] }
                }
              ];

              return {
                items: learningGuideItems,
                total: learningGuideItems.length,
                skip: 0,
                limit: learningGuideItems.length
              };
            }
          );

        jest
          .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntryBySlug")
          .mockImplementation(
            async (slug: string, contentType: ContentfulContentTypeEnum): Promise<Entry<EntrySkeletonType>> => {
              // Create detailed mock learning guide entries with proper chapter structure
              const guideMocks = {
                "investing-basics": {
                  sys: {
                    id: "guide-1",
                    type: "Entry",
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z",
                    environment: { sys: { id: "master", type: "Link", linkType: "Environment" } },
                    contentType: { sys: { id: "learningGuide", type: "Link", linkType: "ContentType" } },
                    locale: "en-US"
                  },
                  fields: {
                    title: "Investing Basics",
                    order: 1,
                    key: "investing-basics",
                    slug: "investing-basics",
                    description: "Learn the fundamentals of investing",
                    backgroundColor: "#F5F5F5",
                    guideIcon: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/icon-1.svg"
                        }
                      }
                    },
                    mobileCoverImage: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/mobile-cover-1.jpg"
                        }
                      }
                    },
                    webCoverImage: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/web-cover-1.jpg"
                        }
                      }
                    },
                    chapters: [
                      {
                        fields: {
                          title: "Chapter 1: Getting Started",
                          body: {
                            nodeType: "document",
                            content: [
                              {
                                nodeType: "paragraph",
                                content: [{ nodeType: "text", value: "Introduction to investing", marks: [] }]
                              }
                            ]
                          },
                          slug: "getting-started"
                        }
                      },
                      {
                        fields: {
                          title: "Chapter 2: Investment Types",
                          body: {
                            nodeType: "document",
                            content: [
                              {
                                nodeType: "paragraph",
                                content: [{ nodeType: "text", value: "Stocks, bonds, and more", marks: [] }]
                              }
                            ]
                          },
                          slug: "investment-types"
                        }
                      }
                    ]
                  },
                  metadata: { tags: [] }
                },
                "understanding-risk": {
                  sys: {
                    id: "guide-2",
                    type: "Entry",
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z",
                    environment: { sys: { id: "master", type: "Link", linkType: "Environment" } },
                    contentType: { sys: { id: "learningGuide", type: "Link", linkType: "ContentType" } },
                    locale: "en-US"
                  },
                  fields: {
                    title: "Understanding Risk",
                    order: 2,
                    key: "understanding-risk",
                    slug: "understanding-risk",
                    description: "Master the concept of investment risk",
                    backgroundColor: "#E8F5E9",
                    guideIcon: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/icon-2.svg"
                        }
                      }
                    },
                    mobileCoverImage: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/mobile-cover-2.jpg"
                        }
                      }
                    },
                    webCoverImage: {
                      fields: {
                        file: {
                          url: "//images.ctfassets.net/example/web-cover-2.jpg"
                        }
                      }
                    },
                    chapters: [
                      {
                        fields: {
                          title: "Chapter 1: Risk vs Reward",
                          body: {
                            nodeType: "document",
                            content: [
                              {
                                nodeType: "paragraph",
                                content: [
                                  { nodeType: "text", value: "Understanding risk-reward ratio", marks: [] }
                                ]
                              }
                            ]
                          },
                          slug: "risk-vs-reward"
                        }
                      },
                      {
                        fields: {
                          title: "Chapter 2: Diversification",
                          body: {
                            nodeType: "document",
                            content: [
                              {
                                nodeType: "paragraph",
                                content: [
                                  { nodeType: "text", value: "How to diversify your portfolio", marks: [] }
                                ]
                              }
                            ]
                          },
                          slug: "diversification"
                        }
                      }
                    ]
                  },
                  metadata: { tags: [] }
                }
              };

              // Return the guide that matches the requested slug
              if (guideMocks[slug]) {
                return guideMocks[slug] as Entry<EntrySkeletonType>;
              }

              throw new Error(`Entry with slug ${slug} not found`);
            }
          );

        // Guides
        await buildContentEntry({
          title: "Investing Basics",
          category: ContentEntryCategoryEnum.GUIDES,
          contentType: ContentEntryContentTypeEnum.GUIDE,
          activeProviders: [ProviderEnum.CONTENTFUL],
          providers: {
            contentful: {
              id: "contentful-guide-id-1",
              spaceId: "space-id",
              environmentId: "env-id"
            }
          }
        });

        await buildContentEntry({
          title: "Understanding Risk",
          category: ContentEntryCategoryEnum.GUIDES,
          contentType: ContentEntryContentTypeEnum.GUIDE,
          activeProviders: [ProviderEnum.CONTENTFUL],
          providers: {
            contentful: {
              id: "contentful-guide-id-2",
              spaceId: "space-id",
              environmentId: "env-id"
            }
          }
        });

        // Referral code
        investedUserReferralCode = await buildReferralCode({ owner: investedUser.id });
        uninvestedUserReferralCode = await buildReferralCode({ owner: uninvestedUser.id });
        savingsUserReferralCode = await buildReferralCode({ owner: investedUserWithSavings.id });

        // Mock investment snapshots and portfolio movers
        const mockInvestmentSnapshot = {
          totalValue: 9800,
          investmentsValue: 8800,
          cashValue: 1000,
          savingsValue: 0,
          savingsDailyInterest: 0,
          weeklyReturns: 0.1,
          upByValue: 800
        };

        const mockInvestmentSnapshotWithSavings = {
          totalValue: 3670,
          investmentsValue: 3150,
          cashValue: 500,
          savingsValue: 20,
          savingsDailyInterest: 0.01,
          weeklyReturns: 0.05,
          upByValue: 150
        };

        const mockPortfolioMovers = {
          winners: [
            { assetId: "equities_amazon", weeklyReturns: 0.15 },
            { assetId: "equities_apple", weeklyReturns: 0.1 }
          ],
          losers: [{ assetId: "equities_microsoft", weeklyReturns: -0.05 }]
        };

        // Cache investment snapshots and portfolio movers
        await Promise.all([
          RedisClientService.Instance.set(`wealthybites:snapshot:${investedUser.id}`, mockInvestmentSnapshot),
          RedisClientService.Instance.set(`wealthybites:movers:${investedUser.id}`, mockPortfolioMovers),
          RedisClientService.Instance.set(
            `wealthybites:snapshot:${investedUserWithSavings.id}`,
            mockInvestmentSnapshotWithSavings
          ),
          RedisClientService.Instance.set(`wealthybites:movers:${investedUserWithSavings.id}`, {
            winners: [],
            losers: []
          })
        ]);

        // Run the method
        await WealthybitesService.processAllWealthybitesEmails();

        createElementCalls = (React.createElement as jest.Mock).mock.calls;
      });

      afterAll(async () => {
        // Clean up Redis cache
        await Promise.all([
          RedisClientService.Instance.del(`wealthybites:snapshot:${investedUser.id}`),
          RedisClientService.Instance.del(`wealthybites:movers:${investedUser.id}`),
          RedisClientService.Instance.del(`wealthybites:snapshot:${investedUserWithSavings.id}`),
          RedisClientService.Instance.del(`wealthybites:movers:${investedUserWithSavings.id}`)
        ]);

        await clearDb();
      });

      it("should render emails for each active user", () => {
        expect(render).toHaveBeenCalledTimes(6); // 3 active users x 2 (HTML and text versions)
      });

      it("should send batch emails via MailerService", () => {
        expect(MailerService.batchEmails).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              To: investedUser.email,
              Subject: "Weekly Roundup: Top news to begin your week"
            }),
            expect.objectContaining({
              To: uninvestedUser.email,
              Subject: "Weekly Roundup: Top news to begin your week"
            }),
            expect.objectContaining({
              To: investedUserWithSavings.email,
              Subject: "Weekly Roundup: Top news to begin your week"
            })
          ])
        );
      });

      it("should not send emails to deleted users", () => {
        const emailBatch = (MailerService.batchEmails as jest.Mock).mock.calls[0][0];
        const deletedUserEmail = emailBatch.find((email: Message) => email.To === deletedUser.email);
        expect(deletedUserEmail).toBeUndefined();
      });

      it("should not prepare an email body for users with Wealthybites email setting disabled", () => {
        const emailModelForDisabledUser = createElementCalls.find((call: any) => {
          return call[1] && call[1].user && call[1].user.email === userWithWealthybitesDisabled.email;
        });
        expect(emailModelForDisabledUser).toBeUndefined();
      });

      it("should include investment snapshot data for invested users", () => {
        // Find the call for the invested user
        const investedUserCall = createElementCalls.find((call) => call[1] && call[1].firstName === "Invested");

        expect(investedUserCall[1]).toMatchObject({
          firstName: "Invested",
          investmentSnapshot: expect.objectContaining({
            totalValue: 9800,
            investmentsValue: 8800
          }),
          topPortfolioMovers: expect.objectContaining({
            winners: expect.arrayContaining([expect.objectContaining({ assetId: "equities_amazon" })])
          })
        });
      });

      it("should not include investment snapshot data for uninvested users", () => {
        // Find the call for the uninvested user
        const uninvestedUserCall = createElementCalls.find(
          (call) => call[1] && call[1].firstName === "Uninvested"
        );

        expect(uninvestedUserCall[1].investmentSnapshot).toBeUndefined();
        expect(uninvestedUserCall[1].topPortfolioMovers).toBeUndefined();
      });

      it("should include market roundup content in the email", () => {
        // Find a call that includes market roundup
        const callWithMarketRoundup = createElementCalls.find((call) => call[1] && call[1].marketRoundup);

        expect(callWithMarketRoundup[1].marketRoundup).toBe(
          "Markets showed strong performance this week with tech stocks leading gains."
        );
      });

      it("should include top news items in the email", () => {
        // Find a call that includes top news
        const callWithTopNews = createElementCalls.find(
          (call) => call[1] && call[1].topNews && Array.isArray(call[1].topNews)
        );

        // Verify we have 2 news items (matching our mock)
        expect(callWithTopNews[1].topNews.length).toBe(2);

        // There should be two specific titles in our mock data
        const titles = callWithTopNews[1].topNews.map((item) => item.title);
        expect(titles).toContain("Apple news headline");
        expect(titles).toContain("Microsoft news headline");

        // There should be two specific content items in our mock data
        const contents = callWithTopNews[1].topNews.map((item) => item.content);
        expect(contents).toContain("Apple news content");
        expect(contents).toContain("Microsoft news content");
      });

      it("should include analyst insights from the database in the email content", () => {
        // Find a call that includes analyst insights
        const callWithInsights = createElementCalls.find(
          (call) => call[1] && call[1].analystInsights && call[1].analystInsights.insights
        );

        expect(callWithInsights[1].analystInsights.insights).toHaveLength(1);
        expect(callWithInsights[1].analystInsights.insights[0].title).toBe(analysisEntry.title);
        expect(callWithInsights[1].analystInsights.insights[0].url).toBe(
          `https://wealthyhood.onelink.me/TwZO/wealthyhubinsights?documentId=${analysisEntry.id}`
        );

        expect(callWithInsights[1].analystInsights.quickTakes).toHaveLength(1);
        expect(callWithInsights[1].analystInsights.quickTakes[0].title).toBe(quickTakeEntry.title);
        expect(callWithInsights[1].analystInsights.quickTakes[0].url).toBe(
          `https://wealthyhood.onelink.me/TwZO/wealthyhubinsights?documentId=${quickTakeEntry.id}`
        );
      });

      it("should use a learning guide from the database", () => {
        // Check that at least one email includes the learning guide
        const emailWithGuide = createElementCalls.find(
          (call) => call[1] && call[1].learningGuide && call[1].learningGuide.title
        );

        expect(["Investing Basics", "Understanding Risk"]).toContain(emailWithGuide[1].learningGuide.title);
        expect(emailWithGuide[1].learningGuide.url).toContain("wealthyhubguides?documentId=");
      });

      it("should log success message after sending emails", () => {
        expect(logger.info).toHaveBeenCalledWith(
          expect.stringContaining("Successfully sent batch of"),
          expect.objectContaining({
            module: "WealthybitesService",
            method: "processAllWealthybitesEmails"
          })
        );
      });
    });
  });
});
