import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAccount,
  buildAssetTransaction,
  buildBankAccount,
  buildHoldingDTO,
  buildMandate,
  buildNotificationSettings,
  buildParticipant,
  buildPortfolio,
  buildRebalanceAutomation,
  buildRevertRewardTransaction,
  buildReward,
  buildSavingsWithdrawal,
  buildSubscription,
  buildUser,
  buildUserDataRequest,
  buildWithdrawalCashTransaction
} from "../../tests/utils/generateModels";
import { Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import { KycStatusEnum, User, UserDocument } from "../../models/User";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import UserDataRequestService from "../userDataRequestService";
import { UserDataRequest, UserDataRequestDocument, UserDataRequestReasonEnum } from "../../models/UserDataRequest";
import { buildWealthkernelValuationResponse } from "../../tests/utils/generateWealthkernel";
import { faker } from "@faker-js/faker";
import { AccountDocument } from "../../models/Account";
import logger from "../../external-services/loggerService";
import MailchimpService from "../../external-services/mailchimpService";
import events from "../../event-handlers/events";
import eventEmitter from "../../loaders/eventEmitter";
import {
  AssetTransaction,
  AssetTransactionDocument,
  RevertRewardTransaction,
  SavingsWithdrawalTransaction,
  SavingsWithdrawalTransactionDocument,
  WithdrawalCashTransaction,
  WithdrawalCashTransactionDocument
} from "../../models/Transaction";
import { Order, OrderDocument, OrderSubmissionIntentEnum } from "../../models/Order";
import { RewardDocument } from "../../models/Reward";
import DateUtil from "../../utils/dateUtil";
import { Subscription, SubscriptionDocument } from "../../models/Subscription";
import { Participant, ParticipantDocument } from "../../models/Participant";
import { BankAccountDocument } from "../../models/BankAccount";
import AutomationService from "../automationService";
import MailerService from "../../external-services/mailerService";
import { Automation, RebalanceAutomationDocument } from "../../models/Automation";
import { ProviderEnum } from "../../configs/providersConfig";
import { MixpanelAccountStatusEnum } from "../../external-services/segmentAnalyticsService";
import { StripeService } from "../../external-services/stripeService";
import { auth0ManagementClient } from "../../external-services/auth0ManagementService";
import { WealthkernelService } from "../../external-services/wealthkernelService";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("UserDataRequestService", () => {
  beforeAll(async () => await connectDb("UserDataRequestService"));
  afterAll(async () => await closeDb());

  describe("createUserDataRequest", () => {
    describe("when a user requests their disassociation", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        await UserDataRequestService.createUserDataRequest(
          user,
          "disassociation",
          UserDataRequestReasonEnum.USER_REQUEST
        );
      });
      afterAll(async () => await clearDb());

      it("should create a user data request document", async () => {
        const userDataRequest = await UserDataRequest.findOne({ owner: user.id });
        expect(userDataRequest).toEqual(
          expect.objectContaining({
            requestType: "disassociation",
            reason: UserDataRequestReasonEnum.USER_REQUEST
          })
        );
      });

      it("should send a create disassociation email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should emit a 'whAccountStatusUpdate' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ accountStatus: MixpanelAccountStatusEnum.Closing })
        );
      });
    });

    describe("when an admin requests the disassociation of a user", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        await UserDataRequestService.createUserDataRequest(
          user,
          "disassociation",
          UserDataRequestReasonEnum.ADMIN_REQUEST
        );
      });
      afterAll(async () => await clearDb());

      it("should create a user data request document", async () => {
        const userDataRequest = await UserDataRequest.findOne({ owner: user.id });
        expect(userDataRequest).toEqual(
          expect.objectContaining({
            requestType: "disassociation",
            reason: UserDataRequestReasonEnum.ADMIN_REQUEST
          })
        );
      });

      it("should send a create disassociation email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should emit a 'whAccountStatusUpdate' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ accountStatus: MixpanelAccountStatusEnum.Closing })
        );
      });
    });

    describe("when we request the disassociation of a user as part of an inactive user closing batch", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        await UserDataRequestService.createUserDataRequest(
          user,
          "disassociation",
          UserDataRequestReasonEnum.INACTIVE_USER
        );
      });
      afterAll(async () => await clearDb());

      it("should create a user data request document", async () => {
        const userDataRequest = await UserDataRequest.findOne({ owner: user.id });
        expect(userDataRequest).toEqual(
          expect.objectContaining({
            requestType: "disassociation",
            reason: UserDataRequestReasonEnum.INACTIVE_USER
          })
        );
      });

      it("should NOT send a create disassociation email to user", async () => {
        expect(MailerService.sendEmail).not.toHaveBeenCalled();
      });

      it("should emit a 'whAccountStatusUpdate' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ accountStatus: MixpanelAccountStatusEnum.Closing })
        );
      });
    });
  });

  describe("processAllUserDisassociations", () => {
    describe("when there is a pending user disassociation and the user has holdings but their account is suspended", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [
            {
              isin: ASSET_CONFIG["equities_eu"].isin,
              quantity: 1,
              price: { currency: "GBP", amount: 1 },
              value: { currency: "GBP", amount: 1 },
              fxRate: 1.0
            }
          ],
          changedAt: new Date()
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        user = await buildUser({ kycStatus: KycStatusEnum.FAILED });
        await buildNotificationSettings({ owner: user.id });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        await buildAccount({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Suspended"
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not attempt to sell the portfolio holdings", async () => {
        const assetTransactions = await AssetTransaction.find({ owner: user.id });
        expect(assetTransactions.length).toBe(0);
      });

      it("should not create a withdrawal request", async () => {
        const withdrawals = await WithdrawalCashTransaction.find({ owner: user.id });
        expect(withdrawals.length).toBe(0);
      });

      it("should log a warning", () => {
        expect(logger.warn).toHaveBeenCalledWith(
          expect.stringContaining(
            "Cannot sell holdings & withdraw cash during processing of disassociation request"
          ),
          expect.objectContaining({
            module: "UserDataRequestService",
            method: "_processPendingDisassociationRequest"
          })
        );
      });

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation and the user has savings but their account is suspended", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        user = await buildUser({ kycStatus: KycStatusEnum.FAILED });
        await buildNotificationSettings({ owner: user.id });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [],
          savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        await buildAccount({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Suspended"
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not attempt to sell the portfolio holdings", async () => {
        const assetTransactions = await AssetTransaction.find({ owner: user.id });
        expect(assetTransactions.length).toBe(0);
      });

      it("should not create a withdrawal request", async () => {
        const withdrawals = await WithdrawalCashTransaction.find({ owner: user.id });
        expect(withdrawals.length).toBe(0);
      });

      it("should log a warning", () => {
        expect(logger.warn).toHaveBeenCalledWith(
          expect.stringContaining(
            "Cannot sell holdings & withdraw cash during processing of disassociation request"
          ),
          expect.objectContaining({
            module: "UserDataRequestService",
            method: "_processPendingDisassociationRequest"
          })
        );
      });

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation and their account is suspended but the user has no holdings & no cash & no savings", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser({ kycStatus: KycStatusEnum.FAILED });
        await buildNotificationSettings({ owner: user.id });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        await buildAccount({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Suspended"
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio").mockImplementation(async (): Promise<any> => {
          return;
        });
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not attempt to sell the portfolio holdings", async () => {
        const assetTransactions = await AssetTransaction.find({ owner: user.id });
        expect(assetTransactions.length).toBe(0);
      });

      it("should not create a withdrawal request", async () => {
        const withdrawals = await WithdrawalCashTransaction.find({ owner: user.id });
        expect(withdrawals.length).toBe(0);
      });

      it("should close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });
    });

    describe("when there is a pending user disassociation and their account is suspended but the user has cash", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 10 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser({ kycStatus: KycStatusEnum.FAILED });
        await buildNotificationSettings({ owner: user.id });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 0, settled: 0 } },
          holdings: [],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        await buildAccount({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Suspended"
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not attempt to sell the portfolio holdings", async () => {
        const assetTransactions = await AssetTransaction.find({ owner: user.id });
        expect(assetTransactions.length).toBe(0);
      });

      it("should not create a withdrawal request", async () => {
        const withdrawals = await WithdrawalCashTransaction.find({ owner: user.id });
        expect(withdrawals.length).toBe(0);
      });

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should log a warning", () => {
        expect(logger.warn).toHaveBeenCalledWith(
          expect.stringContaining(
            "Cannot sell holdings & withdraw cash during processing of disassociation request"
          ),
          expect.objectContaining({
            module: "UserDataRequestService",
            method: "_processPendingDisassociationRequest"
          })
        );
      });
    });

    describe("when there is a pending user disassociation but the user has holdings in WK", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [
            {
              isin: ASSET_CONFIG["equities_eu"].isin,
              quantity: 1,
              price: { currency: "GBP", amount: 1 },
              value: { currency: "GBP", amount: 1 },
              fxRate: 1.0
            }
          ],
          changedAt: new Date()
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation and the user has an active Automation", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let bankAccount: BankAccountDocument;
      let activeAutomation: RebalanceAutomationDocument;
      let inactiveAutomation: RebalanceAutomationDocument;
      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });
        activeAutomation = await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        inactiveAutomation = await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: false
        });

        await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          providers: { gocardless: { id: "MA-123", status: "pending_submission" } }
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();

        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should cancel active automation", async () => {
        const updatedActiveAutomation = await Automation.findById(activeAutomation.id);
        expect(updatedActiveAutomation?.active).toBe(false);
      });

      it("should not change inactive automation", async () => {
        const updatedInactiveAutomation = await Automation.findById(inactiveAutomation.id);
        expect(updatedInactiveAutomation?.active).toBe(false);
      });
    });

    describe("when there is a pending user disassociation and the user has an active card-based subscription", () => {
      const STRIPE_ID = faker.string.uuid();

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        await buildSubscription({
          owner: user.id,
          providers: {
            stripe: {
              id: STRIPE_ID,
              status: "active"
            }
          }
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();

        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);
        jest.spyOn(StripeService.Instance, "cancelSubscription");

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should cancel subscription with Stripe", async () => {
        expect(StripeService.Instance.cancelSubscription).toHaveBeenCalledWith(STRIPE_ID);
      });
    });

    describe("when there is a pending user disassociation and the user has a trialing card-based subscription", () => {
      const STRIPE_ID = faker.string.uuid();

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        await buildSubscription({
          owner: user.id,
          providers: {
            stripe: {
              id: STRIPE_ID,
              status: "trialing"
            }
          }
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();

        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);
        jest.spyOn(StripeService.Instance, "cancelSubscription");

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should cancel subscription with Stripe", async () => {
        expect(StripeService.Instance.cancelSubscription).toHaveBeenCalledWith(STRIPE_ID);
      });
    });

    describe("when there is a pending user disassociation and the user has a cancelled card-based subscription", () => {
      const STRIPE_ID = faker.string.uuid();

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        await buildSubscription({
          owner: user.id,
          providers: {
            stripe: {
              id: STRIPE_ID,
              status: "canceled"
            }
          }
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();

        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);
        jest.spyOn(StripeService.Instance, "cancelSubscription");

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should NOT cancel subscription with Stripe", async () => {
        expect(StripeService.Instance.cancelSubscription).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending user disassociation and the user has 2 active Wealthkernel mandates", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let bankAccount: BankAccountDocument;
      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });

        await Promise.all([
          buildMandate({
            owner: user.id,
            category: "Top-Up",
            bankAccount: bankAccount.id,
            providers: {
              wealthkernel: {
                id: "mandate-id",
                status: "Pending"
              }
            }
          }),

          buildMandate({
            owner: user.id,
            category: "Top-Up",
            bankAccount: bankAccount.id,
            providers: {
              wealthkernel: {
                id: "mandate-id2",
                status: "Active"
              }
            }
          }),

          buildMandate({
            owner: user.id,
            category: "Top-Up",
            bankAccount: bankAccount.id,
            providers: {
              wealthkernel: {
                id: "mandate-id3",
                status: "Cancelled"
              }
            }
          })
        ]);

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "cancelMandate").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel twice to cancel mandates", () => {
        expect(WealthkernelService.UKInstance.cancelMandate).toHaveBeenCalledTimes(2);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });
    });

    describe("when there is a pending user disassociation, request is processed for the first time and the user has holdings & no savings", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        subscription = await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly"
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should create a pending portfolio sell transaction for the whole portfolio value", async () => {
        const portfolioSellTransaction: AssetTransactionDocument = await AssetTransaction.findOne({
          owner: user.id
        });
        expect(portfolioSellTransaction).toEqual(
          expect.objectContaining({
            owner: user._id,
            portfolio: portfolio._id,
            linkedUserDataRequest: request._id
          })
        );

        const orders: OrderDocument[] = await Order.find({ transaction: portfolioSellTransaction.id });
        expect(orders.length).toBe(1);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              isin: ASSET_CONFIG["equities_us"].isin,
              quantity: 1,
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should deactivate the user's subscription", async () => {
        const updatedSubscription = await Subscription.findById(subscription.id);
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            active: false
          })
        );
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, request is processed for the first time and the user has savings & no holdings", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [],
          savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        subscription = await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly"
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should create a pending savings withdrawal transaction for the whole portfolio value", async () => {
        const savingsWithdrawalTransaction: SavingsWithdrawalTransactionDocument =
          (await SavingsWithdrawalTransaction.findOne({
            owner: user.id
          })) as SavingsWithdrawalTransactionDocument;
        expect(savingsWithdrawalTransaction).toEqual(
          expect.objectContaining({
            owner: user._id,
            portfolio: portfolio._id,
            linkedUserDataRequest: request._id
          })
        );
      });

      it("should deactivate the user's subscription", async () => {
        const updatedSubscription = await Subscription.findById(subscription.id);
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            active: false
          })
        );
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, request is processed for the first time and the user has both savings & holdings", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)],
          savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        subscription = await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly"
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should create a pending portfolio sell transaction for the whole portfolio value", async () => {
        const portfolioSellTransaction: AssetTransactionDocument = await AssetTransaction.findOne({
          owner: user.id
        });
        expect(portfolioSellTransaction).toEqual(
          expect.objectContaining({
            owner: user._id,
            portfolio: portfolio._id,
            linkedUserDataRequest: request._id
          })
        );

        const orders: OrderDocument[] = await Order.find({ transaction: portfolioSellTransaction.id });
        expect(orders.length).toBe(1);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              isin: ASSET_CONFIG["equities_us"].isin,
              quantity: 1,
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should create a pending savings withdrawal transaction for the whole portfolio value", async () => {
        const savingsWithdrawalTransaction: SavingsWithdrawalTransactionDocument =
          (await SavingsWithdrawalTransaction.findOne({
            owner: user.id
          })) as SavingsWithdrawalTransactionDocument;
        expect(savingsWithdrawalTransaction).toEqual(
          expect.objectContaining({
            owner: user._id,
            portfolio: portfolio._id,
            linkedUserDataRequest: request._id
          })
        );
      });

      it("should deactivate the user's subscription", async () => {
        const updatedSubscription = await Subscription.findById(subscription.id);
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            active: false
          })
        );
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, request is processed for the first time and the user does not have a WK portfolio/account", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        const account = await buildAccount({
          owner: user.id
        });
        await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id
          },
          false
        );

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed from both audiences", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(2);
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Completed");
      });

      it("should emit a 'whAccountStatusUpdate' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ accountStatus: MixpanelAccountStatusEnum.Closed })
        );
      });
    });

    describe("when there is a pending user disassociation, request is processed after sell asset transaction has been created but is pending", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)],
          mode: PortfolioModeEnum.REAL
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedUserDataRequest: request.id,
          status: "Pending"
        });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, request is processed after savings withdrawal transaction has been created but is pending", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [],
          savings: new Map([["mmf_dist_gbp", { amount: 0, currency: "GBX" }]]),
          mode: PortfolioModeEnum.REAL
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          linkedUserDataRequest: request.id,
          consideration: { amount: 1000, currency: "GBP" }
        });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, request is processed after selling all holdings & savings and the user has cash but no bank accounts", () => {
      const AVAILABLE_CASH = 10; // In GBP

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser({}, false);
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          providers: {
            wealthkernel: {
              status: "Active",
              id: faker.string.uuid()
            }
          }
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedUserDataRequest: request.id,
          status: "Settled"
        });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should NOT create a linked withdrawal transaction", async () => {
        const withdrawalTransaction: WithdrawalCashTransactionDocument = await WithdrawalCashTransaction.findOne({
          owner: user.id
        });
        expect(withdrawalTransaction).toBeNull();
      });

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, request is processed after selling all holdings & savings and the user has cash", () => {
      const AVAILABLE_CASH = 10; // In GBP

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          providers: {
            wealthkernel: {
              status: "Active",
              id: faker.string.uuid()
            }
          }
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedUserDataRequest: request.id,
          status: "Settled"
        });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should create a linked withdrawal transaction", async () => {
        const withdrawalTransaction: WithdrawalCashTransactionDocument = await WithdrawalCashTransaction.findOne({
          owner: user.id
        });
        expect(withdrawalTransaction).toEqual(
          expect.objectContaining({
            owner: user._id,
            portfolio: portfolio._id,
            linkedUserDataRequest: request._id,
            consideration: {
              amount: AVAILABLE_CASH * 100,
              currency: "GBP"
            }
          })
        );
      });

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, request is processed after selling all holdings & savings and the user has cash but unsettled cash", () => {
      const AVAILABLE_CASH = 10; // In GBP

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH - 1 } },
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          providers: {
            wealthkernel: {
              status: "Active",
              id: faker.string.uuid()
            }
          }
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedUserDataRequest: request.id,
          status: "Settled"
        });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not create a linked withdrawal transaction", async () => {
        const withdrawalTransactions = await WithdrawalCashTransaction.find({
          owner: user.id
        });

        expect(withdrawalTransactions.length).toBe(0);
      });

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, request is processed after sselling all holdings & savings and withdrawal has been created but pending", () => {
      const AVAILABLE_CASH = 10; // In GBP

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          providers: {
            wealthkernel: {
              status: "Active",
              id: faker.string.uuid()
            }
          }
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedUserDataRequest: request.id,
          status: "Settled"
        });

        await buildWithdrawalCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          consideration: {
            amount: AVAILABLE_CASH * 100,
            currency: "GBP"
          },
          linkedUserDataRequest: request.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          }
        });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation request and the user has a settled restricted reward", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let reward: RewardDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        reward = await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          accepted: true,
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5), // Reward is still restricted
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          status: "Settled",
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          }
        });

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should create a revert reward transaction for the restricted reward", async () => {
        const revertRewardTransactions = await RevertRewardTransaction.find({ owner: user.id });

        expect(revertRewardTransactions.length).toBe(1);
        expect(revertRewardTransactions[0]).toEqual(
          expect.objectContaining({
            owner: user._id,
            portfolio: portfolio._id,
            linkedUserDataRequest: request._id,
            reward: reward._id,
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          })
        );
      });

      it("should not request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are not closed)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation request and the user has a pending restricted reward", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          accepted: true,
          status: "Pending",
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5), // Reward is still restricted
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Created"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Open"
              }
            }
          }
        });

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not create a revert reward transaction for the restricted reward", async () => {
        const revertRewardTransactions = await RevertRewardTransaction.find({ owner: user.id });
        expect(revertRewardTransactions.length).toBe(0);
      });

      it("should not request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are not closed)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation request and the user has a non accepted reward", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5), // Reward is still restricted
          accepted: false
        });

        await buildReward({
          targetUser: user.id,
          asset: "commodities_gold",
          quantity: 1,
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5) // Reward is still restricted
        });

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not create a revert reward transaction", async () => {
        const revertRewardTransactions = await RevertRewardTransaction.find({ owner: user.id });
        expect(revertRewardTransactions.length).toBe(0);
      });

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are not closed)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation and request and the user has a settled revert reward transaction", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let reward: RewardDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        reward = await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5), // Reward is still restricted
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          }
        });

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        request = await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await buildRevertRewardTransaction({
          owner: user.id,
          linkedUserDataRequest: request.id,
          reward: reward.id,
          status: "Settled"
        });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not create another revert reward transaction", async () => {
        const revertRewardTransactions = await RevertRewardTransaction.find({ owner: user.id });
        expect(revertRewardTransactions.length).toBe(1);
      });

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are not closed)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, user has no cash/holdings and request is processed for the first time", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let participant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        participant = await buildParticipant({ email: user.email });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Closing"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should set the portfolio status as Closing", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);

        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              wealthkernel: expect.objectContaining({
                id: portfolio.providers.wealthkernel.id,
                status: "Closing"
              })
            })
          })
        );
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should update the e-mail of the participant with the deleted prefix", async () => {
        const updatedParticipant: ParticipantDocument = await Participant.findById(participant.id);
        expect(updatedParticipant.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are closing)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation, user has no cash/holdings, request is processed for the first time and the request was created due to inactive user", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let participant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        participant = await buildParticipant({ email: user.email });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Closing"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({
          owner: user.id,
          requestType: "disassociation",
          reason: UserDataRequestReasonEnum.INACTIVE_USER
        });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should set the portfolio status as Closing", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);

        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              wealthkernel: expect.objectContaining({
                id: portfolio.providers.wealthkernel.id,
                status: "Closing"
              })
            })
          })
        );
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should update the e-mail of the participant with the deleted prefix", async () => {
        const updatedParticipant: ParticipantDocument = await Participant.findById(participant.id);
        expect(updatedParticipant.email).toBe(`deleted_${user.email}`);
      });

      it("should send the inactive user disassociation success email to user", async () => {
        expect(MailerService.sendEmail).not.toHaveBeenCalledWith(
          expect.objectContaining({ id: user.id }),
          "deletionSuccess",
          expect.anything()
        );
        expect(MailerService.sendEmail).toHaveBeenCalledWith(
          expect.objectContaining({ id: user.id }),
          "deletionSuccessForInactiveUser",
          expect.anything()
        );
      });

      it("should not complete the disassociation request (because portfolio and account are closing)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation request, e-mail has been disassociated and request is processed again", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let participant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser({ email: `deleted_${faker.internet.email()}` });
        participant = await buildParticipant({ email: user.email });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember").mockImplementation(() => {
          throw Error("User with that e-mail not found");
        });
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Closing"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should set the portfolio status as Closing", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);

        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              wealthkernel: expect.objectContaining({
                id: portfolio.providers.wealthkernel.id,
                status: "Closing"
              })
            })
          })
        );
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not try to update the Mailchimp member", async () => {
        expect(MailchimpService.retrieveMember).not.toHaveBeenCalled();
        expect(MailchimpService.updateMember).not.toHaveBeenCalled();
      });

      it("should not re-update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not re-update the e-mail of the participant with the deleted prefix", async () => {
        const updatedParticipant: ParticipantDocument = await Participant.findById(participant.id);
        expect(updatedParticipant.email).toBe(user.email);
      });

      it("should not send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not complete the disassociation request (because portfolio and account are closing)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation and request is processed after WK has closed the portfolio but it's not in sync with ours", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Closing"
              }
            }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "unsubscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Closed"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Closed"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not request (again) the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should set the portfolio status as Closed", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);

        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              wealthkernel: expect.objectContaining({
                id: portfolio.providers.wealthkernel.id,
                status: "Closed"
              })
            })
          })
        );
      });

      it("should not yet request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not update (again) the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).not.toHaveBeenCalled();
      });

      it("should not update (again) the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not yet complete the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation and request is processed after the portfolio is closed and in sync", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Closed"
              }
            }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "unsubscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Closed"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Closed"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not request (again) the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).toHaveBeenCalledTimes(1);
      });

      it("should not update (again) the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).not.toHaveBeenCalled();
      });

      it("should not update (again) the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not yet complete the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation and request is processed after WK has closed the account but it's not in sync with ours", () => {
      let user: UserDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Closing" } }
        });
        await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Closed"
              }
            }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "unsubscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not request (again) the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not request (again) the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not update (again) the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).not.toHaveBeenCalled();
      });

      it("should not update (again) the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not yet complete the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user disassociation and request is processed and portfolios & accounts are closed and in sync", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Closed" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Closed"
              }
            }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "unsubscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Closed"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Closed"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should not request (again) the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not request (again) the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not update (again) the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).not.toHaveBeenCalled();
      });

      it("should not update (again) the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should complete the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Completed");
      });

      it("should emit a 'whAccountStatusUpdate' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ accountStatus: MixpanelAccountStatusEnum.Closed })
        );
      });
    });

    describe("when there is a pending user disassociation request and the user hasn't linked a bank account and has no cash and no holdings", () => {
      const AVAILABLE_CASH = 0; // In GBP

      let user: UserDocument;
      let participant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(auth0ManagementClient.users, "delete");
        jest.spyOn(auth0ManagementClient.users, "get").mockImplementation(() => {
          return;
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        user = await buildUser({}, false);
        await buildNotificationSettings({ owner: user.id });
        participant = await buildParticipant({ email: user.email });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          providers: {
            wealthkernel: {
              status: "Active",
              id: faker.string.uuid()
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });

        await UserDataRequestService.processAllUserDisassociations();
      });
      afterAll(async () => await clearDb());

      it("should unsubscribe the Mailchimp member from both audiences", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(2);
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should update the e-mail of the participant with the deleted prefix", async () => {
        const updatedParticipant: ParticipantDocument = await Participant.findById(participant.id);
        expect(updatedParticipant.email).toBe(`deleted_${user.email}`);
      });

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should send the disassociation success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request yet", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });
  });

  describe("processAllUserGDPRDeletions", () => {
    describe("when there is a pending user GDPR deletion and the user has holdings but their account is suspended", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [
            {
              isin: ASSET_CONFIG["equities_eu"].isin,
              quantity: 1,
              price: { currency: "GBP", amount: 1 },
              value: { currency: "GBP", amount: 1 },
              fxRate: 1.0
            }
          ],
          changedAt: new Date()
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        user = await buildUser({ kycStatus: KycStatusEnum.FAILED });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        await buildAccount({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Suspended"
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not attempt to sell the portfolio holdings", async () => {
        const assetTransactions = await AssetTransaction.find({ owner: user.id });
        expect(assetTransactions.length).toBe(0);
      });

      it("should not create a withdrawal request", async () => {
        const withdrawals = await WithdrawalCashTransaction.find({ owner: user.id });
        expect(withdrawals.length).toBe(0);
      });

      it("should log a warning", () => {
        expect(logger.warn).toHaveBeenCalledWith(
          expect.stringContaining(
            "Cannot sell holdings & withdraw cash during processing of disassociation request"
          ),
          expect.objectContaining({
            module: "UserDataRequestService",
            method: "_processPendingGDPRDeletionRequest"
          })
        );
      });

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion and their account is suspended but the user has no holdings & no cash", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser({ kycStatus: KycStatusEnum.FAILED });
        await buildNotificationSettings({ owner: user.id });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        await buildAccount({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Suspended"
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio").mockImplementation(async (): Promise<any> => {
          return;
        });
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        jest.spyOn(auth0ManagementClient.users, "delete");
        jest.spyOn(auth0ManagementClient.users, "get").mockImplementation(() => {
          return;
        });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not attempt to sell the portfolio holdings", async () => {
        const assetTransactions = await AssetTransaction.find({ owner: user.id });
        expect(assetTransactions.length).toBe(0);
      });

      it("should not create a withdrawal request", async () => {
        const withdrawals = await WithdrawalCashTransaction.find({ owner: user.id });
        expect(withdrawals.length).toBe(0);
      });

      it("should close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalled();
      });
    });

    describe("when there is a pending user GDPR deletion but the user has holdings in WK", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [
            {
              isin: ASSET_CONFIG["equities_eu"].isin,
              quantity: 1,
              price: { currency: "GBP", amount: 1 },
              value: { currency: "GBP", amount: 1 },
              fxRate: 1.0
            }
          ],
          changedAt: new Date()
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest.spyOn(auth0ManagementClient.users, "delete");
        jest.spyOn(auth0ManagementClient.users, "get").mockImplementation(() => {
          return;
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should delete the Mailchimp member", async () => {
        expect(MailchimpService.deleteMember).toHaveBeenCalled();
      });

      it("should delete the Auth0 user", async () => {
        expect(auth0ManagementClient.users.delete).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not delete the user document", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser).not.toBeNull();
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion but the user has cash in WK", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest.spyOn(auth0ManagementClient.users, "delete");
        jest.spyOn(auth0ManagementClient.users, "get").mockImplementation(() => {
          return;
        });

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 1 },
              value: { currency: "GBP", amount: 1 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should delete the Mailchimp member", async () => {
        expect(MailchimpService.deleteMember).toHaveBeenCalled();
      });

      it("should delete the Auth0 user", async () => {
        expect(auth0ManagementClient.users.delete).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not delete the user document", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser).not.toBeNull();
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is pending user GDPR deletion and the user has an active Automation", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let bankAccount: BankAccountDocument;
      let activeAutomation: RebalanceAutomationDocument;
      let inactiveAutomation: RebalanceAutomationDocument;
      beforeAll(async () => {
        jest.restoreAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });

        activeAutomation = await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        inactiveAutomation = await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: false
        });

        await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          providers: { gocardless: { id: "MA-123", status: "pending_submission" } }
        });

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(AutomationService, "cancelAutomation");

        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should cancel active automation", async () => {
        const updatedActiveAutomation = await Automation.findById(activeAutomation.id);
        expect(updatedActiveAutomation?.active).toBe(false);
      });

      it("should not change inactive automation", async () => {
        const updatedInactiveAutomation = await Automation.findById(inactiveAutomation.id);
        expect(updatedInactiveAutomation?.active).toBe(false);
      });
    });

    describe("when there is a pending user GDPR deletion  and the user has 2 active Wealthkernel mandates", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let bankAccount: BankAccountDocument;
      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });

        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });

        await Promise.all([
          buildMandate({
            owner: user.id,
            category: "Top-Up",
            bankAccount: bankAccount.id,
            providers: {
              wealthkernel: {
                id: "mandate-id",
                status: "Pending"
              }
            }
          }),

          buildMandate({
            owner: user.id,
            category: "Top-Up",
            bankAccount: bankAccount.id,
            providers: {
              wealthkernel: {
                id: "mandate-id2",
                status: "Active"
              }
            }
          }),

          buildMandate({
            owner: user.id,
            category: "Top-Up",
            bankAccount: bankAccount.id,
            providers: {
              wealthkernel: {
                id: "mandate-id3",
                status: "Cancelled"
              }
            }
          })
        ]);

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "cancelMandate").mockResolvedValue();
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers?.wealthkernel?.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel twice to cancel mandates", () => {
        expect(WealthkernelService.UKInstance.cancelMandate).toHaveBeenCalledTimes(2);
      });
    });

    describe("when there is a pending user GDPR deletion, request is processed for the first time and the user has holdings", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)],
          mode: PortfolioModeEnum.REAL,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        subscription = await buildSubscription({
          owner: user.id,
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly"
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should create a pending portfolio sell transaction for the whole portfolio value", async () => {
        const portfolioSellTransaction: AssetTransactionDocument = await AssetTransaction.findOne({
          owner: user.id
        });
        expect(portfolioSellTransaction).toEqual(
          expect.objectContaining({
            owner: user._id,
            portfolio: portfolio._id,
            linkedUserDataRequest: request._id
          })
        );

        const orders: OrderDocument[] = await Order.find({ transaction: portfolioSellTransaction.id });
        expect(orders.length).toBe(1);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              isin: ASSET_CONFIG["equities_us"].isin,
              quantity: 1,
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should deactivate the user's subscription", async () => {
        const updatedSubscription = await Subscription.findById(subscription.id);
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            active: false
          })
        );
      });

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).not.toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion, request is processed after sell transaction has been created but is pending", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)],
          mode: PortfolioModeEnum.REAL
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedUserDataRequest: request.id,
          status: "Pending"
        });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not update the Mailchimp member to be unsubscribed", async () => {
        expect(MailchimpService.updateMember).not.toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the disassociation request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion, request is processed after sell transaction has been settled and the user has cash", () => {
      const AVAILABLE_CASH = 10; // In GBP

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          providers: {
            wealthkernel: {
              status: "Active",
              id: faker.string.uuid()
            }
          }
        });

        request = await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedUserDataRequest: request.id,
          status: "Settled"
        });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should create a linked withdrawal transaction", async () => {
        const withdrawalTransaction: WithdrawalCashTransactionDocument = await WithdrawalCashTransaction.findOne({
          owner: user.id
        });
        expect(withdrawalTransaction).toEqual(
          expect.objectContaining({
            owner: user._id,
            portfolio: portfolio._id,
            linkedUserDataRequest: request._id,
            consideration: {
              amount: AVAILABLE_CASH * 100,
              currency: "GBP"
            }
          })
        );
      });

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not update the Mailchimp member", async () => {
        expect(MailchimpService.updateMember).not.toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the GDPR deletion request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion, request is processed after sell transaction has been settled and withdrawal has been created but pending", () => {
      const AVAILABLE_CASH = 10; // In GBP

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          providers: {
            wealthkernel: {
              status: "Active",
              id: faker.string.uuid()
            }
          }
        });
        request = await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await buildWithdrawalCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          consideration: {
            amount: AVAILABLE_CASH * 100,
            currency: "GBP"
          },
          linkedUserDataRequest: request.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          }
        });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedUserDataRequest: request.id,
          status: "Settled"
        });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not close the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not close the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not update the Mailchimp member", async () => {
        expect(MailchimpService.updateMember).not.toHaveBeenCalled();
      });

      it("should not update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not process the GDPR deletion request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion and request and the user has a settled restricted reward", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let reward: RewardDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        reward = await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          accepted: true,
          status: "Settled",
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5), // Reward is still restricted
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          }
        });

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        request = await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should create a revert reward transaction for the restricted reward", async () => {
        const revertRewardTransactions = await RevertRewardTransaction.find({ owner: user.id });

        expect(revertRewardTransactions.length).toBe(1);
        expect(revertRewardTransactions[0]).toEqual(
          expect.objectContaining({
            owner: user._id,
            portfolio: portfolio._id,
            linkedUserDataRequest: request._id,
            reward: reward._id
          })
        );
      });

      it("should not request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should delete the Mailchimp member", async () => {
        expect(MailchimpService.deleteMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are not closed)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion and request and the user has a non accepted restricted reward", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          accepted: false,
          status: "Pending",
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5) // Reward is still restricted
        });

        await buildReward({
          targetUser: user.id,
          asset: "commodities_gold",
          quantity: 1,
          status: "Pending",
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5) // Reward is still restricted
        });

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not create a revert reward transaction for the restricted reward", async () => {
        const revertRewardTransactions = await RevertRewardTransaction.find({ owner: user.id });

        expect(revertRewardTransactions.length).toBe(0);
      });

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should delete the Mailchimp member", async () => {
        expect(MailchimpService.deleteMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are not closed)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion and request and the user has a pending reward", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          accepted: true,
          status: "Pending",
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5), // Reward is still restricted
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Open"
              }
            }
          }
        });

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not create a revert reward transaction for the restricted reward", async () => {
        const revertRewardTransactions = await RevertRewardTransaction.find({ owner: user.id });

        expect(revertRewardTransactions.length).toBe(0);
      });

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should delete the Mailchimp member", async () => {
        expect(MailchimpService.deleteMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are not closed)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion and request and the user has a settled revert reward transaction for a reward that's now unrestricted", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let reward: RewardDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        reward = await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          accepted: true,
          status: "Settled",
          unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), 100), // Reward is now unrestricted
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          }
        });

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        request = await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await buildRevertRewardTransaction({
          owner: user.id,
          linkedUserDataRequest: request.id,
          reward: reward.id,
          status: "Settled"
        });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not create another revert reward transaction", async () => {
        const revertRewardTransactions = await RevertRewardTransaction.find({ owner: user.id });

        expect(revertRewardTransactions.length).toBe(1);
      });

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should delete the Mailchimp member", async () => {
        expect(MailchimpService.deleteMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are not closed)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion and request and the user has a settled revert reward transaction", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let reward: RewardDocument;
      let request: UserDataRequestDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );
        reward = await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          accepted: true,
          status: "Settled",
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5), // Reward is still restricted
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          }
        });

        await jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        request = await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await buildRevertRewardTransaction({
          owner: user.id,
          linkedUserDataRequest: request.id,
          reward: reward.id,
          status: "Settled"
        });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not create another revert reward transaction", async () => {
        const revertRewardTransactions = await RevertRewardTransaction.find({ owner: user.id });
        expect(revertRewardTransactions.length).toBe(1);
      });

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should delete the Mailchimp member", async () => {
        expect(MailchimpService.deleteMember).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not complete the disassociation request (because portfolio and account are not closed)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion, user has no cash/holdings and request is processed for the first time", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let participant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        participant = await buildParticipant({ email: user.email });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember").mockResolvedValue({
          status: "subscribed"
        } as any);
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest.spyOn(auth0ManagementClient.users, "delete");
        jest.spyOn(auth0ManagementClient.users, "get").mockImplementation(() => {
          return;
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should set the portfolio status as Closing", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);

        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              wealthkernel: expect.objectContaining({
                id: portfolio.providers.wealthkernel.id,
                status: "Closing"
              })
            })
          })
        );
      });

      it("should not yet request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should delete the Mailchimp member", async () => {
        expect(MailchimpService.deleteMember).toHaveBeenCalled();
      });

      it("should delete the Auth0 user", async () => {
        expect(auth0ManagementClient.users.delete).toHaveBeenCalled();
      });

      it("should update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(`deleted_${user.email}`);
      });

      it("should update the e-mail of the participant with the deleted prefix", async () => {
        const updatedParticipant: ParticipantDocument = await Participant.findById(participant.id);
        expect(updatedParticipant.email).toBe(`deleted_${user.email}`);
      });

      it("should send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
      });

      it("should not delete the user document", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser).not.toBeNull();
      });

      it("should not delete the participant document", async () => {
        const updatedParticipant = await Participant.findById(participant.id);
        expect(updatedParticipant).not.toBeNull();
      });

      it("should not complete the GDPR deletion request (because portfolio and account are closing)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion, e-mail has been disassociated and request is processed again", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser({ email: `deleted_${faker.internet.email()}` });
        await buildParticipant({ email: user.email });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember").mockImplementation(() => {
          throw Error("User with that e-mail not found");
        });
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Active"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Closing"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should request the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).toHaveBeenCalledTimes(1);
      });

      it("should set the portfolio status as Closing", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);

        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              wealthkernel: expect.objectContaining({
                id: portfolio.providers.wealthkernel.id,
                status: "Closing"
              })
            })
          })
        );
      });

      it("should not request the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not try to delete the Mailchimp member", async () => {
        expect(MailchimpService.retrieveMember).not.toHaveBeenCalled();
        expect(MailchimpService.deleteMember).not.toHaveBeenCalled();
      });

      it("should not re-update the e-mail of the user with the deleted prefix", async () => {
        const updatedUser: UserDocument = await User.findById(user.id);
        expect(updatedUser.email).toBe(user.email);
      });

      it("should not send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should not complete the GDPR deletion request (because portfolio and account are closing)", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Created");
      });
    });

    describe("when there is a pending user GDPR deletion and request is processed and portfolios & accounts are closed and in sync", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let account: AccountDocument;
      let participant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: 0 },
              value: { currency: "GBP", amount: 0 },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: new Date()
        });

        user = await buildUser({ email: `deleted_${faker.internet.email()}` });
        participant = await buildParticipant({ email: user.email });
        account = await buildAccount({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Closed" } }
        });
        portfolio = await buildPortfolio(
          {
            owner: user.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL,
            account: account.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Closed" } }
          },
          false
        );

        jest.spyOn(MailchimpService, "retrieveMember");
        jest.spyOn(MailchimpService, "deleteMember").mockResolvedValue();
        jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolio").mockResolvedValue({
          id: portfolio.providers.wealthkernel.id,
          status: "Closed"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "retrieveAccount").mockResolvedValue({
          id: account.providers.wealthkernel.id,
          status: "Closed"
        } as any);
        jest.spyOn(WealthkernelService.UKInstance, "closePortfolio");
        jest.spyOn(WealthkernelService.UKInstance, "closeAccount");
        jest.spyOn(auth0ManagementClient.users, "delete");
        jest.spyOn(auth0ManagementClient.users, "get").mockImplementation(() => {
          throw { statusCode: 404 };
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveLatestValuationByPortfolio")
          .mockResolvedValue(wealthkernelValuation);

        await buildUserDataRequest({ owner: user.id, requestType: "gdpr-delete" });

        await UserDataRequestService.processAllUserGDPRDeletions();
      });
      afterAll(async () => await clearDb());

      it("should not request (again) the closing of the wealthkernel portfolio", async () => {
        expect(WealthkernelService.UKInstance.closePortfolio).not.toHaveBeenCalled();
      });

      it("should not request (again) the closing of the wealthkernel account", async () => {
        expect(WealthkernelService.UKInstance.closeAccount).not.toHaveBeenCalled();
      });

      it("should not delete (again) the Mailchimp member", async () => {
        expect(MailchimpService.deleteMember).not.toHaveBeenCalled();
      });

      it("should not delete (again) the Auth0 user", async () => {
        expect(auth0ManagementClient.users.delete).not.toHaveBeenCalled();
      });

      it("should not send the delete success email to user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(0);
      });

      it("should delete the user document", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser).toBeNull();
      });

      it("should delete the participant document", async () => {
        const updatedParticipant = await Participant.findById(participant.id);
        expect(updatedParticipant).toBeNull();
      });

      it("should complete the user data request", async () => {
        const updatedRequest: UserDataRequestDocument = await UserDataRequest.findOne({
          owner: user.id
        });
        expect(updatedRequest.status).toBe("Completed");
      });
    });
  });
});
