import Decimal from "decimal.js";
import { faker } from "@faker-js/faker";
import mongoose from "mongoose";
import { DateTime } from "luxon";
import {
  cashbacksConfig,
  currenciesConfig,
  entitiesConfig,
  giftsConfig,
  investmentUniverseConfig,
  fees,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import {
  GiftedHoldingType,
  HoldingsType,
  InitialHoldingsAllocationType,
  Portfolio,
  PortfolioDocument,
  PortfolioModeEnum,
  SavingType
} from "../../models/Portfolio";
import { RewardDocument } from "../../models/Reward";
import {
  AssetTransaction,
  AssetTransactionDocument,
  CashbackTransaction,
  ChargeTransactionDocument,
  DepositCashTransactionDocument,
  DepositMethodEnum,
  DividendTransactionDocument,
  RebalanceTransaction,
  RebalanceTransactionDocument,
  SavingsDividendTransaction,
  SavingsDividendTransactionDocument,
  SavingsTopupTransaction,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransaction,
  SavingsWithdrawalTransactionDocument,
  Transaction
} from "../../models/Transaction";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { CurrencyEnum, WealthkernelService } from "../../external-services/wealthkernelService";
import { Order, OrderDocument, OrderSubmissionIntentEnum } from "../../models/Order";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAccount,
  buildAssetTransaction,
  buildChargeTransaction,
  buildDailyPortfolioTicker,
  buildDepositCashTransaction,
  buildDividendTransaction,
  buildHoldingDTO,
  buildIntraDayAssetTicker,
  buildIntraDayPortfolioTicker,
  buildInvestmentProduct,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildReward,
  buildSavingsDividend,
  buildSavingsTopup,
  buildSavingsTopUpAutomation,
  buildSavingsWithdrawal,
  buildStockSplitCorporateEvent,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import PortfolioService, { PortfolioAllocationMethodEnum } from "../portfolioService";
import { BadRequestError, InternalServerError } from "../../models/ApiErrors";
import DateUtil from "../../utils/dateUtil";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { TransactionService } from "../transactionService";
import { getTotalFeeAmount } from "../../utils/feesUtil";
import { ProviderEnum } from "../../configs/providersConfig";
import { SubscriptionDocument } from "../../models/Subscription";
import { RedisClientService } from "../../loaders/redis";
import { TenorEnum } from "../../configs/durationConfig";
import { DailyPortfolioTicker } from "../../models/DailyTicker";
import { IntraDayPortfolioTicker } from "../../models/IntraDayTicker";
import { SavingsTopUpAutomationDocument } from "../../models/Automation";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { MINIMUM_AMOUNT_FOR_CASHBACK } = cashbacksConfig;
const { RESTRICTED_HOLDING_PERIOD_DAYS } = giftsConfig;
const { REAL_TIME_ETF_EXECUTION_FEES } = fees;

const ZERO_GBP_FEES = {
  fx: { currency: CurrencyEnum.GBP, amount: 0 },
  commission: { currency: CurrencyEnum.GBP, amount: 0 },
  executionSpread: { currency: CurrencyEnum.GBP, amount: 0 },
  realtimeExecution: { currency: CurrencyEnum.GBP, amount: 0 }
};

describe("PortfolioService", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("PortfolioService"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("buyAssetsForPortfolio", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    const ORDER_AMOUNT = 50;
    const AVAILABLE_CASH = 100;
    const SETTLED_CASH = 90;
    const WK_PORTFOLIO_ID = "WK_PORTFOLIO_ID";
    const ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
      "equities_china",
      "equities_eu",
      "equities_us_biotech",
      "equities_us",
      "equities_global"
    ];
    const ASSET_COMMON_IDS_INCLUDING_USD_TRADED: investmentUniverseConfig.AssetType[] = [
      "equities_uk",
      "government_bonds_us"
    ];

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });
      await buildSubscription({ owner: user.id });
      // The user has £10 of each holding.
      const holdings = await Promise.all(
        ASSET_COMMON_IDS.map((assetId) =>
          buildHoldingDTO(true, assetId, 1, {
            price: 10
          })
        )
      );
      portfolio = await buildPortfolio({
        owner: user.id,
        cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: SETTLED_CASH } },
        providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
        mode: PortfolioModeEnum.REAL,
        holdings
      });
    });

    describe("when the target allocation is empty and target allocation method is selected", () => {
      let portfolioNoTarget: PortfolioDocument;

      beforeEach(async () => {
        jest.spyOn(AssetTransaction.prototype, "save");
        jest.spyOn(Order.prototype, "save");
        jest.spyOn(PortfolioService, "buyAssetsForPortfolio");

        const userNoTarget = await buildUser({ portfolioConversionStatus: "notStarted" });
        portfolioNoTarget = await buildPortfolio({
          owner: userNoTarget.id,
          holdings: [],
          initialHoldingsAllocation: []
        });
      });

      it("should skip the process", async () => {
        await expect(
          async () =>
            await PortfolioService.buyAssetsForPortfolio(portfolioNoTarget, ORDER_AMOUNT, {
              allocationMethod: PortfolioAllocationMethodEnum.TARGET_ALLOCATION,
              executeEtfOrdersInRealtime: false
            })
        ).rejects.toThrow(
          new BadRequestError(
            `Target allocation method was selected but portfolio ${portfolioNoTarget.id} has no target allocation set`
          )
        );
        // create no asset transaction
        expect(AssetTransaction.prototype.save).not.toHaveBeenCalled();

        // do not create order db documents
        expect(AssetTransaction.prototype.save).not.toHaveBeenCalled();

        // do not update portfolio cash
        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolioNoTarget._id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP?.available).toBe(0);
        expect(updatedPortfolio.cash.GBP?.settled).toBe(0);
      });
    });

    describe("when the holdings do not contain ticker information for the corresponding investment products", () => {
      let portfolioNoTicker: PortfolioDocument;

      beforeEach(async () => {
        jest.spyOn(AssetTransaction.prototype, "save");
        jest.spyOn(Order.prototype, "save");
        jest.spyOn(PortfolioService, "buyAssetsForPortfolio");

        const { holdings } = portfolio;
        const holdingsNoTicker = holdings.map(({ assetCommonId, quantity }) => ({ assetCommonId, quantity }));
        portfolioNoTicker = await buildPortfolio({
          holdings: holdingsNoTicker
        });
        portfolioNoTicker.holdings.forEach((holding) => {
          expect(holding.asset?.currentTicker).toBeUndefined();
        });
      });

      it("should skip the process", async () => {
        await expect(
          async () =>
            await PortfolioService.buyAssetsForPortfolio(portfolioNoTicker, ORDER_AMOUNT, {
              allocationMethod: PortfolioAllocationMethodEnum.HOLDINGS,
              executeEtfOrdersInRealtime: false
            })
        ).rejects.toThrow(new BadRequestError("No assets to buy or no linked investment products"));
        // create no asset transaction
        expect(AssetTransaction.prototype.save).not.toHaveBeenCalled();

        // do not create order db documents
        expect(AssetTransaction.prototype.save).not.toHaveBeenCalled();

        // do not update portfolio cash
        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolioNoTicker._id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toBe(portfolioNoTicker.cash.GBP.available);
        expect(updatedPortfolio.cash.GBP.settled).toBe(portfolioNoTicker.cash.GBP.settled);
      });
    });

    describe("when the holdings contain all the information for the linked investment products and do not include USD traded assets", () => {
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        portfolio = await portfolio.populate([
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ]);

        assetTransaction = await PortfolioService.buyAssetsForPortfolio(portfolio, ORDER_AMOUNT, {
          allocationMethod: PortfolioAllocationMethodEnum.HOLDINGS,
          executeEtfOrdersInRealtime: false
        });
      });

      it("should create an asset transaction with the requested order amount", async () => {
        const assetTransactions = await AssetTransaction.find({ portfolio: portfolio.id });
        expect(assetTransactions.length).toBe(1);
        expect(assetTransactions[0]).toEqual(
          expect.objectContaining({
            consideration: {
              amount: ORDER_AMOUNT * 100,
              currency: "GBP"
            },
            owner: user._id,
            portfolio: portfolio._id,
            portfolioTransactionCategory: "buy",
            fees: expect.objectContaining({
              fx: {
                amount: 0,
                currency: "GBP"
              },
              commission: {
                amount: 0,
                currency: "GBP"
              },
              executionSpread: {
                amount: 0, // 0% of £50
                currency: "GBP"
              }
            })
          })
        );
      });

      it("should store the corresponding order documents", async () => {
        const orders = await Order.find({});
        // Created order isins should match the isins of the specified assets.
        // Comparing sets to ignore array element order
        const orderIsins = orders.map(({ isin }) => isin);
        expect(new Set(orderIsins)).toEqual(
          new Set(ASSET_COMMON_IDS.map((assetId) => ASSET_CONFIG[assetId].isin))
        );

        // Total order amount should match the specified order amount
        const ordersTotalAmount = orders
          .map((order) => new Decimal(order.consideration.amount))
          .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
          .toNumber();
        expect(ordersTotalAmount).toBe(
          Decimal.sub(ORDER_AMOUNT, getTotalFeeAmount(assetTransaction.fees)).mul(100).toNumber()
        );

        // All orders should be "Buy" orders and be linked to the created asset transaction
        const createdAssetTransaction = await AssetTransaction.findOne({ status: "Pending" });
        orders.forEach((order) => {
          expect(order.side).toEqual("Buy");
          expect(order.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
          expect(order.transaction.toString()).toEqual(createdAssetTransaction._id.toString());
        });
      });

      it("should update the portfolio available & settled cash", async () => {
        const orders = await Order.find({});
        const finalOrderAmount = orders
          .map((order) => new Decimal(order.consideration.amount))
          .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
          .div(100)
          .add(getTotalFeeAmount(assetTransaction.fees));
        const updatedPortfolio = await Portfolio.findOne({ _id: portfolio._id });
        expect(updatedPortfolio.cash.GBP.available).toBe(
          finalOrderAmount.mul(-1).plus(portfolio.cash.GBP.available).toNumber()
        );
        expect(updatedPortfolio.cash.GBP.settled).toBe(
          finalOrderAmount.mul(-1).plus(portfolio.cash.GBP.settled).toNumber()
        );
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "portfolio",
            frequency: "one-off",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when the holdings contain all the information for the linked investment products and the transaction should get a cashback", () => {
      let portfolio: PortfolioDocument;
      let user: UserDocument;

      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        percentage: number;
        price: number;
      }[] = [
        {
          assetId: "equities_jp",
          percentage: 100,
          price: 100
        }
      ];

      beforeEach(async () => {
        await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, price }) =>
            buildInvestmentProduct(true, { assetId, listed: true }, { price })
          )
        );

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });
        await portfolio.populate({
          path: "initialHoldingsAllocation.asset",
          populate: {
            path: "currentTicker"
          }
        });

        await buildSubscription({ owner: user.id, price: "paid_low_monthly" });

        await PortfolioService.buyAssetsForPortfolio(portfolio, ORDER_AMOUNT, {
          allocationMethod: PortfolioAllocationMethodEnum.TARGET_ALLOCATION,
          executeEtfOrdersInRealtime: false
        });
      });

      it("should create a cashback transaction", async () => {
        const assetTransaction = await AssetTransaction.findOne({ portfolio: portfolio.id }).populate("cashback");

        const cashback = await CashbackTransaction.findOne({ linkedAssetTransaction: assetTransaction.id });
        expect(cashback).toEqual(
          expect.objectContaining({
            deposit: { activeProviders: [ProviderEnum.WEALTHKERNEL] },
            consideration: {
              amount: 13, // 0.25% of £50
              currency: "GBP"
            }
          })
        );

        expect(assetTransaction.cashback).toEqual(
          expect.objectContaining({
            _id: cashback._id
          })
        );
      });
    });

    describe("when the holdings contain all the information for the linked investment products and include USD traded assets", () => {
      let portfolioWithUsdTradedAssets: PortfolioDocument;
      let userWithUsdTradedAssets: UserDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        userWithUsdTradedAssets = await buildUser({ portfolioConversionStatus: "completed" });
        await buildSubscription({ owner: userWithUsdTradedAssets.id });
        portfolioWithUsdTradedAssets = await buildPortfolio({
          owner: userWithUsdTradedAssets.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: SETTLED_CASH } },
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_INCLUDING_USD_TRADED.map((assetId) =>
              buildHoldingDTO(true, assetId, 1, {
                price: 25
              })
            )
          )
        });

        await portfolioWithUsdTradedAssets.populate([
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ]);

        assetTransaction = await PortfolioService.buyAssetsForPortfolio(
          portfolioWithUsdTradedAssets,
          ORDER_AMOUNT,
          {
            allocationMethod: PortfolioAllocationMethodEnum.HOLDINGS,
            executeEtfOrdersInRealtime: false
          }
        );
      });

      it("should create an asset transaction with the requested order amount", async () => {
        const assetTransactions = await AssetTransaction.find({ "consideration.amount": ORDER_AMOUNT * 100 });
        expect(assetTransactions.length).toBe(1);
        expect(assetTransactions[0]).toEqual(
          expect.objectContaining({
            consideration: {
              amount: ORDER_AMOUNT * 100,
              currency: "GBP"
            },
            owner: userWithUsdTradedAssets._id,
            portfolio: portfolioWithUsdTradedAssets._id,
            portfolioTransactionCategory: "buy",
            fees: expect.objectContaining({
              fx: {
                amount: 0.19, // 0.75% of £25 (USD order amount), rounded to 2 decimal places
                currency: "GBP"
              },
              commission: {
                amount: 0,
                currency: "GBP"
              },
              executionSpread: {
                amount: 0, // 0% of £50 (total order amount), rounded to 2 decimal places
                currency: "GBP"
              }
            })
          })
        );
      });

      it("should store the corresponding documents in db linked with the transaction", async () => {
        const orders = await Order.find({});
        expect(orders.length).toEqual(2);

        const createdAssetTransaction = (await AssetTransaction.findOne({
          status: "Pending"
        })) as AssetTransactionDocument;
        expect(createdAssetTransaction).not.toBeNull();

        expect(orders).toContainEqual(
          expect.objectContaining({
            isin: ASSET_CONFIG[ASSET_COMMON_IDS_INCLUDING_USD_TRADED[0]].isin,
            side: "Buy",
            transaction: createdAssetTransaction._id,
            consideration: expect.objectContaining({
              amount: expect.any(Number),
              currency: "GBP"
            })
          })
        );
        expect(orders).toContainEqual(
          expect.objectContaining({
            isin: ASSET_CONFIG[ASSET_COMMON_IDS_INCLUDING_USD_TRADED[1]].isin, // USD traded asset
            side: "Buy",
            transaction: createdAssetTransaction._id,
            consideration: expect.objectContaining({
              amount: expect.any(Number),
              currency: "GBP",
              originalAmount: Decimal.add(
                (
                  orders.find(
                    (order) => order.isin === ASSET_CONFIG[ASSET_COMMON_IDS_INCLUDING_USD_TRADED[1]].isin
                  ) as OrderDocument
                ).consideration?.amount as number,
                Decimal.mul(0.19, 100) // £0.19 from FX fee + £0 from execution spread
              ).toNumber()
            })
          })
        );
      });

      it("should update the portfolio available & settled cash", async () => {
        const orders = await Order.find({});
        // The final order amount is the total of all orders + the FX fee.
        const finalTransactionAmount = orders
          .map((order) => new Decimal(order.consideration.amount))
          .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
          .div(100)
          .add(getTotalFeeAmount(assetTransaction.fees));
        const updatedPortfolio = await Portfolio.findOne({ _id: portfolioWithUsdTradedAssets._id });
        expect(updatedPortfolio.cash.GBP.available).toBe(
          finalTransactionAmount.mul(-1).plus(AVAILABLE_CASH).toNumber()
        );
        expect(updatedPortfolio.cash.GBP.settled).toBe(
          finalTransactionAmount.mul(-1).plus(SETTLED_CASH).toNumber()
        );
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: userWithUsdTradedAssets.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "portfolio",
            frequency: "one-off",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            fxFees: 0.19,
            commissionFees: 0,
            executionSpreadFees: 0
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: userWithUsdTradedAssets.email })
        );
      });
    });
  });

  describe("sellAssetsForPortfolio", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    const ORDER_AMOUNT = 50;
    const AVAILABLE_CASH = 100;
    const WK_PORTFOLIO_ID = "WK_PORTFOLIO_ID";
    const ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
      "equities_china",
      "equities_eu",
      "equities_global"
    ];

    describe("when the holdings contain all the information for the linked investment products", () => {
      describe("and all holdings are eligible", () => {
        beforeEach(async () => {
          user = await buildUser({ portfolioConversionStatus: "completed" });
          await buildSubscription({ owner: user.id });
          const holdings = await Promise.all(
            ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
          );
          portfolio = await buildPortfolio({
            owner: user.id,
            cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
            providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
            mode: PortfolioModeEnum.REAL,
            holdings
          });

          await portfolio.populate([
            { path: "owner" },
            {
              path: "holdings.asset",
              populate: {
                path: "currentTicker"
              }
            }
          ]);
          await PortfolioService.sellAssetsForPortfolio(portfolio, ORDER_AMOUNT);
        });

        it("should store the corresponding documents in db linked with the transaction", async () => {
          const orders = await Order.find({});
          // Created order isins should match the isins of the specified assets.
          // Comparing sets to ignore array element order
          const orderIsins = orders.map(({ isin }) => isin);
          expect(new Set(orderIsins)).toEqual(
            new Set(ASSET_COMMON_IDS.map((assetId) => ASSET_CONFIG[assetId].isin))
          );

          const holdingsDict: Record<string, HoldingsType> = {};
          portfolio.holdings.forEach(
            (holding) => (holdingsDict[ASSET_CONFIG[holding.assetCommonId].isin] = holding)
          );
          // because 3 eligible assets exist with quantity 10, price 50 => 3*(10*50) = 1500
          const expectedHoldingsValue = 1500;

          // All orders should be "Sell" orders and be linked to the created asset transaction
          const createdAssetTransaction = await AssetTransaction.findOne({ status: "Pending" });
          expect(orders.length).toEqual(ASSET_COMMON_IDS.length);
          orders.forEach((order) => {
            const quantity = new Decimal(holdingsDict[order.isin].quantity)
              .mul(ORDER_AMOUNT)
              .div(expectedHoldingsValue)
              .toDecimalPlaces(4);
            expect(order.side).toEqual("Sell");
            expect(order.quantity).toEqual(quantity.toNumber());
            expect(order.transaction.toString()).toEqual(createdAssetTransaction._id.toString());
            expect(order.consideration.currency).toEqual("GBP");
          });
        });

        it("should not update the portfolio available & settled cash", async () => {
          const updatedPortfolio = await Portfolio.findOne({ _id: portfolio._id });
          expect(updatedPortfolio.cash.GBP.available).toBe(portfolio.cash.GBP.available);
          expect(updatedPortfolio.cash.GBP.settled).toBe(portfolio.cash.GBP.settled);
        });

        it("should fire an 'investmentCreation' event", () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.investmentCreation.eventId,
            expect.objectContaining({ email: user.email }),
            expect.objectContaining({
              isFirst: true,
              side: "sell",
              category: "portfolio",
              frequency: "one-off",
              amount: ORDER_AMOUNT,
              currency: "GBP",
              fxFees: 0,
              commissionFees: 0
            })
          );
        });

        it("should fire an 'firstInvestmentCreation' event", () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.firstInvestmentCreation.eventId,
            expect.objectContaining({ email: user.email })
          );
        });
      });

      describe("and all holdings are eligible and active rewards exist for some holdings", () => {
        const rewardsRestrictedQuantities = {
          [ASSET_CONFIG[ASSET_COMMON_IDS[0]].isin]: 5,
          [ASSET_CONFIG[ASSET_COMMON_IDS[1]].isin]: 5
        };
        beforeEach(async () => {
          user = await buildUser({ portfolioConversionStatus: "completed" });
          await buildSubscription({ owner: user.id });
          const holdings = await Promise.all(
            ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
          );
          portfolio = await buildPortfolio({
            owner: user.id,
            cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
            providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
            mode: PortfolioModeEnum.REAL,
            holdings
          });

          await Promise.all([
            buildReward({
              targetUser: user.id,
              asset: ASSET_COMMON_IDS[0],
              quantity: 2.5,
              updatedAt: new Date(),
              status: "Settled",
              accepted: true,
              unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), -30),
              deposit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Settled"
                  }
                }
              },
              order: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Matched"
                  }
                }
              }
            }),
            buildReward({
              targetUser: user.id,
              asset: ASSET_COMMON_IDS[0],
              quantity: 2.5,
              updatedAt: new Date(),
              status: "Settled",
              accepted: true,
              unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), -30),
              deposit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Settled"
                  }
                }
              },
              order: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Matched"
                  }
                }
              }
            }),
            buildReward({
              targetUser: user.id,
              asset: ASSET_COMMON_IDS[1],
              quantity: 5,
              updatedAt: new Date(),
              status: "Settled",
              accepted: true,
              unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), -30),
              deposit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Settled"
                  }
                }
              },
              order: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Matched"
                  }
                }
              }
            })
          ]);

          await portfolio.populate([
            { path: "owner" },
            {
              path: "holdings.asset",
              populate: {
                path: "currentTicker"
              }
            }
          ]);
          await PortfolioService.sellAssetsForPortfolio(portfolio, ORDER_AMOUNT);
        });

        it("should store the corresponding documents in db linked with the transaction", async () => {
          const orders = await Order.find({});
          // Created order isins should match the isins of the specified assets.
          // Comparing sets to ignore array element order
          const orderIsins = orders.map(({ isin }) => isin);
          expect(new Set(orderIsins)).toEqual(
            new Set(ASSET_COMMON_IDS.map((assetId) => ASSET_CONFIG[assetId].isin))
          );

          const holdingsDict: Record<string, HoldingsType> = {};
          portfolio.holdings.forEach(
            (holding) => (holdingsDict[ASSET_CONFIG[holding.assetCommonId].isin] = holding)
          );
          // because 3 eligible assets exist with quantity 10, price 50
          // minus 3 eligible rewards for asset[0] 2.5 + 2.5 = 5, for asset[1] 5
          // 10*50 + (10-5)*50 + (10-5)*50 = 1000
          const expectedHoldingsValue = 1000;

          // All orders should be "Sell" orders and be linked to the created asset transaction
          const createdAssetTransaction = await AssetTransaction.findOne({ status: "Pending" });
          expect(orders.length).toEqual(ASSET_COMMON_IDS.length);
          orders.forEach((order) => {
            const quantity = new Decimal(holdingsDict[order.isin].quantity)
              // reduce restricted reward quantity
              .minus(rewardsRestrictedQuantities[order.isin] ?? 0)
              .mul(ORDER_AMOUNT)
              .div(expectedHoldingsValue)
              .toDecimalPlaces(4);
            expect(order.side).toEqual("Sell");
            expect(order.quantity).toEqual(quantity.toNumber());
            expect(order.transaction.toString()).toEqual(createdAssetTransaction._id.toString());
          });
        });

        it("should not update the portfolio available & settled cash", async () => {
          const updatedPortfolio = await Portfolio.findOne({ _id: portfolio._id });
          expect(updatedPortfolio.cash.GBP.available).toBe(portfolio.cash.GBP.available);
          expect(updatedPortfolio.cash.GBP.settled).toBe(portfolio.cash.GBP.settled);
        });

        it("should fire an 'investmentCreation' event", () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.investmentCreation.eventId,
            expect.objectContaining({ email: user.email }),
            expect.objectContaining({
              isFirst: false,
              side: "sell",
              category: "portfolio",
              frequency: "one-off",
              amount: ORDER_AMOUNT,
              currency: "GBP",
              fxFees: 0,
              commissionFees: 0
            })
          );
        });
      });

      describe("and half portfolio is being sold when all holdings are eligible and pending orders exist", () => {
        let pendingTransaction: AssetTransactionDocument;
        const pendingOrdersDict: { [key in string]: OrderDocument[] } = {};
        const HALF_PORTFOLIO_ORDER_AMOUNT = 450; // 10 quantity * 50 price + ((10 -2 )quantity considering pending order * 50 price) / 2 = 450
        const ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["equities_eu", "equities_global"];

        beforeEach(async () => {
          user = await buildUser({ portfolioConversionStatus: "completed" });
          await buildSubscription({ owner: user.id });
          const holdings = await Promise.all(
            ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
          );
          portfolio = await buildPortfolio({
            owner: user.id,
            cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
            providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
            mode: PortfolioModeEnum.REAL,
            holdings
          });

          pendingTransaction = await buildAssetTransaction({ owner: user.id, status: "Pending" });
          pendingTransaction.orders = [
            await buildOrder({
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
              },
              side: "Sell",
              transaction: pendingTransaction.id,
              isin: ASSET_CONFIG[ASSET_COMMON_IDS[1]].isin,
              quantity: 2
            })
          ];
          pendingOrdersDict[ASSET_CONFIG[ASSET_COMMON_IDS[1]].isin] = pendingTransaction.orders;
          await pendingTransaction.save();
          await portfolio.populate([
            { path: "owner" },
            {
              path: "holdings.asset",
              populate: {
                path: "currentTicker"
              }
            }
          ]);
          await PortfolioService.sellAssetsForPortfolio(portfolio, HALF_PORTFOLIO_ORDER_AMOUNT);
        });

        it("should store the corresponding documents in db linked with the transaction", async () => {
          const holdingsDict: Record<string, HoldingsType> = {};
          portfolio.holdings.forEach(
            (holding) => (holdingsDict[ASSET_CONFIG[holding.assetCommonId].isin] = holding)
          );
          // because 3 eligible assets exist with quantity 10, price 50 => 2*(10*50) = 1000
          const expectedHoldingsValue = HALF_PORTFOLIO_ORDER_AMOUNT * 2;

          // All orders should be "Sell" orders and be linked to the created asset transaction
          // At this point should exist 2 pending transactions in db we want to find the one created from sellAssetsForPortfolio
          const createdAssetTransaction = (await AssetTransaction.find({ status: "Pending" })).find(
            (transaction) => transaction.id != pendingTransaction.id
          );
          const orders = await Order.find({ transaction: createdAssetTransaction._id });
          // Created order isins should match the isins of the specified assets.
          // Comparing sets to ignore array element order
          const orderIsins = orders.map(({ isin }) => isin);
          expect(new Set(orderIsins)).toEqual(
            new Set(ASSET_COMMON_IDS.map((assetId) => ASSET_CONFIG[assetId].isin))
          );

          expect(orders.length).toEqual(ASSET_COMMON_IDS.length);

          orders.forEach((order) => {
            const quantity = new Decimal(holdingsDict[order.isin].quantity)
              .minus(
                pendingOrdersDict[order.isin]
                  ?.map(({ quantity }) => new Decimal(quantity))
                  .reduce((sum, quantity) => sum.plus(quantity), new Decimal(0)) ?? 0
              )
              .mul(HALF_PORTFOLIO_ORDER_AMOUNT)
              .div(expectedHoldingsValue)
              .toDecimalPlaces(4);

            expect(order.side).toEqual("Sell");
            expect(order.quantity).toEqual(quantity.toNumber());
            expect(order.transaction.toString()).toEqual(createdAssetTransaction._id.toString());
          });
        });

        it("should not update the portfolio available & settled cash", async () => {
          const updatedPortfolio = await Portfolio.findOne({ _id: portfolio._id });
          expect(updatedPortfolio.cash.GBP.available).toBe(portfolio.cash.GBP.available);
          expect(updatedPortfolio.cash.GBP.settled).toBe(portfolio.cash.GBP.settled);
        });

        it("should have two orders created for equities_global at the end of the method execution", async () => {
          const orders = await Order.find({ isin: ASSET_CONFIG[ASSET_COMMON_IDS[1]].isin });
          expect(orders.length).toEqual(2);
          const totalQuantity = orders
            .map(({ quantity }) => new Decimal(quantity))
            .reduce((sum, quantity) => sum.plus(quantity), new Decimal(0))
            .toNumber();
          expect(totalQuantity).toEqual(6); // 2 pending order + 4 (10 - 2/2) created order
        });

        it("should fire an 'investmentCreation' event", () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.investmentCreation.eventId,
            expect.objectContaining({ email: user.email }),
            expect.objectContaining({
              isFirst: false,
              side: "sell",
              category: "portfolio",
              frequency: "one-off",
              amount: HALF_PORTFOLIO_ORDER_AMOUNT,
              currency: "GBP",
              fxFees: 0,
              commissionFees: 0
            })
          );
        });
      });

      describe("and a holding is not eligible because of low quantity", () => {
        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "equities_china",
          "equities_global"
        ];
        const NOT_ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["equities_eu"];

        beforeEach(async () => {
          user = await buildUser({ portfolioConversionStatus: "completed" });
          await buildSubscription({ owner: user.id });
          const holdings = await Promise.all([
            ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 })),
            ...NOT_ELIGIBLE_ASSET_COMMON_IDS.map((assetId) =>
              buildHoldingDTO(true, assetId, 0.00005, { price: 50 })
            )
          ]);
          portfolio = await buildPortfolio({
            owner: user.id,
            cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
            providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
            mode: PortfolioModeEnum.REAL,
            holdings
          });

          await portfolio.populate([
            { path: "owner" },
            {
              path: "holdings.asset",
              populate: {
                path: "currentTicker"
              }
            }
          ]);
          await PortfolioService.sellAssetsForPortfolio(portfolio, ORDER_AMOUNT);
        });

        it("should store the corresponding documents in db linked with the transaction", async () => {
          const orders = await Order.find({});
          // Created order isins should match the isins of the specified assets.
          // Comparing sets to ignore array element order
          const orderIsins = orders.map(({ isin }) => isin);
          expect(new Set(orderIsins)).toEqual(
            new Set(ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => ASSET_CONFIG[assetId].isin))
          );

          const holdingsDict: Record<string, HoldingsType> = {};
          portfolio.holdings.forEach(
            (holding) => (holdingsDict[ASSET_CONFIG[holding.assetCommonId].isin] = holding)
          );
          // because 2 eligible assets exist with quantity 10, price 50 => 2*(10*50) = 1000
          const expectedHoldingsValue = 1000;

          // All orders should be "Sell" orders and be linked to the created asset transaction
          const createdAssetTransaction = await AssetTransaction.findOne({ status: "Pending" });
          expect(orders.length).toEqual(ELIGIBLE_ASSET_COMMON_IDS.length);
          orders.forEach((order) => {
            const quantity = parseFloat(
              (holdingsDict[order.isin].quantity * (ORDER_AMOUNT / expectedHoldingsValue)).toFixed(4)
            );
            expect(order.side).toEqual("Sell");
            expect(order.quantity).toEqual(quantity);
            expect(order.transaction.toString()).toEqual(createdAssetTransaction._id.toString());
          });
        });

        it("should not update the portfolio available & settled cash", async () => {
          const updatedPortfolio = await Portfolio.findOne({ _id: portfolio._id });
          expect(updatedPortfolio.cash.GBP.available).toBe(portfolio.cash.GBP.available);
          expect(updatedPortfolio.cash.GBP.settled).toBe(portfolio.cash.GBP.settled);
        });

        it("should fire an 'investmentCreation' event", () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.investmentCreation.eventId,
            expect.objectContaining({ email: user.email }),
            expect.objectContaining({
              isFirst: true,
              side: "sell",
              category: "portfolio",
              frequency: "one-off",
              amount: ORDER_AMOUNT,
              currency: "GBP",
              fxFees: 0,
              commissionFees: 0
            })
          );
        });

        it("should fire an 'firstInvestmentCreation' event", () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.firstInvestmentCreation.eventId,
            expect.objectContaining({ email: user.email })
          );
        });
      });

      describe("and a holding is not eligible because of low amount", () => {
        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "equities_china",
          "equities_global"
        ];
        const NOT_ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["equities_eu"];
        const ORDER_AMOUNT = 1;
        let holdingsValue: number;
        beforeEach(async () => {
          user = await buildUser({ portfolioConversionStatus: "completed" });
          await buildSubscription({ owner: user.id });
          // Total order amount is £1 for a portfolio of value £1.
          // Orders that would be created if we had no limits would be £0.45, £0.45 and £0.1, but since £0.1 is
          // below our MIN_ALLOWED_ASSET_INVESTMENT, we don't create it.
          const holdings = await Promise.all([
            ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 1, { price: 0.45 })),
            ...NOT_ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 1, { price: 0.1 }))
          ]);
          holdingsValue = holdings
            .map(({ quantity, asset }) => Decimal.mul(quantity, asset.currentTicker.getPrice("GBP")))
            .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
            .toNumber();

          portfolio = await buildPortfolio({
            owner: user.id,
            cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
            providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
            mode: PortfolioModeEnum.REAL,
            holdings
          });

          await portfolio.populate([
            { path: "owner" },
            {
              path: "holdings.asset",
              populate: {
                path: "currentTicker"
              }
            }
          ]);
          await PortfolioService.sellAssetsForPortfolio(portfolio, ORDER_AMOUNT);
        });

        it("should store the corresponding documents in db linked with the transaction", async () => {
          const orders = await Order.find({});
          // Created order isins should match the isins of the specified assets.
          // Comparing sets to ignore array element order
          const orderIsins = orders.map(({ isin }) => isin);
          expect(new Set(orderIsins)).toEqual(
            new Set(ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => ASSET_CONFIG[assetId].isin))
          );

          const holdingsDict: Record<string, HoldingsType> = {};
          portfolio.holdings.forEach(
            (holding) => (holdingsDict[ASSET_CONFIG[holding.assetCommonId].isin] = holding)
          );

          // All orders should be "Sell" orders and be linked to the created asset transaction
          const createdAssetTransaction = await AssetTransaction.findOne({ status: "Pending" });
          expect(orders.length).toEqual(ELIGIBLE_ASSET_COMMON_IDS.length);
          orders.forEach((order) => {
            const quantity = parseFloat(
              Decimal.mul(holdingsDict[order.isin].quantity, Decimal.div(ORDER_AMOUNT, holdingsValue)).toFixed(4)
            );
            expect(order.side).toEqual("Sell");
            expect(order.quantity).toEqual(quantity);
            expect(order.transaction.toString()).toEqual(createdAssetTransaction._id.toString());
          });
        });

        it("should not update the portfolio available & settled cash", async () => {
          const updatedPortfolio = await Portfolio.findOne({ _id: portfolio._id });
          expect(updatedPortfolio.cash.GBP.available).toBe(portfolio.cash.GBP.available);
          expect(updatedPortfolio.cash.GBP.settled).toBe(portfolio.cash.GBP.settled);
        });

        it("should fire an 'investmentCreation' event", () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.investmentCreation.eventId,
            expect.objectContaining({ email: user.email }),
            expect.objectContaining({
              isFirst: true,
              side: "sell",
              category: "portfolio",
              frequency: "one-off",
              amount: ORDER_AMOUNT,
              currency: "GBP",
              fxFees: 0,
              commissionFees: 0
            })
          );
        });

        it("should fire an 'firstInvestmentCreation' event", () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.firstInvestmentCreation.eventId,
            expect.objectContaining({ email: user.email })
          );
        });
      });
    });
  });

  describe("createBrokeragePortfolio", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    const wealthkernelPortfolioId = faker.string.uuid();

    beforeEach(async () => {
      jest.spyOn(eventEmitter, "emit");
      user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        providers: { wealthkernel: { id: faker.string.uuid() } },
        portfolioConversionStatus: "completed",
        submittedRequiredInfoAt: new Date(),
        isManuallyKycPassed: false
      });

      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [],
        account: await buildAccount({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          }
        })
      });

      await user.populate("portfolios bankAccounts");

      WealthkernelService.UKInstance.createPortfolio = jest.fn((): any => {
        return { id: wealthkernelPortfolioId };
      });

      WealthkernelService.UKInstance.retrievePortfolios = jest.fn((): any => {
        return [];
      });

      jest.spyOn(Portfolio, "findOneAndUpdate");
      jest.spyOn(PortfolioService, "createBrokeragePortfolio");
    });

    it("should create a wealthkernel portfolio and update the portfolio document for a verified user that hasn't created a wealthkernel portfolio before", async () => {
      await PortfolioService.createBrokeragePortfolio(portfolio);
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.user.verification.eventId,
        expect.objectContaining(portfolio.owner),
        expect.objectContaining({ emailNotification: false })
      );
      const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
      expect(updatedPortfolio.providers?.wealthkernel).toEqual({
        id: wealthkernelPortfolioId,
        status: "Created"
      });
    });

    it("should update the portfolio document with WK portfolio id if a wealthkernel portfolio exists but WK portfolio id is missing in the document", async () => {
      const wealthkernelId = faker.string.uuid();
      WealthkernelService.UKInstance.retrievePortfolios = jest.fn((): any => {
        return [{ id: wealthkernelId, status: "Active" }];
      });

      await PortfolioService.createBrokeragePortfolio(portfolio);
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.user.verification.eventId,
        expect.objectContaining(portfolio.owner),
        expect.objectContaining({ emailNotification: false })
      );
      const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio._id })) as PortfolioDocument;
      expect(updatedPortfolio.providers?.wealthkernel).toEqual({ id: wealthkernelId, status: "Active" });
    });

    it("should throw a BadRequestError with proper message and abort if wealthkernel portfolio id exists but no WK portfolio can be retrieved", async () => {
      portfolio = (await Portfolio.findOneAndUpdate(
        { _id: portfolio.id },
        {
          "providers.wealthkernel.id": faker.string.uuid()
        },
        {
          new: true
        }
      )) as PortfolioDocument;
      await expect(async () => await PortfolioService.createBrokeragePortfolio(portfolio)).rejects.toThrow(
        new BadRequestError("Wealthkernel portfolio exists already")
      );

      expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(0);
    });

    it("should throw BadRequestError with proper message and abort if more than one wealthkernel portfolios are retrieved", async () => {
      WealthkernelService.UKInstance.retrievePortfolios = jest.fn((): any => {
        return [{ id: faker.string.uuid() }, { id: faker.string.uuid() }];
      });

      await expect(async () => await PortfolioService.createBrokeragePortfolio(portfolio)).rejects.toThrow(
        new BadRequestError("Found more than one portfolios on Wealthkernel")
      );

      expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(0);
      expect(Portfolio.findOneAndUpdate).toBeCalledTimes(0);
    });

    describe("and user is manually kyc passed", () => {
      beforeEach(async () => {
        user = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(12),
          providers: { wealthkernel: { id: faker.string.uuid() } },
          portfolioConversionStatus: "completed",
          isManuallyKycPassed: true
        });

        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          account: await buildAccount({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending"
              }
            }
          })
        });
      });
      it("should create a wealthkernel portfolio", async () => {
        await PortfolioService.createBrokeragePortfolio(portfolio);
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.verification.eventId,
          expect.objectContaining(portfolio.owner),
          expect.objectContaining({ emailNotification: true })
        );
        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.providers?.wealthkernel).toEqual({
          id: wealthkernelPortfolioId,
          status: "Created"
        });
      });
    });

    describe("and user is not kyc", () => {
      beforeEach(async () => {
        user = await buildUser({
          portfolios: [portfolio],
          kycStatus: KycStatusEnum.PENDING,
          submittedRequiredInfoAt: new Date(),
          providers: { wealthkernel: { id: faker.string.uuid() } }
        });

        portfolio = await buildPortfolio({
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          account: await buildAccount({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending"
              }
            }
          })
        });
      });
      it("should throw BadRequestError with proper message and abort if user is not verified", async () => {
        await expect(async () => await PortfolioService.createBrokeragePortfolio(portfolio)).rejects.toThrow(
          new BadRequestError("User has not passed kyc")
        );
        expect(eventEmitter.emit).not.toHaveBeenCalled();
        expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(0);
        expect(Portfolio.findOneAndUpdate).toBeCalledTimes(0);
      });
    });
  });

  describe("updateCashAvailability", () => {
    it("should update the user's GBP available and settled cash if in GBP", async () => {
      const AVAILABLE_CASH = 11.1;
      const SETTLED_CASH = 11.1;
      const CASH_UP_BY = 1.3;
      const user = await buildUser({ portfolioConversionStatus: "completed" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: SETTLED_CASH } }
      });

      await PortfolioService.updateCashAvailability(portfolio.id, "GBP", -CASH_UP_BY, {
        available: true,
        settled: true
      });

      const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio._id })) as PortfolioDocument;
      expect(updatedPortfolio.cash.GBP?.available).toEqual(Decimal.sub(AVAILABLE_CASH, CASH_UP_BY).toNumber());
      expect(updatedPortfolio.cash.GBP?.settled).toEqual(Decimal.sub(SETTLED_CASH, CASH_UP_BY).toNumber());
    });

    it("should update the user's settled cash if only the settled option is true", async () => {
      const AVAILABLE_CASH = 11;
      const SETTLED_CASH = 11;
      const CASH_UP_BY = 1;
      const user = await buildUser({ portfolioConversionStatus: "completed" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        cash: { GBP: { available: AVAILABLE_CASH, settled: SETTLED_CASH, reserved: 0 } }
      });

      await PortfolioService.updateCashAvailability(portfolio.id, "GBP", CASH_UP_BY, {
        available: false,
        settled: true
      });

      const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio._id })) as PortfolioDocument;
      expect(updatedPortfolio.cash.GBP?.available).toEqual(AVAILABLE_CASH);
      expect(updatedPortfolio.cash.GBP?.settled).toEqual(Decimal.add(SETTLED_CASH, CASH_UP_BY).toNumber());
    });

    it("should update the user's EUR available and settled cash if in EUR", async () => {
      const AVAILABLE_CASH = 11.1;
      const SETTLED_CASH = 11.1;
      const CASH_UP_BY = 1.3;
      const user = await buildUser({ portfolioConversionStatus: "completed" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        cash: { EUR: { available: AVAILABLE_CASH, settled: SETTLED_CASH, reserved: 0 } }
      });

      await PortfolioService.updateCashAvailability(portfolio.id, "EUR", -CASH_UP_BY, {
        available: true,
        settled: true
      });

      const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio._id })) as PortfolioDocument;
      expect(updatedPortfolio.cash.EUR?.available).toEqual(Decimal.sub(AVAILABLE_CASH, CASH_UP_BY).toNumber());
      expect(updatedPortfolio.cash.EUR?.settled).toEqual(Decimal.sub(SETTLED_CASH, CASH_UP_BY).toNumber());
    });
  });

  describe("submitOrder", () => {
    let portfolio: PortfolioDocument;
    let user: UserDocument;
    const QUANTITY = 2;
    const PRICE = 20;
    const ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
      "equities_china",
      "equities_eu",
      "equities_global",
      "government_bonds_us",
      "equities_apple"
    ];
    const ORDER_AMOUNT = 10;
    const AVAILABLE_CASH = 100;
    const SETTLED_CASH = 90;
    let WK_ORDER_ID: string;

    beforeEach(async () => {
      WK_ORDER_ID = faker.string.uuid();
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: WK_ORDER_ID });
      user = await buildUser({ portfolioConversionStatus: "completed" });
      await buildSubscription({ owner: user.id });
      // holdings total value will be 3x1x10 = 30
      const holdings = await Promise.all(
        ASSET_COMMON_IDS.map((assetId: investmentUniverseConfig.AssetType) =>
          buildHoldingDTO(true, assetId, QUANTITY, { price: PRICE })
        )
      );
      portfolio = await buildPortfolio({
        owner: user.id,
        cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: SETTLED_CASH } },
        providers: { wealthkernel: { id: "WK_PORTFOLIO_ID", status: "Active" } },
        mode: PortfolioModeEnum.REAL,
        holdings
      });

      WealthkernelService.UKInstance.createPortfolio = jest.fn((): any => {
        return { id: faker.string.uuid() };
      });
    });

    describe("and there is one 'sell all' order under MIN_ALLOWED_ASSET_INVESTMENT", () => {
      const orderAmount = 0.25;
      const price = 0.25;
      const quantity = 1;

      beforeEach(async () => {
        // User has one asset of value £0.25, and they try to sell all of it.
        const holdings = [await buildHoldingDTO(true, "equities_global_clean_energy", quantity, { price })];
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: "WK_PORTFOLIO_ID", status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        await PortfolioService.submitOrder(
          portfolio._id,
          "equities_global_clean_energy",
          {
            side: "sell",
            quantity: quantity
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should create order", async () => {
        const order = await Order.findOne({ isin: ASSET_CONFIG["equities_global_clean_energy"].isin });

        expect(order).not.toBe(null);
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            isFirst: true,
            side: "sell",
            category: "etf",
            frequency: "one-off",
            amount: orderAmount,
            currency: "GBP",
            fxFees: 0,
            commissionFees: 0
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("and there is one sell order which is less than MIN_ALLOWED_ASSET_INVESTMENT but is not a 'sell all' order", () => {
      const orderAmount = 0.2;
      const orderQuantity = 1;
      const price = 0.2;
      const quantity = 2;

      beforeEach(async () => {
        // User has one asset of value £0.4, and they try to half of it.
        const holdings = [await buildHoldingDTO(true, "equities_global_ai", quantity, { price })];
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: "WK_PORTFOLIO_ID", status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        await expect(
          async () =>
            await PortfolioService.submitOrder(
              portfolio._id,
              "equities_global_ai",
              {
                side: "sell",
                quantity: orderQuantity,
                money: orderAmount
              },
              { executeEtfOrdersInRealtime: false }
            )
        ).rejects.toThrow(new BadRequestError("The submitted order does not pass our investment criteria"));
      });

      it("should not create an asset transaction", async () => {
        const transactions = await AssetTransaction.find({});
        expect(transactions.length).toEqual(0);
      });

      it("should not create any orders", async () => {
        const orders = await Order.find({});
        expect(orders.length).toEqual(0);
      });

      it("should not fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("and there is one valid buy order for an ETF", () => {
      const EXPECTED_EXECUTION_SPREAD_FEE = 0; // 0% of £10

      beforeEach(async () => {
        await PortfolioService.submitOrder(
          portfolio._id,
          "equities_eu",
          {
            side: "buy",
            money: ORDER_AMOUNT
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should create a new transaction, a new order and update portfolio available & settled cash", async () => {
        const transaction = await AssetTransaction.findOne({ portfolio: portfolio._id });
        expect(transaction.fees).toMatchObject({
          fx: { currency: "GBP", amount: 0 },
          commission: { currency: "GBP", amount: 0 },
          executionSpread: { currency: "GBP", amount: EXPECTED_EXECUTION_SPREAD_FEE },
          realtimeExecution: { currency: "GBP", amount: 0 }
        });

        const order = await Order.findOne({ isin: ASSET_CONFIG["equities_eu"].isin });

        expect(order).toMatchObject({
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
          fees: {
            fx: {
              amount: 0,
              currency: "GBP"
            },
            commission: {
              amount: 0,
              currency: "GBP"
            },
            executionSpread: {
              amount: EXPECTED_EXECUTION_SPREAD_FEE, // 0.35% of £10
              currency: "GBP"
            },
            realtimeExecution: {
              amount: 0,
              currency: "GBP"
            }
          }
        });

        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
        expect(updatedPortfolio.cash.GBP.settled).toEqual(SETTLED_CASH - ORDER_AMOUNT);
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "etf",
            assetName: "EU Stocks",
            frequency: "one-off",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: EXPECTED_EXECUTION_SPREAD_FEE
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should not submit the order to wealthkernel", () => {
        expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      });
    });

    describe("and there is one valid sell order for an ETF", () => {
      const SELL_QUANTITY = 0.5; // We are trying to sell 0.5 of a £20 asset -> £10 estimated proceeds
      const ESTIMATED_EXECUTION_SPREAD_FEE = 0; // 0% of £10

      beforeEach(async () => {
        await PortfolioService.submitOrder(
          portfolio._id,
          "equities_eu",
          {
            side: "sell",
            quantity: SELL_QUANTITY
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should create a new transaction & a new order", async () => {
        const transaction = await Transaction.findOne({ portfolio: portfolio._id });
        expect(transaction).toEqual(
          expect.objectContaining({
            fees: undefined
          })
        );

        const order = await Order.findOne({ isin: ASSET_CONFIG["equities_eu"].isin });
        expect(order.fees).toBeUndefined();
        expect(order).toMatchObject({
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
          consideration: {
            currency: "GBP"
          }
        });

        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH);
        expect(updatedPortfolio.cash.GBP.settled).toEqual(SETTLED_CASH);
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            isFirst: true,
            side: "sell",
            category: "etf",
            assetName: "EU Stocks",
            frequency: "one-off",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: ESTIMATED_EXECUTION_SPREAD_FEE
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should not submit the order to wealthkernel", () => {
        expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      });
    });

    describe("and there is one valid buy order for a (USD-traded) stock", () => {
      const EXPECTED_EXECUTION_SPREAD_FEE = 0; // 0% of £10
      const EXPECTED_FX_FEE = 0.08; // 0.75% of £10

      beforeEach(async () => {
        Date.now = jest.fn(() => new Date("2024-07-02T15:00:00Z").getTime());
        await PortfolioService.submitOrder(
          portfolio._id,
          "equities_apple",
          {
            side: "buy",
            money: ORDER_AMOUNT
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should create a new transaction, a new order and update portfolio cash", async () => {
        const transaction = await AssetTransaction.findOne({ portfolio: portfolio._id });
        expect(transaction.fees).toMatchObject({
          fx: {
            amount: EXPECTED_FX_FEE,
            currency: "GBP"
          },
          commission: {
            amount: 0,
            currency: "GBP"
          },
          executionSpread: {
            amount: EXPECTED_EXECUTION_SPREAD_FEE,
            currency: "GBP"
          },
          realtimeExecution: {
            amount: 0,
            currency: "GBP"
          }
        });

        const order = await Order.findOne({ isin: ASSET_CONFIG["equities_apple"].isin });
        expect(order).toMatchObject({
          providers: { wealthkernel: expect.objectContaining({ id: WK_ORDER_ID }) },
          submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
          fees: {
            fx: {
              amount: EXPECTED_FX_FEE,
              currency: "GBP"
            },
            commission: {
              amount: 0,
              currency: "GBP"
            },
            executionSpread: {
              amount: EXPECTED_EXECUTION_SPREAD_FEE,
              currency: "GBP"
            },
            realtimeExecution: {
              amount: 0,
              currency: "GBP"
            }
          }
        });

        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
        expect(updatedPortfolio.cash.GBP.settled).toEqual(SETTLED_CASH - ORDER_AMOUNT);
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "stock",
            assetName: "Apple",
            frequency: "one-off",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            fxFees: EXPECTED_FX_FEE,
            executionSpreadFees: EXPECTED_EXECUTION_SPREAD_FEE,
            commissionFees: 0
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should submit the order to wealthkernel", async () => {
        const order = await Order.findOne({ isin: ASSET_CONFIG["equities_apple"].isin });
        expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
          {
            aggregate: false,
            consideration: {
              amount: 9.92,
              currency: "GBP"
            },
            isin: "US0378331005",
            portfolioId: "WK_PORTFOLIO_ID",
            settlementCurrency: "GBP",
            side: "Buy"
          },
          order.id
        );
      });
    });

    describe("and there is one valid buy order for a (USD-traded) stock but has aggregatedSubmission flag set to true", () => {
      const EXPECTED_EXECUTION_SPREAD_FEE = 0; // 0% of £10
      const EXPECTED_FX_FEE = 0.08; // 0.75% of £10

      beforeEach(async () => {
        Date.now = jest.fn(() => new Date("2024-07-02T15:00:00Z").getTime());
        await buildInvestmentProduct(true, { assetId: "equities_polestar" });
        await PortfolioService.submitOrder(
          portfolio._id,
          "equities_polestar",
          {
            side: "buy",
            money: ORDER_AMOUNT
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should create a new transaction, a new order and update portfolio cash", async () => {
        const transaction = await AssetTransaction.findOne({ portfolio: portfolio._id });
        expect(transaction.fees).toMatchObject({
          fx: {
            amount: EXPECTED_FX_FEE,
            currency: "GBP"
          },
          commission: {
            amount: 0,
            currency: "GBP"
          },
          executionSpread: {
            amount: EXPECTED_EXECUTION_SPREAD_FEE,
            currency: "GBP"
          },
          realtimeExecution: {
            amount: 0,
            currency: "GBP"
          }
        });

        const order = await Order.findOne({ isin: ASSET_CONFIG["equities_polestar"].isin });
        expect(order).toMatchObject({
          isSubmittedToBroker: false,
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
          fees: {
            fx: {
              amount: EXPECTED_FX_FEE,
              currency: "GBP"
            },
            commission: {
              amount: 0,
              currency: "GBP"
            },
            executionSpread: {
              amount: EXPECTED_EXECUTION_SPREAD_FEE,
              currency: "GBP"
            },
            realtimeExecution: {
              amount: 0,
              currency: "GBP"
            }
          }
        });

        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
        expect(updatedPortfolio.cash.GBP.settled).toEqual(SETTLED_CASH - ORDER_AMOUNT);
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "stock",
            assetName: "Polestar",
            frequency: "one-off",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            fxFees: EXPECTED_FX_FEE,
            executionSpreadFees: EXPECTED_EXECUTION_SPREAD_FEE,
            commissionFees: 0
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should not submit the order to wealthkernel", () => {
        expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      });
    });

    describe("and there is one valid sell order for a (USD-traded) stock with estimated amount less than £1", () => {
      const EXPECTED_EXECUTION_SPREAD_FEE = 0;
      const EXPECTED_FX_FEE = 0.01;

      beforeEach(async () => {
        Date.now = jest.fn(() => new Date("2024-07-02T15:00:00Z").getTime());
        await PortfolioService.submitOrder(
          portfolio._id,
          "equities_apple",
          {
            side: "sell",
            quantity: 0.05
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should create a new transaction, a new order and update portfolio cash", async () => {
        const transaction = await AssetTransaction.findOne({ portfolio: portfolio._id });
        expect(transaction.fees).toBeUndefined();

        const order = await Order.findOne({ isin: ASSET_CONFIG["equities_apple"].isin });
        expect(order.fees).toBeUndefined();
        expect(order).toMatchObject({
          isSubmittedToBroker: false,
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });

        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
        expect(updatedPortfolio.cash.GBP.settled).toEqual(portfolio.cash.GBP?.settled);
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            isFirst: true,
            side: "sell",
            category: "stock",
            assetName: "Apple",
            frequency: "one-off",
            amount: 1,
            currency: "GBP",
            fxFees: EXPECTED_FX_FEE,
            executionSpreadFees: EXPECTED_EXECUTION_SPREAD_FEE,
            commissionFees: 0
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should not submit the order to wealthkernel", () => {
        expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      });
    });

    describe("and there is one valid buy order for a (USD-traded) stock outside of market hours", () => {
      const EXPECTED_EXECUTION_SPREAD_FEE = 0; // 0% of £10
      const EXPECTED_FX_FEE = 0.08; // 0.75% of £10

      beforeEach(async () => {
        Date.now = jest.fn(() => new Date("2024-07-02T09:00:00Z").getTime());
        await PortfolioService.submitOrder(
          portfolio._id,
          "equities_apple",
          {
            side: "buy",
            money: ORDER_AMOUNT
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should create a new transaction, a new order and update portfolio cash", async () => {
        const transaction = await AssetTransaction.findOne({ portfolio: portfolio._id });
        expect(transaction.fees).toMatchObject({
          fx: {
            amount: EXPECTED_FX_FEE,
            currency: "GBP"
          },
          commission: {
            amount: 0,
            currency: "GBP"
          },
          executionSpread: {
            amount: EXPECTED_EXECUTION_SPREAD_FEE,
            currency: "GBP"
          },
          realtimeExecution: {
            amount: 0,
            currency: "GBP"
          }
        });

        const order = await Order.findOne({ isin: ASSET_CONFIG["equities_apple"].isin });
        expect(order).toMatchObject({
          submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
          fees: {
            fx: {
              amount: EXPECTED_FX_FEE,
              currency: "GBP"
            },
            commission: {
              amount: 0,
              currency: "GBP"
            },
            executionSpread: {
              amount: EXPECTED_EXECUTION_SPREAD_FEE,
              currency: "GBP"
            },
            realtimeExecution: {
              amount: 0,
              currency: "GBP"
            }
          }
        });

        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
        expect(updatedPortfolio.cash.GBP.settled).toEqual(SETTLED_CASH - ORDER_AMOUNT);
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "stock",
            assetName: "Apple",
            frequency: "one-off",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            fxFees: EXPECTED_FX_FEE,
            executionSpreadFees: EXPECTED_EXECUTION_SPREAD_FEE,
            commissionFees: 0
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should not submit the order to wealthkernel", () => {
        expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      });
    });

    describe("and there is one valid buy order that should get a cashback", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        await buildSubscription({ owner: user.id, price: "paid_low_monthly" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: "WK_PORTFOLIO_ID", status: "Active" } },
          mode: PortfolioModeEnum.REAL
        });

        await PortfolioService.submitOrder(
          portfolio._id,
          "equities_eu",
          {
            side: "buy",
            money: MINIMUM_AMOUNT_FOR_CASHBACK
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should create a new cashback transaction", async () => {
        const transaction = await AssetTransaction.findOne({ portfolio: portfolio._id }).populate("cashback");

        const cashback = await CashbackTransaction.findOne({ linkedAssetTransaction: transaction.id });
        expect(cashback).toEqual(
          expect.objectContaining({
            deposit: { activeProviders: [ProviderEnum.WEALTHKERNEL] },
            consideration: {
              amount: 13, // 0.25% of £50
              currency: "GBP"
            }
          })
        );

        expect(transaction.cashback).toEqual(
          expect.objectContaining({
            _id: cashback._id
          })
        );
      });
    });

    describe("and there is one valid buy order that would get a cashback but the user is in the Europe company entity", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id, price: "paid_low_monthly" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: "WK_PORTFOLIO_ID", status: "Active" } },
          mode: PortfolioModeEnum.REAL
        });

        await PortfolioService.submitOrder(
          portfolio._id,
          "equities_eu",
          {
            side: "buy",
            money: MINIMUM_AMOUNT_FOR_CASHBACK
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should NOT create a new cashback transaction", async () => {
        const transaction = await AssetTransaction.findOne({ portfolio: portfolio._id }).populate("cashback");

        const cashbacks = await CashbackTransaction.find({ linkedAssetTransaction: transaction.id });
        expect(cashbacks.length).toBe(0);
      });
    });

    describe("and there is one buy order for more cash than available balance", () => {
      const NEW_AVAILABLE_CASH = AVAILABLE_CASH + 1;

      beforeEach(async () => {
        await expect(
          async () =>
            await PortfolioService.submitOrder(
              portfolio._id,
              "equities_eu",
              {
                side: "buy",
                money: NEW_AVAILABLE_CASH
              },
              { executeEtfOrdersInRealtime: false }
            )
        ).rejects.toThrow(
          new BadRequestError("You have placed buy orders of value £101.00 but you only have £100.00")
        );
      });

      it("should not create an asset transaction", async () => {
        const transactions = await AssetTransaction.find({});
        expect(transactions.length).toEqual(0);
      });

      it("should not create any orders", async () => {
        const orders = await Order.find({});
        expect(orders.length).toEqual(0);
      });

      it("should not update portfolio cash", async () => {
        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
        expect(updatedPortfolio.cash.GBP.settled).toEqual(portfolio.cash.GBP.settled);
      });

      it("should not fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("and there is one sell order for a quantity more than the available one", () => {
      beforeEach(async () => {
        await expect(
          async () =>
            await PortfolioService.submitOrder(
              portfolio._id,
              "equities_eu",
              {
                side: "sell",
                quantity: QUANTITY + 1,
                money: ORDER_AMOUNT
              },
              { executeEtfOrdersInRealtime: false }
            )
        ).rejects.toThrow(
          new BadRequestError("You have placed a sell order with quantity larger than what you hold")
        );
      });

      it("should not update portfolio cash", async () => {
        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
        expect(updatedPortfolio.cash.GBP.settled).toEqual(portfolio.cash.GBP.settled);
      });

      it("should not fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("and there is one buy order for an asset not in user's holdings", () => {
      const NEW_ASSET_ID: investmentUniverseConfig.AssetType = "government_bonds_uk";
      const NEW_ORDER_AMOUNT = 10;
      const NEW_PRODUCT_PRICE = 20;

      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        await buildInvestmentProduct(true, {
          assetId: NEW_ASSET_ID,
          price: NEW_PRODUCT_PRICE
        });

        // validate that the asset we buy is not part of holdings
        const holdingAssetIDs = portfolio.holdings.map(({ assetCommonId }) => assetCommonId);
        expect(new Set(holdingAssetIDs).has(NEW_ASSET_ID)).toEqual(false);

        // ensuring that when we later retrieve the transaction & orders it will be the new one
        const assetTransactions = await AssetTransaction.find();
        expect(assetTransactions.length).toBe(0);
        const orders = await Order.find();
        expect(orders.length).toBe(0);

        assetTransaction = await PortfolioService.submitOrder(
          portfolio._id,
          NEW_ASSET_ID,
          {
            side: "buy",
            money: NEW_ORDER_AMOUNT
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should create an asset transaction with corresponding orders", async () => {
        const assetTransactions = await AssetTransaction.find().populate("orders");

        // validate correct asset transaction creation
        expect(assetTransactions.length).toEqual(1);
        expect(assetTransactions[0]).toMatchObject({
          consideration: { currency: "GBP" },
          owner: new mongoose.Types.ObjectId(user._id),
          portfolio: new mongoose.Types.ObjectId(portfolio._id),
          portfolioTransactionCategory: "update"
        });

        // validate correct order creation
        const { orders } = assetTransactions[0];
        expect(orders.length).toEqual(1);
        expect(orders[0]).toMatchObject(
          expect.objectContaining({
            consideration: expect.objectContaining({
              currency: "GBP",
              originalAmount: Decimal.mul(NEW_ORDER_AMOUNT, 100).toNumber(),
              amount: Decimal.sub(NEW_ORDER_AMOUNT, getTotalFeeAmount(assetTransaction.fees)).mul(100).toNumber()
            }),
            isin: ASSET_CONFIG[NEW_ASSET_ID].isin,
            side: "Buy"
          })
        );
      });

      it("should reduce portfolio cash by order amount", async () => {
        const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio.id })) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - NEW_ORDER_AMOUNT);
        expect(updatedPortfolio.cash.GBP.settled).toEqual(SETTLED_CASH - NEW_ORDER_AMOUNT);
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "etf",
            frequency: "one-off",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: assetTransaction.fees.executionSpread.amount
          })
        );
      });

      it("should fire an 'firstInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("and there is one valid sell for an ETF with realtime execution enabled", () => {
      const NEW_ASSET_ID = "equities_us";
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id, price: "free_monthly" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: SETTLED_CASH } },
          providers: { wealthkernel: { id: "WK_PORTFOLIO_ID", status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, NEW_ASSET_ID, 100, { price: 100 })]
        });

        assetTransaction = await PortfolioService.submitOrder(
          portfolio._id,
          NEW_ASSET_ID,
          {
            side: "sell",
            quantity: 10
          },
          { executeEtfOrdersInRealtime: true }
        );
      });

      it("should return a transaction containing an order with an estimated real time execution fee", async () => {
        const order = assetTransaction.orders[0];
        expect(order).toMatchObject({
          submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
          estimatedRealTimeCommission: REAL_TIME_ETF_EXECUTION_FEES["free"]
        });
      });
    });

    describe("and there is one valid buy for an ETF with realtime execution enabled", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id, price: "free_monthly" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: SETTLED_CASH } },
          providers: { wealthkernel: { id: "WK_PORTFOLIO_ID", status: "Active" } },
          mode: PortfolioModeEnum.REAL
        });

        assetTransaction = await PortfolioService.submitOrder(
          portfolio._id,
          ASSET_COMMON_IDS[0],
          {
            side: "buy",
            money: 10
          },
          { executeEtfOrdersInRealtime: true }
        );
      });

      it("should return a transaction containing an order with a final realtime execution fee and without an estimated real time execution fee", async () => {
        const order = assetTransaction.orders[0];
        expect(order).toMatchObject({
          submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
          fees: expect.objectContaining({
            realtimeExecution: { amount: REAL_TIME_ETF_EXECUTION_FEES["free"], currency: "GBP" }
          }),
          estimatedRealTimeCommission: undefined
        });
      });
    });
  });

  describe("getAveragePricePerShare", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeAll(async () => {
      user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({ owner: user.id });
    });
    afterAll(() => jest.clearAllMocks());

    it("should return 0 when there are no orders for the asset", async () => {
      const orders = await Order.find();
      expect(orders.length).toBe(0);
      const averagePrice = await PortfolioService.getAveragePricePerShare("equities_apple", {
        assetTransactions: [],
        rebalanceTransactions: [],
        rewards: []
      });
      expect(averagePrice).toMatchObject({
        priceInTradedCurrency: 0,
        priceInSettlementCurrency: 0
      });
    });

    it("should return 0 when there are only pending orders for the asset", async () => {
      const pendingTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending"
      });
      pendingTransaction.orders = [
        await buildOrder({
          status: "Pending",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
          },
          side: "Buy",
          transaction: pendingTransaction.id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin,
          quantity: 2
        })
      ];
      await pendingTransaction.save();
      await pendingTransaction.populate("orders");

      const averagePrice = await PortfolioService.getAveragePricePerShare("equities_apple", {
        assetTransactions: [pendingTransaction],
        rebalanceTransactions: [],
        rewards: []
      });
      expect(averagePrice).toMatchObject({
        priceInTradedCurrency: 0,
        priceInSettlementCurrency: 0
      });
    });

    it("should return 0 when there are no buy orders for the asset", async () => {
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });
      assetTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: new Date(),
          side: "Sell",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_airbnb"]?.isin,
          quantity: 2
        })
      ];
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const averagePrice = await PortfolioService.getAveragePricePerShare("equities_apple", {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        rewards: []
      });
      expect(averagePrice).toMatchObject({
        priceInTradedCurrency: 0,
        priceInSettlementCurrency: 0
      });
    });

    it("should return the average share price for matched buy orders", async () => {
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 = 10
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1
        },
        {
          // price => 40 / 2 = 20
          amount: 40 * 100,
          quantity: 2,
          exchangeRate: 1
        },
        {
          // price => 20 / 1 = 20
          amount: 20 * 100,
          quantity: 1
          // here we intentionally leave exchange rate empty (it will use fallback of 1)
        }
      ];
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const averagePrice = await PortfolioService.getAveragePricePerShare("equities_eu", {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        rewards: []
      });
      // see comment on top for explanation on 15
      expect(averagePrice).toMatchObject({
        priceInTradedCurrency: 15,
        priceInSettlementCurrency: 15
      });
    });

    it("should take into account the traded currency", async () => {
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 33 + 24 = 57 / 5 = 11.4

      // 3 * 11 + 2 * 12 / ( 3 + 2 ) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 * 1.1 = 11
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1.1
        },
        {
          // price => 40 / 2 * 1.2 = 12
          amount: 20 * 100,
          quantity: 2,
          exchangeRate: 1.2
        }
      ];
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_airbnb"]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const averagePrice = await PortfolioService.getAveragePricePerShare("equities_airbnb", {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        rewards: []
      });
      // see comment on top for explanation on $11.4
      expect(averagePrice).toMatchObject({
        priceInTradedCurrency: 11.4,
        priceInSettlementCurrency: 10
      });
    });

    it("should take into account rewards", async () => {
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 = 10
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1
        },
        {
          // price => 40 / 2 = 20
          amount: 40 * 100,
          quantity: 2,
          exchangeRate: 1
        }
      ];

      const reward = await buildReward({
        status: "Settled",
        asset: "equities_eu",
        // price => 20 / 1 = 20
        consideration: {
          amount: 20 * 100,
          currency: "GBP"
        },
        quantity: 1
        // here we intentionally leave exchange rate empty (it will use fallback of 1)
      });
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const averagePrice = await PortfolioService.getAveragePricePerShare("equities_eu", {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        rewards: [reward]
      });
      // see comment on top for explanation on 15
      expect(averagePrice).toMatchObject({
        priceInTradedCurrency: 15,
        priceInSettlementCurrency: 15
      });
    });

    it("should take into account rebalance buy orders", async () => {
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 = 10
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1
        },
        {
          // price => 40 / 2 = 20
          amount: 40 * 100,
          quantity: 2,
          exchangeRate: 1
        }
      ];
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      // Settled rebalance transaction (with a buy order) - will use original amount
      // because we have execution spread fee
      const settledRebalanceTransactionWithBuyOrder = await buildRebalanceTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        rebalanceStatus: "Settled"
      });
      await buildOrder({
        status: "Matched",
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: settledRebalanceTransactionWithBuyOrder.id,
        side: "Buy",
        filledAt: new Date(),
        consideration: {
          currency: "GBP",
          originalAmount: 20 * 100,
          amount: 20 * 100
        },
        quantity: 1,
        providers: {
          wealthkernel: {
            id: "matched-rebalance-buy-order-id",
            status: "Matched",
            submittedAt: new Date()
          }
        },
        fees: {
          fx: {
            amount: 0,
            currency: "GBP"
          },
          executionSpread: {
            amount: 0,
            currency: "GBP"
          }
        }
      });
      await settledRebalanceTransactionWithBuyOrder.populate("orders");

      const averagePrice = await PortfolioService.getAveragePricePerShare("equities_eu", {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [settledRebalanceTransactionWithBuyOrder],
        rewards: []
      });
      // see comment on top for explanation on 15
      expect(averagePrice).toMatchObject({
        priceInTradedCurrency: 15,
        priceInSettlementCurrency: 15
      });
    });

    it("should take into account orders with deprecated isin", async () => {
      const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro_deprecated_1";
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 = 10
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1
        },
        {
          // price => 40 / 2 = 20
          amount: 40 * 100,
          quantity: 2,
          exchangeRate: 1
        },
        {
          // price => 20 / 1 = 20
          amount: 20 * 100,
          quantity: 1
          // here we intentionally leave exchange rate empty (it will use fallback of 1)
        }
      ];
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG[DEPRECATED_ASSET_ID]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const averagePrice = await PortfolioService.getAveragePricePerShare(DEPRECATED_ASSET_ID, {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        rewards: []
      });
      // see comment on top for explanation on 15
      expect(averagePrice).toMatchObject({
        priceInTradedCurrency: 15,
        priceInSettlementCurrency: 15
      });
    });

    it("should take into account stock splits", async () => {
      const TODAY = new Date("2024-01-10");
      const YESTERDAY = DateUtil.getDateOfDaysAgo(TODAY, 1);
      const SIX_MONTHS_AGO = DateUtil.getDateOfMonthsAgo(TODAY, 6);
      const ONE_YEAR_AGO = DateUtil.getDateOfYearsAgo(TODAY, 1);
      const TWO_YEARS_AGO = DateUtil.getDateOfYearsAgo(TODAY, 2);
      const THREE_YEARS_AGO = DateUtil.getDateOfYearsAgo(TODAY, 3);

      // The timeline we simulate for this test is:
      // User bought one share of Apple (£10) three years ago
      // Stock split (1 -> 10) two years ago
      // User bought one share of Apple (£1) one year ago
      // Stock split (1 -> 2) six months ago
      // User bought one share of Apple (£0.5) yesterday

      Date.now = jest.fn(() => +TODAY);

      const [firstAssetTransaction, secondAssetTransaction, thirdAssetTransaction] = await Promise.all([
        buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        }),
        buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        }),
        buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        }),
        buildStockSplitCorporateEvent({
          asset: "equities_apple",
          splitRatio: "10.000000/1.000000", // 1 -> 10 split ratio
          date: TWO_YEARS_AGO
        }),
        buildStockSplitCorporateEvent({
          asset: "equities_apple",
          splitRatio: "2.000000/1.000000", // 1 -> 2 split ratio
          date: SIX_MONTHS_AGO
        })
      ]);

      firstAssetTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: THREE_YEARS_AGO,
          side: "Buy",
          transaction: firstAssetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"]?.isin,
          consideration: {
            amountSubmitted: 1000,
            amount: 1000,
            currency: "GBP"
          },
          quantity: 1
        })
      ];

      secondAssetTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: ONE_YEAR_AGO,
          side: "Buy",
          transaction: secondAssetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"]?.isin,
          consideration: {
            amountSubmitted: 100,
            amount: 100,
            currency: "GBP"
          },
          quantity: 1
        })
      ];

      thirdAssetTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          filledAt: YESTERDAY,
          side: "Buy",
          transaction: thirdAssetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"]?.isin,
          consideration: {
            amountSubmitted: 50,
            amount: 50,
            currency: "GBP"
          },
          quantity: 1
        })
      ];

      await Promise.all([
        firstAssetTransaction.save(),
        secondAssetTransaction.save(),
        thirdAssetTransaction.save()
      ]);

      const averagePrice = await PortfolioService.getAveragePricePerShare("equities_apple", {
        assetTransactions: [firstAssetTransaction, secondAssetTransaction, thirdAssetTransaction],
        rebalanceTransactions: [],
        rewards: []
      });

      expect(averagePrice).toMatchObject({
        priceInTradedCurrency: 0.5,
        priceInSettlementCurrency: 0.5
      });
    });
  });

  describe("getAssetReturns", () => {
    let user: UserDocument;

    beforeAll(async () => {
      user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
    });
    afterAll(() => jest.clearAllMocks());

    it("should return 0 when the current price is same as the average price per share", async () => {
      const PRICE = 15;
      const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_eu" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        holdings: [{ asset: investmentProduct.id, assetCommonId: "equities_eu", quantity: 6 }]
      });
      expect(ASSET_CONFIG["equities_eu"]?.tradedCurrency).toBe("GBP");
      await buildIntraDayAssetTicker({
        investmentProduct: investmentProduct?.id,
        pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: PRICE }
      });
      await Promise.all([
        investmentProduct.populate("currentTicker"),
        portfolio.populate([
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ])
      ]);

      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 = 10
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1
        },
        {
          // price => 40 / 2 = 20
          amount: 40 * 100,
          quantity: 2,
          exchangeRate: 1
        },
        {
          // price => 20 / 1 = 20
          amount: 20 * 100,
          quantity: 1,
          exchangeRate: 1
        }
      ];
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const assetReturns = await PortfolioService.getAssetReturns("GBP", portfolio.holdings[0], {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      });
      // 15/15 - 1
      expect(assetReturns).toBe(0);
    });

    it("should return positive performance when the asset price is larger than the average price per share", async () => {
      const PRICE = 22.5;
      const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_eu" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        holdings: [{ asset: investmentProduct.id, assetCommonId: "equities_eu", quantity: 6 }]
      });
      expect(ASSET_CONFIG["equities_eu"]?.tradedCurrency).toBe("GBP");
      await buildIntraDayAssetTicker({
        investmentProduct: investmentProduct?.id,
        pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: PRICE }
      });
      await Promise.all([
        investmentProduct.populate("currentTicker"),
        portfolio.populate([
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ])
      ]);

      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 = 10
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1
        },
        {
          // price => 40 / 2 = 20
          amount: 40 * 100,
          quantity: 2,
          exchangeRate: 1
        },
        {
          // price => 20 / 1 = 20
          amount: 20 * 100,
          quantity: 1
          // here we intentionally leave exchange rate empty (it will use fallback of 1)
        }
      ];
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const assetReturns = await PortfolioService.getAssetReturns("GBP", portfolio.holdings[0], {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      });
      // 22.5/15 - 1
      expect(assetReturns).toBe(0.5);
    });

    it("should return negative performance when the asset price is larger than the average price per share", async () => {
      const PRICE = 7.5;
      const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_eu" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        holdings: [{ asset: investmentProduct.id, assetCommonId: "equities_eu", quantity: 6 }]
      });
      expect(ASSET_CONFIG["equities_eu"]?.tradedCurrency).toBe("GBP");
      await buildIntraDayAssetTicker({
        investmentProduct: investmentProduct?.id,
        pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: PRICE }
      });
      await Promise.all([
        investmentProduct.populate("currentTicker"),
        portfolio.populate([
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ])
      ]);

      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 = 10
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1
        },
        {
          // price => 40 / 2 = 20
          amount: 40 * 100,
          quantity: 2,
          exchangeRate: 1
        },
        {
          // price => 20 / 1 = 20
          amount: 20 * 100,
          quantity: 1,
          exchangeRate: 1
        }
      ];
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_eu"]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const assetReturns = await PortfolioService.getAssetReturns("GBP", portfolio.holdings[0], {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      });
      // 7.5/15 - 1
      expect(assetReturns).toBe(-0.5);
    });

    it("should take into account the traded price", async () => {
      const PRICE = 30;
      const USD_PRICE = PRICE * 1.2;
      const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        holdings: [{ asset: investmentProduct.id, assetCommonId: "equities_apple", quantity: 6 }]
      });
      expect(ASSET_CONFIG["equities_apple"]?.tradedCurrency).toBe("USD");
      await buildIntraDayAssetTicker({
        investmentProduct: investmentProduct?.id,
        pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: USD_PRICE }
      });
      await Promise.all([
        investmentProduct.populate("currentTicker"),
        portfolio.populate([
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ])
      ]);

      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 = 10
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1
        },
        {
          // price => 40 / 2 = 20
          amount: 40 * 100,
          quantity: 2,
          exchangeRate: 1
        },
        {
          // price => 20 / 1 = 20
          amount: 20 * 100,
          quantity: 1
          // here we intentionally leave exchange rate empty (it will use fallback of 1)
        }
      ];
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const assetReturns = await PortfolioService.getAssetReturns("GBP", portfolio.holdings[0], {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      });
      // returns = 30/15 -1
      // if we wanted returns based on traded currency that would be: 30/15 * 1.2 -1
      expect(assetReturns).toBe(1);
    });

    it("should take into account orders with deprecated isin", async () => {
      const ACTIVE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro";
      const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro_deprecated_1";
      const PRICE = 22.5;
      await buildInvestmentProduct(false, { assetId: DEPRECATED_ASSET_ID });
      const investmentProduct = await buildInvestmentProduct(false, { assetId: ACTIVE_ASSET_ID });
      const portfolio = await buildPortfolio({
        owner: user.id,
        holdings: [{ asset: investmentProduct.id, assetCommonId: ACTIVE_ASSET_ID, quantity: 6 }]
      });

      await buildIntraDayAssetTicker({
        investmentProduct: investmentProduct?.id,
        pricePerCurrency: { GBP: PRICE, EUR: PRICE, USD: PRICE }
      });
      await Promise.all([
        investmentProduct.populate("currentTicker"),
        portfolio.populate([
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ])
      ]);

      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      // 3 * 10 + 2 * 20 + 1 * 20 / ( 3 + 2 + 1) = 15 average price per share
      const orderConfig = [
        {
          // price => 30 / 3 = 10
          amount: 30 * 100,
          quantity: 3,
          exchangeRate: 1
        },
        {
          // price => 40 / 2 = 20
          amount: 40 * 100,
          quantity: 2,
          exchangeRate: 1
        },
        {
          // price => 20 / 1 = 20
          amount: 20 * 100,
          quantity: 1
          // here we intentionally leave exchange rate empty (it will use fallback of 1)
        }
      ];
      assetTransaction.orders = await Promise.all(
        orderConfig.map(({ amount, quantity, exchangeRate }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            filledAt: new Date(),
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG[DEPRECATED_ASSET_ID]?.isin,
            consideration: {
              amountSubmitted: amount,
              amount,
              currency: "GBP"
            },
            quantity,
            exchangeRate
          })
        )
      );
      await assetTransaction.save();
      await assetTransaction.populate("orders");

      const assetReturns = await PortfolioService.getAssetReturns("GBP", portfolio.holdings[0], {
        assetTransactions: [assetTransaction],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      });
      // 22.5/15 - 1
      expect(assetReturns).toBe(0.5);
    });
  });

  describe("getHoldingUpByValue", () => {
    describe("when the asset has asset transactions, rebalances, charges & rewards", () => {
      const HOLDING_QUANTITY = 0.05;
      const PRICE = 50;
      const ASSET_TRANSACTION_AMOUNT = 1100;
      const ORDER_ASSET_BUY_AMOUNT = 1000;
      const ORDER_ASSET_SELL_AMOUNT = 1000;
      const ORDER_ASSET_BUY_AMOUNT_POST_FEES = 1000 - 50;
      const REBALANCE_BUY_AMOUNT = 2000;
      const REBALANCE_SELL_AMOUNT = 500;
      const CHARGE_SELL_AMOUNT = 500;
      const REWARD_AMOUNT = 100;
      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };

      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_eu", HOLDING_QUANTITY, { price: PRICE })]
        });
        await portfolio.populate([
          { path: "currentTicker" },
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ]);

        // Settled transaction (eligible to upBy calculation) - will use original amount
        // for one order because we have execution spread fee and post-fees amount for the
        // order with 0 execution spread.
        const settledTransactionWithBuyOrder = await buildAssetTransaction({
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: ORDER_ASSET_BUY_AMOUNT,
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const matchedBuyOrderPreFeesAmount = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: settledTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: ORDER_ASSET_BUY_AMOUNT,
            amount: ORDER_ASSET_BUY_AMOUNT - 1
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          },
          fees: {
            fx: {
              amount: 0,
              currency: "GBP"
            },
            executionSpread: {
              amount: 1,
              currency: "GBP"
            }
          }
        });
        const matchedBuyOrderPostFeesAmount = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: settledTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: ORDER_ASSET_BUY_AMOUNT,
            amount: ORDER_ASSET_BUY_AMOUNT_POST_FEES
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          },
          fees: {
            fx: {
              amount: 50,
              currency: "GBP"
            },
            executionSpread: {
              amount: 0,
              currency: "GBP"
            }
          }
        });
        settledTransactionWithBuyOrder.orders = [
          matchedBuyOrderPreFeesAmount.id,
          matchedBuyOrderPostFeesAmount.id
        ];

        await settledTransactionWithBuyOrder.save();
        await settledTransactionWithBuyOrder.populate("orders");

        // Settled transaction (eligible to upBy calculation)
        const settledTransactionWithSellOrder = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const matchedSellOrder = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: settledTransactionWithBuyOrder.id,
          side: "Sell",
          consideration: {
            currency: "GBP",
            amount: ORDER_ASSET_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-sell-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        settledTransactionWithSellOrder.orders = [matchedSellOrder.id];
        await settledTransactionWithSellOrder.save();
        await settledTransactionWithSellOrder.populate("orders");

        // Pending transaction (NOT eligible to upBy calculation)
        const pendingTransactionWithBuyOrder = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending"
        });
        const pendingBuyOrder = await buildOrder({
          status: "Pending",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: settledTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: ASSET_TRANSACTION_AMOUNT,
            amount: ASSET_TRANSACTION_AMOUNT - 1
          }
        });
        pendingTransactionWithBuyOrder.orders = [pendingBuyOrder.id];
        await pendingTransactionWithBuyOrder.save();
        await pendingTransactionWithBuyOrder.populate("orders");

        // Settled rebalance transaction (with a buy order) - will use original amount
        // because we have execution spread fee
        const settledRebalanceTransactionWithBuyOrder = await buildRebalanceTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          rebalanceStatus: "Settled"
        });
        await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: settledRebalanceTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: REBALANCE_BUY_AMOUNT,
            amount: REBALANCE_BUY_AMOUNT - 1
          },
          providers: {
            wealthkernel: {
              id: "matched-rebalance-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          },
          fees: {
            fx: {
              amount: 0,
              currency: "GBP"
            },
            executionSpread: {
              amount: 1,
              currency: "GBP"
            }
          }
        });
        await settledRebalanceTransactionWithBuyOrder.populate("orders");

        // Another settled rebalance transaction (with a sell order)
        const settledRebalanceTransactionWithSellOrder = await buildRebalanceTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          rebalanceStatus: "Settled"
        });
        await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: settledRebalanceTransactionWithSellOrder.id,
          side: "Sell",
          consideration: {
            currency: "GBP",
            amount: REBALANCE_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-rebalance-sell-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        await settledRebalanceTransactionWithSellOrder.populate("orders");

        const subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: user.id
        });

        // Pending charge transaction with unmatched orders (should not affect calculation)
        const chargeTransactionWithUnmatchedOrders = await buildChargeTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          subscription: subscription.id,
          chargeMethod: "combined",
          status: "Pending"
        });
        await buildOrder({
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: chargeTransactionWithUnmatchedOrders.id,
          side: "Sell",
          status: "Pending",
          consideration: {
            currency: "GBP",
            originalAmount: CHARGE_SELL_AMOUNT,
            amount: CHARGE_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "open-charge-buy-order-id",
              status: "Open",
              submittedAt: new Date()
            }
          }
        });
        await chargeTransactionWithUnmatchedOrders.populate("orders");

        // Pending charge transaction with matched orders
        const chargeTransactionWithMatchedOrders = await buildChargeTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          subscription: subscription.id,
          chargeMethod: "combined",
          status: "Pending"
        });
        await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: chargeTransactionWithMatchedOrders.id,
          side: "Sell",
          consideration: {
            currency: "GBP",
            originalAmount: CHARGE_SELL_AMOUNT,
            amount: CHARGE_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-charge-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        await chargeTransactionWithMatchedOrders.populate("orders");

        // Settled reward (eligible to upBy calculation)
        const settledReward = await buildReward({
          targetUser: user.id,
          asset: "equities_eu",
          consideration: {
            currency: "GBP",
            amount: REWARD_AMOUNT
          },
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
          }
        });

        await buildReward({
          targetUser: user.id,
          status: "Pending",
          asset: "equities_eu",
          consideration: {
            currency: "GBP",
            amount: REWARD_AMOUNT
          }
        });

        transactions.assetTransactions = [settledTransactionWithBuyOrder, settledTransactionWithSellOrder];
        transactions.chargeTransactions = [chargeTransactionWithMatchedOrders];
        transactions.rebalanceTransactions = [
          settledRebalanceTransactionWithSellOrder,
          settledRebalanceTransactionWithBuyOrder
        ];
        transactions.rewards = [settledReward];
      });

      it("should properly calculate up by value for asset 'equities_eu'", () => {
        const holding = portfolio.holdings[0];
        const holdingUpBy = PortfolioService.getHoldingUpByValue("GBP", holding, transactions);
        expect(holdingUpBy).toBe(
          new Decimal(HOLDING_QUANTITY)
            .mul(PRICE)
            .plus(ORDER_ASSET_SELL_AMOUNT / 100)
            .plus(REBALANCE_SELL_AMOUNT / 100)
            .plus(CHARGE_SELL_AMOUNT / 100)
            // for both transactions we should take into account the order amount pre-fees for buys
            .minus((2 * ORDER_ASSET_BUY_AMOUNT) / 100)
            .minus(REWARD_AMOUNT / 100)
            .minus(REBALANCE_BUY_AMOUNT / 100)
            .toNumber()
        );
      });
    });

    describe("when the asset was fully sold, and then bought again", () => {
      const HOLDING_QUANTITY = 0.05;
      const PRICE = 50;
      const BEFORE_SELLING_REWARD_AMOUNT = 100;
      const BEFORE_SELLING_ORDER_ASSET_BUY_AMOUNT = 1000;
      const BEFORE_SELLING_ORDER_ASSET_SELL_AMOUNT = 1100; //cover both buy order and reward
      const AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT = 2000;
      const AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT = 500;
      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };

      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_eu", HOLDING_QUANTITY, { price: PRICE })]
        });
        await portfolio.populate([
          { path: "currentTicker" },
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ]);

        // Settled reward (eligible to upBy calculation)
        const beforeFullSellSettledReward = await buildReward({
          targetUser: user.id,
          asset: "equities_eu",
          consideration: {
            currency: "GBP",
            amount: BEFORE_SELLING_REWARD_AMOUNT
          },
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
          }
        });

        await buildReward({
          targetUser: user.id,
          status: "Pending",
          asset: "equities_eu",
          consideration: {
            currency: "GBP",
            amount: BEFORE_SELLING_REWARD_AMOUNT
          }
        });

        const beforeFullSellSettledTransactionWithBuyOrder = await buildAssetTransaction({
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: BEFORE_SELLING_ORDER_ASSET_BUY_AMOUNT,
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const beforeFullSellBuyOrder = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: beforeFullSellSettledTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: BEFORE_SELLING_ORDER_ASSET_BUY_AMOUNT,
            amount: BEFORE_SELLING_ORDER_ASSET_BUY_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        beforeFullSellSettledTransactionWithBuyOrder.orders = [beforeFullSellBuyOrder.id];

        await beforeFullSellSettledTransactionWithBuyOrder.save();
        await beforeFullSellSettledTransactionWithBuyOrder.populate("orders");

        const beforeFullSellSettledTransactionWithSellOrder = await buildAssetTransaction({
          portfolioTransactionCategory: "sell",
          originalInvestmentAmount: BEFORE_SELLING_ORDER_ASSET_SELL_AMOUNT,
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const beforeFullSellSellOrder = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: beforeFullSellSettledTransactionWithBuyOrder.id,
          side: "Sell",
          consideration: {
            currency: "GBP",
            originalAmount: BEFORE_SELLING_ORDER_ASSET_SELL_AMOUNT,
            amount: BEFORE_SELLING_ORDER_ASSET_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        beforeFullSellSettledTransactionWithSellOrder.orders = [beforeFullSellSellOrder.id];

        await beforeFullSellSettledTransactionWithSellOrder.save();
        await beforeFullSellSettledTransactionWithSellOrder.populate("orders");

        const afterFullSellSettledTransactionWithBuyOrder = await buildAssetTransaction({
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT,
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const afterFullSellBuyOrder = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: beforeFullSellSettledTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT,
            amount: AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        afterFullSellSettledTransactionWithBuyOrder.orders = [afterFullSellBuyOrder.id];

        await afterFullSellSettledTransactionWithBuyOrder.save();
        await afterFullSellSettledTransactionWithBuyOrder.populate("orders");

        const afterFullSellSettledTransactionWithSellOrder = await buildAssetTransaction({
          portfolioTransactionCategory: "sell",
          originalInvestmentAmount: AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT,
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const afterFullSellSellOrder = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: beforeFullSellSettledTransactionWithBuyOrder.id,
          side: "Sell",
          consideration: {
            currency: "GBP",
            originalAmount: AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT,
            amount: AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        afterFullSellSettledTransactionWithSellOrder.orders = [afterFullSellSellOrder.id];

        await afterFullSellSettledTransactionWithSellOrder.save();
        await afterFullSellSettledTransactionWithSellOrder.populate("orders");

        transactions.assetTransactions = [
          beforeFullSellSettledTransactionWithBuyOrder,
          beforeFullSellSettledTransactionWithSellOrder,
          afterFullSellSettledTransactionWithBuyOrder,
          afterFullSellSettledTransactionWithSellOrder
        ];
        transactions.rewards = [beforeFullSellSettledReward];
      });

      it("should properly calculate up by value for asset 'equities_eu' ignoring the orders before the full sell", () => {
        const holding = portfolio.holdings[0];
        const holdingUpBy = PortfolioService.getHoldingUpByValue("GBP", holding, transactions);
        expect(holdingUpBy).toBe(
          new Decimal(HOLDING_QUANTITY)
            .mul(PRICE)
            .plus(AFTER_SELLING_ORDER_ASSET_SELL_AMOUNT / 100)
            .minus(AFTER_SELLING_ORDER_ASSET_BUY_AMOUNT / 100)
            .toNumber()
        );
      });
    });

    describe("when the asset has deprecated isin", () => {
      const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro_deprecated_1";
      const ACTIVE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_supermicro";
      const HOLDING_QUANTITY = 0.05;
      const PRICE = 50;
      const ASSET_TRANSACTION_AMOUNT = 1100;
      const ORDER_ASSET_BUY_AMOUNT = 1000;
      const ORDER_ASSET_SELL_AMOUNT = 1000;
      const ORDER_ASSET_BUY_AMOUNT_POST_FEES = 1000 - 50;
      const REBALANCE_BUY_AMOUNT = 2000;
      const REBALANCE_SELL_AMOUNT = 500;
      const CHARGE_SELL_AMOUNT = 500;
      const REWARD_AMOUNT = 100;
      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };

      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, ACTIVE_ASSET_ID, HOLDING_QUANTITY, { price: PRICE })]
        });
        await portfolio.populate([
          { path: "currentTicker" },
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ]);

        // Settled transaction (eligible to upBy calculation) - will use original amount
        // for one order because we have execution spread fee and post-fees amount for the
        // order with 0 execution spread.
        const settledTransactionWithBuyOrder = await buildAssetTransaction({
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: ORDER_ASSET_BUY_AMOUNT,
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const matchedBuyOrderPreFeesAmount = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
          transaction: settledTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: ORDER_ASSET_BUY_AMOUNT,
            amount: ORDER_ASSET_BUY_AMOUNT - 1
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          },
          fees: {
            fx: {
              amount: 0,
              currency: "GBP"
            },
            executionSpread: {
              amount: 1,
              currency: "GBP"
            }
          }
        });
        const matchedBuyOrderPostFeesAmount = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
          transaction: settledTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: ORDER_ASSET_BUY_AMOUNT,
            amount: ORDER_ASSET_BUY_AMOUNT_POST_FEES
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          },
          fees: {
            fx: {
              amount: 50,
              currency: "GBP"
            },
            executionSpread: {
              amount: 0,
              currency: "GBP"
            }
          }
        });
        settledTransactionWithBuyOrder.orders = [
          matchedBuyOrderPreFeesAmount.id,
          matchedBuyOrderPostFeesAmount.id
        ];

        await settledTransactionWithBuyOrder.save();
        await settledTransactionWithBuyOrder.populate("orders");

        // Settled transaction (eligible to upBy calculation)
        const settledTransactionWithSellOrder = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const matchedSellOrder = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
          transaction: settledTransactionWithBuyOrder.id,
          side: "Sell",
          consideration: {
            currency: "GBP",
            amount: ORDER_ASSET_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-sell-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        settledTransactionWithSellOrder.orders = [matchedSellOrder.id];
        await settledTransactionWithSellOrder.save();
        await settledTransactionWithSellOrder.populate("orders");

        // Pending transaction (NOT eligible to upBy calculation)
        const pendingTransactionWithBuyOrder = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending"
        });
        const pendingBuyOrder = await buildOrder({
          status: "Pending",
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
          transaction: settledTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: ASSET_TRANSACTION_AMOUNT,
            amount: ASSET_TRANSACTION_AMOUNT - 1
          }
        });
        pendingTransactionWithBuyOrder.orders = [pendingBuyOrder.id];
        await pendingTransactionWithBuyOrder.save();
        await pendingTransactionWithBuyOrder.populate("orders");

        // Settled rebalance transaction (with a buy order) - will use original amount
        // because we have execution spread fee
        const settledRebalanceTransactionWithBuyOrder = await buildRebalanceTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          rebalanceStatus: "Settled"
        });
        await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
          transaction: settledRebalanceTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: REBALANCE_BUY_AMOUNT,
            amount: REBALANCE_BUY_AMOUNT - 1
          },
          providers: {
            wealthkernel: {
              id: "matched-rebalance-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          },
          fees: {
            fx: {
              amount: 0,
              currency: "GBP"
            },
            executionSpread: {
              amount: 1,
              currency: "GBP"
            }
          }
        });
        await settledRebalanceTransactionWithBuyOrder.populate("orders");

        // Another settled rebalance transaction (with a sell order)
        const settledRebalanceTransactionWithSellOrder = await buildRebalanceTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          rebalanceStatus: "Settled"
        });
        await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
          transaction: settledRebalanceTransactionWithSellOrder.id,
          side: "Sell",
          consideration: {
            currency: "GBP",
            amount: REBALANCE_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-rebalance-sell-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        await settledRebalanceTransactionWithSellOrder.populate("orders");

        const subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: user.id
        });

        // Pending charge transaction with unmatched orders (should not affect calculation)
        const chargeTransactionWithUnmatchedOrders = await buildChargeTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          subscription: subscription.id,
          chargeMethod: "combined",
          status: "Pending"
        });
        await buildOrder({
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
          transaction: chargeTransactionWithUnmatchedOrders.id,
          side: "Sell",
          status: "Pending",
          consideration: {
            currency: "GBP",
            originalAmount: CHARGE_SELL_AMOUNT,
            amount: CHARGE_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "open-charge-buy-order-id",
              status: "Open",
              submittedAt: new Date()
            }
          }
        });
        await chargeTransactionWithUnmatchedOrders.populate("orders");

        // Pending charge transaction with matched orders
        const chargeTransactionWithMatchedOrders = await buildChargeTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          subscription: subscription.id,
          chargeMethod: "combined",
          status: "Pending"
        });
        await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
          transaction: chargeTransactionWithMatchedOrders.id,
          side: "Sell",
          consideration: {
            currency: "GBP",
            originalAmount: CHARGE_SELL_AMOUNT,
            amount: CHARGE_SELL_AMOUNT
          },
          providers: {
            wealthkernel: {
              id: "matched-charge-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });
        await chargeTransactionWithMatchedOrders.populate("orders");

        // Settled reward (eligible to upBy calculation)
        const settledReward = await buildReward({
          targetUser: user.id,
          asset: ACTIVE_ASSET_ID,
          consideration: {
            currency: "GBP",
            amount: REWARD_AMOUNT
          },
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
          }
        });

        await buildReward({
          targetUser: user.id,
          status: "Pending",
          asset: ACTIVE_ASSET_ID,
          consideration: {
            currency: "GBP",
            amount: REWARD_AMOUNT
          }
        });

        transactions.assetTransactions = [settledTransactionWithBuyOrder, settledTransactionWithSellOrder];
        transactions.chargeTransactions = [chargeTransactionWithMatchedOrders];
        transactions.rebalanceTransactions = [
          settledRebalanceTransactionWithSellOrder,
          settledRebalanceTransactionWithBuyOrder
        ];
        transactions.rewards = [settledReward];
      });

      it("should calculate the up by value", () => {
        const holding = portfolio.holdings[0];
        const holdingUpBy = PortfolioService.getHoldingUpByValue("GBP", holding, transactions);
        expect(holdingUpBy).toBe(
          new Decimal(HOLDING_QUANTITY)
            .mul(PRICE)
            .plus(ORDER_ASSET_SELL_AMOUNT / 100)
            .plus(REBALANCE_SELL_AMOUNT / 100)
            .plus(CHARGE_SELL_AMOUNT / 100)
            // for both transactions we should take into account the order amount pre-fees for buys
            .minus((2 * ORDER_ASSET_BUY_AMOUNT) / 100)
            .minus(REWARD_AMOUNT / 100)
            .minus(REBALANCE_BUY_AMOUNT / 100)
            .toNumber()
        );
      });
    });

    describe("when the asset is first bought and has FX fees and Real Time Execution fees", () => {
      const PRICE = 50;
      const ORDER_ASSET_BUY_AMOUNT_PRE_FEES = 1000;
      const REAL_TIME_EXECUTION_FEE = 100;
      const FX_FEE = 10;
      const ORDER_ASSET_BUY_AMOUNT_POST_FEES = ORDER_ASSET_BUY_AMOUNT_PRE_FEES - REAL_TIME_EXECUTION_FEE - FX_FEE;
      const HOLDING_QUANTITY_AFTER_BUY = ORDER_ASSET_BUY_AMOUNT_POST_FEES / (PRICE * 100);

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };

      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: user.id
        });
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_eu", HOLDING_QUANTITY_AFTER_BUY, { price: PRICE })]
        });
        await portfolio.populate([
          { path: "currentTicker" },
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ]);

        // Settled transaction (eligible to upBy calculation)
        const settledTransactionWithBuyOrder = await buildAssetTransaction({
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: ORDER_ASSET_BUY_AMOUNT_PRE_FEES,
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const matchedBuyOrder = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: settledTransactionWithBuyOrder.id,
          side: "Buy",
          consideration: {
            currency: "GBP",
            originalAmount: ORDER_ASSET_BUY_AMOUNT_PRE_FEES,
            amount: ORDER_ASSET_BUY_AMOUNT_POST_FEES
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          },
          fees: {
            fx: {
              amount: FX_FEE / 100,
              currency: "GBP"
            },
            realtimeExecution: {
              amount: REAL_TIME_EXECUTION_FEE / 100,
              currency: "GBP"
            }
          }
        });
        settledTransactionWithBuyOrder.orders = [matchedBuyOrder.id];

        await settledTransactionWithBuyOrder.save();
        await settledTransactionWithBuyOrder.populate("orders");

        transactions.assetTransactions = [settledTransactionWithBuyOrder];
      });

      it("should properly calculate up by value for asset 'equities_eu' taking into account the fx fee, but not the real time execution fee", () => {
        const holding = portfolio.holdings[0];
        const holdingUpBy = PortfolioService.getHoldingUpByValue("GBP", holding, transactions);
        expect(holdingUpBy).toBe(
          new Decimal(HOLDING_QUANTITY_AFTER_BUY)
            .mul(PRICE)
            // For buys we use the amount pre-fees, but remove the real time commission fee
            .minus((ORDER_ASSET_BUY_AMOUNT_PRE_FEES - REAL_TIME_EXECUTION_FEE) / 100)
            .toNumber()
        );
      });
    });

    describe("when the asset is sold and has FX fees and Real Time Execution fees", () => {
      const PRICE = 50;
      const HOLDING_QUANTITY = 1;
      const ORDER_ASSET_SELL_AMOUNT_PRE_FEES = 1000;
      const REAL_TIME_EXECUTION_FEE = 100;
      const FX_FEE = 10;
      const ORDER_ASSET_SELL_AMOUNT_POST_FEES =
        ORDER_ASSET_SELL_AMOUNT_PRE_FEES - REAL_TIME_EXECUTION_FEE - FX_FEE;

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };

      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: user.id
        });
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_eu", HOLDING_QUANTITY, { price: PRICE })]
        });
        await portfolio.populate([
          { path: "currentTicker" },
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ]);

        // Settled transaction (eligible to upBy calculation)
        const settledTransactionWithSellOrder = await buildAssetTransaction({
          portfolioTransactionCategory: "sell",
          originalInvestmentAmount: ORDER_ASSET_SELL_AMOUNT_PRE_FEES,
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const matchedSellOrder = await buildOrder({
          status: "Matched",
          isin: ASSET_CONFIG["equities_eu"].isin,
          transaction: settledTransactionWithSellOrder.id,
          side: "Sell",
          consideration: {
            currency: "GBP",
            originalAmount: ORDER_ASSET_SELL_AMOUNT_PRE_FEES,
            amount: ORDER_ASSET_SELL_AMOUNT_POST_FEES
          },
          providers: {
            wealthkernel: {
              id: "matched-buy-order-id",
              status: "Matched",
              submittedAt: new Date()
            }
          },
          fees: {
            fx: {
              amount: FX_FEE / 100,
              currency: "GBP"
            },
            realtimeExecution: {
              amount: REAL_TIME_EXECUTION_FEE / 100,
              currency: "GBP"
            }
          }
        });
        settledTransactionWithSellOrder.orders = [matchedSellOrder.id];

        await settledTransactionWithSellOrder.save();
        await settledTransactionWithSellOrder.populate("orders");

        transactions.assetTransactions = [settledTransactionWithSellOrder];
      });

      it("should properly calculate up by value for asset 'equities_eu' taking into account the fx_fee, but not the real time execution fee", () => {
        const holding = portfolio.holdings[0];
        const holdingUpBy = PortfolioService.getHoldingUpByValue("GBP", holding, transactions);
        expect(holdingUpBy).toBe(
          new Decimal(HOLDING_QUANTITY)
            .mul(PRICE)
            // For sells we use the amount post-fees, but we add back the real time commission fee
            .plus((ORDER_ASSET_SELL_AMOUNT_POST_FEES + REAL_TIME_EXECUTION_FEE) / 100)
            .toNumber()
        );
      });
    });
  });

  describe("getPortfolioMWRR", () => {
    describe("when the portfolio is real", () => {
      let portfolio: PortfolioDocument;

      describe("and rewards do not exist", () => {
        describe("and tenor is passed", () => {
          const transactions: {
            assetTransactions: AssetTransactionDocument[];
            chargeTransactions: ChargeTransactionDocument[];
            rebalanceTransactions: RebalanceTransactionDocument[];
            dividendTransactions: DividendTransactionDocument[];
            rewards: RewardDocument[];
          } = {
            assetTransactions: [],
            chargeTransactions: [],
            rebalanceTransactions: [],
            dividendTransactions: [],
            rewards: []
          };

          beforeEach(async () => {
            const user = await buildUser();
            portfolio = await buildPortfolio({
              owner: user.id,
              mode: PortfolioModeEnum.REAL,
              holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: 206.69 })]
            });
            await buildIntraDayPortfolioTicker({
              portfolio: portfolio.id,
              pricePerCurrency: { GBP: 206.69 },
              timestamp: new Date(2021, 10, 25)
            });
            const ordersConfig = [
              {
                side: "Buy",
                amount: 50,
                updatedAt: new Date(2021, 7, 18)
              },
              {
                side: "Buy",
                amount: 50,
                updatedAt: new Date(2021, 8, 10)
              },
              {
                side: "Buy",
                amount: 50,
                updatedAt: new Date(2021, 8, 29)
              },
              {
                side: "Buy",
                amount: 50.14,
                updatedAt: new Date(2021, 10, 2)
              }
            ];

            const subscription = await buildSubscription({
              category: "FeeBasedSubscription",
              active: true,
              price: "free_monthly",
              owner: user.id
            });

            // Pending charge transaction with unmatched orders (should not affect calculation)
            const chargeTransactionWithUnmatchedOrders = await buildChargeTransaction({
              owner: user.id,
              portfolio: portfolio.id,
              subscription: subscription.id,
              chargeMethod: "combined",
              status: "Pending",
              createdAt: new Date(2021, 11, 2)
            });
            await buildOrder({
              status: "Pending",
              providers: {
                wealthkernel: {
                  status: "Pending",
                  id: faker.string.uuid(),
                  submittedAt: new Date(2021, 11, 2)
                }
              },
              isin: ASSET_CONFIG["equities_eu"].isin,
              transaction: chargeTransactionWithUnmatchedOrders.id,
              side: "Sell",
              consideration: {
                currency: "GBP",
                amount: 100
              }
            });
            await chargeTransactionWithUnmatchedOrders.populate("orders");

            const transaction = await buildAssetTransaction({
              owner: user.id,
              status: "Settled"
            });
            const orders = await Promise.all(
              ordersConfig.map(({ side, amount, updatedAt }) =>
                buildOrder({
                  status: "Matched",
                  providers: {
                    wealthkernel: {
                      status: "Matched",
                      id: faker.string.uuid(),
                      submittedAt: updatedAt
                    }
                  },
                  transaction: transaction.id,
                  side: side as "Buy" | "Sell",
                  consideration: {
                    originalAmount: amount * 100,
                    amount: amount * 100 - 1,
                    currency: "GBP"
                  },
                  fees: {
                    executionSpread: {
                      amount: 1,
                      currency: "GBP"
                    },
                    fx: {
                      amount: 0,
                      currency: "GBP"
                    }
                  },
                  updatedAt,
                  filledAt: updatedAt
                })
              )
            );
            transaction.orders = orders.map((order) => order.id);
            await transaction.save();
            await transaction.populate("orders");

            transactions.assetTransactions = [transaction];
            transactions.chargeTransactions = [chargeTransactionWithUnmatchedOrders];
          });

          it("should calculate successfully portfolio return value", async () => {
            portfolio = (await Portfolio.findById(portfolio.id).populate([
              { path: "currentTicker" }
            ])) as PortfolioDocument;
            const portfolioReturn = await PortfolioService.getPortfolioMWRR(
              portfolio,
              transactions,
              TenorEnum.ALL_TIME
            );
            expect(portfolioReturn).toEqual({ max: 0.03272709103627453 });
          });
        });

        describe("and tenor is not passed", () => {
          const transactions: {
            assetTransactions: AssetTransactionDocument[];
            chargeTransactions: ChargeTransactionDocument[];
            rebalanceTransactions: RebalanceTransactionDocument[];
            dividendTransactions: DividendTransactionDocument[];
            rewards: RewardDocument[];
          } = {
            assetTransactions: [],
            chargeTransactions: [],
            rebalanceTransactions: [],
            dividendTransactions: [],
            rewards: []
          };

          beforeEach(async () => {
            const user = await buildUser();
            portfolio = await buildPortfolio({
              owner: user.id,
              mode: PortfolioModeEnum.REAL,
              holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: 206.69 })]
            });
            await buildIntraDayPortfolioTicker({
              portfolio: portfolio.id,
              pricePerCurrency: { GBP: 206.69 },
              timestamp: new Date(2021, 10, 25)
            });
            const ordersConfig = [
              {
                side: "Buy",
                amount: 50,
                updatedAt: new Date(2021, 7, 18)
              },
              {
                side: "Buy",
                amount: 50,
                updatedAt: new Date(2021, 8, 10)
              },
              {
                side: "Buy",
                amount: 50,
                updatedAt: new Date(2021, 8, 29)
              },
              {
                side: "Buy",
                amount: 50.14,
                updatedAt: new Date(2021, 10, 2)
              }
            ];

            const subscription = await buildSubscription({
              category: "FeeBasedSubscription",
              active: true,
              price: "free_monthly",
              owner: user.id
            });

            // Pending charge transaction with unmatched orders (should not affect calculation)
            const chargeTransactionWithUnmatchedOrders = await buildChargeTransaction({
              owner: user.id,
              portfolio: portfolio.id,
              subscription: subscription.id,
              chargeMethod: "combined",
              status: "Pending",
              createdAt: new Date()
            });
            await buildOrder({
              isin: ASSET_CONFIG["equities_eu"].isin,
              transaction: chargeTransactionWithUnmatchedOrders.id,
              side: "Sell",
              consideration: {
                currency: "GBP",
                amount: 100
              },
              status: "Pending",
              providers: {
                wealthkernel: {
                  id: "open-charge-buy-order-id",
                  status: "Open",
                  submittedAt: new Date()
                }
              }
            });
            await chargeTransactionWithUnmatchedOrders.populate("orders");

            const transaction = await buildAssetTransaction({
              owner: user.id,
              status: "Settled"
            });
            const orders = await Promise.all(
              ordersConfig.map(({ side, amount, updatedAt }) =>
                buildOrder({
                  status: "Matched",
                  providers: {
                    wealthkernel: {
                      status: "Matched",
                      id: faker.string.uuid(),
                      submittedAt: updatedAt
                    }
                  },
                  filledAt: updatedAt,
                  transaction: transaction.id,
                  side: side as "Buy" | "Sell",
                  consideration: {
                    amount: amount * 100 - 1,
                    originalAmount: amount * 100,
                    currency: "GBP"
                  },
                  fees: {
                    executionSpread: {
                      amount: 1,
                      currency: "GBP"
                    },
                    fx: {
                      amount: 0,
                      currency: "GBP"
                    }
                  },
                  updatedAt
                })
              )
            );
            transaction.orders = orders.map((order) => order.id);
            await transaction.save();
            await transaction.populate("orders");

            transactions.assetTransactions = [transaction];
            transactions.chargeTransactions = [chargeTransactionWithUnmatchedOrders];
          });

          it("should calculate successfully portfolio return value", async () => {
            portfolio = (await Portfolio.findById(portfolio.id).populate([
              { path: "currentTicker" }
            ])) as PortfolioDocument;
            const portfolioReturn = await PortfolioService.getPortfolioMWRR(portfolio, transactions);
            expect(portfolioReturn).toMatchObject({
              today: 0,
              "1d": 0,
              "1w": 0,
              "1m": 0,
              "3m": 0,
              "6m": 0,
              "1y": 0,
              max: 0.03272709103627453
            });
          });
        });
      });

      describe("and rewards also exist", () => {
        const transactions: {
          assetTransactions: AssetTransactionDocument[];
          chargeTransactions: ChargeTransactionDocument[];
          rebalanceTransactions: RebalanceTransactionDocument[];
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          assetTransactions: [],
          chargeTransactions: [],
          rebalanceTransactions: [],
          dividendTransactions: [],
          rewards: []
        };

        beforeEach(async () => {
          const user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: 206.69 / 100 })]
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: 206.69 / 100 },
            timestamp: new Date(2021, 10, 25)
          });
          const ordersConfig = [
            {
              side: "Buy",
              amount: 50,
              updatedAt: new Date(2021, 7, 18)
            },
            {
              side: "Buy",
              amount: 50,
              updatedAt: new Date(2021, 8, 10)
            },
            {
              side: "Buy",
              amount: 50,
              updatedAt: new Date(2021, 8, 29)
            },
            {
              side: "Buy",
              amount: 50.14,
              updatedAt: new Date(2021, 10, 2)
            }
          ];

          const reward = await buildReward({
            targetUser: user.id,
            consideration: { currency: "GBP", amount: 14 },
            status: "Settled",
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            updatedAt: new Date(2021, 9, 10)
          });

          const transaction = await buildAssetTransaction({
            owner: user.id,
            status: "Settled"
          });
          const orders = await Promise.all(
            ordersConfig.map(({ side, amount, updatedAt }) =>
              buildOrder({
                status: "Matched",
                providers: {
                  wealthkernel: {
                    status: "Matched",
                    id: faker.string.uuid(),
                    submittedAt: updatedAt
                  }
                },
                filledAt: updatedAt,
                transaction: transaction.id,
                side: side as "Buy" | "Sell",
                consideration: {
                  amount: amount - 1,
                  originalAmount: amount,
                  currency: "GBP"
                },
                fees: {
                  executionSpread: {
                    amount: 1,
                    currency: "GBP"
                  },
                  fx: {
                    amount: 0,
                    currency: "GBP"
                  }
                },
                updatedAt
              })
            )
          );
          transaction.orders = orders.map((order) => order.id);
          await transaction.save();
          await transaction.populate("orders");

          transactions.assetTransactions = [transaction];
          transactions.rewards = [reward];
        });

        it("should calculate successfully portfolio return value", async () => {
          portfolio = (await Portfolio.findById(portfolio.id).populate([
            { path: "currentTicker" }
          ])) as PortfolioDocument;
          const portfolioReturn = await PortfolioService.getPortfolioMWRR(
            portfolio,
            transactions,
            TenorEnum.ALL_TIME
          );
          expect(portfolioReturn).toEqual({ max: -0.0347903240870458 });
        });
      });
    });

    describe("when the portfolio is not real", () => {
      let portfolio: PortfolioDocument;
      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };

      beforeEach(async () => {
        const user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id, mode: "VIRTUAL" });
      });

      it("should throw error", async () => {
        await expect(async () => await PortfolioService.getPortfolioMWRR(portfolio, transactions)).rejects.toThrow(
          new InternalServerError("Portfolio returns can only be calculated for real portfolios")
        );
      });
    });

    describe("when requested with a TODAY tenor but the portfolio has no ticker for the day", () => {
      const START_INTRADAY_PORTFOLIO_VALUE = 100;

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = "2024-03-31";
        Date.now = jest.fn(() => +new Date(TODAY));

        user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          }
        });
        await buildIntraDayPortfolioTicker({
          timestamp: DateUtil.getDateOfDaysAgo(new Date(TODAY), 1),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        });
      });

      it("should return 0 returns for today", async () => {
        portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
        const mwrr = await PortfolioService.getPortfolioMWRR(portfolio, transactions, TenorEnum.TODAY);
        expect(mwrr).toEqual({ today: 0 });
      });
    });

    describe("when requested with a TODAY tenor and the portfolio has multiple tickers for the day", () => {
      const START_INTRADAY_PORTFOLIO_VALUE = 100;
      const END_INTRADAY_PORTFOLIO_VALUE = 150;

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = new Date("2024-03-31T04:20:00Z");
        const LATER_TODAY = new Date("2024-03-31T12:20:00Z");
        Date.now = jest.fn(() => DateUtil.getStartOfDay(TODAY).getTime());

        user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: END_INTRADAY_PORTFOLIO_VALUE })],
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          }
        });
        await buildIntraDayPortfolioTicker({
          timestamp: new Date(TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        });
        await buildIntraDayPortfolioTicker({
          timestamp: new Date(LATER_TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: END_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: END_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: END_INTRADAY_PORTFOLIO_VALUE
          }
        });

        const ordersConfig = [
          {
            side: "Buy",
            amount: 400,
            updatedAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
          }
        ];
        const transaction = await buildAssetTransaction({
          owner: user.id,
          status: "Settled"
        });
        const orders = await Promise.all(
          ordersConfig.map(({ side, amount, updatedAt }) =>
            buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: {
                  status: "Matched",
                  id: faker.string.uuid(),
                  submittedAt: updatedAt
                }
              },
              filledAt: updatedAt,
              transaction: transaction.id,
              side: side as "Buy" | "Sell",
              consideration: {
                amount: amount - 1,
                originalAmount: amount,
                currency: "GBP"
              },
              fees: {
                executionSpread: {
                  amount: 1,
                  currency: "GBP"
                },
                fx: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              updatedAt
            })
          )
        );
        transaction.orders = orders.map((order) => order.id);
        await transaction.save();
        await transaction.populate("orders");

        transactions.assetTransactions = [transaction];
      });

      it("should return the correct MWRR for today", async () => {
        portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
        const mwrr = await PortfolioService.getPortfolioMWRR(portfolio, transactions, TenorEnum.TODAY);
        expect(mwrr).toEqual({
          today: 0.5
        });
      });
    });
  });

  describe("getPortfolioReturnsAllTenors", () => {
    describe("when portfolio MWRR & value has been cached", () => {
      const transactionsForReturns: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      const CACHED_MWRR = {
        max: 10,
        "1y": 5,
        "6m": 2,
        "3m": 1,
        "1m": 2,
        "1w": 4
      };
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [await buildHoldingDTO(true, "equities_apple", 1, { price: 200 })],
          mode: PortfolioModeEnum.REAL
        });
        // daily return = (200 - 100) / 100 = 1
        const yesterdayTicker = await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: 100 },
          timestamp: DateUtil.getYesterday(new Date(2021, 10, 25))
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: 200 },
          timestamp: new Date(2021, 10, 25)
        });
        const ordersConfig = [
          {
            side: "Buy",
            amount: 50,
            updatedAt: new Date(2021, 7, 18)
          },
          {
            side: "Buy",
            amount: 50,
            updatedAt: new Date(2021, 8, 10)
          },
          {
            side: "Buy",
            amount: 50,
            updatedAt: new Date(2021, 8, 29)
          },
          {
            side: "Buy",
            amount: 50.14,
            updatedAt: new Date(2021, 10, 2)
          }
        ];
        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const orders = await Promise.all(
          ordersConfig.map(({ side, amount, updatedAt }) =>
            buildOrder({
              transaction: transaction.id,
              side: side as "Buy" | "Sell",
              consideration: {
                amount: amount * 100,
                currency: "GBP"
              },
              updatedAt
            })
          )
        );
        transaction.orders = orders.map((order) => order.id);
        await transaction.save();
        await transaction.populate("orders");
        transactionsForReturns.assetTransactions = [transaction];
        await RedisClientService.Instance.set(`portfolios:mwrr:${portfolio.id}`, CACHED_MWRR);
        await RedisClientService.Instance.set(
          `portfolios:value_at_mwrr:${portfolio.id}`,
          yesterdayTicker.getPrice("GBP")
        );
      });

      it("should calculate successfully the portfolio return value", async () => {
        const portfolioReturnsByTenor = await PortfolioService.getPortfolioReturnsAllTenors(
          portfolio,
          transactionsForReturns
        );

        // (1 + CACHED_MWRR) * (1 + daily return) - 1
        // daily return = (200 - 100) / 100 = 1
        expect(portfolioReturnsByTenor).toMatchObject(
          expect.objectContaining({
            "1w": (1 + CACHED_MWRR["1w"]) * 2 - 1,
            "1m": (1 + CACHED_MWRR["1m"]) * 2 - 1,
            "3m": (1 + CACHED_MWRR["3m"]) * 2 - 1,
            "6m": (1 + CACHED_MWRR["6m"]) * 2 - 1,
            "1y": (1 + CACHED_MWRR["1y"]) * 2 - 1,
            max: (1 + CACHED_MWRR["max"]) * 2 - 1
          })
        );
      });
    });

    describe("when portfolio MWRR has not been cached", () => {
      const transactionsForReturns: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [await buildHoldingDTO(true, "equities_apple", 1, { price: 200 })],
          mode: PortfolioModeEnum.REAL
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: 100 },
          timestamp: DateUtil.getYesterday(new Date(2021, 10, 25))
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: 200 },
          timestamp: new Date(2021, 10, 25)
        });
        const ordersConfig = [
          {
            side: "Buy",
            amount: 50,
            updatedAt: new Date(2021, 7, 18)
          },
          {
            side: "Buy",
            amount: 50,
            updatedAt: new Date(2021, 8, 10)
          },
          {
            side: "Buy",
            amount: 50,
            updatedAt: new Date(2021, 8, 29)
          },
          {
            side: "Buy",
            amount: 50.14,
            updatedAt: new Date(2021, 10, 2)
          }
        ];

        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const orders = await Promise.all(
          ordersConfig.map(({ side, amount, updatedAt }) =>
            buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: {
                  status: "Matched",
                  id: faker.string.uuid(),
                  submittedAt: updatedAt
                }
              },
              filledAt: updatedAt,
              transaction: transaction.id,
              side: side as "Buy" | "Sell",
              consideration: {
                amount: amount * 100,
                currency: "GBP"
              },
              updatedAt
            })
          )
        );
        transaction.orders = orders.map((order) => order.id);
        await transaction.save();
        await transaction.populate("orders");

        transactionsForReturns.assetTransactions = [transaction];
      });

      it("should calculate successfully the portfolio return value", async () => {
        const portfolioReturnsByTenor = await PortfolioService.getPortfolioReturnsAllTenors(
          portfolio,
          transactionsForReturns
        );

        expect(portfolioReturnsByTenor).toMatchObject(
          expect.objectContaining({
            "1w": 0,
            "1m": 0,
            "3m": 0,
            "6m": 0,
            "1y": 0,
            max: -0.0006995103427600676
          })
        );
      });
    });
  });

  describe("calculateUpByValues", () => {
    describe("when requested with an ALL_TIME tenor", () => {
      describe("when user has a portfolio buy transaction", () => {
        const PORTFOLIO_VALUE = 110;
        const BUY_AMOUNT = 100;
        const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

        const transactions: {
          assetTransactions: AssetTransactionDocument[];
          chargeTransactions: ChargeTransactionDocument[];
          rebalanceTransactions: RebalanceTransactionDocument[];
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          assetTransactions: [],
          chargeTransactions: [],
          rebalanceTransactions: [],
          dividendTransactions: [],
          rewards: []
        };
        let user: UserDocument;
        let portfolio: PortfolioDocument;

        beforeEach(async () => {
          user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            cash: {
              GBP: { available: 0, reserved: 0, settled: 0 }
            },
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          });
          const assetTransaction = await buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: SETTLED_DATE
          });
          const order = await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: SETTLED_DATE
              }
            },
            filledAt: SETTLED_DATE,
            transaction: assetTransaction.id,
            side: "Buy",
            consideration: {
              originalAmount: BUY_AMOUNT * 100,
              amount: BUY_AMOUNT * 100,
              currency: "GBP"
            },
            updatedAt: SETTLED_DATE
          });
          assetTransaction.orders = [order];
          await assetTransaction.save();

          transactions.assetTransactions = [assetTransaction];
        });

        it("should return an up-by number value for all time", async () => {
          const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
          expect(upBy).toEqual({ max: 10 }); // 110 - 100
        });
      });

      describe("when user has an asset buy transaction", () => {
        const PORTFOLIO_VALUE = 110;
        const BUY_AMOUNT = 100;

        const transactions: {
          assetTransactions: AssetTransactionDocument[];
          chargeTransactions: ChargeTransactionDocument[];
          rebalanceTransactions: RebalanceTransactionDocument[];
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          assetTransactions: [],
          chargeTransactions: [],
          rebalanceTransactions: [],
          dividendTransactions: [],
          rewards: []
        };

        let user: UserDocument;
        let portfolio: PortfolioDocument;

        beforeEach(async () => {
          user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            cash: {
              GBP: { available: 0, reserved: 0, settled: 0 }
            },
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          });

          // Create an asset buy transaction for £100
          const assetBuyTransaction = await buildAssetTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            portfolioTransactionCategory: "update",
            consideration: { currency: "GBP" },
            settledAt: new Date("2022-07-17T11:00:00Z")
          });
          assetBuyTransaction.orders = [
            await buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
              },
              side: "Buy",
              transaction: assetBuyTransaction.id,
              isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
              consideration: {
                originalAmount: BUY_AMOUNT * 100,
                amountSubmitted: BUY_AMOUNT * 100,
                amount: BUY_AMOUNT * 100,
                currency: "GBP"
              },
              quantity: 1
            })
          ];

          await assetBuyTransaction.save();
          transactions.assetTransactions = [assetBuyTransaction];
        });

        it("should return an up-by number value for all time", async () => {
          const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
          expect(upBy).toEqual({ max: 10 }); // 110 - 100
        });
      });

      describe("when user has an old portfolio update transaction with multiple orders", () => {
        const PORTFOLIO_VALUE = 210;
        const BUY_AMOUNT = 100;

        const transactions: {
          assetTransactions: AssetTransactionDocument[];
          chargeTransactions: ChargeTransactionDocument[];
          rebalanceTransactions: RebalanceTransactionDocument[];
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          assetTransactions: [],
          chargeTransactions: [],
          rebalanceTransactions: [],
          dividendTransactions: [],
          rewards: []
        };
        let user: UserDocument;
        let portfolio: PortfolioDocument;

        beforeEach(async () => {
          user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            cash: {
              GBP: { available: 0, reserved: 0, settled: 0 }
            },
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          });

          const portfolioUpdateTransaction = await buildAssetTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            portfolioTransactionCategory: "update",
            consideration: { currency: "GBP" },
            settledAt: new Date("2022-07-17T11:00:00Z")
          });
          portfolioUpdateTransaction.orders = [
            await buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
              },
              side: "Buy",
              transaction: portfolioUpdateTransaction.id,
              isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
              consideration: {
                originalAmount: BUY_AMOUNT * 100,
                amountSubmitted: BUY_AMOUNT * 100,
                amount: BUY_AMOUNT * 100,
                currency: "GBP"
              },
              quantity: 1
            }),
            await buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
              },
              side: "Buy",
              transaction: portfolioUpdateTransaction.id,
              isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
              consideration: {
                originalAmount: BUY_AMOUNT * 100,
                amountSubmitted: BUY_AMOUNT * 100,
                amount: BUY_AMOUNT * 100,
                currency: "GBP"
              },
              quantity: 1
            })
          ];

          await portfolioUpdateTransaction.save();
          transactions.assetTransactions = [portfolioUpdateTransaction];
        });

        it("should return an up-by number value for all time", async () => {
          const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
          expect(upBy).toEqual({ max: 10 }); // 110 - 100
        });
      });

      describe("when user has multiple asset transactions", () => {
        const PORTFOLIO_VALUE = 110;
        const PORTFOLIO_BUY_AMOUNT = 100;
        const ASSET_BUY_AMOUNT = 100;
        const SELL_AMOUNT = 100;

        const transactions: {
          assetTransactions: AssetTransactionDocument[];
          chargeTransactions: ChargeTransactionDocument[];
          rebalanceTransactions: RebalanceTransactionDocument[];
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          assetTransactions: [],
          chargeTransactions: [],
          rebalanceTransactions: [],
          dividendTransactions: [],
          rewards: []
        };
        let user: UserDocument;
        let portfolio: PortfolioDocument;

        beforeEach(async () => {
          user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            cash: {
              GBP: { available: 0, reserved: 0, settled: 0 }
            },
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          });

          // Create a portfolio buy transaction for £100
          const assetTransaction1 = await buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: PORTFOLIO_BUY_AMOUNT * 100
            },
            originalInvestmentAmount: PORTFOLIO_BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: new Date("2022-07-17T11:00:00Z")
          });

          // Create an asset buy transaction for £100
          const assetBuyTransaction = await buildAssetTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            portfolioTransactionCategory: "update",
            consideration: { currency: "GBP" },
            settledAt: new Date("2022-07-17T11:00:00Z")
          });
          assetBuyTransaction.orders = [
            await buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
              },
              side: "Buy",
              transaction: assetBuyTransaction.id,
              isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
              consideration: {
                originalAmount: ASSET_BUY_AMOUNT * 100,
                amountSubmitted: ASSET_BUY_AMOUNT * 100,
                amount: ASSET_BUY_AMOUNT * 100,
                currency: "GBP"
              },
              quantity: 1
            })
          ];

          await assetBuyTransaction.save();

          // Create a portfolio sell transaction for £100
          const assetTransaction2 = await buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: SELL_AMOUNT * 100
            },
            owner: user.id,
            portfolioTransactionCategory: "sell",
            status: "Settled",
            settledAt: new Date("2022-07-17T11:00:00Z")
          });

          transactions.assetTransactions = [assetTransaction1, assetTransaction2, assetBuyTransaction];
        });

        it("should return an up-by number value for all time", async () => {
          const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
          expect(upBy).toEqual({ max: 10 }); // 110 + 100 - 200 (portfolio value + total sells - total buys)
        });
      });

      describe("when user has been charged from holdings", () => {
        const PORTFOLIO_VALUE = 109;
        const CHARGE_VALUE = 1;
        const BUY_AMOUNT = 100;
        const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

        const transactions: {
          assetTransactions: AssetTransactionDocument[];
          chargeTransactions: ChargeTransactionDocument[];
          rebalanceTransactions: RebalanceTransactionDocument[];
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          assetTransactions: [],
          chargeTransactions: [],
          rebalanceTransactions: [],
          dividendTransactions: [],
          rewards: []
        };
        let user: UserDocument;
        let portfolio: PortfolioDocument;

        beforeEach(async () => {
          user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            cash: {
              GBP: { available: 0, reserved: 0, settled: 0 }
            },
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
          });
          const subscription = await buildSubscription({
            owner: user.id,
            price: "free_monthly"
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          });
          const assetTransaction = await buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: SETTLED_DATE
          });
          const assetBuyOrder = await buildOrder({
            status: "Matched",
            side: "Buy",
            transaction: assetTransaction.id,
            consideration: {
              amount: BUY_AMOUNT * 100,
              currency: "GBP"
            },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: SETTLED_DATE
              }
            },
            filledAt: SETTLED_DATE
          });
          assetTransaction.orders = [assetBuyOrder];
          await assetTransaction.save();

          const chargeTransaction = await buildChargeTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              holdingsAmount: CHARGE_VALUE * 100,
              amount: CHARGE_VALUE * 100
            },
            owner: user.id,
            subscription: subscription.id,
            chargeMethod: "holdings",
            chargeType: "subscription",
            status: "Settled",
            settledAt: SETTLED_DATE
          });
          await buildOrder({
            status: "Matched",
            side: "Sell",
            transaction: chargeTransaction.id,
            consideration: {
              amount: CHARGE_VALUE * 100,
              currency: "GBP"
            },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: SETTLED_DATE
              }
            },
            filledAt: SETTLED_DATE
          });
          await chargeTransaction.populate("orders");

          transactions.assetTransactions = [assetTransaction];
          transactions.chargeTransactions = [chargeTransaction];
        });

        it("should return an up-by number value for all time", async () => {
          const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
          expect(upBy).toEqual({ max: 10 }); // 109 + 1 - 100
        });
      });

      describe("when user been charged from holdings but charge order has not been submitted to WK yet", () => {
        const PORTFOLIO_VALUE = 109;
        const CHARGE_VALUE = 1;
        const BUY_AMOUNT = 100;
        const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

        const transactions: {
          assetTransactions: AssetTransactionDocument[];
          chargeTransactions: ChargeTransactionDocument[];
          rebalanceTransactions: RebalanceTransactionDocument[];
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          assetTransactions: [],
          chargeTransactions: [],
          rebalanceTransactions: [],
          dividendTransactions: [],
          rewards: []
        };
        let user: UserDocument;
        let portfolio: PortfolioDocument;

        beforeEach(async () => {
          user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            cash: {
              GBP: { available: 0, reserved: 0, settled: 0 }
            },
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
          });
          const subscription = await buildSubscription({
            owner: user.id,
            price: "free_monthly"
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          });
          const assetTransaction = await buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: new Date("2022-07-17T11:00:00Z")
          });
          const assetBuyOrder = await buildOrder({
            status: "Matched",
            side: "Buy",
            transaction: assetTransaction.id,
            consideration: {
              amount: BUY_AMOUNT * 100,
              currency: "GBP"
            },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: SETTLED_DATE
              }
            },
            filledAt: SETTLED_DATE
          });
          assetTransaction.orders = [assetBuyOrder];
          await assetTransaction.save();

          const chargeTransaction = await buildChargeTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              holdingsAmount: CHARGE_VALUE * 100,
              amount: CHARGE_VALUE * 100
            },
            owner: user.id,
            subscription: subscription.id,
            chargeMethod: "holdings",
            chargeType: "subscription",
            status: "Pending",
            settledAt: new Date("2022-07-17T11:00:00Z")
          });
          await buildOrder({
            status: "Pending",
            side: "Sell",
            transaction: chargeTransaction.id,
            consideration: {
              amount: CHARGE_VALUE * 100,
              currency: "GBP"
            },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending",
                submittedAt: SETTLED_DATE
              }
            },
            filledAt: SETTLED_DATE
          });
          await chargeTransaction.populate("orders");

          transactions.assetTransactions = [assetTransaction];
          transactions.chargeTransactions = [chargeTransaction];
        });

        it("should return an up-by number value for all time", async () => {
          const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
          expect(upBy).toEqual({ max: 9 }); // 109 - 100
        });
      });

      describe("when user has been given a reward", () => {
        const PORTFOLIO_VALUE = 110;
        const REWARD_VALUE = 10;
        const BUY_AMOUNT = 90;
        const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

        const transactions: {
          assetTransactions: AssetTransactionDocument[];
          chargeTransactions: ChargeTransactionDocument[];
          rebalanceTransactions: RebalanceTransactionDocument[];
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          assetTransactions: [],
          chargeTransactions: [],
          rebalanceTransactions: [],
          dividendTransactions: [],
          rewards: []
        };
        let user: UserDocument;
        let portfolio: PortfolioDocument;

        beforeEach(async () => {
          user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            cash: {
              GBP: { available: 0, reserved: 0, settled: 0 }
            },
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          });
          const assetTransaction = await buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: SETTLED_DATE
          });
          const assetBuyOrder = await buildOrder({
            status: "Matched",
            side: "Buy",
            transaction: assetTransaction.id,
            consideration: {
              amount: BUY_AMOUNT * 100,
              currency: "GBP"
            },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: SETTLED_DATE
              }
            },
            filledAt: SETTLED_DATE
          });
          assetTransaction.orders = [assetBuyOrder];
          await assetTransaction.save();

          const reward = await buildReward({
            targetUser: user.id,
            referral: user.id,
            consideration: {
              currency: "GBP",
              amount: REWARD_VALUE * 100
            },
            asset: "equities_uk",
            quantity: 1,
            status: "Settled",
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            updatedAt: SETTLED_DATE
          });

          transactions.assetTransactions = [assetTransaction];
          transactions.rewards = [reward];
        });

        it("should return an up-by number value for all time", async () => {
          const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
          expect(upBy).toEqual({ max: 10 }); // 100 + 10 - 100
        });
      });

      describe("when user has received a dividend", () => {
        const PORTFOLIO_VALUE = 110;
        const DIVIDEND_VALUE = 1;
        const BUY_AMOUNT = 100;
        const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

        const transactions: {
          assetTransactions: AssetTransactionDocument[];
          chargeTransactions: ChargeTransactionDocument[];
          rebalanceTransactions: RebalanceTransactionDocument[];
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          assetTransactions: [],
          chargeTransactions: [],
          rebalanceTransactions: [],
          dividendTransactions: [],
          rewards: []
        };
        let user: UserDocument;
        let portfolio: PortfolioDocument;

        beforeEach(async () => {
          user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            cash: {
              GBP: { available: 0, reserved: 0, settled: 0 }
            },
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          });
          const assetTransaction = await buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: SETTLED_DATE
          });
          const assetBuyOrder = await buildOrder({
            status: "Matched",
            side: "Buy",
            transaction: assetTransaction.id,
            consideration: {
              amount: BUY_AMOUNT * 100,
              currency: "GBP"
            },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: SETTLED_DATE
              }
            },
            filledAt: SETTLED_DATE
          });
          assetTransaction.orders = [assetBuyOrder];
          await assetTransaction.save();

          const dividendTransaction = await buildDividendTransaction({
            portfolio: portfolio.id,
            consideration: {
              amount: DIVIDEND_VALUE * 100,
              currency: "GBP"
            },
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } },
            createdAt: SETTLED_DATE
          });

          transactions.assetTransactions = [assetTransaction];
          transactions.dividendTransactions = [dividendTransaction];
        });

        it("should return an up-by number value for all time", async () => {
          const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
          expect(upBy).toEqual({ max: 11 }); // 110 - 100 + 1
        });
      });
    });

    describe("when requested with a ONE_WEEK tenor and user has a portfolio buy transaction exactly 7 days ago", () => {
      const PORTFOLIO_VALUE = 110;
      const BUY_AMOUNT = 100;
      const SETTLED_DATE = new Date("2024-01-01T23:00:00Z");

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = "2024-01-08";
        Date.now = jest.fn(() => +new Date(TODAY));

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
        });

        const [assetTransaction] = await Promise.all([
          buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: SETTLED_DATE
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE },
            timestamp: SETTLED_DATE
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: 0 },
            timestamp: DateUtil.getStartOfDay(SETTLED_DATE)
          })
        ]);

        const assetBuyOrder = await buildOrder({
          status: "Matched",
          side: "Buy",
          transaction: assetTransaction.id,
          consideration: {
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        assetTransaction.orders = [assetBuyOrder];
        await assetTransaction.save();

        transactions.assetTransactions = [assetTransaction];
      });

      it("should return an up-by number value for one week", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ONE_WEEK);
        expect(upBy).toEqual({ "1w": 10 }); // 110 - 100
      });
    });

    describe("when requested with a ONE_WEEK tenor and intraday ticker exists at start of tenor", () => {
      const START_INTRADAY_PORTFOLIO_VALUE = 100;
      const START_DAILY_PORTFOLIO_VALUE = 105;
      const END_PORTFOLIO_VALUE = 110;

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const START_DATE = "2024-01-01";
        const TODAY = "2024-01-08";
        Date.now = jest.fn(() => +new Date(TODAY));

        user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: END_PORTFOLIO_VALUE })]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            timestamp: DateTime.fromJSDate(new Date(START_DATE))
              .setZone("America/New_York", { keepLocalTime: true })
              .set({
                hour: 13,
                minute: 0
              })
              .toJSDate(),
            portfolio: portfolio.id,
            pricePerCurrency: {
              USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
              EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
              GBP: START_INTRADAY_PORTFOLIO_VALUE
            }
          }),
          buildIntraDayPortfolioTicker({
            timestamp: DateTime.fromJSDate(new Date(START_DATE))
              .setZone("America/New_York", { keepLocalTime: true })
              .set({
                hour: 16,
                minute: 0
              })
              .toJSDate(),
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: START_DAILY_PORTFOLIO_VALUE }
          }),
          buildIntraDayPortfolioTicker({
            timestamp: DateTime.fromJSDate(new Date(TODAY))
              .setZone("America/New_York", { keepLocalTime: true })
              .set({
                hour: 16,
                minute: 0
              })
              .toJSDate(),
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: END_PORTFOLIO_VALUE }
          })
        ]);
      });

      it("should return an up-by number value for one week", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ONE_WEEK);
        expect(upBy).toEqual({ "1w": 10 }); // 110 - 100
      });
    });

    describe("when requested with a ONE_MONTH tenor and intraday ticker exists at start of tenor", () => {
      const START_INTRADAY_PORTFOLIO_VALUE = 100;
      const START_DAILY_PORTFOLIO_VALUE = 105;
      const END_PORTFOLIO_VALUE = 110;

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const START_DATE = "2024-03-01";
        const TODAY = "2024-03-31";
        Date.now = jest.fn(() => +new Date(TODAY));

        user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: END_PORTFOLIO_VALUE })]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            timestamp: DateTime.fromJSDate(new Date(START_DATE))
              .setZone("America/New_York", { keepLocalTime: true })
              .set({
                hour: 13,
                minute: 0
              })
              .toJSDate(),
            portfolio: portfolio.id,
            pricePerCurrency: {
              USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
              EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
              GBP: START_INTRADAY_PORTFOLIO_VALUE
            }
          }),
          buildIntraDayPortfolioTicker({
            timestamp: DateTime.fromJSDate(new Date(START_DATE))
              .setZone("America/New_York", { keepLocalTime: true })
              .set({
                hour: 16,
                minute: 0
              })
              .toJSDate(),
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: START_DAILY_PORTFOLIO_VALUE }
          }),
          buildIntraDayPortfolioTicker({
            timestamp: DateTime.fromJSDate(new Date(TODAY))
              .setZone("America/New_York", { keepLocalTime: true })
              .set({
                hour: 16,
                minute: 0
              })
              .toJSDate(),
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: END_PORTFOLIO_VALUE }
          })
        ]);
      });

      it("should return an up-by number value for one month", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ONE_MONTH);
        expect(upBy).toEqual({ "1m": 10 }); // 110 - 100
      });
    });

    describe("when requested with a TODAY tenor but the portfolio has no ticker for the day", () => {
      const START_INTRADAY_PORTFOLIO_VALUE = 100;

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = "2024-03-31";
        Date.now = jest.fn(() => +new Date(TODAY));

        user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          }
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            timestamp: DateUtil.getDateOfDaysAgo(new Date(TODAY), 1),
            portfolio: portfolio.id,
            pricePerCurrency: {
              USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
              EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
              GBP: START_INTRADAY_PORTFOLIO_VALUE
            }
          })
        ]);
      });

      it("should return 0 up-by for today", async () => {
        portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY);
        expect(upBy).toEqual({ today: 0 });
      });
    });

    describe("when requested with a TODAY tenor and the portfolio has multiple tickers for the day", () => {
      const START_INTRADAY_PORTFOLIO_VALUE = 100;
      const END_INTRADAY_PORTFOLIO_VALUE = 150;

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = new Date("2024-03-31T04:20:00Z");
        const LATER_TODAY = new Date("2024-03-31T12:20:00Z");
        Date.now = jest.fn(() => +TODAY);

        user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: END_INTRADAY_PORTFOLIO_VALUE })]
        });
        await buildIntraDayPortfolioTicker({
          timestamp: new Date(TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        });
        await buildIntraDayPortfolioTicker({
          timestamp: new Date(LATER_TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: END_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: END_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: END_INTRADAY_PORTFOLIO_VALUE
          }
        });

        const ordersConfig = [
          {
            side: "Buy",
            amount: 400,
            updatedAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
          }
        ];
        const transactionSettledYesterday = await buildAssetTransaction({
          owner: user.id,
          status: "Settled"
        });
        const orderFilledYesterday = await Promise.all(
          ordersConfig.map(({ side, amount, updatedAt }) =>
            buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: {
                  status: "Matched",
                  id: faker.string.uuid(),
                  submittedAt: updatedAt
                }
              },
              filledAt: updatedAt,
              transaction: transactionSettledYesterday.id,
              side: side as "Buy" | "Sell",
              consideration: {
                amount: amount - 1,
                originalAmount: amount,
                currency: "GBP"
              },
              fees: {
                executionSpread: {
                  amount: 1,
                  currency: "GBP"
                },
                fx: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              updatedAt
            })
          )
        );
        transactionSettledYesterday.orders = orderFilledYesterday.map((order) => order.id);
        await transactionSettledYesterday.save();
        await transactionSettledYesterday.populate("orders");

        transactions.assetTransactions = [transactionSettledYesterday];
      });

      it("should return the correct up-by for today", async () => {
        portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY);
        expect(upBy).toEqual({ today: 50 });
      });
    });

    describe("when requested with a TODAY tenor and there is a buy order with real time execution fees", () => {
      const START_INTRADAY_PORTFOLIO_VALUE = 10; // Whole
      const ORDER_BUY_AMOUNT_PRE_REAL_TIME_FEE = 400; // Cents
      const REAL_TIME_EXECUTION_FEE = 100; // Cents
      const END_INTRADAY_PORTFOLIO_VALUE = 15; // Whole

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = new Date("2024-03-31T04:20:00Z");
        const LATER_TODAY = new Date("2024-03-31T12:20:00Z");
        Date.now = jest.fn(() => +TODAY);

        user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: END_INTRADAY_PORTFOLIO_VALUE })]
        });
        await buildIntraDayPortfolioTicker({
          timestamp: new Date(TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        });
        await buildIntraDayPortfolioTicker({
          timestamp: new Date(LATER_TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: END_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: END_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: END_INTRADAY_PORTFOLIO_VALUE
          }
        });

        const ordersConfig = [
          {
            side: "Buy",
            amount: ORDER_BUY_AMOUNT_PRE_REAL_TIME_FEE,
            updatedAt: TODAY
          }
        ];
        const transactionSettledYesterday = await buildAssetTransaction({
          owner: user.id,
          status: "Settled"
        });
        const orderFilledToday = await Promise.all(
          ordersConfig.map(({ side, amount, updatedAt }) =>
            buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: {
                  status: "Matched",
                  id: faker.string.uuid(),
                  submittedAt: updatedAt
                }
              },
              filledAt: updatedAt,
              transaction: transactionSettledYesterday.id,
              side: side as "Buy" | "Sell",
              consideration: {
                amount: amount - REAL_TIME_EXECUTION_FEE,
                originalAmount: amount,
                currency: "GBP"
              },
              fees: {
                realtimeExecution: {
                  amount: REAL_TIME_EXECUTION_FEE / 100,
                  currency: "GBP"
                },
                fx: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              updatedAt
            })
          )
        );
        transactionSettledYesterday.orders = orderFilledToday.map((order) => order.id);
        await transactionSettledYesterday.save();
        await transactionSettledYesterday.populate("orders");

        transactions.assetTransactions = [transactionSettledYesterday];
      });

      it("should return the correct up-by for today, not taking into account real time execution fees", async () => {
        portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY);
        expect(upBy).toEqual({ today: 2 }); // 15 - 10 - 3
      });
    });
  });

  describe("getHoldingsExcludingPendingOrders", () => {
    const HOLDINGS_CONFIG: { quantity: number; assetCommonId: investmentUniverseConfig.AssetType }[] = [
      { quantity: 1, assetCommonId: "equities_microsoft" },
      { quantity: 2, assetCommonId: "equities_apple" }
    ];
    let holdings: HoldingsType[];
    let portfolio: PortfolioDocument;
    let user: UserDocument;
    let subscription: SubscriptionDocument;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });
      holdings = await Promise.all(
        HOLDINGS_CONFIG.map(
          async (config) => await buildHoldingDTO(true, config.assetCommonId, config.quantity, { price: 100 })
        )
      );
      portfolio = await buildPortfolio({
        owner: user.id,
        cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        mode: PortfolioModeEnum.REAL,
        holdings
      });
      subscription = await buildSubscription({ owner: user.id });
    });

    it("should return holdings with the quantity that matches the portfolio holdings when the user has no pending transactions with orders", async () => {
      const resultHoldings = (await PortfolioService.getHoldingsExcludingPendingOrders(portfolio)).map(
        (holding) => ({ quantity: holding.quantity, assetCommonId: holding.assetCommonId })
      );
      expect(resultHoldings).toMatchObject(HOLDINGS_CONFIG);
    });

    describe("when the user has a pending asset investment with a sell order", () => {
      beforeEach(async () => {
        await PortfolioService.submitOrder(
          portfolio.id.toString(),
          "equities_apple" as investmentUniverseConfig.AssetType,
          {
            side: "sell",
            quantity: 2
          },
          { executeEtfOrdersInRealtime: false }
        );
      });

      it("should return holdings excluding the quantity of the sold asset", async () => {
        const resultHoldings = (await PortfolioService.getHoldingsExcludingPendingOrders(portfolio)).map(
          (holding) => ({ quantity: holding.quantity, assetCommonId: holding.assetCommonId })
        );
        expect(resultHoldings).toMatchObject([
          { quantity: 1, assetCommonId: "equities_microsoft" },
          { quantity: 0, assetCommonId: "equities_apple" }
        ]);
      });
    });

    describe("when the user has a pending charge transaction with orders", () => {
      beforeEach(async () => {
        const chargeTransactionWithUnmatchedOrders = await buildChargeTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          subscription: subscription.id,
          chargeMethod: "holdings",
          status: "Pending"
        });
        await buildOrder({
          isin: ASSET_CONFIG["equities_microsoft" as investmentUniverseConfig.AssetType]?.isin as string,
          transaction: chargeTransactionWithUnmatchedOrders.id,
          side: "Sell",
          quantity: 1,
          consideration: {
            currency: "GBP",
            amount: 1 * 100
          }
        });
      });

      it("should return holdings excluding the quantity of the sold asset", async () => {
        const resultHoldings = (await PortfolioService.getHoldingsExcludingPendingOrders(portfolio)).map(
          (holding) => ({ quantity: holding.quantity, assetCommonId: holding.assetCommonId })
        );
        expect(resultHoldings).toMatchObject([
          { quantity: 0, assetCommonId: "equities_microsoft" },
          { quantity: 2, assetCommonId: "equities_apple" }
        ]);
      });
    });

    describe("when the user has a pending rebalance transaction", () => {
      beforeEach(async () => {
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          timestamp: new Date(),
          pricePerCurrency: {
            GBP: holdings
              .map((holding) => (holding.asset?.currentTicker.getPrice("GBP") as number) * holding.quantity)
              .reduce((a, b) => a + b, 0)
          }
        });
        const rebalanceDocument = await TransactionService.createRebalanceTransaction(portfolio, [
          {
            assetCommonId: "equities_microsoft",
            percentage: 100
          },
          {
            assetCommonId: "equities_apple",
            percentage: 0
          }
        ]);
        await RebalanceTransaction.findOneAndUpdate({ _id: rebalanceDocument.id, rebalanceStatus: "NotStarted" });
        await TransactionService.processRebalanceTransactions();
      });

      it("should return holdings excluding the quantity of the sold asset", async () => {
        const resultHoldings = (await PortfolioService.getHoldingsExcludingPendingOrders(portfolio)).map(
          (holding) => ({ quantity: holding.quantity, assetCommonId: holding.assetCommonId })
        );
        expect(resultHoldings).toMatchObject([
          { quantity: 1, assetCommonId: "equities_microsoft" },
          { quantity: 0, assetCommonId: "equities_apple" }
        ]);
      });
    });
  });

  describe("getAvailableHoldings", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    const WK_PORTFOLIO_ID = "WK_PORTFOLIO_ID";

    describe("when the portfolio has gifted holdings", () => {
      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, "equities_uk", 1),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        const giftedHoldings = new Map(
          Object.entries({
            equities_uk: [
              {
                quantity: 1,
                unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 1)
              }
            ],
            equities_us: [
              {
                quantity: 0.2,
                unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 1)
              },
              {
                quantity: 0.2,
                unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), 1)
              }
            ]
          })
        ) as Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings,
          giftedHoldings
        });
      });

      it("should return all holdings minus the gifted quantities", async () => {
        const availableHoldings = await PortfolioService.getAvailableHoldings(portfolio);

        expect(availableHoldings).toEqual([
          expect.objectContaining({
            assetCommonId: "equities_us",
            quantity: 0.8
          }),
          expect.objectContaining({
            assetCommonId: "equities_china",
            quantity: 1
          })
        ]);
      });
    });
  });

  describe("rebalancePortfolio", () => {
    const changeTargetAllocation = (currentTargetAllocation: InitialHoldingsAllocationType[]) => {
      currentTargetAllocation[0].percentage++;
      currentTargetAllocation[1].percentage--;
      return currentTargetAllocation;
    };
    let user: UserDocument;
    let realPortfolio: PortfolioDocument;

    beforeEach(async () => {
      jest.spyOn(eventEmitter, "emit");

      user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      await buildSubscription({ owner: user.id });

      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 3, price: 10, percentage: 25 },
        { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
        { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
        { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );

      realPortfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        holdings,
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });

      await buildIntraDayPortfolioTicker({
        portfolio: realPortfolio._id,
        timestamp: new Date(),
        pricePerCurrency: { GBP: 100 }
      });

      realPortfolio = await PortfolioService.getPortfolio(realPortfolio._id, true);
    });

    describe("when a rebalance transaction is already running", () => {
      let runningRebalanceTransaction: RebalanceTransactionDocument;

      beforeEach(async () => {
        runningRebalanceTransaction = await buildRebalanceTransaction({
          owner: user.id,
          portfolio: realPortfolio.id,
          rebalanceStatus: "PendingSell",
          targetAllocation: realPortfolio.initialHoldingsAllocation.map((allocation) => {
            return {
              percentage: allocation.percentage,
              assetCommonId: allocation.assetCommonId
            };
          })
        });
      });

      describe("and a rebalance transaction with 'NotStarted' rebalanceStatus exists", () => {
        beforeEach(async () => {
          await buildRebalanceTransaction({
            owner: user.id,
            portfolio: realPortfolio.id,
            rebalanceStatus: "NotStarted",
            targetAllocation: realPortfolio.initialHoldingsAllocation.map((allocation) => {
              return {
                percentage: allocation.percentage,
                assetCommonId: allocation.assetCommonId
              };
            })
          });
        });

        it("should update the rebalance transaction with 'NotStarted' rebalanceStatus", async () => {
          const TODAY = new Date("2022-07-17T11:00:00Z");
          Date.now = jest.fn(() => +TODAY);

          const newTargetAllocation = changeTargetAllocation(realPortfolio.initialHoldingsAllocation);

          const rebalanceTransaction = await PortfolioService.rebalancePortfolio(
            realPortfolio,
            newTargetAllocation
          );

          expect(rebalanceTransaction).toEqual(
            expect.objectContaining({
              category: "RebalanceTransaction",
              portfolio: realPortfolio._id,
              targetAllocation: expect.arrayContaining([
                ...newTargetAllocation.map((allocation) => {
                  return expect.objectContaining({
                    percentage: allocation.percentage,
                    assetCommonId: allocation.assetCommonId
                  });
                })
              ])
            })
          );
        });

        it("should NOT change the running rebalance transaction", async () => {
          const runningRebalanceTransactionReFetched = (await RebalanceTransaction.findOne(
            runningRebalanceTransaction._id
          )) as RebalanceTransactionDocument;

          expect(runningRebalanceTransactionReFetched).toEqual(
            expect.objectContaining({
              category: "RebalanceTransaction",
              portfolio: realPortfolio._id,
              targetAllocation: expect.arrayContaining([
                ...runningRebalanceTransaction.targetAllocation.map((allocation) => {
                  return expect.objectContaining({
                    percentage: allocation.percentage,
                    assetCommonId: allocation.assetCommonId
                  });
                })
              ])
            })
          );
        });
      });

      describe("and a rebalance transaction with 'NotStarted' rebalanceStatus does not exist", () => {
        it("should create a new rebalance transaction with 'NotStarted' rebalanceStatus", async () => {
          const TODAY = new Date("2022-07-17T11:00:00Z");
          Date.now = jest.fn(() => +TODAY);

          const newTargetAllocation = changeTargetAllocation(realPortfolio.initialHoldingsAllocation);

          const rebalanceTransaction = await PortfolioService.rebalancePortfolio(
            realPortfolio,
            newTargetAllocation
          );

          expect(rebalanceTransaction).toEqual(
            expect.objectContaining({
              category: "RebalanceTransaction",
              portfolio: realPortfolio._id,
              targetAllocation: expect.arrayContaining([
                ...newTargetAllocation.map((allocation) => {
                  return expect.objectContaining({
                    percentage: allocation.percentage,
                    assetCommonId: allocation.assetCommonId
                  });
                })
              ])
            })
          );
        });

        it("should NOT change the running rebalance transaction", async () => {
          const runningRebalanceTransactionReFetched = (await RebalanceTransaction.findOne(
            runningRebalanceTransaction._id
          )) as RebalanceTransactionDocument;

          expect(runningRebalanceTransactionReFetched).toEqual(
            expect.objectContaining({
              category: "RebalanceTransaction",
              portfolio: realPortfolio._id,
              targetAllocation: expect.arrayContaining([
                ...runningRebalanceTransaction.targetAllocation.map((allocation) => {
                  return expect.objectContaining({
                    percentage: allocation.percentage,
                    assetCommonId: allocation.assetCommonId
                  });
                })
              ])
            })
          );
        });
      });
    });

    describe("when no rebalance transaction exists", () => {
      it("should create a new rebalance transaction with 'NotStarted' rebalanceStatus", async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        const rebalanceTransaction = await PortfolioService.rebalancePortfolio(
          realPortfolio,
          realPortfolio.initialHoldingsAllocation
        );

        expect(rebalanceTransaction).toEqual(
          expect.objectContaining({
            category: "RebalanceTransaction",
            portfolio: realPortfolio._id,
            targetAllocation: expect.arrayContaining([
              ...realPortfolio.initialHoldingsAllocation.map((allocation) => {
                return expect.objectContaining({
                  percentage: allocation.percentage,
                  assetCommonId: allocation.assetCommonId
                });
              })
            ])
          })
        );
        // reparse rebalance transaction to stringify dates
        const reparsedRebalanceTransaction = JSON.parse(JSON.stringify(rebalanceTransaction));
        expect(reparsedRebalanceTransaction.buyExecutionWindow).toMatchObject({
          start: "2022-07-19T12:00:00.000Z",
          end: "2022-07-19T17:00:00.000Z"
        });
        expect(reparsedRebalanceTransaction.sellExecutionWindow).toMatchObject({
          start: "2022-07-18T12:00:00.000Z",
          end: "2022-07-18T17:00:00.000Z"
        });
      });
    });
  });

  describe("getAssetRestrictionDetails", () => {
    describe("when an asset has no restricted quantity", () => {
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 2),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        const giftedHoldings = new Map(
          Object.entries({
            [ASSET]: [
              {
                quantity: 0.5,
                unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), 5)
              } // unrestricted gift
            ]
          })
        ) as Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings,
          giftedHoldings
        });

        await Promise.all([
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: 0.5,
            unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), 5),
            status: "Settled",
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }) // unrestricted reward
        ]);
      });

      it("should return no restricted quantity", async () => {
        const response = await PortfolioService.getAssetRestrictionDetails(portfolio, ASSET);
        expect(response).toEqual({
          assetCommonId: ASSET,
          restrictedQuantity: 0,
          hasRestrictedQuantity: false
        });
      });
    });

    describe("when an asset has reward restricted quantity", () => {
      const EXPECTED_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 1),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        await Promise.all([
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_RESTICTED_QUANTITY,
            unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), RESTRICTED_HOLDING_PERIOD_DAYS + 5),
            status: "Settled",
            accepted: true,
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }), // unrestricted reward
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_RESTICTED_QUANTITY,
            unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5),
            status: "Settled",
            accepted: true,
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }), // restricted for 5 more days

          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_RESTICTED_QUANTITY
          }) // pending reward
        ]);
      });

      it("should return restricted quantity based on active rewards", async () => {
        const response = await PortfolioService.getAssetRestrictionDetails(portfolio, ASSET);
        expect(response).toEqual({
          assetCommonId: ASSET,
          restrictedQuantity: EXPECTED_RESTICTED_QUANTITY,
          hasRestrictedQuantity: true
        });
      });
    });

    describe("when an asset has gifted restricted quantity", () => {
      const EXPECTED_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 2),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        const giftedHoldings = new Map(
          Object.entries({
            [ASSET]: [
              {
                quantity: EXPECTED_RESTICTED_QUANTITY,
                unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 1)
              }
            ]
          })
        ) as Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings,
          giftedHoldings
        });
      });

      it("should return restricted quantity based on restricted gift", async () => {
        const response = await PortfolioService.getAssetRestrictionDetails(portfolio, ASSET);
        expect(response).toEqual({
          assetCommonId: ASSET,
          restrictedQuantity: EXPECTED_RESTICTED_QUANTITY,
          hasRestrictedQuantity: true
        });
      });
    });

    describe("when an asset has both gifted and reward restricted quantity", () => {
      const EXPECTED_GIFT_RESTICTED_QUANTITY = 0.5;
      const EXPECTED_REWARD_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 2),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        const giftedHoldings = new Map(
          Object.entries({
            [ASSET]: [
              {
                quantity: EXPECTED_GIFT_RESTICTED_QUANTITY,
                unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 1)
              }
            ]
          })
        ) as Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings,
          giftedHoldings
        });

        await Promise.all([
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_REWARD_RESTICTED_QUANTITY,
            unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), RESTRICTED_HOLDING_PERIOD_DAYS + 5),
            status: "Settled",
            accepted: true,
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }), // unrestricted reward
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_REWARD_RESTICTED_QUANTITY,
            unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5),
            status: "Settled",
            accepted: true,
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }) // restricted for 5 more days
        ]);
      });

      it("should return restricted quantity based on restricted gift and active rewards", async () => {
        const response = await PortfolioService.getAssetRestrictionDetails(portfolio, ASSET);
        expect(response).toEqual({
          assetCommonId: ASSET,
          restrictedQuantity: EXPECTED_GIFT_RESTICTED_QUANTITY + EXPECTED_REWARD_RESTICTED_QUANTITY,
          hasRestrictedQuantity: true
        });
      });
    });

    describe("when an asset has reward restricted quantity but reward has not been accepted", () => {
      const EXPECTED_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 1),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        await Promise.all([
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5),
            quantity: EXPECTED_RESTICTED_QUANTITY,
            accepted: undefined
          }) // resctricted reward that has not been accepted
        ]);
      });

      it("should return no restricted quantity", async () => {
        const response = await PortfolioService.getAssetRestrictionDetails(portfolio, ASSET);
        expect(response).toEqual({
          assetCommonId: ASSET,
          restrictedQuantity: 0,
          hasRestrictedQuantity: false
        });
      });
    });
  });

  describe("getPricesByTenor", () => {
    describe("when user is not invested", () => {
      let owner: UserDocument;
      let portfolio: PortfolioDocument;

      const TODAY = new Date("2023-08-23");
      // weekend zeros are skipped
      const data = [
        { timestamp: new Date("2023-08-16T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 16 Aug 2023
        { timestamp: new Date("2023-08-17T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 17 Aug 2023
        { timestamp: new Date("2023-08-18T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 18 Aug 2023
        { timestamp: new Date("2023-08-21T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 21 Aug 2023
        { timestamp: new Date("2023-08-22T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 22 Aug 2023
        { timestamp: new Date("2023-08-23T12:00:00.000Z").getTime(), value: 0, displayIntraday: false } // 23 Aug 2023
      ];
      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          currency: "GBP",
          portfolioConversionStatus: "notStarted"
        });
        portfolio = await buildPortfolio({ owner: owner.id, holdings: [] });

        const dailyTickers = await DailyPortfolioTicker.find({ portfolio: portfolio.id });
        expect(dailyTickers.length).toBe(0);
        const intraDayTickers = await IntraDayPortfolioTicker.find({ portfolio: portfolio.id });
        expect(intraDayTickers.length).toBe(0);
      });
      afterAll(async () => await clearDb());

      it("should return an array of elements with price 0 that correspond to the last week", async () => {
        const pricesByTenor = await PortfolioService.getPricesByTenor(
          portfolio.id,
          owner.currency as currenciesConfig.MainCurrencyType
        );

        expect(pricesByTenor).toMatchObject(
          expect.objectContaining({
            "1w": {
              data
            },
            "1m": {
              data
            },
            "3m": {
              data
            },
            "6m": {
              data
            },
            "1y": {
              data
            },
            max: {
              data
            }
          })
        );
      });
    });

    describe("when user is invested for the last 3 days", () => {
      let owner: UserDocument;
      let portfolio: PortfolioDocument;

      const TODAY = new Date("2023-08-23");
      // weekend zeros are skipped
      const oneWeekData = [
        { timestamp: new Date("2023-08-16T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 16 Aug 2023
        { timestamp: new Date("2023-08-17T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 17 Aug 2023
        { timestamp: new Date("2023-08-18T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 18 Aug 2023
        { timestamp: new Date("2023-08-21T00:00:00.000Z").getTime(), value: 10, displayIntraday: true }, // 21 Aug 2023
        { timestamp: new Date("2023-08-22T00:00:00.000Z").getTime(), value: 10, displayIntraday: true } // 22 Aug 2023
      ];
      const oneWeekDataDaily = oneWeekData.map(({ timestamp, value }) => ({
        timestamp: DateTime.fromJSDate(new Date(timestamp))
          .setZone("Europe/London", { keepLocalTime: true })
          .set({
            hour: 13,
            minute: 0
          })
          .toJSDate()
          .getTime(),
        value,
        displayIntraday: false
      }));

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          currency: "GBP",
          portfolioConversionStatus: "completed"
        });
        portfolio = await buildPortfolio({
          owner: owner.id,
          holdings: [
            await buildHoldingDTO(true, "equities_apple", 1, {
              price: 10
            })
          ]
        });

        await Promise.all(
          // [0, 1, 2]
          [...Array(3).keys()].map(async (daysAgo) => {
            await Promise.all([
              buildDailyPortfolioTicker({
                portfolio: portfolio._id,
                date: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 10, EUR: 11, USD: 14 },
                currency: owner.currency
              }),
              buildIntraDayPortfolioTicker({
                portfolio: portfolio._id,
                timestamp: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 10, EUR: 11, USD: 14 },
                currency: owner.currency
              })
            ]);
          })
        );
      });
      afterAll(async () => await clearDb());

      it("should return an array of elements with price 0 for the empty entries of the tenor", async () => {
        const pricesByTenor = await PortfolioService.getPricesByTenor(
          portfolio.id,
          owner.currency as currenciesConfig.MainCurrencyType
        );

        expect(pricesByTenor).toMatchObject(
          expect.objectContaining({
            "1w": {
              data: [
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: true
                })
              ]
            },
            "1m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: true
                })
              ])
            },
            "3m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(), // 24/6 is weekend
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: false
                })
              ])
            },
            "6m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-02-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-03-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-04-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: false
                })
              ])
            },
            "1y": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2022-08-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-09-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-10-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-11-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-12-23T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-01-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-02-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-03-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-04-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: false
                })
              ])
            },
            max: {
              data: [
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: true
                })
              ]
            }
          })
        );
      });
    });

    describe("when user is invested for exactly one week (7 days)", () => {
      let owner: UserDocument;
      let portfolio: PortfolioDocument;

      const TODAY = new Date("2023-08-23");
      // weekends for non-zeros are not skipped
      const oneWeekData = [
        { timestamp: new Date("2023-08-16T00:00:00.000Z").getTime(), value: 10, displayIntraday: true }, // 16 Aug 2023
        { timestamp: new Date("2023-08-17T00:00:00.000Z").getTime(), value: 11, displayIntraday: true }, // 17 Aug 2023
        { timestamp: new Date("2023-08-18T00:00:00.000Z").getTime(), value: 12, displayIntraday: true }, // 18 Aug 2023
        { timestamp: new Date("2023-08-19T00:00:00.000Z").getTime(), value: 13, displayIntraday: true }, // 19 Aug 2023
        { timestamp: new Date("2023-08-20T00:00:00.000Z").getTime(), value: 14, displayIntraday: true }, // 20 Aug 2023
        { timestamp: new Date("2023-08-21T00:00:00.000Z").getTime(), value: 15, displayIntraday: true }, // 21 Aug 2023
        { timestamp: new Date("2023-08-22T00:00:00.000Z").getTime(), value: 16, displayIntraday: true } // 22 Aug 2023
      ];
      const oneWeekDataDaily = oneWeekData.map(({ timestamp, value }) => ({
        timestamp: DateTime.fromJSDate(new Date(timestamp))
          .setZone("Europe/London", { keepLocalTime: true })
          .set({
            hour: 13,
            minute: 0
          })
          .toJSDate()
          .getTime(),
        value,
        displayIntraday: false
      }));

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          currency: "GBP",
          portfolioConversionStatus: "completed"
        });
        portfolio = await buildPortfolio({
          owner: owner.id,
          holdings: [
            await buildHoldingDTO(true, "equities_apple", 1, {
              price: 17
            })
          ]
        });

        await Promise.all(
          // [0, 1, 2, 3, 4, 5, 6, 7]
          [...Array(8).keys()].map(async (daysAgo) => {
            await Promise.all([
              buildDailyPortfolioTicker({
                portfolio: portfolio._id,
                date: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 17 - daysAgo, EUR: 18 - daysAgo, USD: 21 - daysAgo },
                currency: owner.currency
              }),
              buildIntraDayPortfolioTicker({
                portfolio: portfolio._id,
                timestamp: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 17 - daysAgo, EUR: 18 - daysAgo, USD: 21 - daysAgo },
                currency: owner.currency
              })
            ]);
          })
        );
      });
      afterAll(async () => await clearDb());

      it("should return an array of elements with price 0 for the empty entries of the tenor", async () => {
        const pricesByTenor = await PortfolioService.getPricesByTenor(
          portfolio.id,
          owner.currency as currenciesConfig.MainCurrencyType
        );

        expect(pricesByTenor).toMatchObject(
          expect.objectContaining({
            "1w": {
              data: [
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: true
                })
              ]
            },
            "1m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: true
                })
              ])
            },
            "3m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(), // 24/6 is weekend
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: false
                })
              ])
            },
            "6m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-02-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-03-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-04-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: false
                })
              ])
            },
            "1y": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2022-08-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-09-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-10-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-11-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-12-23T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-01-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-02-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-03-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-04-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: false
                })
              ])
            },
            max: {
              data: [
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: true
                })
              ]
            }
          })
        );
      });
    });

    describe("when user is invested for at least a week but less than a month (10 days)", () => {
      let owner: UserDocument;
      let portfolio: PortfolioDocument;

      const TODAY = new Date("2023-08-23");
      const oneWeekData = [
        { timestamp: new Date("2023-08-16T00:00:00.000Z").getTime(), value: 10, displayIntraday: true }, // 16 Aug 2023
        { timestamp: new Date("2023-08-17T00:00:00.000Z").getTime(), value: 11, displayIntraday: true }, // 17 Aug 2023
        { timestamp: new Date("2023-08-18T00:00:00.000Z").getTime(), value: 12, displayIntraday: true }, // 18 Aug 2023
        { timestamp: new Date("2023-08-19T00:00:00.000Z").getTime(), value: 13, displayIntraday: true }, // 19 Aug 2023
        { timestamp: new Date("2023-08-20T00:00:00.000Z").getTime(), value: 14, displayIntraday: true }, // 20 Aug 2023
        { timestamp: new Date("2023-08-21T00:00:00.000Z").getTime(), value: 15, displayIntraday: true }, // 21 Aug 2023
        { timestamp: new Date("2023-08-22T00:00:00.000Z").getTime(), value: 16, displayIntraday: true } // 22 Aug 2023
      ];
      const fullData = [
        { timestamp: new Date("2023-08-14T00:00:00.000Z").getTime(), value: 8, displayIntraday: true },
        { timestamp: new Date("2023-08-15T00:00:00.000Z").getTime(), value: 9, displayIntraday: true },
        ...oneWeekData
      ];
      const fullDataDaily = fullData.map(({ timestamp, value }) => ({
        timestamp: DateTime.fromJSDate(new Date(timestamp))
          .setZone("Europe/London", { keepLocalTime: true })
          .set({
            hour: 13,
            minute: 0
          })
          .toJSDate()
          .getTime(),
        value,
        displayIntraday: false
      }));

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          currency: "GBP",
          portfolioConversionStatus: "completed"
        });
        portfolio = await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_apple", 1, { price: 17 })]
        });

        await Promise.all(
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          [...Array(10).keys()].map(async (daysAgo) => {
            await Promise.all([
              buildDailyPortfolioTicker({
                portfolio: portfolio._id,
                date: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 17 - daysAgo, EUR: 18 - daysAgo, USD: 21 - daysAgo },
                currency: owner.currency
              }),
              buildIntraDayPortfolioTicker({
                portfolio: portfolio._id,
                timestamp: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 17 - daysAgo, EUR: 18 - daysAgo, USD: 21 - daysAgo },
                currency: owner.currency
              })
            ]);
          })
        );
      });
      afterAll(async () => await clearDb());

      it("should return an array of elements with price 0 for the empty entries of the tenor", async () => {
        const pricesByTenor = await PortfolioService.getPricesByTenor(
          portfolio.id,
          owner.currency as currenciesConfig.MainCurrencyType
        );

        expect(pricesByTenor).toMatchObject(
          expect.objectContaining({
            "1w": {
              data: [
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: true
                })
              ]
            },
            "1m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...fullData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: true
                })
              ])
            },
            "3m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...fullDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: false
                })
              ])
            },
            "6m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-02-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-03-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-04-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...fullDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: false
                })
              ])
            },
            "1y": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2022-08-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-09-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-10-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-11-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-12-23T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-01-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-02-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-03-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-04-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...fullDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: false
                })
              ])
            },
            max: {
              data: [
                ...fullData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T00:00:00.000Z").getTime(),
                  value: 17,
                  displayIntraday: true
                })
              ]
            }
          })
        );
      });
    });

    describe("when user is invested for 40 days and they do NOT have a daily portfolio ticker for today", () => {
      let owner: UserDocument;
      let portfolio: PortfolioDocument;

      const TODAY = new Date("2023-08-23T12:00:00Z");
      const oneWeekData = [
        { timestamp: new Date("2023-08-16T12:00:00Z").getTime(), value: 10, displayIntraday: true }, // 16 Aug 2023
        { timestamp: new Date("2023-08-17T12:00:00Z").getTime(), value: 11, displayIntraday: true }, // 17 Aug 2023
        { timestamp: new Date("2023-08-18T12:00:00Z").getTime(), value: 12, displayIntraday: true }, // 18 Aug 2023
        { timestamp: new Date("2023-08-19T12:00:00Z").getTime(), value: 13, displayIntraday: true }, // 19 Aug 2023
        { timestamp: new Date("2023-08-20T12:00:00Z").getTime(), value: 14, displayIntraday: true }, // 20 Aug 2023
        { timestamp: new Date("2023-08-21T12:00:00Z").getTime(), value: 15, displayIntraday: true }, // 21 Aug 2023
        { timestamp: new Date("2023-08-22T12:00:00Z").getTime(), value: 16, displayIntraday: true }, // 22 Aug 2023
        { timestamp: new Date("2023-08-23T12:00:00Z").getTime(), value: 17, displayIntraday: true } // 23 Aug 2023
      ];

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          currency: "GBP",
          portfolioConversionStatus: "completed"
        });
        portfolio = await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_apple", 1, { price: 17 })]
        });

        // We have 40 intra-day tickers BUT only have 39 daily portfolio tickers to simulate the scenario where today's daily
        // portfolio ticker has not been created.
        await Promise.all([
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, ..., 39]
          ...[...Array(40).keys()].map(async (daysAgo) => {
            await Promise.all([
              buildIntraDayPortfolioTicker({
                portfolio: portfolio._id,
                timestamp: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 17 - daysAgo, EUR: 18 - daysAgo, USD: 21 - daysAgo },
                currency: owner.currency
              })
            ]);
          }),
          // [1, 2, 3, 4, 5, 6, 7, 8, 9, ..., 39]
          [...Array(40).keys()]
            .filter((number) => number > 0)
            .map(async (daysAgo) => {
              await Promise.all([
                buildDailyPortfolioTicker({
                  portfolio: portfolio._id,
                  date: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                  pricePerCurrency: { GBP: 17 - daysAgo, EUR: 18 - daysAgo, USD: 21 - daysAgo },
                  currency: owner.currency
                })
              ]);
            })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return the latest intra-day ticker as the last element of the array", async () => {
        const pricesByTenor = await PortfolioService.getPricesByTenor(
          portfolio.id,
          owner.currency as currenciesConfig.MainCurrencyType
        );

        // The last element in the 3m array should be the latest intra-day portfolio ticker.
        expect(pricesByTenor["3m"].data.at(-1)).toEqual(
          expect.objectContaining({
            timestamp: TODAY.getTime(),
            value: 17,
            displayIntraday: false
          })
        );

        // The element one before the last in the 3m array should be the latest daily portfolio ticker
        expect(pricesByTenor["3m"].data.at(-2)).toEqual(
          expect.objectContaining({
            timestamp: DateTime.fromJSDate(DateUtil.getDateOfDaysAgo(TODAY, 1))
              .setZone("Europe/London", { keepLocalTime: true })
              .set({
                hour: 13,
                minute: 0
              })
              .toJSDate()
              .getTime(),
            value: 16,
            displayIntraday: false
          })
        );

        // The 1w tenor should be unchanged.
        expect(pricesByTenor).toMatchObject(
          expect.objectContaining({
            "1w": {
              data: oneWeekData
            }
          })
        );
      });
    });

    describe("when user is invested for 40 days and they do have a daily portfolio ticker for today", () => {
      let owner: UserDocument;
      let portfolio: PortfolioDocument;

      const TODAY = new Date("2023-08-23T12:00:00Z");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          currency: "GBP",
          portfolioConversionStatus: "completed"
        });
        portfolio = await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_apple", 1, { price: 17 })]
        });

        // We have 40 intra-day tickers BUT only have 39 daily portfolio tickers to simulate the scenario where today's daily
        // portfolio ticker has not been created.
        await Promise.all([
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, ..., 39]
          ...[...Array(40).keys()].map(async (daysAgo) => {
            await Promise.all([
              buildIntraDayPortfolioTicker({
                portfolio: portfolio._id,
                timestamp: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 17 - daysAgo, EUR: 18 - daysAgo, USD: 21 - daysAgo },
                currency: owner.currency
              })
            ]);
          }),
          // [1, 2, 3, 4, 5, 6, 7, 8, 9, ..., 39]
          [...Array(40).keys()]
            .filter((number) => number > 0)
            .map(async (daysAgo) => {
              await Promise.all([
                buildDailyPortfolioTicker({
                  portfolio: portfolio._id,
                  date: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                  pricePerCurrency: { GBP: 17 - daysAgo, EUR: 18 - daysAgo, USD: 21 - daysAgo },
                  currency: owner.currency
                })
              ]);
            }),
          // The today's daily portfolio ticker is a bit lower than the latest intra-day.
          buildDailyPortfolioTicker({
            portfolio: portfolio._id,
            date: TODAY,
            pricePerCurrency: { GBP: 16.95, EUR: 17.95, USD: 20.95 },
            currency: owner.currency
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return the latest intra-day ticker as the last element of the array", async () => {
        const pricesByTenor = await PortfolioService.getPricesByTenor(
          portfolio.id,
          owner.currency as currenciesConfig.MainCurrencyType
        );

        // The last element in the 3m array should be the latest intra-day portfolio ticker.
        expect(pricesByTenor["3m"].data.at(-1)).toEqual(
          expect.objectContaining({
            timestamp: TODAY.getTime(),
            value: 17,
            displayIntraday: false
          })
        );

        // The element one before the last in the 3m array should be the latest daily portfolio ticker
        expect(pricesByTenor["3m"].data.at(-2)).toEqual(
          expect.objectContaining({
            timestamp: DateTime.fromJSDate(DateUtil.getDateOfDaysAgo(TODAY, 1))
              .setZone("Europe/London", { keepLocalTime: true })
              .set({
                hour: 13,
                minute: 0
              })
              .toJSDate()
              .getTime(),
            value: 16,
            displayIntraday: false
          })
        );
      });
    });
  });

  describe("getRestrictedHoldings", () => {
    describe("when a portfolio has no restricted quantity", () => {
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 2),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        const giftedHoldings = new Map(
          Object.entries({
            [ASSET]: [
              {
                quantity: 0.5,
                unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), 5)
              } // unrestricted gift
            ]
          })
        ) as Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings,
          giftedHoldings
        });

        await Promise.all([
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: 0.5,
            unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), 5),
            status: "Settled",
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }) // unrestricted reward
        ]);
      });

      it("should return no restricted quantity", async () => {
        const response = await PortfolioService.getRestrictedHoldings(portfolio);
        expect(response).toEqual({
          restrictedAssets: [],
          hasRestrictedQuantity: false
        });
      });
    });

    describe("when a portfolio has a restricted reward asset", () => {
      const EXPECTED_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 1),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        await Promise.all([
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_RESTICTED_QUANTITY,
            unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), RESTRICTED_HOLDING_PERIOD_DAYS + 5),
            status: "Settled",
            accepted: true,
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }), // unrestricted reward
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_RESTICTED_QUANTITY,
            unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5),
            status: "Settled",
            accepted: true,
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }), // restricted for 5 more days

          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_RESTICTED_QUANTITY
          }) // pending reward
        ]);
      });

      it("should return restricted quantity based on active rewards", async () => {
        const response = await PortfolioService.getRestrictedHoldings(portfolio);
        expect(response).toEqual({
          restrictedAssets: [{ assetCommonId: ASSET, restrictedQuantity: EXPECTED_RESTICTED_QUANTITY }],
          hasRestrictedQuantity: true
        });
      });
    });

    describe("when a portfolio has a restricted gifted asset", () => {
      const EXPECTED_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 2),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        const giftedHoldings = new Map(
          Object.entries({
            [ASSET]: [
              {
                quantity: EXPECTED_RESTICTED_QUANTITY,
                unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 1)
              }
            ]
          })
        ) as Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings,
          giftedHoldings
        });
      });

      it("should return restricted quantity based on restricted gift", async () => {
        const response = await PortfolioService.getRestrictedHoldings(portfolio);
        expect(response).toEqual({
          restrictedAssets: [
            {
              assetCommonId: ASSET,
              restrictedQuantity: EXPECTED_RESTICTED_QUANTITY
            }
          ],
          hasRestrictedQuantity: true
        });
      });
    });

    describe("when a portfolio has a restricted gifted/reward asset", () => {
      const EXPECTED_GIFT_RESTICTED_QUANTITY = 0.5;
      const EXPECTED_REWARD_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 2),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        const giftedHoldings = new Map(
          Object.entries({
            [ASSET]: [
              {
                quantity: EXPECTED_GIFT_RESTICTED_QUANTITY,
                unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 1)
              }
            ]
          })
        ) as Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings,
          giftedHoldings
        });

        await Promise.all([
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_REWARD_RESTICTED_QUANTITY,
            unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), RESTRICTED_HOLDING_PERIOD_DAYS + 5),
            status: "Settled",
            accepted: true,
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }), // unrestricted reward
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            quantity: EXPECTED_REWARD_RESTICTED_QUANTITY,
            unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5),
            status: "Settled",
            accepted: true,
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Matched"
                }
              }
            },
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Settled"
                }
              }
            }
          }) // restricted for 5 more days
        ]);
      });

      it("should return restricted quantity based on restricted gift and active rewards", async () => {
        const response = await PortfolioService.getRestrictedHoldings(portfolio);
        expect(response).toEqual({
          restrictedAssets: [
            {
              assetCommonId: ASSET,
              restrictedQuantity: EXPECTED_GIFT_RESTICTED_QUANTITY + EXPECTED_REWARD_RESTICTED_QUANTITY
            }
          ],
          hasRestrictedQuantity: true
        });
      });
    });

    describe("when a portfolio has a non accepted restricted reward asset", () => {
      const EXPECTED_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        const user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all([
          buildHoldingDTO(true, ASSET, 1),
          buildHoldingDTO(true, "equities_us", 1),
          buildHoldingDTO(true, "equities_china", 1)
        ]);
        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        await Promise.all([
          buildReward({
            targetUser: user.id,
            asset: ASSET,
            unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5),
            quantity: EXPECTED_RESTICTED_QUANTITY,
            accepted: undefined
          }) // resctricted reward that has not been accepted
        ]);
      });

      it("should return no restricted quantity", async () => {
        const response = await PortfolioService.getRestrictedHoldings(portfolio);
        expect(response).toEqual({
          restrictedAssets: [],
          hasRestrictedQuantity: false
        });
      });
    });
  });

  describe("topupSavings", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    const ORDER_AMOUNT = 50;
    const AVAILABLE_CASH = 100;
    const SAVINGS: [savingsUniverseConfig.SavingsProductType, SavingType][] = [
      [
        "mmf_dist_gbp",
        {
          amount: 0,
          currency: "GBX"
        }
      ]
    ];

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });

      portfolio = await buildPortfolio({
        owner: user.id,
        cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } },
        mode: PortfolioModeEnum.REAL,
        savings: new Map(SAVINGS)
      });
    });

    describe("when there is not enough cash to invest in savings ", () => {
      it("should throw an error", async () => {
        await expect(
          PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", AVAILABLE_CASH + 10)
        ).rejects.toThrow(
          new BadRequestError("You have placed a buy order of value £110.00 but you only have £100.00")
        );
      });
    });

    describe("when there is enough cash", () => {
      beforeEach(async () => {
        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const savingsTopupTransaction = (await SavingsTopupTransaction.findOne({
          owner: user._id
        })) as SavingsTopupTransactionDocument;
        const orderCount = await Order.countDocuments({ transaction: savingsTopupTransaction._id });

        expect(orderCount).toBe(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
      });
    });

    describe("when it's linked to a pending deposit", () => {
      let pendingDeposit: DepositCashTransactionDocument;

      beforeEach(async () => {
        pendingDeposit = await buildDepositCashTransaction();
        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT, {
          pendingDeposit: pendingDeposit
        });
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "PendingDeposit",
          pendingDeposit: pendingDeposit._id,
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const savingsTopupTransaction =
          (await SavingsTopupTransaction.findOne()) as SavingsTopupTransactionDocument;
        const orderCount = await Order.countDocuments({ transaction: savingsTopupTransaction._id });

        expect(orderCount).toBe(0);
      });

      it("should not emit an Investment Created event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });

      it("should not update portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
      });
    });

    describe("when it's linked to an automation", () => {
      let automation: SavingsTopUpAutomationDocument;
      let pendingDeposit: DepositCashTransactionDocument;

      beforeEach(async () => {
        pendingDeposit = await buildDepositCashTransaction();
        automation = await buildSavingsTopUpAutomation({ owner: user.id });

        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT, {
          pendingDeposit: pendingDeposit,
          linkedAutomation: automation
        });
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "PendingDeposit",
          pendingDeposit: pendingDeposit._id,
          linkedAutomation: automation._id,
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const savingsTopupTransaction =
          (await SavingsTopupTransaction.findOne()) as SavingsTopupTransactionDocument;
        const orderCount = await Order.countDocuments({ transaction: savingsTopupTransaction._id });

        expect(orderCount).toBe(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "repeating"
          }
        );
      });

      it("should not update portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
      });
    });

    describe("when dividend is passed as an option", () => {
      let savingsDividend: SavingsDividendTransactionDocument;

      beforeEach(async () => {
        savingsDividend = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id
        });
        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT, {
          dividend: savingsDividend
        });
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const savingsTopupTransaction =
          (await SavingsTopupTransaction.findOne()) as SavingsTopupTransactionDocument;
        const orderCount = await Order.countDocuments({ transaction: savingsTopupTransaction._id });

        expect(orderCount).toBe(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should not update portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
      });

      it("should not change the status of the savings dividend", async () => {
        const updatedSavingsDividend = await SavingsDividendTransaction.findById(savingsDividend._id);

        expect(updatedSavingsDividend?.status).toEqual("Pending");
      });
    });

    describe("when the created savings topup completely fills a withdrawal", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            }
          },
          {},
          false
        );

        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should create 2 internally filled orders", async () => {
        const savingsTopup = (await SavingsTopupTransaction.findOne()) as SavingsTopupTransactionDocument;
        const orders = await Order.find();

        const filledAmount = savingsTopup.consideration.amount;

        expect(orders.length).toEqual(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmount,
                originalAmount: filledAmount,
                amountSubmitted: filledAmount
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmount,
                originalAmount: filledAmount,
                amountSubmitted: filledAmount
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update portfolio cash by subscracting topup amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
      });
    });

    describe("when a PendingTopUp savings withdrawal also exists with same amount", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            status: "PendingTopUp",
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            }
          },
          {},
          false
        );

        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should create 2 internally filled orders", async () => {
        const savingsTopup = (await SavingsTopupTransaction.findOne()) as SavingsTopupTransactionDocument;
        const orders = await Order.find();

        const filledAmount = savingsTopup.consideration.amount;

        expect(orders.length).toEqual(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmount,
                originalAmount: filledAmount,
                amountSubmitted: filledAmount
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmount,
                originalAmount: filledAmount,
                amountSubmitted: filledAmount
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update portfolio cash by subscracting topup amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
      });
    });

    describe("when a PendingTopUp savings withdrawal also exists with same amount but it is for a different MMF", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildSavingsWithdrawal(
          {
            status: "PendingTopUp",
            owner: user.id,
            portfolio: portfolio.id,
            savingsProduct: "mmf_dist_eur",
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            }
          },
          {},
          false
        );

        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const orders = await Order.find();

        expect(orders.length).toEqual(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
      });
    });

    describe("when the created savings topup completely fills a withdrawal but we're inside the execution window", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T10:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            }
          },
          {},
          false
        );

        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const orders = await Order.find();

        expect(orders.length).toEqual(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
      });
    });

    describe("when there is a savings withdrawal with same amount but it's submitted", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        }); // Submitted Withdrawal

        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const savingsTopup = await SavingsTopupTransaction.findOne();
        const orders = await Order.find({ transaction: savingsTopup.id });

        expect(orders.length).toEqual(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
      });
    });

    describe("when there is a savings withdrawal that will cause a remaining amount below the minimum", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(49.99, 100).toNumber(),
              currency: "GBP"
            }
          },
          {},
          false
        );

        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const savingsTopup = await SavingsTopupTransaction.findOne();
        const orders = await Order.find({ transaction: savingsTopup.id });

        expect(orders.length).toEqual(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
      });
    });

    describe("when the created savings topup fills 2 unsubmitted withdrawals", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      let anotherSavingsWithdrawal: SavingsWithdrawalTransactionDocument;
      let submittedSavingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        [savingsWithdrawal, anotherSavingsWithdrawal, submittedSavingsWithdrawal] = await Promise.all([
          buildSavingsWithdrawal(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(ORDER_AMOUNT / 2, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ), // Unsubmitted withdrwal with half the topup amount
          buildSavingsWithdrawal(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(ORDER_AMOUNT / 2, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ), // Unsubmitted withdrwal with half the topup amount
          buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            }
          }) // Submitted withdrwal (this gets ignored)
        ]);

        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should create 4 new internally filled orders with the available amount from withdrawals", async () => {
        const savingsTopup = (await SavingsTopupTransaction.findOne()) as SavingsTopupTransactionDocument;
        const orders = await Order.find();

        const filledAmountFromWithdrawal = savingsWithdrawal.consideration.amount;
        const filledAmountFromAnotherWithdrawal = anotherSavingsWithdrawal.consideration.amount;

        expect(orders.length).toEqual(5);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromWithdrawal,
                originalAmount: filledAmountFromWithdrawal,
                amountSubmitted: filledAmountFromWithdrawal
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromAnotherWithdrawal,
                originalAmount: filledAmountFromWithdrawal,
                amountSubmitted: filledAmountFromWithdrawal
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromWithdrawal,
                originalAmount: filledAmountFromWithdrawal,
                amountSubmitted: filledAmountFromWithdrawal
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: anotherSavingsWithdrawal._id,
              commonId: anotherSavingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromAnotherWithdrawal,
                originalAmount: filledAmountFromWithdrawal,
                amountSubmitted: filledAmountFromWithdrawal
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: submittedSavingsWithdrawal._id
            }) // Existing order
          ])
        );
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update portfolio cash by subscracting topup amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
      });
    });

    describe("when the created savings topup fills 2 unsubmitted withdrawal but the second withdrawal remain with an amount below the minimum", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        [savingsWithdrawal] = await Promise.all([
          buildSavingsWithdrawal(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(25, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ), // Unsubmitted withdrwal for £25
          buildSavingsWithdrawal(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(24.99, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ) // Unsubmitted withdrwal for £24.99 (this wont be filled because the remaining amount post fill would be below the minimum)
        ]);

        await PortfolioService.topupSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsTopup transaction", async () => {
        const savingsTopupTransactions = await SavingsTopupTransaction.find();

        expect(savingsTopupTransactions).toHaveLength(1);
        expect(savingsTopupTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should create 2 internally filled orders for the first withdrawal only", async () => {
        const savingsTopup = (await SavingsTopupTransaction.findOne()) as SavingsTopupTransactionDocument;
        const orders = await Order.find();

        const filledAmountFromWithdrawal = savingsWithdrawal.consideration.amount;

        expect(orders.length).toEqual(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromWithdrawal,
                originalAmount: filledAmountFromWithdrawal,
                amountSubmitted: filledAmountFromWithdrawal
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromWithdrawal,
                originalAmount: filledAmountFromWithdrawal,
                amountSubmitted: filledAmountFromWithdrawal
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update portfolio cash by subscracting topup amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(AVAILABLE_CASH - ORDER_AMOUNT);
      });
    });
  });

  describe("withdrawSavings", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    const SAVINGS_AMOUNT = 100; // In pounds
    const SAVINGS: [savingsUniverseConfig.SavingsProductType, SavingType][] = [
      [
        "mmf_dist_gbp",
        {
          amount: Decimal.mul(SAVINGS_AMOUNT, 100).toNumber(),
          currency: "GBX"
        }
      ]
    ];

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });

      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        savings: new Map(SAVINGS)
      });
    });

    describe("when there is not enough saving holding to fullfil the sell order", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT + 10;

      it("should throw an error", async () => {
        await expect(PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT)).rejects.toThrow(
          new BadRequestError("You have placed a sell order with quantity larger than what you hold")
        );
      });
    });

    describe("when there are enough saving holdings to fullfil the sell order", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;

      beforeEach(async () => {
        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const newSavingsWithdrawal = await SavingsWithdrawalTransaction.findOne();

        const orderCount = await Order.countDocuments({ transaction: newSavingsWithdrawal?._id });
        expect(orderCount).toBe(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is not enough saving holdings but a Pending SavingsTopup exists", () => {
      const PENDING_SAVINGS_TOPUP_AMOUNT = 10;
      const ORDER_AMOUNT = SAVINGS_AMOUNT + PENDING_SAVINGS_TOPUP_AMOUNT;

      beforeEach(async () => {
        await buildSavingsTopup({
          portfolio: portfolio.id,
          owner: user.id,
          consideration: {
            amount: Decimal.mul(PENDING_SAVINGS_TOPUP_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find();

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "PendingTopUp",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const newSavingsWithdrawal = await SavingsWithdrawalTransaction.findOne();

        const orderCount = await Order.countDocuments({ transaction: newSavingsWithdrawal?._id });
        expect(orderCount).toBe(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should not allow a second withdraw", async () => {
        await expect(PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT)).rejects.toThrow(
          new BadRequestError("You have placed a sell order with quantity larger than what you hold")
        );
      });
    });

    describe("when there is enough saving holdings and a 'PendingTopUp' SavingsWithdrawal exists with a 'Pending' SavingTopup that covers the diff", () => {
      const PENDING_SAVINGS_WITHDRAWAL_AMOUNT = SAVINGS_AMOUNT - 10;
      const ORDER_AMOUNT = SAVINGS_AMOUNT;
      let pendingTopUpSavingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        pendingTopUpSavingsWithdrawal = await buildSavingsWithdrawal({
          portfolio: portfolio.id,
          owner: user.id,
          status: "PendingTopUp",
          consideration: {
            amount: Decimal.mul(PENDING_SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
        await buildSavingsTopup({
          portfolio: portfolio.id,
          owner: user.id,
          status: "Pending",
          consideration: {
            amount: Decimal.mul(PENDING_SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({
          _id: { $ne: pendingTopUpSavingsWithdrawal._id }
        });

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "PendingTopUp",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const newSavingsWithdrawal = await SavingsWithdrawalTransaction.findOne({
          _id: { $ne: pendingTopUpSavingsWithdrawal._id }
        });

        const orderCount = await Order.countDocuments({ transaction: newSavingsWithdrawal?._id });
        expect(orderCount).toBe(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should not allow a second withdraw", async () => {
        await expect(PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT)).rejects.toThrow(
          new BadRequestError("You have placed a sell order with quantity larger than what you hold")
        );
      });
    });

    describe("when there is enough saving holdings but a 'Pending' SavingsWithdrawal exists", () => {
      const PENDING_SAVINGS_WITHDRAWAL_AMOUNT = SAVINGS_AMOUNT - 10;
      const ORDER_AMOUNT = SAVINGS_AMOUNT;

      beforeEach(async () => {
        await buildSavingsWithdrawal({
          portfolio: portfolio.id,
          owner: user.id,
          status: "Pending",
          consideration: {
            amount: Decimal.mul(PENDING_SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should throw an error", async () => {
        await expect(PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT)).rejects.toThrow(
          new BadRequestError("You have placed a sell order with quantity larger than what you hold")
        );
      });
    });

    describe("when there is a savings topup with same amount but it's submitted", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders for the withdrawal", async () => {
        const savingsWithdrawal =
          (await SavingsWithdrawalTransaction.findOne()) as SavingsWithdrawalTransactionDocument;
        const orders = await Order.find({ transaction: savingsWithdrawal.id });

        expect(orders.length).toEqual(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is an unsubmitted savings topup and completely fills the withdrawal", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            }
          },
          {},
          false
        );

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one Settled SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Settled",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should create 2 internally filled orders", async () => {
        const savingsWithdrawal =
          (await SavingsWithdrawalTransaction.findOne()) as SavingsWithdrawalTransactionDocument;
        const orders = await Order.find();

        const filledAmount = savingsTopup.consideration.amount;

        expect(orders.length).toEqual(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmount,
                originalAmount: filledAmount,
                amountSubmitted: filledAmount
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmount,
                originalAmount: filledAmount,
                amountSubmitted: filledAmount
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update the portfolio cash since the withdrawal got settled", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available + ORDER_AMOUNT);
        expect(updatedPortfolio.savings).toMatchObject({});
      });
    });

    describe("when there is a PendingDeposit savings topup linked to incomplete deposit and completely fills the withdrawal", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const pendingDeposit = await buildDepositCashTransaction({
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          },
          status: "Pending",
          providers: {
            truelayer: {
              status: "authorizing",
              id: faker.string.uuid(),
              version: "v3"
            }
          }
        });
        await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            },
            status: "PendingDeposit",
            pendingDeposit: pendingDeposit.id
          },
          {},
          false
        );

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one Pending SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const orders = await Order.find();

        expect(orders.length).toEqual(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is a PendingDeposit savings topup linked to executed deposit and completely fills the withdrawal", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const pendingDeposit = await buildDepositCashTransaction({
          consideration: {
            amount: ORDER_AMOUNT,
            currency: "GBP"
          },
          status: "Pending",
          providers: {
            truelayer: {
              status: "executed",
              id: faker.string.uuid(),
              version: "v3"
            }
          }
        });
        await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            },
            status: "PendingDeposit",
            pendingDeposit: pendingDeposit.id
          },
          {},
          false
        );

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one Pending SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const orders = await Order.find();

        expect(orders.length).toEqual(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should not update the portfolio cash since the withdrawal got settled", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
      });
    });

    describe("when there is an unsubmitted savings topup and completely fills the withdrawal but we're inside the execution window", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T10:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            }
          },
          {},
          false
        );

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const orders = await Order.find();

        expect(orders.length).toEqual(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is an unsubmitted savings topup and partially fills the withdrawal", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT / 2, 100).toNumber(),
              currency: "GBP"
            }
          },
          {},
          false
        );

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should create 2 internally filled orders with the available topup amount", async () => {
        const savingsWithdrawal =
          (await SavingsWithdrawalTransaction.findOne()) as SavingsWithdrawalTransactionDocument;
        const orders = await Order.find();

        const filledAmount = savingsTopup.consideration.amount;

        expect(orders.length).toEqual(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmount,
                originalAmount: filledAmount,
                amountSubmitted: filledAmount
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmount,
                originalAmount: filledAmount,
                amountSubmitted: filledAmount
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should not update the portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
      });
    });

    describe("when there is an unsubmitted savings topup with linked deposit direct debit", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const pendingDeposit = await buildDepositCashTransaction({
          consideration: {
            amount: ORDER_AMOUNT,
            currency: "GBP"
          },
          status: "Pending",
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Collected"
              }
            }
          }
        });

        // Create unsubmitted topup with linked deposit direct debit
        await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            },
            status: "PendingDeposit",
            pendingDeposit: pendingDeposit.id
          } as any,
          {},
          false
        );

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders", async () => {
        const orders = await Order.find({});
        expect(orders).toHaveLength(0);
      });

      it("should not update portfolio cash", async () => {
        const updatedPortfolio = (await Portfolio.findById(portfolio._id)) as PortfolioDocument;
        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
      });

      it("should fire an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is an unsubmitted savings topup and partially fills the withdrawal but the remaining amount is below the minimum", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT - 0.01, 100).toNumber(),
              currency: "GBP"
            }
          },
          {},
          false
        ); // Unsubmitted Topup with the order amount minus 0.01 that will cause remaning amount to be below the minimum

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should not create any orders because the remaning amount is below the minimum", async () => {
        const orders = await Order.find();

        expect(orders).toHaveLength(0);
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should not update the portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
      });
    });

    describe("when there are 2 savings topups that completely fill the withdrawal amount", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;
      let savingsTopup: SavingsTopupTransactionDocument;
      let anotherSavingsTopup: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        [savingsTopup, anotherSavingsTopup] = await Promise.all([
          buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(ORDER_AMOUNT / 2, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ), // Unsubmitted Topup with half the order amount
          buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(ORDER_AMOUNT / 2, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ) // Unsubmitted Topup with half the order amount
        ]);

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one Settled SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Settled",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should create 4 internally filled orders with the available amount from topups", async () => {
        const savingsWithdrawal =
          (await SavingsWithdrawalTransaction.findOne()) as SavingsWithdrawalTransactionDocument;
        const orders = await Order.find();

        const filledAmountFromTopup = savingsTopup.consideration.amount;
        const filledAmountFromAnotherTopup = anotherSavingsTopup.consideration.amount;

        expect(orders.length).toEqual(4);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromTopup,
                originalAmount: filledAmountFromTopup,
                amountSubmitted: filledAmountFromTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: anotherSavingsTopup._id,
              commonId: anotherSavingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromAnotherTopup,
                originalAmount: filledAmountFromAnotherTopup,
                amountSubmitted: filledAmountFromAnotherTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromTopup,
                originalAmount: filledAmountFromTopup,
                amountSubmitted: filledAmountFromTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromAnotherTopup,
                originalAmount: filledAmountFromAnotherTopup,
                amountSubmitted: filledAmountFromAnotherTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update the portfolio cash since the withdrawal got settled", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available + ORDER_AMOUNT);
      });
    });

    describe("when there are 2 savings topups that partially fill the withdrawal amount", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT - 10;
      let savingsTopup: SavingsTopupTransactionDocument;
      let anotherSavingsTopup: SavingsTopupTransactionDocument;
      let submittedTopup: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        [savingsTopup, anotherSavingsTopup, submittedTopup] = await Promise.all([
          buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(ORDER_AMOUNT / 2 - 10, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ), // Unsubmitted Topup with less than half the order amount
          buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(ORDER_AMOUNT / 2 - 10, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ), // Unsubmitted Topup with half the order amount
          buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
              currency: "GBP"
            }
          }) // Submitted Topup (this gets ignored)
        ]);

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should create 4 new internally filled orders with the available amount from topups", async () => {
        const savingsWithdrawal =
          (await SavingsWithdrawalTransaction.findOne()) as SavingsWithdrawalTransactionDocument;
        const orders = await Order.find();

        const filledAmountFromTopup = savingsTopup.consideration.amount;
        const filledAmountFromAnotherTopup = anotherSavingsTopup.consideration.amount;

        expect(orders.length).toEqual(5);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromTopup,
                originalAmount: filledAmountFromTopup,
                amountSubmitted: filledAmountFromTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: anotherSavingsTopup._id,
              commonId: anotherSavingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromAnotherTopup,
                originalAmount: filledAmountFromAnotherTopup,
                amountSubmitted: filledAmountFromAnotherTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromTopup,
                originalAmount: filledAmountFromTopup,
                amountSubmitted: filledAmountFromTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromAnotherTopup,
                originalAmount: filledAmountFromAnotherTopup,
                amountSubmitted: filledAmountFromAnotherTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: submittedTopup._id
            }) // Existing order
          ])
        );
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there are 2 savings topups that partially fill the withdrawal amount but the second topup has remaining amount below the minimum", () => {
      const ORDER_AMOUNT = SAVINGS_AMOUNT;
      let validSavingsTopup: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-01-31T12:20:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        [validSavingsTopup] = await Promise.all([
          buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(50, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ), // Unsubmitted Topup for £50 (valid)
          buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              consideration: {
                amount: Decimal.mul(49.99, 100).toNumber(),
                currency: "GBP"
              }
            },
            {},
            false
          ) // Unsubmitted Topup for £49.99 (this gets ignored because if it got internally filled it would leave remaining amount below the £0.02 miniumum)
        ]);

        await PortfolioService.withdrawSavings(portfolio, "mmf_dist_gbp", ORDER_AMOUNT);
      });

      it("should create one SavingsWithdrawal transaction", async () => {
        const savingsWithdrawalTransactions = await SavingsWithdrawalTransaction.find({});

        expect(savingsWithdrawalTransactions).toHaveLength(1);
        expect(savingsWithdrawalTransactions[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          savingsProduct: "mmf_dist_gbp",
          consideration: {
            amount: Decimal.mul(ORDER_AMOUNT, 100).toNumber(),
            currency: "GBP"
          }
        });
      });

      it("should create 2 internally filled orders with the available amount from first topup", async () => {
        const savingsWithdrawal =
          (await SavingsWithdrawalTransaction.findOne()) as SavingsWithdrawalTransactionDocument;
        const orders = await Order.find();

        const filledAmountFromTopup = validSavingsTopup.consideration.amount;

        expect(orders.length).toEqual(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: validSavingsTopup._id,
              commonId: validSavingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromTopup,
                originalAmount: filledAmountFromTopup,
                amountSubmitted: filledAmountFromTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              status: "InternallyFilled",
              activeProviders: [],
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: filledAmountFromTopup,
                originalAmount: filledAmountFromTopup,
                amountSubmitted: filledAmountFromTopup
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: ORDER_AMOUNT,
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should not update the portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
      });
    });
  });

  describe("updatePortfolioSavings", () => {
    describe("when portfolio savings map doesn't exist", () => {
      let portfolio: PortfolioDocument;
      const AMOUNT_UP_BY = 100;
      const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

      beforeEach(async () => {
        portfolio = await buildPortfolio();

        await PortfolioService.updatePortfolioSavings(portfolio.id, SAVINGS_PRODUCT_ID, AMOUNT_UP_BY);
      });

      it("should intialize savings Map with the proper amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: AMOUNT_UP_BY,
          currency: "GBX"
        });
      });
    });

    describe("when portfolio savings map exist but is empty and the 'amountUpBy' is negative", () => {
      let portfolio: PortfolioDocument;
      const AMOUNT_UP_BY = -100;
      const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

      beforeEach(async () => {
        portfolio = await buildPortfolio({ savings: new Map() });
      });

      it("should throw an error", async () => {
        await expect(
          PortfolioService.updatePortfolioSavings(portfolio.id, SAVINGS_PRODUCT_ID, AMOUNT_UP_BY)
        ).rejects.toThrow(new BadRequestError("Cannot initialize savings with negative amount"));
      });
    });

    describe("when portfolio savings map exist but is empty and the 'amountUpBy' is zero", () => {
      let portfolio: PortfolioDocument;
      const AMOUNT_UP_BY = 0;
      const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

      beforeEach(async () => {
        portfolio = await buildPortfolio({ savings: new Map() });

        await PortfolioService.updatePortfolioSavings(portfolio.id, SAVINGS_PRODUCT_ID, AMOUNT_UP_BY);
      });

      it("should intialize savings Map with the proper amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toBeUndefined();
      });
    });

    describe("when portfolio savings map exist but is empty", () => {
      let portfolio: PortfolioDocument;
      const AMOUNT_UP_BY = 100;
      const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

      beforeEach(async () => {
        portfolio = await buildPortfolio({ savings: new Map() });

        await PortfolioService.updatePortfolioSavings(portfolio.id, SAVINGS_PRODUCT_ID, AMOUNT_UP_BY);
      });

      it("should intialize savings Map with the proper amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: AMOUNT_UP_BY,
          currency: "GBX"
        });
      });
    });

    describe("when portfolio savings map exist but it's intialised", () => {
      let portfolio: PortfolioDocument;
      const INITIAL_SAVINGS_AMOUNT = 100;
      const AMOUNT_UP_BY = 100;
      const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

      beforeEach(async () => {
        portfolio = await buildPortfolio({
          savings: new Map([
            [
              SAVINGS_PRODUCT_ID,
              {
                amount: INITIAL_SAVINGS_AMOUNT,
                currency: "GBX"
              }
            ]
          ])
        });

        await PortfolioService.updatePortfolioSavings(portfolio.id, SAVINGS_PRODUCT_ID, AMOUNT_UP_BY);
      });

      it("should intialize savings Map with the proper amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT + AMOUNT_UP_BY,
          currency: "GBX"
        });
      });
    });

    describe("when portfolio savings map exist but it's intialised and the 'amountUpBy' is negative", () => {
      let portfolio: PortfolioDocument;
      const INITIAL_SAVINGS_AMOUNT = 110;
      const AMOUNT_UP_BY = -100;
      const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

      beforeEach(async () => {
        portfolio = await buildPortfolio({
          savings: new Map([
            [
              SAVINGS_PRODUCT_ID,
              {
                amount: INITIAL_SAVINGS_AMOUNT,
                currency: "GBX"
              }
            ]
          ])
        });

        await PortfolioService.updatePortfolioSavings(portfolio.id, SAVINGS_PRODUCT_ID, AMOUNT_UP_BY);
      });

      it("should intialize savings Map with the proper amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT + AMOUNT_UP_BY,
          currency: "GBX"
        });
      });
    });

    describe("when portfolio savings map exist but it's intialised and the 'amountUpBy' is negative and equal to current amount", () => {
      let portfolio: PortfolioDocument;
      const INITIAL_SAVINGS_AMOUNT = 100;
      const AMOUNT_UP_BY = -100;
      const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

      beforeEach(async () => {
        portfolio = await buildPortfolio({
          savings: new Map([
            [
              SAVINGS_PRODUCT_ID,
              {
                amount: INITIAL_SAVINGS_AMOUNT,
                currency: "GBX"
              }
            ]
          ])
        });

        await PortfolioService.updatePortfolioSavings(portfolio.id, SAVINGS_PRODUCT_ID, AMOUNT_UP_BY);
      });

      it("should intialize savings Map with the proper amount", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toBeUndefined();
      });
    });

    describe("when portfolio savings map exist but it's intialised and the 'amountUpBy' is negative and greater than current amount", () => {
      let portfolio: PortfolioDocument;
      const INITIAL_SAVINGS_AMOUNT = 100;
      const AMOUNT_UP_BY = -110;
      const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

      beforeEach(async () => {
        portfolio = await buildPortfolio({
          savings: new Map([
            [
              SAVINGS_PRODUCT_ID,
              {
                amount: INITIAL_SAVINGS_AMOUNT,
                currency: "GBX"
              }
            ]
          ])
        });
      });

      it("should throw an error", async () => {
        await expect(
          PortfolioService.updatePortfolioSavings(portfolio.id, SAVINGS_PRODUCT_ID, AMOUNT_UP_BY)
        ).rejects.toThrow(new BadRequestError("Cannot update savings with negative amount"));
      });
    });
  });

  describe("withdrawAllSavings", () => {
    describe("when the portfolio has no savings", () => {
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id, savings: new Map() });
      });

      it("should not create any savings withdrawal transactions", async () => {
        await PortfolioService.withdrawAllSavings(portfolio);

        const savingsWithdrawals = await SavingsWithdrawalTransaction.find();
        expect(savingsWithdrawals).toHaveLength(0);
      });
    });

    describe("when the portfolio has savings", () => {
      const SAVINGS_AMOUNT = 100;
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
      });

      it("should one savings withdrawal transaction", async () => {
        await PortfolioService.withdrawAllSavings(portfolio);

        const savingsWithdrawals = await SavingsWithdrawalTransaction.find();
        expect(savingsWithdrawals[0]).toMatchObject({
          portfolio: portfolio._id,
          owner: user._id,
          status: "Pending",
          consideration: {
            amount: SAVINGS_AMOUNT,
            currency: "GBP"
          }
        });
      });
    });
  });

  describe("getPortfolioUpByValuesAllTenors", () => {
    describe("when up by values are not cached", () => {
      const PORTFOLIO_VALUE = 110;
      const BUY_AMOUNT = 100;
      const NOW = new Date("2024-01-08T20:00:00Z");
      const SETTLED_DATE = DateUtil.getDateOfHoursAgo(NOW, 6);

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        Date.now = jest.fn(() => +new Date(NOW));

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: PORTFOLIO_VALUE })],
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          }
        });

        const [assetTransaction] = await Promise.all([
          buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: SETTLED_DATE
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            timestamp: NOW,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            timestamp: DateUtil.getStartOfDay(SETTLED_DATE),
            pricePerCurrency: { GBP: 0 }
          })
        ]);

        const assetBuyOrder = await buildOrder({
          status: "Matched",
          side: "Buy",
          transaction: assetTransaction.id,
          consideration: {
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        assetTransaction.orders = [assetBuyOrder];
        await assetTransaction.save();

        transactions.assetTransactions = [assetTransaction];
      });

      it("should return all up by values and update cache", async () => {
        await RedisClientService.Instance.del(`portfolios:up_by:${portfolio.id}`);
        await RedisClientService.Instance.del(`portfolios:value_at_up_by:${portfolio.id}`);

        const upBy = await PortfolioService.getPortfolioUpByValuesAllTenors(portfolio, transactions);
        expect(upBy).toMatchObject({
          today: 10,
          "1d": 10,
          "1w": 10,
          "1m": 10,
          "3m": 10,
          "6m": 10,
          "1y": 10,
          max: 10
        }); // 110 - 100

        const cachedUpByValues = await RedisClientService.Instance.get(`portfolios:up_by:${portfolio.id}`);
        const cachedPortfolioPrice = await RedisClientService.Instance.get(
          `portfolios:value_at_up_by:${portfolio.id}`
        );
        expect(cachedPortfolioPrice).toEqual(PORTFOLIO_VALUE);
        expect(cachedUpByValues).toMatchObject({
          today: 10,
          "1d": 10,
          "1w": 10,
          "1m": 10,
          "3m": 10,
          "6m": 10,
          "1y": 10,
          max: 10
        }); // 110 - 100
      });
    });

    describe("when up by values are cached and portfolio price has remained the same", () => {
      const PORTFOLIO_VALUE = 110;
      const PORTFOLIO_VALUE_AFTER_CACHE = 110;
      const BUY_AMOUNT = 100;
      const SETTLED_DATE = new Date("2024-01-01");

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = "2024-01-08";
        Date.now = jest.fn(() => +new Date(TODAY));

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: PORTFOLIO_VALUE_AFTER_CACHE })],
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          }
        });

        const [assetTransaction] = await Promise.all([
          buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: SETTLED_DATE
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            timestamp: DateUtil.getYesterday(),
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_AFTER_CACHE }
          })
        ]);

        const assetBuyOrder = await buildOrder({
          status: "Matched",
          side: "Buy",
          transaction: assetTransaction.id,
          consideration: {
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        assetTransaction.orders = [assetBuyOrder];
        await assetTransaction.save();

        transactions.assetTransactions = [assetTransaction];

        // Cache up by values and portfolio price
        await RedisClientService.Instance.set(`portfolios:up_by:${portfolio.id}`, {
          "1m": 10,
          "1w": 10,
          "1y": 10,
          "3m": 10,
          "6m": 10,
          max: 10
        });
        await RedisClientService.Instance.set(`portfolios:value_at_up_by:${portfolio.id}`, PORTFOLIO_VALUE);
      });

      it("should return all up by values without updating cache", async () => {
        jest.spyOn(RedisClientService.Instance, "set");

        const upBy = await PortfolioService.getPortfolioUpByValuesAllTenors(portfolio, transactions);
        expect(upBy).toEqual({
          "1m": 10,
          "1w": 10,
          "1y": 10,
          "3m": 10,
          "6m": 10,
          max: 10
        });

        expect(RedisClientService.Instance.set).not.toHaveBeenCalled();
      });
    });

    describe("when up by values are cached and portfolio price has increased", () => {
      const PORTFOLIO_VALUE = 110;
      const PORTFOLIO_VALUE_AFTER_CACHE = 120;
      const BUY_AMOUNT = 100;
      const SETTLED_DATE = new Date("2024-01-01");

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = "2024-01-08";
        Date.now = jest.fn(() => +new Date(TODAY));

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: PORTFOLIO_VALUE_AFTER_CACHE })],
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          }
        });

        const [assetTransaction] = await Promise.all([
          buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: SETTLED_DATE
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            timestamp: DateUtil.getYesterday(),
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_AFTER_CACHE }
          })
        ]);

        const assetBuyOrder = await buildOrder({
          status: "Matched",
          side: "Buy",
          transaction: assetTransaction.id,
          consideration: {
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        assetTransaction.orders = [assetBuyOrder];
        await assetTransaction.save();

        transactions.assetTransactions = [assetTransaction];

        // Cache up by values and portfolio price
        await RedisClientService.Instance.set(`portfolios:up_by:${portfolio.id}`, {
          "1m": 10,
          "1w": 10,
          "1y": 10,
          "3m": 10,
          "6m": 10,
          max: 10
        });
        await RedisClientService.Instance.set(`portfolios:value_at_up_by:${portfolio.id}`, PORTFOLIO_VALUE);
      });

      it("should return all up by values factoring portfolio price increase", async () => {
        const upBy = await PortfolioService.getPortfolioUpByValuesAllTenors(portfolio, transactions);
        expect(upBy).toEqual({
          "1m": 20,
          "1w": 20,
          "1y": 20,
          "3m": 20,
          "6m": 20,
          max: 20
        });
      });
    });

    describe("when up by values are cached and portfolio price has decreased", () => {
      const PORTFOLIO_VALUE = 110;
      const PORTFOLIO_VALUE_AFTER_CACHE = 105;
      const BUY_AMOUNT = 100;
      const SETTLED_DATE = new Date("2024-01-01");

      const transactions: {
        assetTransactions: AssetTransactionDocument[];
        chargeTransactions: ChargeTransactionDocument[];
        rebalanceTransactions: RebalanceTransactionDocument[];
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        assetTransactions: [],
        chargeTransactions: [],
        rebalanceTransactions: [],
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        const TODAY = "2024-01-08";
        Date.now = jest.fn(() => +new Date(TODAY));

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: PORTFOLIO_VALUE_AFTER_CACHE })],
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          }
        });

        const [assetTransaction] = await Promise.all([
          buildAssetTransaction({
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: BUY_AMOUNT * 100
            },
            originalInvestmentAmount: BUY_AMOUNT * 100,
            owner: user.id,
            portfolioTransactionCategory: "buy",
            status: "Settled",
            settledAt: SETTLED_DATE
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            timestamp: DateUtil.getYesterday(),
            pricePerCurrency: { GBP: PORTFOLIO_VALUE }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_AFTER_CACHE }
          })
        ]);

        const assetBuyOrder = await buildOrder({
          status: "Matched",
          side: "Buy",
          transaction: assetTransaction.id,
          consideration: {
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        assetTransaction.orders = [assetBuyOrder];
        await assetTransaction.save();

        transactions.assetTransactions = [assetTransaction];

        // Cache up by values and portfolio price
        await RedisClientService.Instance.set(`portfolios:up_by:${portfolio.id}`, {
          "1m": 10,
          "1w": 10,
          "1y": 10,
          "3m": 10,
          "6m": 10,
          max: 10
        });
        await RedisClientService.Instance.set(`portfolios:value_at_up_by:${portfolio.id}`, PORTFOLIO_VALUE);
      });

      it("should return all up by values factoring portfolio price decrease", async () => {
        const upBy = await PortfolioService.getPortfolioUpByValuesAllTenors(portfolio, transactions);
        expect(upBy).toEqual({
          "1m": 5,
          "1w": 5,
          "1y": 5,
          "3m": 5,
          "6m": 5,
          max: 5
        });
      });
    });
  });
});
