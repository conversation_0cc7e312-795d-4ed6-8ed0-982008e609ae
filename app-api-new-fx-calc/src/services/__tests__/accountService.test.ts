import { faker } from "@faker-js/faker";
import AccountService from "../accountService";
import { WealthkernelService, AccountType } from "../../external-services/wealthkernelService";
import { Account, AccountDocument } from "../../models/Account";
import { Subscription } from "../../models/Subscription";
import { KycStatusEnum, User, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildAccount, buildKycOperation, buildSubscription, buildUser } from "../../tests/utils/generateModels";
import { buildWealthkernelAccountResponse } from "../../tests/utils/generateWealthkernel";
import { ProviderEnum } from "../../configs/providersConfig";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { MixpanelAccountStatusEnum } from "../../external-services/segmentAnalyticsService";

describe("AccountService", () => {
  beforeAll(async () => await connectDb("AccountService"));
  afterAll(async () => await closeDb());

  describe("syncAllPendingWkAccounts", () => {
    describe("when the account has status Pending and is Suspended on Wealthkernel", () => {
      let pendingToSuspendedUser: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");

        pendingToSuspendedUser = await buildUser({
          kycStatus: KycStatusEnum.PENDING,
          providers: { wealthkernel: { id: faker.string.uuid() } }
        });
        const pendingToSuspendedAccount = await buildAccount({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          owner: pendingToSuspendedUser._id
        });
        await buildSubscription({ owner: pendingToSuspendedUser.id, active: true });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveAccount")
          .mockImplementation(async (accountId: string): Promise<AccountType> => {
            if (accountId === pendingToSuspendedAccount.providers?.wealthkernel?.id) {
              return buildWealthkernelAccountResponse({
                id: accountId,
                status: "Suspended",
                owner: pendingToSuspendedUser.providers.wealthkernel.id
              });
            }

            return buildWealthkernelAccountResponse();
          });

        await AccountService.syncAllPendingWkAccounts();
      });
      afterAll(async () => await clearDb());

      it("should update the status of the suspended accounts, set kyc status flag to false, deactivate the subscription for the corresponding user and emit an account suspended event", async () => {
        const updatedAccount = (await Account.findOne({ owner: pendingToSuspendedUser.id })) as AccountDocument;
        expect(updatedAccount.toObject()).toEqual(
          expect.objectContaining({
            providers: Object.fromEntries([
              [
                ProviderEnum.WEALTHKERNEL,
                {
                  id: updatedAccount.providers.wealthkernel.id,
                  status: "Suspended"
                }
              ]
            ])
          })
        );

        const updatedUser = (await User.findById(pendingToSuspendedUser.id)) as UserDocument;
        expect(updatedUser.hasFailedKyc).toEqual(true);

        const subscription = await Subscription.findOne({ owner: pendingToSuspendedUser.id });
        expect(subscription?.active).toEqual(false);

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.accountSuspended.eventId,
          expect.objectContaining({ id: pendingToSuspendedUser.id })
        );

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({ id: pendingToSuspendedUser.id }),
          expect.objectContaining({ accountStatus: MixpanelAccountStatusEnum.WkSuspendedClosed })
        );
      });
    });

    describe("when the account has status Pending and is Pending on Wealthkernel for more than 15 minutes", () => {
      let pendingToFailedKycUser: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        pendingToFailedKycUser = await buildUser({
          submittedRequiredInfoAt: new Date(Date.now() - 1 * 1 * 20 * 60 * 1000),
          kycStatus: KycStatusEnum.PENDING,
          providers: { wealthkernel: { id: faker.string.uuid() } }
        });
        const pendingToFailedKycAccount = await buildAccount({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          owner: pendingToFailedKycUser._id
        });
        await buildSubscription({ owner: pendingToFailedKycUser.id, active: true });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveAccount")
          .mockImplementation(async (accountId: string): Promise<AccountType> => {
            if (accountId === pendingToFailedKycAccount.providers?.wealthkernel?.id) {
              return buildWealthkernelAccountResponse({
                id: accountId,
                status: "Pending",
                owner: pendingToFailedKycUser.providers.wealthkernel.id
              });
            }

            return buildWealthkernelAccountResponse();
          });

        await AccountService.syncAllPendingWkAccounts();
      });
      afterAll(async () => await clearDb());

      it("should not update the status of the pending accounts, set kyc status flag to failed and deactivate the subscription for the corresponding user", async () => {
        const updatedAccount = (await Account.findOne({ owner: pendingToFailedKycUser.id })) as AccountDocument;
        expect(updatedAccount.toObject().providers?.wealthkernel?.status).toEqual("Pending");

        const updatedUser = (await User.findById(pendingToFailedKycUser.id)) as UserDocument;
        expect(updatedUser.hasFailedKyc).toEqual(true);

        const subscription = await Subscription.findOne({ owner: pendingToFailedKycUser.id });
        expect(subscription?.active).toEqual(false);
      });
    });

    describe("when the account has status Pending and is Pending on Wealthkernel for more than 15 minutes, and the user has a direct debit subscription", () => {
      let pendingToFailedKycUser: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        pendingToFailedKycUser = await buildUser({
          submittedRequiredInfoAt: new Date(Date.now() - 1 * 1 * 20 * 60 * 1000),
          kycStatus: KycStatusEnum.PENDING,
          providers: { wealthkernel: { id: faker.string.uuid() } }
        });
        const pendingToFailedKycAccount = await buildAccount({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          owner: pendingToFailedKycUser._id
        });
        await buildSubscription({
          owner: pendingToFailedKycUser.id,
          active: true,
          category: "DirectDebitSubscription",
          price: "paid_low_monthly"
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveAccount")
          .mockImplementation(async (accountId: string): Promise<AccountType> => {
            if (accountId === pendingToFailedKycAccount.providers?.wealthkernel?.id) {
              return buildWealthkernelAccountResponse({
                id: accountId,
                status: "Pending",
                owner: pendingToFailedKycUser.providers.wealthkernel.id
              });
            }

            return buildWealthkernelAccountResponse();
          });

        await AccountService.syncAllPendingWkAccounts();
      });
      afterAll(async () => await clearDb());

      it("should not deactivate the subscription for the corresponding user", async () => {
        const subscription = await Subscription.findOne({ owner: pendingToFailedKycUser.id });
        expect(subscription?.active).toEqual(true);
      });
    });

    describe("when the account has status Pending and is Active on Wealthkernel", () => {
      let pendingToActiveUser: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        pendingToActiveUser = await buildUser({
          kycStatus: KycStatusEnum.PENDING,
          providers: { wealthkernel: { id: faker.string.uuid() } }
        });
        const pendingToActiveAccount = await buildAccount({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          owner: pendingToActiveUser._id
        });
        await buildSubscription({ owner: pendingToActiveUser.id, active: false });
        await buildKycOperation({
          owner: pendingToActiveUser.id,
          activeProviders: [ProviderEnum.SUMSUB],
          status: "Passed",
          providers: {
            sumsub: {
              id: faker.string.uuid(),
              status: "completed",
              decision: "GREEN"
            }
          }
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveAccount")
          .mockImplementation(async (accountId: string): Promise<AccountType> => {
            if (accountId === pendingToActiveAccount.providers?.wealthkernel?.id) {
              return buildWealthkernelAccountResponse({
                id: accountId,
                status: "Active",
                owner: pendingToActiveUser.providers.wealthkernel.id
              });
            }

            return buildWealthkernelAccountResponse();
          });

        await AccountService.syncAllPendingWkAccounts();
      });
      afterAll(async () => await clearDb());

      it("should set the status of the pending accounts to active, set kyc status flag to passed and activate the subscription for the corresponding user", async () => {
        const updatedAccount = (await Account.findOne({ owner: pendingToActiveUser.id })) as AccountDocument;
        expect(updatedAccount.toObject()).toEqual(
          expect.objectContaining({
            providers: Object.fromEntries([
              [
                ProviderEnum.WEALTHKERNEL,
                {
                  id: updatedAccount.providers.wealthkernel.id,
                  status: "Active"
                }
              ]
            ])
          })
        );

        const updatedUser = (await User.findById(pendingToActiveUser.id)) as UserDocument;
        expect(updatedUser.hasPassedKyc).toEqual(true);

        const subscription = await Subscription.findOne({ owner: pendingToActiveUser.id });
        expect(subscription?.active).toEqual(true);

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({
            id: pendingToActiveUser.id
          }),
          expect.objectContaining({
            accountStatus: MixpanelAccountStatusEnum.Active
          })
        );
        // we don't want to emit a verification event, because this event will be emitted when user creates a wk portfolio
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.user.verification.eventId,
          expect.objectContaining({ id: pendingToActiveUser.id })
        );
      });
    });

    describe("when the account has status Pending, the kycStatus is failed and the account is Active on Wealthkernel", () => {
      let failedKycToActiveUser: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        failedKycToActiveUser = await buildUser({
          kycStatus: KycStatusEnum.FAILED,
          providers: { wealthkernel: { id: faker.string.uuid() } }
        });
        const failedKycToActiveAccount = await buildAccount({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          owner: failedKycToActiveUser._id
        });
        await buildSubscription({ owner: failedKycToActiveUser.id, active: false });
        await buildKycOperation({
          owner: failedKycToActiveUser.id,
          activeProviders: [ProviderEnum.SUMSUB],
          status: "Passed",
          providers: {
            sumsub: {
              id: faker.string.uuid(),
              status: "completed",
              decision: "GREEN"
            }
          }
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveAccount")
          .mockImplementation(async (accountId: string): Promise<AccountType> => {
            if (accountId === failedKycToActiveAccount.providers?.wealthkernel?.id) {
              return buildWealthkernelAccountResponse({
                id: accountId,
                status: "Active",
                owner: failedKycToActiveUser.providers.wealthkernel.id
              });
            }

            return buildWealthkernelAccountResponse();
          });

        await AccountService.syncAllPendingWkAccounts();
      });
      afterAll(async () => await clearDb());

      it("should set the status of the pending accounts to active, set kyc status flag to passed and activate the subscription for the corresponding user", async () => {
        const updatedAccount = (await Account.findOne({ owner: failedKycToActiveUser.id })) as AccountDocument;
        expect(updatedAccount.toObject().providers?.wealthkernel?.status).toEqual("Active");

        const updatedUser = (await User.findById(failedKycToActiveUser.id)) as UserDocument;
        expect(updatedUser.hasPassedKyc).toEqual(true);

        const subscription = await Subscription.findOne({ owner: failedKycToActiveUser.id });
        expect(subscription?.active).toEqual(true);
      });
    });
  });

  describe("syncAllSuspendedWkAccounts", () => {
    let suspendedToActiveUser: UserDocument;
    let suspendedUser: UserDocument;

    beforeAll(async () => {
      jest.resetAllMocks();

      suspendedToActiveUser = await buildUser({
        kycStatus: KycStatusEnum.FAILED,
        providers: { wealthkernel: { id: faker.string.uuid() } }
      });
      const suspendedToActiveAccount = await buildAccount({
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Suspended"
          }
        },
        owner: suspendedToActiveUser._id
      });

      suspendedUser = await buildUser({
        kycStatus: KycStatusEnum.PENDING,
        providers: { wealthkernel: { id: faker.string.uuid() } }
      });
      const suspendedAccount = await buildAccount({
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Suspended"
          }
        },
        owner: suspendedUser.id
      });

      await buildSubscription({ owner: suspendedToActiveUser.id, active: false });
      await buildKycOperation({
        owner: suspendedToActiveUser.id,
        activeProviders: [ProviderEnum.SUMSUB],
        status: "Passed",
        providers: {
          sumsub: {
            id: faker.string.uuid(),
            status: "completed",
            decision: "GREEN"
          }
        }
      });

      jest
        .spyOn(WealthkernelService.UKInstance, "retrieveAccount")
        .mockImplementation(async (accountId: string): Promise<AccountType> => {
          if (accountId === suspendedToActiveAccount.providers?.wealthkernel?.id) {
            return buildWealthkernelAccountResponse({
              id: accountId,
              status: "Active",
              owner: suspendedToActiveUser?.providers?.wealthkernel?.id
            });
          } else if (accountId === suspendedAccount.providers?.wealthkernel?.id) {
            return buildWealthkernelAccountResponse({
              id: accountId,
              status: "Suspended",
              owner: suspendedAccount?.providers?.wealthkernel?.id
            });
          }

          return buildWealthkernelAccountResponse();
        });

      await AccountService.syncAllSuspendedWkAccounts();
    });
    afterAll(async () => await clearDb());

    it("should set the status of the pending accounts to active, set kyc status flag to passed and activate the subscription for the corresponding user", async () => {
      const updatedAccount = (await Account.findOne({ owner: suspendedToActiveUser.id })) as AccountDocument;
      expect(updatedAccount.toObject().providers?.wealthkernel?.status).toEqual("Active");

      const updatedUser = (await User.findById(suspendedToActiveUser.id)) as UserDocument;
      expect(updatedUser.hasPassedKyc).toEqual(true);

      const subscription = await Subscription.findOne({ owner: suspendedToActiveUser.id });
      expect(subscription?.active).toEqual(true);

      // we don't want to emit a verification event, because this event will be emitted when user creates a wk portfolio
      expect(eventEmitter.emit).not.toHaveBeenCalledWith(
        events.user.verification.eventId,
        expect.objectContaining({ id: suspendedToActiveUser.id })
      );
    });

    it("should not emit a suspended event if the account is already suspended", () => {
      // we don't want to emit a verification event, because this event will be emitted when user creates a wk portfolio
      expect(eventEmitter.emit).not.toHaveBeenCalledWith(
        events.user.accountSuspended.eventId,
        expect.objectContaining({ id: suspendedUser.id })
      );
    });

    it("should emit a 'whAccountStatusUpdate' event for the suspended user", () => {
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.user.whAccountStatusUpdate.eventId,
        expect.objectContaining({ id: suspendedUser.id }),
        expect.objectContaining({ accountStatus: MixpanelAccountStatusEnum.WkSuspendedClosed })
      );
    });

    it("should emit a 'whAccountStatusUpdate' event for the verified user", () => {
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.user.whAccountStatusUpdate.eventId,
        expect.objectContaining({ id: suspendedToActiveUser.id }),
        expect.objectContaining({ accountStatus: MixpanelAccountStatusEnum.Active })
      );
    });
  });
});
