import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import IntraDayTickerService from "../intraDayTickerService";
import { IntraDayAssetTicker } from "../../models/IntraDayTicker";
import DateUtil from "../../utils/dateUtil";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildIntraDayAssetTicker,
  buildIntraDayPortfolioTicker,
  buildInvestmentProduct,
  buildPortfolio,
  buildUser
} from "../../tests/utils/generateModels";

describe("IntraDayTickerService", () => {
  beforeAll(async () => await connectDb("IntraDayTickerService"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("getTodaysTickersForAsset", () => {
    it("should ignore any intra day tickers that do not correspond to the requested asset", async () => {
      // set conditions
      const ASSET_ID: investmentUniverseConfig.AssetType = "equities_apple";
      const ASSET_ID_2: investmentUniverseConfig.AssetType = "equities_coinbase";
      const existingTickers = await IntraDayAssetTicker.find();
      expect(existingTickers.length).toBe(0);

      const YESTERDAY = DateUtil.getDateOfDaysAgo(new Date(), 1);
      const investmentProduct = await buildInvestmentProduct(false, { assetId: ASSET_ID });
      // yestarday ticker
      await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: new Date(YESTERDAY),
        pricePerCurrency: { GBP: 10, EUR: 10, USD: 11 },
        investmentProduct: investmentProduct.id
      });
      // today's ticker
      await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: new Date(),
        pricePerCurrency: { GBP: 20, EUR: 20, USD: 21 },
        investmentProduct: investmentProduct.id
      });

      // run method
      const intraDayAssetTickers = await IntraDayTickerService.getTodaysTickersForAsset(ASSET_ID_2);

      // assertions
      expect(intraDayAssetTickers.length).toBe(0);
    });

    it("should return only today's intra day tickers for the requested asset, sorted by timestamp", async () => {
      // set conditions
      const ASSET_ID: investmentUniverseConfig.AssetType = "equities_apple";
      const existingTickers = await IntraDayAssetTicker.find();
      expect(existingTickers.length).toBe(0);

      const YESTERDAY = DateUtil.getDateOfDaysAgo(new Date(), 1);
      const investmentProduct = await buildInvestmentProduct(false, { assetId: ASSET_ID });
      // yestarday ticker
      await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: new Date(YESTERDAY),
        pricePerCurrency: { GBP: 10, EUR: 10, USD: 11 },
        investmentProduct: investmentProduct.id
      });
      // today's ticker
      const todaysIntraDayTicker = await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: new Date(Date.now() - 1000),
        pricePerCurrency: { GBP: 20, EUR: 20, USD: 21 },
        investmentProduct: investmentProduct.id
      });
      const todaysLatestIntraDayTicker = await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: new Date(),
        pricePerCurrency: { GBP: 22, EUR: 22, USD: 25 },
        investmentProduct: investmentProduct.id
      });

      // run method
      const intraDayAssetTickers = await IntraDayTickerService.getTodaysTickersForAsset(ASSET_ID);

      // assertions
      expect(intraDayAssetTickers.length).toBe(2);

      await intraDayAssetTickers[0].populate("investmentProduct");
      await intraDayAssetTickers[1].populate("investmentProduct");

      expect(intraDayAssetTickers[0]).toMatchObject(
        expect.objectContaining({
          currency: todaysIntraDayTicker.currency,
          timestamp: todaysIntraDayTicker.timestamp,
          pricePerCurrency: expect.objectContaining({
            GBP: todaysIntraDayTicker.pricePerCurrency.GBP,
            EUR: todaysIntraDayTicker.pricePerCurrency.EUR,
            USD: todaysIntraDayTicker.pricePerCurrency.USD
          }),
          investmentProduct: expect.objectContaining({ commonId: ASSET_ID })
        })
      );
      expect(intraDayAssetTickers[1]).toMatchObject(
        expect.objectContaining({
          currency: todaysLatestIntraDayTicker.currency,
          timestamp: todaysLatestIntraDayTicker.timestamp,
          pricePerCurrency: expect.objectContaining({
            GBP: todaysLatestIntraDayTicker.pricePerCurrency.GBP,
            EUR: todaysLatestIntraDayTicker.pricePerCurrency.EUR,
            USD: todaysLatestIntraDayTicker.pricePerCurrency.USD
          }),
          investmentProduct: expect.objectContaining({ commonId: ASSET_ID })
        })
      );
    });
  });

  describe("getMonthsTickersForPortfolio", () => {
    it("should ingore any intra day tickers that do not correspond to the requested portfolio", async () => {
      const owner = await buildUser({ portfolioConversionStatus: "completed", currency: "GBP" });
      const portfolio = await buildPortfolio({
        owner: owner.id
      });
      await buildIntraDayPortfolioTicker({
        currency: owner.currency,
        portfolio: portfolio.id,
        timestamp: new Date() // now
      });

      const owner2 = await buildUser({ portfolioConversionStatus: "completed", currency: "GBP" });
      const portfolio2 = await buildPortfolio({ owner: owner2.id });

      const monthTickers = await IntraDayTickerService.getMonthsTickersForPortfolio(portfolio2.id);
      expect(monthTickers.length).toBe(0);
    });

    it("should return only the month's intra day tickers for the requested portfolio, sorted by timestamp", async () => {
      const owner = await buildUser({ portfolioConversionStatus: "completed", currency: "GBP" });
      const portfolio = await buildPortfolio({
        owner: owner.id
      });
      const latestTicker = await buildIntraDayPortfolioTicker({
        currency: owner.currency,
        portfolio: portfolio.id,
        pricePerCurrency: { GBP: 10, EUR: 10, USD: 11 },
        timestamp: new Date() // now
      });
      const twoWeeksTicker = await buildIntraDayPortfolioTicker({
        currency: owner.currency,
        portfolio: portfolio.id,
        pricePerCurrency: { GBP: 20, EUR: 20, USD: 21 },
        timestamp: DateUtil.getDateOfDaysAgo(new Date(), 14) // 2 weeks
      });
      // two months - will be skipped
      await buildIntraDayPortfolioTicker({
        currency: owner.currency,
        portfolio: portfolio.id,
        pricePerCurrency: { GBP: 30, EUR: 30, USD: 31 },
        timestamp: DateUtil.getDateOfDaysAgo(new Date(), 60) // 2 months
      });

      const intraDayPortfolioTickers = await IntraDayTickerService.getMonthsTickersForPortfolio(portfolio.id);
      expect(intraDayPortfolioTickers.length).toBe(2);

      expect(intraDayPortfolioTickers).toMatchObject(
        expect.arrayContaining([
          expect.objectContaining({
            pricePerCurrency: expect.objectContaining({
              EUR: twoWeeksTicker.pricePerCurrency.EUR,
              GBP: twoWeeksTicker.pricePerCurrency.GBP,
              USD: twoWeeksTicker.pricePerCurrency.USD
            }),
            currency: owner.currency,
            timestamp: new Date(twoWeeksTicker.timestamp),
            portfolio: portfolio._id
          }),
          expect.objectContaining({
            pricePerCurrency: expect.objectContaining({
              EUR: latestTicker.pricePerCurrency.EUR,
              GBP: latestTicker.pricePerCurrency.GBP,
              USD: latestTicker.pricePerCurrency.USD
            }),
            currency: owner.currency,
            timestamp: new Date(latestTicker.timestamp),
            portfolio: portfolio._id
          })
        ])
      );
    });
  });
});
