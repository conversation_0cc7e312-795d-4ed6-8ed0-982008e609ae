import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { faker } from "@faker-js/faker";
import { UserDocument } from "../../models/User";
import {
  buildAssetTransaction,
  buildDailyPortfolioTicker,
  buildDepositCashTransaction,
  buildHoldingDTO,
  buildIntraDayPortfolioTicker,
  buildMandate,
  buildPortfolio,
  buildRebalanceAutomation,
  buildRebalanceTransaction,
  buildSavingsTopUpAutomation,
  buildSubscription,
  buildTopUpAutomation,
  buildUser
} from "../../tests/utils/generateModels";
import AutomationService from "../automationService";
import {
  Automation,
  AutomationDocument,
  AutomationDTOInterface,
  SavingsTopUpAutomationDocument,
  SavingsTopUpAutomationDTOInterface,
  TopUpAutomationDTOInterface,
  TopUpAutomationDocument
} from "../../models/Automation";
import { entitiesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import DateUtil from "../../utils/dateUtil";
import { MandateDocument } from "../../models/Mandate";
import {
  AssetTransaction,
  DepositCashTransaction,
  DepositMethodEnum,
  RebalanceTransaction,
  SavingsTopupTransaction,
  Transaction
} from "../../models/Transaction";
import { PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import { ProviderEnum } from "../../configs/providersConfig";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { PortfolioAllocationMethodEnum } from "../portfolioService";
import { BadRequestError } from "../../models/ApiErrors";

describe("AutomationService", () => {
  beforeAll(async () => await connectDb("AutomationService"));
  afterAll(async () => await closeDb());

  describe("createAllRecurringTopUps", () => {
    describe("when there is only a rebalance automation", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await Transaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is a top-up automation but it's inactive", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 2, // 2 work days from today
          active: false
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await Transaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is an active top-up automation based in UK company entity but its day of month is in 8 work days", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 12,
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await Transaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is an active top-up automation based in EU company entity but its day of month is in 11 work days", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 14,
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await Transaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is an active top-up automation, its day of month is in 2 work days but already has a top-up created", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: AutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 6,
          active: true
        });

        // User already has a deposit transaction created yesterday
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await Transaction.find({ owner: user.id });
        expect(transactions.length).toEqual(1);
      });
    });

    describe("when there is an active top-up automation and its day of month is in 2 work days", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: TopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 2, // 2 work days from today
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit).toEqual(
          expect.objectContaining({
            depositMethod: DepositMethodEnum.DIRECT_DEBIT,
            activeProviders: [],
            directDebit: expect.objectContaining({
              collectionRequestDate: new Date("2022-09-02T11:00:00Z"),
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }),
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            bankAccount: user.bankAccounts[0]._id,
            status: "Pending"
          })
        );
      });

      it("should create an asset transaction pending the above deposit", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });

        const assetTransaction = await AssetTransaction.findOne({ linkedAutomation: automation.id });
        expect(assetTransaction).toEqual(
          expect.objectContaining({
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            pendingDeposit: newDeposit._id
          })
        );
      });
    });

    describe("when there is an active top-up automation and its day of month is the next work day", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: TopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          providers: {
            gocardless: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: -1,
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit).toEqual(
          expect.objectContaining({
            depositMethod: DepositMethodEnum.DIRECT_DEBIT,
            activeProviders: [],
            directDebit: expect.objectContaining({
              collectionRequestDate: new Date("2022-08-30T11:00:00Z"),
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }),
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            bankAccount: user.bankAccounts[0]._id,
            status: "Pending",
            createdWhilePendingMandate: false
          })
        );
      });

      it("should create an asset transaction pending the above deposit", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });

        const assetTransaction = await AssetTransaction.findOne({ linkedAutomation: automation.id });
        expect(assetTransaction).toEqual(
          expect.objectContaining({
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            pendingDeposit: newDeposit._id
          })
        );
      });
    });

    describe("when there is an active top-up automation, its day of month is the next work day and the mandate is still pending", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: TopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          providers: {
            gocardless: {
              id: faker.string.uuid(),
              status: "pending_customer_approval"
            }
          }
        });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: -1,
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation with createdWhilePendingMandate set to true", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit).toEqual(
          expect.objectContaining({
            createdWhilePendingMandate: true
          })
        );
      });
    });

    describe("when there are 2 active top-up automation", () => {
      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const userA = await buildUser({});
        await buildSubscription({ owner: userA.id });
        const portfolioA = await buildPortfolio({
          owner: userA.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });
        const mandateA = await buildMandate({ owner: userA.id, bankAccount: userA.bankAccounts[0].id });
        await buildTopUpAutomation({
          owner: userA.id,
          portfolio: portfolioA.id,
          mandate: mandateA.id,
          dayOfMonth: -1,
          active: true
        });

        const userB = await buildUser({});
        await buildSubscription({ owner: userB.id });
        const portfolioB = await buildPortfolio({
          owner: userB.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)]
        });
        const mandateB = await buildMandate({ owner: userA.id, bankAccount: userA.bankAccounts[0].id });
        await buildTopUpAutomation({
          owner: userB.id,
          portfolio: portfolioB.id,
          mandate: mandateB.id,
          dayOfMonth: -1,
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create 2 deposits", async () => {
        const newDeposits = await DepositCashTransaction.find({ linkedAutomation: { $exists: true } });
        expect(newDeposits).toHaveLength(2);
      });

      it("should create 2 asset transactions", async () => {
        const assetTransactions = await AssetTransaction.find({ linkedAutomation: { $exists: true } });
        expect(assetTransactions).toHaveLength(2);
      });
    });

    describe("when there is an active top-up automation, its day of month is the next work day and user's company entity is Europe", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: TopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: -1,
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit.toObject()).toEqual(
          expect.objectContaining({
            depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
            activeProviders: [ProviderEnum.DEVENGO, ProviderEnum.WEALTHKERNEL],
            directDebit: expect.objectContaining({
              activeProviders: [ProviderEnum.GOCARDLESS]
            }),
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            transferWithIntermediary: {
              collection: {
                incomingPayment: {
                  providers: {
                    devengo: {
                      accountId: process.env.DEVENGO_GOCARDLESS_PAYOUTS_COLLECTION_ACCOUNT_ID
                    }
                  }
                }
              }
            },
            linkedAutomation: automation._id,
            bankAccount: user.bankAccounts[0]._id,
            status: "Pending"
          })
        );
      });

      it("should create an asset transaction pending the above deposit", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });

        const assetTransaction = await AssetTransaction.findOne({ linkedAutomation: automation.id });
        expect(assetTransaction).toEqual(
          expect.objectContaining({
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            pendingDeposit: newDeposit._id
          })
        );
      });
    });

    describe("when there is an active top-up automation, its day of month is the next work day and the initialisedAt date is in the past", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: TopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        const LAST_WEEK = new Date("2022-08-23T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: -1,
          active: true,
          initialiseAt: LAST_WEEK
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit).toEqual(
          expect.objectContaining({
            depositMethod: DepositMethodEnum.DIRECT_DEBIT,
            activeProviders: [],
            directDebit: expect.objectContaining({
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }),
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            bankAccount: user.bankAccounts[0]._id,
            status: "Pending"
          })
        );
      });

      it("should create an asset transaction pending the above deposit", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });

        const assetTransaction = await AssetTransaction.findOne({ linkedAutomation: automation.id });
        expect(assetTransaction).toEqual(
          expect.objectContaining({
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            pendingDeposit: newDeposit._id
          })
        );
      });
    });

    describe("when there is an active top-up automation, its day of month is the next work day and the initialisedAt date is in the future", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: TopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        const NEXT_WEEK = new Date("2022-09-06T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: -1,
          active: true,
          initialiseAt: NEXT_WEEK
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create a deposit transaction for this automation", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit).toBeNull();
      });

      it("should create an asset transaction pending the above deposit", async () => {
        const assetTransaction = await AssetTransaction.findOne({ linkedAutomation: automation.id });
        expect(assetTransaction).toBeNull();
      });
    });

    describe("when there is an active top-up automation, but the user has no holdings or target allocation", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: TopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [],
          initialHoldingsAllocation: []
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 2, // 2 work days from today
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit).toEqual(
          expect.objectContaining({
            depositMethod: DepositMethodEnum.DIRECT_DEBIT,
            activeProviders: [],
            directDebit: expect.objectContaining({
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }),
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            bankAccount: user.bankAccounts[0]._id,
            status: "Pending"
          })
        );
      });

      it("should NOT create an asset transaction pending the above deposit", async () => {
        const assetTransaction = await AssetTransaction.findOne({ linkedAutomation: automation.id });
        expect(assetTransaction).toBeNull();
      });
    });

    // this is a test for a bug fix - dates are copied from the issue
    describe("when there is an active top-up automation, for UK user and its day of month is -1", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: TopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2024-10-21T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          providers: {
            gocardless: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: -1,
          active: true
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation", async () => {
        const newDeposits = await DepositCashTransaction.find({ linkedAutomation: automation.id });
        expect(newDeposits.length).toBe(1);

        const newDeposit = newDeposits[0];
        expect(newDeposit).toEqual(
          expect.objectContaining({
            depositMethod: DepositMethodEnum.DIRECT_DEBIT,
            activeProviders: [],
            directDebit: expect.objectContaining({
              collectionRequestDate: new Date("2024-10-29T11:00:00Z"),
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }),
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            bankAccount: user.bankAccounts[0]._id,
            status: "Pending",
            createdWhilePendingMandate: false
          })
        );
      });

      it("should create an asset transaction pending the above deposit", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });

        const assetTransaction = await AssetTransaction.findOne({ linkedAutomation: automation.id });
        expect(assetTransaction).toEqual(
          expect.objectContaining({
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            pendingDeposit: newDeposit._id
          })
        );
      });
    });

    // this is a test for a bug fix - dates are copied from the issue
    describe("when there is an active top-up automation, for UK user and its day of month is -1 and top-up & investment docs were created 10 days ago", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: TopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const OLD_TODAY = new Date("2024-10-21T11:00:00Z");
        const TODAY = new Date("2024-10-31T11:00:02Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          providers: {
            gocardless: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: -1,
          active: true
        });

        const deposit = await buildDepositCashTransaction({
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          owner: automation.owner._id,
          portfolio: automation.portfolio._id,
          consideration: automation.consideration,
          activeProviders: [],
          providers: {},
          linkedAutomation: automation._id,
          bankAccount: mandate.bankAccount._id,
          createdAt: OLD_TODAY,
          status: "Pending",
          createdWhilePendingMandate: false,
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            collectionRequestDate: new Date("2024-10-29T11:00:00.000Z"),
            providers: {}
          }
        });
        await buildAssetTransaction({
          createdAt: OLD_TODAY,
          consideration: automation.consideration,
          owner: user._id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "PendingDeposit",
          pendingDeposit: deposit._id,
          linkedAutomation: automation._id
        });

        await AutomationService.createAllRecurringTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create a new deposit & asset transaction document", async () => {
        const newDeposits = await DepositCashTransaction.find({ linkedAutomation: automation.id });
        const newAssetTransactions = await AssetTransaction.find({ linkedAutomation: automation.id });

        expect(newDeposits.length).toBe(1);
        expect(newAssetTransactions.length).toBe(1);
      });
    });
  });

  describe("createAllRecurringSavingsTopUps", () => {
    describe("when there is only a rebalance automation", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        await AutomationService.createAllRecurringSavingsTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await Transaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is a savings top-up automation but it's inactive", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        await buildSavingsTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 2, // 2 work days from today
          active: false
        });

        await AutomationService.createAllRecurringSavingsTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await Transaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is an active savings top-up automation but its day of month is in 7 work days", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        await buildSavingsTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 9,
          active: true
        });

        await AutomationService.createAllRecurringSavingsTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await Transaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is an active savings top-up automation, its day of month is in 2 work days but already has a savings top-up created", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: AutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildSavingsTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 6,
          active: true
        });

        // User already has a deposit transaction created yesterday
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });

        await AutomationService.createAllRecurringSavingsTopUps();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await Transaction.find({ owner: user.id });
        expect(transactions.length).toEqual(1);
      });
    });

    describe("when there are 2 active savings top-up automation", () => {
      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const userA = await buildUser({});
        await buildSubscription({ owner: userA.id });
        const portfolioA = await buildPortfolio({
          owner: userA.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });
        const mandateA = await buildMandate({ owner: userA.id, bankAccount: userA.bankAccounts[0].id });
        await buildSavingsTopUpAutomation({
          owner: userA.id,
          portfolio: portfolioA.id,
          mandate: mandateA.id,
          dayOfMonth: -1,
          active: true
        });

        const userB = await buildUser({});
        await buildSubscription({ owner: userB.id });
        const portfolioB = await buildPortfolio({
          owner: userB.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)]
        });
        const mandateB = await buildMandate({ owner: userA.id, bankAccount: userA.bankAccounts[0].id });
        await buildSavingsTopUpAutomation({
          owner: userB.id,
          portfolio: portfolioB.id,
          mandate: mandateB.id,
          dayOfMonth: -1,
          active: true
        });

        await AutomationService.createAllRecurringSavingsTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create 2 deposits", async () => {
        const newDeposits = await DepositCashTransaction.find({ linkedAutomation: { $exists: true } });
        expect(newDeposits).toHaveLength(2);
      });

      it("should create 2 savings topups", async () => {
        const savingsTopups = await SavingsTopupTransaction.find({ linkedAutomation: { $exists: true } });
        expect(savingsTopups).toHaveLength(2);
      });
    });

    describe("when there is an active savings top-up automation and its day of month is in 2 work days", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: SavingsTopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildSavingsTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 2, // 2 work days from today
          active: true
        });

        await AutomationService.createAllRecurringSavingsTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit).toEqual(
          expect.objectContaining({
            depositMethod: DepositMethodEnum.DIRECT_DEBIT,
            activeProviders: [],
            directDebit: expect.objectContaining({
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }),
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            bankAccount: user.bankAccounts[0]._id,
            status: "Pending"
          })
        );
      });

      it("should create an savings topup transaction pending the above deposit", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });

        const savingsTransaction = await SavingsTopupTransaction.findOne({ linkedAutomation: automation.id });
        expect(savingsTransaction).toEqual(
          expect.objectContaining({
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            pendingDeposit: newDeposit._id
          })
        );
      });
    });

    describe("when there is an active savings top-up automation and its day of month is the next work day", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: SavingsTopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({});
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildSavingsTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: -1,
          active: true
        });

        await AutomationService.createAllRecurringSavingsTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit).toEqual(
          expect.objectContaining({
            depositMethod: DepositMethodEnum.DIRECT_DEBIT,
            activeProviders: [],
            directDebit: expect.objectContaining({
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }),
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            bankAccount: user.bankAccounts[0]._id,
            status: "Pending"
          })
        );
      });

      it("should create an savings top up transaction pending the above deposit", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });

        const savingsTransaction = await SavingsTopupTransaction.findOne({ linkedAutomation: automation.id });
        expect(savingsTransaction).toEqual(
          expect.objectContaining({
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            pendingDeposit: newDeposit._id
          })
        );
      });
    });

    describe("when there is an active savings top-up automation and user's company entity is Europe", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: SavingsTopUpAutomationDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-30T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });

        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });

        automation = await buildSavingsTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: -1,
          active: true
        });

        await AutomationService.createAllRecurringSavingsTopUps();
      });
      afterAll(async () => await clearDb());

      it("should create a deposit transaction for this automation", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });
        expect(newDeposit).toEqual(
          expect.objectContaining({
            depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
            activeProviders: [ProviderEnum.DEVENGO, ProviderEnum.WEALTHKERNEL],
            directDebit: expect.objectContaining({
              activeProviders: [ProviderEnum.GOCARDLESS]
            }),
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            bankAccount: user.bankAccounts[0]._id,
            status: "Pending"
          })
        );
      });

      it("should create an savings top up transaction pending the above deposit", async () => {
        const newDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automation.id });

        const savingsTransaction = await SavingsTopupTransaction.findOne({ linkedAutomation: automation.id });
        expect(savingsTransaction).toEqual(
          expect.objectContaining({
            owner: automation.owner,
            portfolio: portfolio._id,
            consideration: {
              amount: automation.consideration.amount,
              currency: automation.consideration.currency
            },
            linkedAutomation: automation._id,
            pendingDeposit: newDeposit._id
          })
        );
      });
    });
  });

  describe("createAllAutomatedRebalances", () => {
    describe("when there is only a top-up automation", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: []
        });

        await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          category: "TopUpAutomation",
          dayOfMonth: 6,
          active: true
        });

        await AutomationService.createAllAutomatedRebalances();
      });
      afterAll(async () => await clearDb());

      it("should not create any rebalance transactions", async () => {
        const transactions = await RebalanceTransaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is a rebalance automation but it's inactive", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const WK_PORTFOLIO_ID = "WK-ID";

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          holdings: []
        });

        await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: false
        });

        await AutomationService.createAllAutomatedRebalances();
      });
      afterAll(async () => await clearDb());

      it("should not create any rebalance transactions", async () => {
        const transactions = await RebalanceTransaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is an active rebalance automation but portfolio is already balanced", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: "passed" });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "equities_china", quantity: 2.5, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 2.5, price: 10, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 30, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 20, percentage: 20 }
        ];

        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          holdings
        });
        await buildDailyPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: {
            GBP: 86
          },
          date: TODAY
        });

        await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        await AutomationService.createAllAutomatedRebalances();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await RebalanceTransaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is an active rebalance automation, portfolio is imbalanced but it has value of less than £10", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: "passed" });
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "equities_china", quantity: 1, price: 2, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 2, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 2.7, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 1.7, percentage: 19 },
          { assetId: "equities_us", quantity: 0, price: 1.7, percentage: 1 },
          { assetId: "equities_jp", quantity: 2, price: 1, percentage: 0 }
        ];

        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.filter((config) => config.percentage > 0).map(
            (config) => ({
              assetCommonId: config.assetId,
              percentage: config.percentage
            })
          ),
          holdings
        });
        await buildDailyPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: {
            GBP: 8.6
          },
          date: TODAY
        });

        await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        await AutomationService.createAllAutomatedRebalances();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await RebalanceTransaction.find({ owner: user.id });
        expect(transactions.length).toEqual(0);
      });
    });

    describe("when there is an active rebalance automation, portfolio is imbalanced but it already has a rebalance created yesterday", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: AutomationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: "passed" });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "equities_china", quantity: 1, price: 20, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 20, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 27, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 17, percentage: 19 },
          { assetId: "equities_us", quantity: 0, price: 17, percentage: 1 },
          { assetId: "equities_jp", quantity: 2, price: 1, percentage: 0 }
        ];

        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          holdings
        });
        await buildDailyPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: {
            GBP: 86
          },
          date: TODAY
        });

        automation = await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        // User already has a rebalance transaction created yesterday
        await buildRebalanceTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });

        await AutomationService.createAllAutomatedRebalances();
      });
      afterAll(async () => await clearDb());

      it("should not create any transactions", async () => {
        const transactions = await RebalanceTransaction.find({ owner: user.id });
        expect(transactions.length).toEqual(1);
      });
    });

    describe("when there is an active rebalance automation, portfolio is imbalanced and it does not have a rebalance created for this month", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let automation: AutomationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: "passed" });
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "equities_china", quantity: 1, price: 20, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 20, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 27, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 17, percentage: 19 },
          { assetId: "equities_us", quantity: 0, price: 17, percentage: 1 },
          { assetId: "equities_jp", quantity: 2, price: 1, percentage: 0 }
        ];

        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.filter((config) => config.percentage > 0).map(
            (config) => ({
              assetCommonId: config.assetId,
              percentage: config.percentage
            })
          ),
          holdings
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: {
            GBP: 86
          },
          timestamp: TODAY
        });

        automation = await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        await AutomationService.createAllAutomatedRebalances();
      });
      afterAll(async () => await clearDb());

      it("should create a rebalance transaction for this automation", async () => {
        const newRebalance = await RebalanceTransaction.findOne({ linkedAutomation: automation.id });
        expect(newRebalance).toEqual(
          expect.objectContaining({
            owner: automation.owner,
            portfolio: portfolio._id,
            targetAllocation: [
              expect.objectContaining({ assetCommonId: "equities_china", percentage: 25 }),
              expect.objectContaining({ assetCommonId: "equities_global", percentage: 25 }),
              expect.objectContaining({ assetCommonId: "equities_uk", percentage: 30 }),
              expect.objectContaining({ assetCommonId: "equities_eu", percentage: 19 }),
              expect.objectContaining({ assetCommonId: "equities_us", percentage: 1 })
            ],
            linkedAutomation: automation._id
          })
        );
      });
    });
  });

  describe("cancelAutomation", () => {
    describe("when automation is a top-up automation", () => {
      let user: UserDocument;
      let automation: AutomationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({});
        const portfolio = await buildPortfolio({ owner: user.id });
        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          category: "Top-Up"
        });

        automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });

        await AutomationService.cancelAutomation(automation);
      });
      afterAll(async () => await clearDb());

      it("should set automation active status as false", async () => {
        const updatedAutomation = await Automation.findById(automation.id);
        expect(updatedAutomation.active).toBe(false);
      });

      it("should emit a 'recurringInvestmentCancellation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCancellation.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when automation is a savings top-up automation", () => {
      let user: UserDocument;
      let automation: AutomationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({});
        const portfolio = await buildPortfolio({ owner: user.id });
        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          category: "Top-Up"
        });

        automation = await buildSavingsTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true,
          savingsProduct: "mmf_dist_gbp"
        });

        await AutomationService.cancelAutomation(automation);
      });

      afterAll(async () => await clearDb());

      it("should set automation active status as false", async () => {
        const updatedAutomation = await Automation.findById(automation.id);
        expect(updatedAutomation.active).toBe(false);
      });

      it("should emit a 'recurringSavingsCancellation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringSavingsCancellation.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when automation is a rebalance automation", () => {
      let user: UserDocument;
      let automation: AutomationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({});
        const portfolio = await buildPortfolio({ owner: user.id });

        automation = await buildRebalanceAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        await AutomationService.cancelAutomation(automation);
      });

      afterAll(async () => await clearDb());

      it("should set automation active status as false", async () => {
        const updatedAutomation = await Automation.findById(automation.id);
        expect(updatedAutomation.active).toBe(false);
      });

      it("should emit a 'recurringRebalanceCancellation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringRebalanceCancellation.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });
  });

  describe("createOrUpdateAutomation", () => {
    beforeAll(() => {
      jest.clearAllMocks();
      const TODAY = new Date("2024-11-26T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());
    });

    describe("when the automation is a RebalanceAutomation", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        user = await buildUser({});
        portfolio = await buildPortfolio({ owner: user.id });
      });

      afterAll(async () => {
        await clearDb();
      });

      it("should create a new RebalanceAutomation when none exists", async () => {
        const automationData: AutomationDTOInterface = {
          category: "RebalanceAutomation",
          owner: user.id,
          portfolio: portfolio.id,
          frequency: "monthly"
        };

        const automation = await AutomationService.createOrUpdateAutomation(automationData);

        expect(automation).toBeDefined();
        expect(automation.category).toBe("RebalanceAutomation");
        expect(automation.owner.toString()).toBe(user.id);
        expect(automation.portfolio.toString()).toBe(portfolio.id);
        expect(automation.active).toBe(true);
      });
    });

    describe("when the automation is a TopUpAutomation", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        user = await buildUser({});
        portfolio = await buildPortfolio({ owner: user.id });
        mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });
      });

      afterAll(async () => {
        await clearDb();
      });

      it("should create a new TopUpAutomation when none exists", async () => {
        const automationData: TopUpAutomationDTOInterface = {
          category: "TopUpAutomation",
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 1,
          consideration: { amount: 10000, currency: "GBP" },
          allocationMethod: PortfolioAllocationMethodEnum.HOLDINGS,
          frequency: "monthly"
        };

        const automation = await AutomationService.createOrUpdateAutomation(automationData);

        expect(automation).toBeDefined();
        expect(automation.category).toBe("TopUpAutomation");
        expect(automation.owner.toString()).toBe(user.id);
        expect(automation.portfolio.toString()).toBe(portfolio.id);
        expect(automation.mandate.id.toString()).toBe(mandate.id);
        expect(automation.dayOfMonth).toBe(1);
        expect(automation.consideration.amount).toBe(10000);
        expect(automation.consideration.currency).toBe("GBP");
        expect(automation.active).toBe(true);

        const existingDepositTransactions = await DepositCashTransaction.find({
          owner: user.id,
          linkedAutomation: automation.id
        });
        expect(existingDepositTransactions.length).toBe(1);
      });

      it("should update existing TopUpAutomation when one exists", async () => {
        // Create an existing automation
        const automationData: TopUpAutomationDTOInterface = {
          category: "TopUpAutomation",
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 1,
          consideration: { amount: 10000, currency: "GBP" },
          allocationMethod: PortfolioAllocationMethodEnum.HOLDINGS,
          frequency: "monthly"
        };

        await AutomationService.createOrUpdateAutomation(automationData);

        // Update the automation
        const updatedAutomationData: TopUpAutomationDTOInterface = {
          ...automationData,
          dayOfMonth: 15,
          consideration: { amount: 20000, currency: "GBP" }
        };

        const automation = await AutomationService.createOrUpdateAutomation(updatedAutomationData);

        expect(automation).toBeDefined();
        expect(automation.dayOfMonth).toBe(15);
        expect(automation.consideration.amount).toBe(20000);

        const existingDepositTransactions = await DepositCashTransaction.find({
          owner: user.id,
          linkedAutomation: automation.id
        });
        expect(existingDepositTransactions.length).toBe(1);
      });

      it("should not create a new DepositCashTransaction when activating an automation that already has a repeating deposit", async () => {
        // Create and cancel an automation
        const automationData: TopUpAutomationDTOInterface = {
          category: "TopUpAutomation",
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 22,
          consideration: { amount: 10000, currency: "GBP" },
          allocationMethod: PortfolioAllocationMethodEnum.HOLDINGS,
          frequency: "monthly"
        };
        const automation = await AutomationService.createOrUpdateAutomation(automationData);
        // Fetch all deposit transactions linked to the automation
        const existingDepositTransactions = await DepositCashTransaction.find({
          owner: user.id,
          linkedAutomation: automation.id
        });
        expect(existingDepositTransactions.length).toBe(1);

        // cancel automation
        await AutomationService.cancelAutomation(automation);
        // Call method to active automation again
        await AutomationService.createOrUpdateAutomation(automationData);

        const depositTransactions = await DepositCashTransaction.find({
          owner: user.id,
          linkedAutomation: automation.id
        });
        // Verify that no new deposit transaction is created
        expect(depositTransactions.length).toBe(1);
        expect(depositTransactions[0].id.toString()).toBe(existingDepositTransactions[0].id.toString());
      });
    });

    describe("when the automation is a SavingsTopUpAutomation", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        user = await buildUser({});
        portfolio = await buildPortfolio({ owner: user.id });
        mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });
      });

      afterAll(async () => {
        await clearDb();
      });

      it("should create a new SavingsTopUpAutomation when none exists", async () => {
        const automationData: SavingsTopUpAutomationDTOInterface = {
          category: "SavingsTopUpAutomation",
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 1,
          consideration: { amount: 10000, currency: "GBP" },
          savingsProduct: "mmf_dist_gbp",
          frequency: "monthly"
        };

        const automation = await AutomationService.createOrUpdateAutomation(automationData);

        expect(automation).toBeDefined();
        expect(automation.category).toBe("SavingsTopUpAutomation");
        expect(automation.owner.toString()).toBe(user.id);
        expect(automation.portfolio.id.toString()).toBe(portfolio.id);
        expect(automation.mandate.id.toString()).toBe(mandate.id);
        expect(automation.dayOfMonth).toBe(1);
        expect(automation.consideration.amount).toBe(10000);
        expect(automation.consideration.currency).toBe("GBP");
        expect(automation.savingsProduct).toBe("mmf_dist_gbp");
        expect(automation.active).toBe(true);

        const existingDepositTransactions = await DepositCashTransaction.find({
          owner: user.id,
          linkedAutomation: automation.id
        });
        expect(existingDepositTransactions.length).toBe(1);
      });

      it("should update existing SavingsTopUpAutomation when one exists", async () => {
        // Create an existing automation
        const automationData: SavingsTopUpAutomationDTOInterface = {
          category: "SavingsTopUpAutomation",
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 1,
          consideration: { amount: 10000, currency: "GBP" },
          savingsProduct: "mmf_dist_gbp",
          frequency: "monthly"
        };

        await AutomationService.createOrUpdateAutomation(automationData);

        // Update the automation
        const updatedAutomationData: SavingsTopUpAutomationDTOInterface = {
          ...automationData,
          dayOfMonth: 15,
          consideration: { amount: 20000, currency: "GBP" }
        };

        const automation = await AutomationService.createOrUpdateAutomation(updatedAutomationData);

        expect(automation).toBeDefined();
        expect(automation.dayOfMonth).toBe(15);
        expect(automation.consideration.amount).toBe(20000);

        const existingDepositTransactions = await DepositCashTransaction.find({
          owner: user.id,
          linkedAutomation: automation.id
        });
        expect(existingDepositTransactions.length).toBe(1);
      });
    });

    describe("when the automation is of unsupported category", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        user = await buildUser({});
        portfolio = await buildPortfolio({ owner: user.id });
      });

      afterAll(async () => {
        await clearDb();
      });

      it("should throw BadRequestError for unsupported automation category", async () => {
        const automationData: any = {
          category: "UnsupportedCategory" as any,
          owner: user.id,
          portfolio: portfolio.id
        };

        await expect(AutomationService.createOrUpdateAutomation(automationData)).rejects.toThrow(BadRequestError);
      });
    });
  });
});
