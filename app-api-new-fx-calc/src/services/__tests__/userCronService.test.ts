import { KycStatusEnum, User, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildAddress, buildUser } from "../../tests/utils/generateModels";
import UserCronService from "../../jobs/services/userCronService";
import { GoCardlessPaymentsService } from "../../external-services/goCardlessPaymentsService";
import DateUtil from "../../utils/dateUtil";
import { AddressDocument } from "../../models/Address";
import { ProviderEnum } from "../../configs/providersConfig";

describe("UserCronService", () => {
  beforeAll(async () => await connectDb("UserCronService"));
  afterAll(async () => await closeDb());

  describe("createAllGoCardlessCustomers", () => {
    describe("when user has not submitted all required info", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createCustomer");

        await buildUser({ submittedRequiredInfoAt: undefined });

        await UserCronService.createAllGoCardlessCustomers();
      });
      afterAll(async () => await clearDb());

      it("should not call GoCardless", async () => {
        expect(GoCardlessPaymentsService.Instance.createCustomer).not.toHaveBeenCalled();
      });
    });

    describe("when user has submitted all required info less than 10 minutes ago", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createCustomer");

        await buildUser({ submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5) });

        await UserCronService.createAllGoCardlessCustomers();
      });
      afterAll(async () => await clearDb());

      it("should not call GoCardless", async () => {
        expect(GoCardlessPaymentsService.Instance.createCustomer).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible and hasn't been created in GoCardless", () => {
      let user: UserDocument;
      let address: AddressDocument;

      const goCardlessID = "CU-123";

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createCustomer").mockResolvedValue({ id: goCardlessID });

        user = await buildUser({
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(15),
          activeProviders: [ProviderEnum.GOCARDLESS]
        });
        address = await buildAddress({ owner: user.id });

        await UserCronService.createAllGoCardlessCustomers();
      });
      afterAll(async () => await clearDb());

      it("should call GoCardless", async () => {
        expect(GoCardlessPaymentsService.Instance.createCustomer).toHaveBeenCalledTimes(1);
        expect(GoCardlessPaymentsService.Instance.createCustomer).toHaveBeenCalledWith({
          email: user.email,
          given_name: user.firstName,
          family_name: user.lastName,
          address_line1: address.line1,
          address_line2: address.line2,
          city: address.city,
          postal_code: address.postalCode,
          country_code: address.countryCode,
          metadata: {
            wealthyhoodId: user.id
          }
        });
      });

      it("should update the user document with the GoCardless ID", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.providers?.gocardless?.id).toEqual(goCardlessID);
      });
    });

    describe("when user has already been created in GoCardless", () => {
      const goCardlessID = "CU-123";

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createCustomer");

        await buildUser({
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(15),
          kycStatus: KycStatusEnum.PASSED,
          providers: { gocardless: { id: goCardlessID } }
        });

        await UserCronService.createAllGoCardlessCustomers();
      });
      afterAll(async () => await clearDb());

      it("should not call GoCardless", async () => {
        expect(GoCardlessPaymentsService.Instance.createCustomer).not.toHaveBeenCalled();
      });
    });
  });
});
