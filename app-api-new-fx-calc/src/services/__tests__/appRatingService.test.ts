import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAppRating,
  buildAssetTransaction,
  buildTopUpAutomation,
  buildUser
} from "../../tests/utils/generateModels";
import AppRatingService from "../appRatingService";
import { AppRating, AppRatingDocument, AppRatingStatusEnum } from "../../models/AppRating";

describe("AppRatingService", () => {
  beforeAll(async () => await connectDb("AppRatingService"));
  afterAll(async () => await closeDb());

  describe("getPrompt", () => {
    describe("when user has skipped app rating more than 1 month ago", () => {
      let user: UserDocument;
      let appRating: AppRatingDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => new Date("2024-02-04").getTime());

        user = await buildUser();
        appRating = await buildAppRating({
          owner: user.id,
          createdAt: new Date("2024-01-01")
        });
      });
      afterAll(async () => await clearDb());

      it("should should return a new app rating id and show the app rating prompt", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const latestAppRating = await AppRating.findOne({ owner: user.id }).sort({ createdAt: -1 });

        expect(prompt).toEqual({
          appRatingId: latestAppRating.id
        });

        expect(prompt.appRatingId).not.toEqual(appRating.id);
      });
    });

    describe("when user has skipped app rating less than 1 month ago", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => new Date("2024-01-25").getTime());

        user = await buildUser();
        await buildAppRating({
          owner: user.id,
          createdAt: new Date("2024-01-01")
        });
      });
      afterAll(async () => await clearDb());

      it("should should not create a new app rating", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const appRatings = await AppRating.find({ owner: user.id });
        expect(appRatings).toHaveLength(1);

        expect(prompt).toBeUndefined();
      });
    });

    describe("when user has an app rating <5 stars more than 2 months ago", () => {
      let user: UserDocument;
      let appRating: AppRatingDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => new Date("2024-03-04").getTime());

        user = await buildUser();
        appRating = await buildAppRating({
          owner: user.id,
          createdAt: new Date("2024-01-01"),
          status: AppRatingStatusEnum.COMPLETED,
          starRating: 4,
          feedback: "ios app sucks"
        });
      });
      afterAll(async () => await clearDb());

      it("should should return a new app rating id and show the app rating prompt", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const latestAppRating = await AppRating.findOne({ owner: user.id }).sort({ createdAt: -1 });

        expect(prompt).toEqual({
          appRatingId: latestAppRating.id
        });

        expect(prompt.appRatingId).not.toEqual(appRating.id);
      });
    });

    describe("when user has an app rating <5 stars less than 2 months ago", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => new Date("2024-02-25").getTime());

        user = await buildUser();
        await buildAppRating({
          owner: user.id,
          createdAt: new Date("2024-01-01"),
          status: AppRatingStatusEnum.COMPLETED,
          starRating: 4,
          feedback: "lightyear is better"
        });
      });
      afterAll(async () => await clearDb());

      it("should should not create a new app rating", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const appRatings = await AppRating.find({ owner: user.id });
        expect(appRatings).toHaveLength(1);

        expect(prompt).toBeUndefined();
      });
    });

    describe("when user has no app rating but has an active automation and invested", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
        await buildAssetTransaction({ owner: user.id });
        await buildTopUpAutomation({ owner: user.id, active: true });
      });
      afterAll(async () => await clearDb());

      it("should should return a new app rating id and show the app rating prompt", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const appRatings = await AppRating.find({ owner: user.id });

        expect(appRatings).toHaveLength(1);

        expect(prompt).toEqual({
          appRatingId: appRatings[0].id
        });
      });
    });

    describe("when user has no app rating but has an active automation", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
        await buildTopUpAutomation({ owner: user.id, active: true });
      });
      afterAll(async () => await clearDb());

      it("should should return a new app rating id and show the app rating prompt", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const appRatings = await AppRating.find({ owner: user.id });

        expect(appRatings).toHaveLength(1);

        expect(prompt).toEqual({
          appRatingId: appRatings[0].id
        });
      });
    });

    describe("when user has no app rating but has an invested once", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
        await buildAssetTransaction({ owner: user.id });
      });
      afterAll(async () => await clearDb());

      it("should should return a new app rating id and show the app rating prompt", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const appRatings = await AppRating.find({ owner: user.id });

        expect(appRatings).toHaveLength(1);

        expect(prompt).toEqual({
          appRatingId: appRatings[0].id
        });
      });
    });

    describe("when user has no app rating no automation and no investments", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should should not create a new app rating", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const appRatings = await AppRating.find({ owner: user.id });
        expect(appRatings).toHaveLength(0);

        expect(prompt).toBeUndefined();
      });
    });

    describe("when user has no app rating but has only one cancelled investment", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
        await buildAssetTransaction({ owner: user.id, status: "Cancelled" });
      });
      afterAll(async () => await clearDb());

      it("should should not create a new app rating", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const appRatings = await AppRating.find({ owner: user.id });
        expect(appRatings).toHaveLength(0);

        expect(prompt).toBeUndefined();
      });
    });

    describe("when user has no app rating but has an inactive automation", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
        await buildTopUpAutomation({ owner: user.id, active: false });
      });
      afterAll(async () => await clearDb());

      it("should should not create a new app rating", async () => {
        const prompt = await AppRatingService.getPrompt(user);

        const appRatings = await AppRating.find({ owner: user.id });
        expect(appRatings).toHaveLength(0);

        expect(prompt).toBeUndefined();
      });
    });
  });
});
