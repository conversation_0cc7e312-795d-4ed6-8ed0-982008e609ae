import { User, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildUser } from "../../tests/utils/generateModels";
import { Address, AddressDTOInterface } from "../../models/Address";
import mongoose from "mongoose";
import AddressService from "../addressService";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import PostCodeFetchifyService from "../../external-services/postCodeFetchifyService";
import { ProviderEnum } from "../../configs/providersConfig";
import { entitiesConfig } from "@wealthyhood/shared-configs";

describe("AddressService", () => {
  beforeAll(async () => await connectDb("AddressService"));
  afterAll(async () => await closeDb());

  describe("createOrUpdateAddress", () => {
    describe("when user has a non-UK address", () => {
      let user: UserDocument;

      beforeEach(async () => {
        jest.resetAllMocks();
        jest.spyOn(PostCodeFetchifyService, "validateUKPostCode").mockImplementation((): any => null);

        user = await buildUser();

        const addressData: AddressDTOInterface = {
          owner: new mongoose.Types.ObjectId(user._id),
          line1: "Thessalonikis 63",
          city: "Nea Filadelfeia",
          countryCode: "GR",
          postalCode: "14342",
          activeProviders: []
        };

        await AddressService.createOrUpdateAddress(user.id, addressData);
      });
      afterEach(async () => await clearDb());

      it("should not try to validate the postal code", async () => {
        expect(PostCodeFetchifyService.validateUKPostCode).not.toHaveBeenCalled();
      });

      it("should not add the Wealthkernel active provider", async () => {
        const updatedAddress = await Address.findOne({ owner: user.id });
        expect(updatedAddress.toObject()).toEqual(
          expect.objectContaining({
            activeProviders: []
          })
        );
      });
    });

    describe("when user has residency country different than the address country", () => {
      let user: UserDocument;
      beforeEach(async () => {
        jest.clearAllMocks();
        jest.spyOn(PostCodeFetchifyService, "validateUKPostCode").mockImplementation((): any => null);

        user = await buildUser({ residencyCountry: "GR" });
        const addressData: AddressDTOInterface = {
          owner: new mongoose.Types.ObjectId(user._id),
          line1: "8 West St",
          city: "Crewkerne",
          countryCode: "GB",
          postalCode: "TA188AX",
          activeProviders: [ProviderEnum.WEALTHKERNEL]
        };
        await AddressService.createOrUpdateAddress(user.id, addressData);
      });
      afterEach(async () => await clearDb());

      it("should override user's residency country and emit an event", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser).toEqual(
          expect.objectContaining({
            residencyCountry: "GB",
            currency: "GBP",
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
            activeProviders: expect.arrayContaining([
              ProviderEnum.WEALTHKERNEL,
              ProviderEnum.STRIPE,
              ProviderEnum.SUMSUB
            ]),
            w8BenForm: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.residencyCountryChange.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });
  });
});
