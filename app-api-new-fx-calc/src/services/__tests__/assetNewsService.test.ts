import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import AssetNewsService from "../assetNewsService";
import { buildAssetNews, buildInvestmentProduct } from "../../tests/utils/generateModels";
import { AssetNewsDocument } from "../../models/AssetNews";
import DateUtil from "../../utils/dateUtil";

describe("AssetNewsService", () => {
  beforeAll(async () => await connectDb("AssetNewsService"));
  afterAll(async () => await closeDb());

  describe("getAssetNews", () => {
    describe("when asset news exist for the investment product", () => {
      let retrievedAssetNews: AssetNewsDocument[];
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        const investmentProduct = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        await Promise.all([await buildAssetNews(), await buildAssetNews(), await buildAssetNews()]);

        retrievedAssetNews = await AssetNewsService.getAssetNews(investmentProduct.id);
      });

      it("should an empty array the asset news for the given investment product", () => {
        expect(retrievedAssetNews).toEqual([]);
      });
    });

    describe("when asset news exist for the investment product", () => {
      let retrievedAssetNews: AssetNewsDocument[];
      let assetNews1: AssetNewsDocument;
      let assetNews2: AssetNewsDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        const investmentProduct = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        [assetNews1, assetNews2] = await Promise.all([
          buildAssetNews({ investmentProducts: [investmentProduct.id], date: new Date(Date.now()) }),
          buildAssetNews({
            investmentProducts: [investmentProduct.id],
            date: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 3)
          }),
          await buildAssetNews()
        ]);

        retrievedAssetNews = await AssetNewsService.getAssetNews(investmentProduct.id);
      });

      it("should return the asset news for the given investment product", () => {
        expect(retrievedAssetNews.length).toEqual(2);
      });

      it("should return the assets ordered from newest to oldest", () => {
        expect(retrievedAssetNews[0]._id).toEqual(assetNews1._id);
        expect(retrievedAssetNews[1]._id).toEqual(assetNews2._id);
      });
    });
  });
});
