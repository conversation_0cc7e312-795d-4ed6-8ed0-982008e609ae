import { faker } from "@faker-js/faker";
import events from "../../event-handlers/events";
import eventEmitter from "../../loaders/eventEmitter";
import { MonitorType, TransactionMonitor } from "../../models/TransactionMonitor";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildDepositCashTransaction,
  buildRiskAssessment,
  buildTransactionMonitor,
  buildUser,
  buildWithdrawalCashTransaction
} from "../../tests/utils/generateModels";
import { TransactionMonitorService } from "../transactionMonitorService";
import { RiskScoreClassificationEnum } from "../../models/RiskAssessment";

const buildUserWithRiskScore = async (
  riskScoreClassification: RiskScoreClassificationEnum
): Promise<UserDocument> => {
  const user = await buildUser();
  let score = 0;
  if (riskScoreClassification === RiskScoreClassificationEnum.Prohibited) {
    score = 110;
  } else if (riskScoreClassification === RiskScoreClassificationEnum.HighRisk) {
    score = 90;
  } else if (riskScoreClassification === RiskScoreClassificationEnum.MediumRisk) {
    score = 9;
  }
  const riskAssesment = await buildRiskAssessment({ owner: user.id, totalScore: score });

  expect(riskAssesment.classification).toEqual(riskScoreClassification);

  return user;
};

describe("TransactionMonitorService", () => {
  beforeAll(async () => await connectDb("TransactionMonitorService"));
  afterAll(async () => await closeDb());

  describe("checkSuspiciousTransactionActivity", () => {
    describe("when a high risk user has reached aggregate amount 65k threshold", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 4000000, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 4000000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({
          owner: user._id,
          type: MonitorType.AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
        });

        expect(transactionMonitors).toHaveLength(1);
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.aggregateAmountForHighRiskUser.eventId,
          user
        );
      });
    });

    describe("when a high risk user has reached aggregate amount 65k threshold but transactions created 7 months ago", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-01");
      const SEVEN_MONTHS_AGO = new Date("2023-01-01");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);

        await Promise.all([
          buildDepositCashTransaction(
            {
              consideration: { amount: 4000000, currency: "GBP" },
              status: "Settled",
              createdAt: SEVEN_MONTHS_AGO
            },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 4000000, currency: "GBP" },
            createdAt: SEVEN_MONTHS_AGO,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(0);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith();
      });
    });

    describe("when a high risk user has reached aggregate amount 65k threshold but a transaction monitor exists", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-01");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);

        await Promise.all([
          buildDepositCashTransaction(
            {
              consideration: { amount: 4000000, currency: "GBP" },
              status: "Settled"
            },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 4000000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }),
          buildTransactionMonitor({
            owner: user.id,
            type: MonitorType.AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER,
            createdAt: TODAY
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a new transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({
          owner: user._id,
          type: MonitorType.AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
        });

        expect(transactionMonitors).toHaveLength(1);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith();
      });
    });

    describe("when a high risk user has reached aggregate amount 65k threshold but a transaction monitor is 7 months old", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-01");
      const SEVEN_MONTHS_AGO = new Date("2023-01-01");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);

        await Promise.all([
          buildDepositCashTransaction(
            {
              consideration: { amount: 4000000, currency: "GBP" },
              status: "Settled"
            },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 4000000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }),
          buildTransactionMonitor({
            owner: user.id,
            type: MonitorType.AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER,
            createdAt: SEVEN_MONTHS_AGO
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a new transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({
          owner: user._id,
          type: MonitorType.AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
        }).sort({ createdAt: -1 });

        expect(transactionMonitors).toHaveLength(2);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER,
          createdAt: TODAY
        });
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.aggregateAmountForHighRiskUser.eventId,
          user
        );
      });
    });

    describe("when a low risk user has reached aggregate amount 35k threshold", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.LowRisk);

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 2000000, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 2000000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.AGGREGATE_AMOUNT_FOR_LOW_RISK_USER
        });
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.aggregateAmountForLowMediumRiskUser.eventId,
          user
        );
      });
    });

    describe("when a medium risk user has reached aggregate amount 35k threshold", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.MediumRisk);

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 2000000, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 2000000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.AGGREGATE_AMOUNT_FOR_LOW_RISK_USER
        });
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.aggregateAmountForLowMediumRiskUser.eventId,
          user
        );
      });
    });

    describe("when a low risk user has reached aggregate amount 35k threshold but transactions created 7 months ago", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-01");
      const SEVEN_MONTHS_AGO = new Date("2023-01-01");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.LowRisk);

        await Promise.all([
          buildDepositCashTransaction(
            {
              consideration: { amount: 2000000, currency: "GBP" },
              status: "Settled",
              createdAt: SEVEN_MONTHS_AGO
            },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 2000000, currency: "GBP" },
            createdAt: SEVEN_MONTHS_AGO,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(0);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith();
      });
    });

    describe("when a low risk user has reached aggregate amount 35k threshold but a transaction monitor exists", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-01");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.LowRisk);

        await Promise.all([
          buildDepositCashTransaction(
            {
              consideration: { amount: 2000000, currency: "GBP" },
              status: "Settled"
            },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 2000000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }),
          buildTransactionMonitor({
            owner: user.id,
            type: MonitorType.AGGREGATE_AMOUNT_FOR_LOW_RISK_USER,
            createdAt: TODAY
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a new transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith();
      });
    });

    describe("when a low risk user has reached aggregate amount 35k threshold but a transaction monitor is 7 months old", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-01");
      const SEVEN_MONTHS_AGO = new Date("2023-01-01");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.LowRisk);

        await Promise.all([
          buildDepositCashTransaction(
            {
              consideration: { amount: 2000000, currency: "GBP" },
              status: "Settled"
            },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 2000000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }),
          buildTransactionMonitor({
            owner: user.id,
            type: MonitorType.AGGREGATE_AMOUNT_FOR_LOW_RISK_USER,
            createdAt: SEVEN_MONTHS_AGO
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a new transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id }).sort({ createdAt: -1 });

        expect(transactionMonitors).toHaveLength(2);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.AGGREGATE_AMOUNT_FOR_LOW_RISK_USER,
          createdAt: TODAY
        });
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.aggregateAmountForLowMediumRiskUser.eventId,
          user
        );
      });
    });

    describe("when a high risk user withdrawn 10 days after deposit", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-11");
      const TEN_DAYS_AGO = new Date("2023-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: TEN_DAYS_AGO },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 100, currency: "GBP" },
            createdAt: TODAY,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.WITHDRAWAL_AFTER_DEPOSIT_FOR_HIGH_RISK_USER
        });
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.withdrawalAfterDeposit.eventId,
          user
        );
      });
    });

    describe("when a high risk user withdrawn 31 days after deposit", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-04-01");
      const THIRTY_ONE_DAYS_AGO = new Date("2023-02-01");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: THIRTY_ONE_DAYS_AGO },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 100, currency: "GBP" },
            createdAt: TODAY,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(0);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when a high risk user has not withdrawn after deposit", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-04-01");
      const THIRTY_ONE_DAYS_AGO = new Date("2023-02-01");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);
        await buildDepositCashTransaction(
          { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: THIRTY_ONE_DAYS_AGO },
          user
        );

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(0);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when a user has made multiple high volume deposits", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-11");
      const TEN_DAYS_AGO = new Date("2023-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled", createdAt: TODAY },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled", createdAt: TEN_DAYS_AGO },
            user
          )
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.HIGH_VOLUME_DEPOSITS
        });
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.highVolumeDeposits.eventId,
          user
        );
      });
    });

    describe("when a user has made multiple high volume deposits but they have 'Pending' status", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-11");
      const TEN_DAYS_AGO = new Date("2023-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Pending", createdAt: TEN_DAYS_AGO },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Pending", createdAt: TEN_DAYS_AGO },
            user
          )
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(0);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when a user has made multiple high volume deposits but they have 31 days difference", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-04-01");
      const THIRTY_ONE_DAYS_AGO = new Date("2023-02-01");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Pending", createdAt: TODAY },
            user
          ),
          buildDepositCashTransaction(
            {
              consideration: { amount: 990001, currency: "GBP" },
              status: "Pending",
              createdAt: THIRTY_ONE_DAYS_AGO
            },
            user
          )
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(0);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when a user has made multiple high volume deposits but a transaction monitor already exists", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-11");
      const TEN_DAYS_AGO = new Date("2023-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled", createdAt: TEN_DAYS_AGO },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled", createdAt: TEN_DAYS_AGO },
            user
          ),
          buildTransactionMonitor({
            owner: user.id,
            type: MonitorType.HIGH_VOLUME_DEPOSITS
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a new transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when a user has made multiple deposits but do not pass high volume threshold", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-11");
      const TEN_DAYS_AGO = new Date("2023-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 990000, currency: "GBP" }, status: "Settled", createdAt: TEN_DAYS_AGO },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 200000, currency: "GBP" }, status: "Settled", createdAt: TEN_DAYS_AGO },
            user
          )
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(0);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when a user has made a deposit after 1 year of inactivity", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-11");
      const ONE_YEAR_AGO = new Date("2022-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: TODAY },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: ONE_YEAR_AGO },
            user
          )
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.TRANSACTION_AFTER_1_YEAR
        });
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.transactionAfterAccountInactivity.eventId,
          user
        );
      });
    });

    describe("when a user has made a withdrawal after 1 year of inactivity", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-11");
      const ONE_YEAR_AGO = new Date("2022-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: ONE_YEAR_AGO },
            user
          ),
          buildWithdrawalCashTransaction({
            consideration: { amount: 100, currency: "GBP" },
            status: "Settled",
            createdAt: TODAY,
            owner: user.id
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.TRANSACTION_AFTER_1_YEAR
        });
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.transactionAfterAccountInactivity.eventId,
          user
        );
      });
    });

    describe("when a user has made an asset transaction after 1 year of inactivity", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-11");
      const ONE_YEAR_AGO = new Date("2022-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: ONE_YEAR_AGO },
            user
          ),
          buildAssetTransaction({
            consideration: { amount: 100, currency: "GBP" },
            status: "Settled",
            createdAt: TODAY,
            owner: user.id
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(0);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when a user has made a deposit after 1 year of inactivity but a transaction monitor exists", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-11");
      const ONE_YEAR_AGO = new Date("2022-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: TODAY },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: ONE_YEAR_AGO },
            user
          ),
          buildTransactionMonitor({
            owner: user.id,
            type: MonitorType.TRANSACTION_AFTER_1_YEAR,
            createdAt: TODAY
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a new transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when a user has made an asset transaction after 1 year of inactivity and a deposit within 1 month of coming back", () => {
      let user: UserDocument;
      const TODAY = new Date("2023-08-18");
      const ONE_WEEK_AGO = new Date("2023-08-11");
      const ONE_YEAR_AGO = new Date("2022-08-10");

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        user = await buildUser();

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: ONE_YEAR_AGO },
            user
          ),
          buildAssetTransaction({
            consideration: { amount: 100, currency: "GBP" },
            status: "Settled",
            createdAt: ONE_WEEK_AGO,
            owner: user.id
          }),
          buildDepositCashTransaction(
            { consideration: { amount: 100, currency: "GBP" }, status: "Settled", createdAt: TODAY },
            user
          )
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.TRANSACTION_AFTER_1_YEAR
        });
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.transactionAfterAccountInactivity.eventId,
          user
        );
      });
    });

    describe("when a high risk user has a net aggregate amount >=1k", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 210000, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 100000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({
          owner: user._id,
          type: MonitorType.NET_AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
        });

        expect(transactionMonitors).toHaveLength(1);
      });

      it("should emit an event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.netAggregateAmountForHighRiskUser.eventId,
          user
        );
      });
    });

    describe("when a high risk user has a net aggregate amount less than 1k", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 110000, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 100000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({
          owner: user._id,
          type: MonitorType.NET_AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
        });

        expect(transactionMonitors).toHaveLength(0);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transactionMonitoring.netAggregateAmountForHighRiskUser.eventId,
          user
        );
      });
    });

    describe("when a high risk user has a net aggregate amount >=1 but a transaction monitor exists", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUserWithRiskScore(RiskScoreClassificationEnum.HighRisk);

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 1100, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildWithdrawalCashTransaction({
            owner: user.id,
            consideration: { amount: 1000, currency: "GBP" },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }),
          buildTransactionMonitor({
            owner: user.id,
            type: MonitorType.NET_AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
          })
        ]);

        await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not create a new transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({
          owner: user._id,
          type: MonitorType.NET_AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
        });

        expect(transactionMonitors).toHaveLength(1);
      });

      it("should not emit an event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transactionMonitoring.netAggregateAmountForHighRiskUser.eventId,
          user
        );
      });
    });
  });
});
