import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { BankAccountType, WealthkernelService } from "../../external-services/wealthkernelService";
import { BankAccount, BankAccountDocument, DeactivationReasonEnum } from "../../models/BankAccount";
import BankAccountService from "../bankAccountService";
import { buildBankAccount, buildUser } from "../../tests/utils/generateModels";
import { buildWealthkernelBankAccountResponse } from "../../tests/utils/generateWealthkernel";
import { UserDocument } from "../../models/User";
import logger from "../../external-services/loggerService";
import { faker } from "@faker-js/faker";
import DateUtil from "../../utils/dateUtil";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { ProviderEnum } from "../../configs/providersConfig";
import BankAccountNameChecker, { BankAccountNameCheckResultEnum } from "../../lib/bankAccountNameChecker";

describe("BankAccountService", () => {
  const DATE = new Date("2022-08-31T11:00:00Z");

  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("BankAccountService"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("createBankAccount", () => {
    describe("when there is already a bank account with the same IBAN and owner", () => {
      const IBAN = faker.finance.iban();

      beforeEach(async () => {
        // Create a user with a bank account with the same IBAN
        const user = await buildUser({}, false);
        await buildBankAccount({ owner: user.id, iban: IBAN });

        await BankAccountService.createBankAccount(
          {
            owner: user.id,
            active: true,
            activeProviders: [ProviderEnum.WEALTHYHOOD],
            name: faker.finance.accountName(),
            number: faker.finance.accountNumber(),
            sortCode: faker.finance.accountNumber(),
            iban: IBAN
          },
          user
        );
      });

      it("should not create a new bank account", async () => {
        const bankAccounts = await BankAccount.find();
        expect(bankAccounts.length).toBe(1);
      });
    });

    describe("when the name of the bank account does not match the name of the owner", () => {
      const IBAN = faker.finance.iban();

      let user: UserDocument;

      beforeEach(async () => {
        // Create a user with a bank account with a different IBAN
        user = await buildUser({}, false);
        await buildBankAccount({ owner: user.id, iban: faker.finance.iban() });

        jest
          .spyOn(BankAccountNameChecker, "checkBankAccountName")
          .mockResolvedValue(BankAccountNameCheckResultEnum.NO_MATCH);

        await BankAccountService.createBankAccount(
          {
            owner: user.id,
            active: true,
            activeProviders: [ProviderEnum.WEALTHYHOOD],
            name: faker.finance.accountName(),
            number: faker.finance.accountNumber(),
            sortCode: faker.finance.accountNumber(),
            iban: IBAN
          },
          user
        );
      });

      it("should create a new bank account with pending status and emit a stagnant event", async () => {
        const bankAccounts = await BankAccount.find();
        expect(bankAccounts.length).toBe(2);

        const newlyCreatedBankAccount = await BankAccount.findOne({ iban: IBAN });
        expect(newlyCreatedBankAccount.toObject()).toEqual(
          expect.objectContaining({
            stagnantEventEmitted: true,
            providers: {
              wealthyhood: {
                status: "Pending"
              }
            }
          })
        );

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.bankAccount.bankAccountStagnant.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when the AI checker for bank account naming returns undefined due to an error", () => {
      const IBAN = faker.finance.iban();

      let user: UserDocument;

      beforeEach(async () => {
        // Create a user with a bank account with a different IBAN
        user = await buildUser({}, false);
        await buildBankAccount({ owner: user.id, iban: faker.finance.iban() });

        jest.spyOn(BankAccountNameChecker, "checkBankAccountName").mockResolvedValue(undefined);

        await BankAccountService.createBankAccount(
          {
            owner: user.id,
            active: true,
            activeProviders: [ProviderEnum.WEALTHYHOOD],
            name: faker.finance.accountName(),
            number: faker.finance.accountNumber(),
            sortCode: faker.finance.accountNumber(),
            iban: IBAN
          },
          user
        );
      });

      it("should create a new bank account with pending status and emit a stagnant event", async () => {
        const bankAccounts = await BankAccount.find();
        expect(bankAccounts.length).toBe(2);

        const newlyCreatedBankAccount = await BankAccount.findOne({ iban: IBAN });
        expect(newlyCreatedBankAccount.toObject()).toEqual(
          expect.objectContaining({
            stagnantEventEmitted: true,
            providers: {
              wealthyhood: {
                status: "Pending"
              }
            }
          })
        );

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.bankAccount.bankAccountStagnant.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when the name of the bank account matches the name of the owner", () => {
      const IBAN = faker.finance.iban();

      beforeEach(async () => {
        // Create a user with a bank account with a different IBAN
        const user = await buildUser({}, false);
        await buildBankAccount({ owner: user.id, iban: faker.finance.iban() });

        jest
          .spyOn(BankAccountNameChecker, "checkBankAccountName")
          .mockResolvedValue(BankAccountNameCheckResultEnum.MATCH);

        await BankAccountService.createBankAccount(
          {
            owner: user.id,
            active: true,
            activeProviders: [ProviderEnum.WEALTHYHOOD],
            name: faker.finance.accountName(),
            number: faker.finance.accountNumber(),
            sortCode: faker.finance.accountNumber(),
            iban: IBAN
          },
          user
        );
      });

      it("should create a new bank account with active status", async () => {
        const bankAccounts = await BankAccount.find();
        expect(bankAccounts.length).toBe(2);

        const newlyCreatedBankAccount = await BankAccount.findOne({ iban: IBAN });
        expect(newlyCreatedBankAccount.toObject()).toEqual(
          expect.objectContaining({
            providers: {
              wealthyhood: {
                status: "Active"
              }
            }
          })
        );
      });
    });
  });

  describe("syncAllWkBankAccounts", () => {
    let user: UserDocument;
    let wealthkernelBankAccount: BankAccountType;
    let minimumCreationTime: Date;

    beforeEach(async () => {
      jest.resetAllMocks();
      await clearDb();
      Date.now = jest.fn(() => DATE.valueOf());
      minimumCreationTime = new Date(Date.now() - 11 * 60 * 1000);
    });

    describe("when there is a single bank account that does not have a WK ID", () => {
      beforeEach(async () => {
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts");
      });

      it("should not add a wealthkernel id to the bank account", async () => {
        const bankAccountWithoutWkId: BankAccountDocument = await buildBankAccount({
          createdAt: minimumCreationTime
        });
        await BankAccountService.syncAllWkBankAccounts();

        const allBankAccounts = await BankAccount.find({ owner: bankAccountWithoutWkId.owner });
        expect(allBankAccounts.length).toEqual(1);
        expect(allBankAccounts[0]?.providers?.wealthkernel?.id).toBeUndefined();
      });
    });

    describe("when there is a bank account that has no status", () => {
      wealthkernelBankAccount = buildWealthkernelBankAccountResponse();

      beforeEach(async () => {
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveBankAccount")
          .mockResolvedValue(wealthkernelBankAccount);
      });

      it("should update the status of our bank account", async () => {
        const user = await buildUser();
        const pendingBankAccount: BankAccountDocument = await buildBankAccount({
          providers: {
            wealthkernel: { id: wealthkernelBankAccount.id }
          },
          owner: user,
          createdAt: minimumCreationTime
        });
        await BankAccountService.syncAllWkBankAccounts();

        const updatedBankAccount = (await BankAccount.findById(pendingBankAccount.id)) as BankAccountDocument;
        expect(updatedBankAccount.toObject()).toEqual(
          expect.objectContaining({
            providers: {
              wealthkernel: {
                id: wealthkernelBankAccount.id,
                status: wealthkernelBankAccount.status
              }
            }
          })
        );
      });
    });

    describe("when there is a bank account that has Pending status", () => {
      beforeEach(async () => {
        user = await buildUser();
      });

      it("should emit an event for stagnant bankAccount if it has not already emited one when it's pending in Wealthkernel", async () => {
        wealthkernelBankAccount = buildWealthkernelBankAccountResponse({ status: "Pending" });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveBankAccount")
          .mockResolvedValue(wealthkernelBankAccount);
        jest.spyOn(eventEmitter, "emit");

        await buildBankAccount({
          providers: {
            wealthkernel: { id: wealthkernelBankAccount.id, status: "Pending" }
          },
          owner: user,
          createdAt: minimumCreationTime
        });
        await BankAccountService.syncAllWkBankAccounts();

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.bankAccount.bankAccountStagnant.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should not emit an event for stagnant bankAccount if it has already emited one when it's pending in Wealthkernel", async () => {
        wealthkernelBankAccount = buildWealthkernelBankAccountResponse({ status: "Pending" });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveBankAccount")
          .mockResolvedValue(wealthkernelBankAccount);
        jest.spyOn(eventEmitter, "emit");

        await buildBankAccount({
          providers: {
            wealthkernel: { id: wealthkernelBankAccount.id, status: "Pending" }
          },
          owner: user,
          createdAt: minimumCreationTime,
          stagnantEventEmitted: true
        });
        await BankAccountService.syncAllWkBankAccounts();

        expect(eventEmitter.emit).toBeCalledTimes(0);
      });

      it("should update the status of the bank account to suspended and log an error  when it's suspended in Wealthkernel", async () => {
        wealthkernelBankAccount = buildWealthkernelBankAccountResponse({ status: "Suspended" });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveBankAccount")
          .mockResolvedValue(wealthkernelBankAccount);

        const pendingBankAccount: BankAccountDocument = await buildBankAccount({
          providers: {
            wealthkernel: { id: wealthkernelBankAccount.id, status: "Pending" }
          },
          owner: user,
          createdAt: minimumCreationTime
        });
        await BankAccountService.syncAllWkBankAccounts();

        const updatedBankAccount = (await BankAccount.findById(pendingBankAccount.id)) as BankAccountDocument;

        expect(eventEmitter.emit).toBeCalledTimes(0);
        expect(updatedBankAccount.toObject()).toEqual(
          expect.objectContaining({
            providers: {
              wealthkernel: {
                id: wealthkernelBankAccount.id,
                status: wealthkernelBankAccount.status
              }
            }
          })
        );
        expect(logger.error).toHaveBeenCalledWith(`BankAccount ${updatedBankAccount._id} got suspended`, {
          module: "BankAccountService",
          method: "_syncWkStatusSafely",
          data: {
            bankAccountWkID: wealthkernelBankAccount.id
          }
        });
      });

      it("should update the status of the bank account to Inactive when it's Inactive in Wealthkernel", async () => {
        wealthkernelBankAccount = buildWealthkernelBankAccountResponse({ status: "Inactive" });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveBankAccount")
          .mockResolvedValue(wealthkernelBankAccount);

        const pendingBankAccount: BankAccountDocument = await buildBankAccount({
          providers: {
            wealthkernel: { id: wealthkernelBankAccount.id, status: "Pending" }
          },
          owner: user,
          createdAt: minimumCreationTime
        });
        await BankAccountService.syncAllWkBankAccounts();

        const updatedBankAccount = (await BankAccount.findById(pendingBankAccount.id)) as BankAccountDocument;

        expect(eventEmitter.emit).toBeCalledTimes(0);
        expect(updatedBankAccount.toObject()).toEqual(
          expect.objectContaining({
            providers: {
              wealthkernel: {
                id: wealthkernelBankAccount.id,
                status: wealthkernelBankAccount.status
              }
            }
          })
        );
      });

      it("should update the status of the bank account to active when it's active in Wealthkernel", async () => {
        wealthkernelBankAccount = buildWealthkernelBankAccountResponse({ status: "Active" });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveBankAccount")
          .mockResolvedValue(wealthkernelBankAccount);

        const pendingBankAccount: BankAccountDocument = await buildBankAccount({
          providers: {
            wealthkernel: { id: wealthkernelBankAccount.id, status: "Pending" }
          },
          owner: user,
          createdAt: minimumCreationTime
        });
        await BankAccountService.syncAllWkBankAccounts();

        const updatedBankAccount = (await BankAccount.findById(pendingBankAccount.id)) as BankAccountDocument;

        expect(eventEmitter.emit).toBeCalledTimes(0);
        expect(updatedBankAccount.toObject()).toEqual(
          expect.objectContaining({
            providers: {
              wealthkernel: {
                id: wealthkernelBankAccount.id,
                status: wealthkernelBankAccount.status
              }
            }
          })
        );
      });
    });

    describe("when there is a bank account that has Pending status and reaching out to WK fails", () => {
      beforeEach(async () => {
        user = await buildUser();
      });

      it("should not emit an event", async () => {
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccount").mockImplementation(() => {
          throw Error("Some error by WK!");
        });
        jest.spyOn(eventEmitter, "emit");

        await buildBankAccount({
          providers: {
            wealthkernel: { id: wealthkernelBankAccount.id, status: "Pending" }
          },
          owner: user,
          createdAt: minimumCreationTime
        });

        await BankAccountService.syncAllWkBankAccounts();

        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });
  });

  describe("getSuspendedBankAccounts", () => {
    let bankAccount: BankAccountDocument;

    beforeEach(async () => {
      jest.resetAllMocks();
      Date.now = jest.fn(() => DATE.valueOf());
    });

    describe("when there are no suspended bank accounts", () => {
      beforeEach(async () => {
        await clearDb();
        bankAccount = await buildBankAccount();
      });

      it("should return an empty array", async () => {
        const suspendedBankAccounts = await BankAccountService.getSuspendedBankAccounts();
        expect(suspendedBankAccounts.length).toEqual(0);
      });
    });

    describe("when there is 1 suspended bank account", () => {
      beforeEach(async () => {
        await clearDb();
        bankAccount = await buildBankAccount({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Suspended"
            }
          }
        });
      });

      it("should return the bank account", async () => {
        const suspendedBankAccounts = await BankAccountService.getSuspendedBankAccounts();
        expect(suspendedBankAccounts.length).toEqual(1);
        expect(suspendedBankAccounts[0].id).toEqual(bankAccount.id);
        expect(suspendedBankAccounts[0].providers?.wealthkernel?.status).toEqual("Suspended");
      });
    });

    describe("when there are 3 suspended bank accounts", () => {
      beforeEach(async () => {
        await clearDb();
        await Promise.all([
          buildBankAccount({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Suspended"
              }
            }
          }),
          buildBankAccount({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Suspended"
              }
            }
          }),
          buildBankAccount({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Suspended"
              }
            }
          }),
          buildBankAccount()
        ]);
      });

      it("should return an array with 3 bank accounts", async () => {
        const suspendedBankAccounts = await BankAccountService.getSuspendedBankAccounts();
        expect(suspendedBankAccounts.length).toEqual(3);
      });
    });
  });

  describe("getPendingBankAccounts", () => {
    let bankAccount: BankAccountDocument;
    let minimumCreationTime: Date;

    beforeEach(async () => {
      jest.resetAllMocks();
      Date.now = jest.fn(() => DATE.valueOf());
      minimumCreationTime = DateUtil.getDateOfMinutesAgo(16);
    });

    describe("when there are no pending bank accounts", () => {
      beforeEach(async () => {
        await clearDb();
        bankAccount = await buildBankAccount();
      });
      it("should return an empty array", async () => {
        const pendingBankAccounts = await BankAccountService.getPendingBankAccounts();
        expect(pendingBankAccounts.length).toEqual(0);
      });
    });

    describe("when there is 1 pending bank account created at least 15 minutes ago", () => {
      beforeEach(async () => {
        await clearDb();
        bankAccount = await buildBankAccount({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          createdAt: minimumCreationTime
        });
      });

      it("should return the bank account", async () => {
        const suspendedBankAccounts = await BankAccountService.getPendingBankAccounts();
        expect(suspendedBankAccounts.length).toEqual(1);
        expect(suspendedBankAccounts[0].id).toEqual(bankAccount.id);
        expect(suspendedBankAccounts[0].providers?.wealthkernel?.status).toEqual("Pending");
      });
    });

    describe("when there is 1 pending bank account created at least 15 minutes ago but is deactivated due to bank name missmatch", () => {
      beforeEach(async () => {
        await clearDb();
        bankAccount = await buildBankAccount({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          deactivationReason: DeactivationReasonEnum.BANK_NAME_MISMATCH,
          createdAt: minimumCreationTime
        });
      });

      it("should not return the bank account", async () => {
        const suspendedBankAccounts = await BankAccountService.getPendingBankAccounts();
        expect(suspendedBankAccounts.length).toEqual(0);
      });
    });

    describe("when there is 1 pending bank account created in the last 15 minutes", () => {
      beforeEach(async () => {
        await clearDb();
        bankAccount = await buildBankAccount({
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          createdAt: new Date()
        });
      });

      it("should not return the bank account", async () => {
        const suspendedBankAccounts = await BankAccountService.getPendingBankAccounts();
        expect(suspendedBankAccounts.length).toEqual(0);
      });
    });
  });
});
