import Decimal from "decimal.js";
import { RedisClientService } from "../../loaders/redis";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildDailyPortfolioTicker,
  buildDailySavingsProductTicker,
  buildIntraDayAssetTicker,
  buildInvestmentProduct,
  buildPortfolio,
  buildSavingsProduct,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import DailyTickerService from "../dailyTickerService";
import { DailyPortfolioSavingsTicker, DailyPortfolioTicker } from "../../models/DailyTicker";
import DateUtil from "../../utils/dateUtil";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { HoldingsType, InitialHoldingsAllocationType, PortfolioDocument } from "../../models/Portfolio";
import { UserDocument } from "../../models/User";
import { SavingsProductDocument } from "../../models/SavingsProduct";

describe("DailyTickerService", () => {
  beforeAll(async () => await connectDb("DailyTickerService"));
  afterAll(async () => await closeDb());

  describe("createDailyPortfolioTickers", () => {
    afterEach(async () => await clearDb());

    describe("when there is a portfolio with an owner that has not made an investment yet", () => {
      beforeEach(async () => {
        const owner = await buildUser({ portfolioConversionStatus: "notStarted" });
        await buildPortfolio({
          owner: owner.id,
          initialHoldingsAllocation: [],
          holdings: []
        });
        const dailyPortfolioTickers = await DailyPortfolioTicker.find();
        expect(dailyPortfolioTickers.length).toBe(0);

        await DailyTickerService.createDailyPortfolioTickers();
      });

      it("should not create intra-day portfolio tickers", async () => {
        const intraDayPortfolioTickers = await DailyPortfolioTicker.find();
        expect(intraDayPortfolioTickers.length).toBe(0);
      });
    });

    describe("when there is a portfolio that belongs to a deleted user", () => {
      let portfolio: PortfolioDocument;
      let owner: UserDocument;

      beforeEach(async () => {
        // create investment products & daily asset tickers
        const ASSET_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          close: number;
          timestamp: number;
          quantity: number;
          percentage: number;
        }[] = [
          {
            assetId: "equities_apple",
            close: 2000,
            timestamp: new Date().getTime(),
            quantity: 1,
            percentage: 40
          },
          {
            assetId: "equities_microsoft",
            close: 1000,
            timestamp: new Date().getTime() - 1,
            quantity: 1,
            percentage: 30
          },
          {
            assetId: "equities_coinbase",
            close: 1000,
            timestamp: DateUtil.getDateOfDaysAgo(new Date(), 3).getTime(),
            quantity: 1,
            percentage: 30
          }
        ];
        const holdings: HoldingsType[] = [];
        const initialHoldingsAllocation: InitialHoldingsAllocationType[] = [];
        for (let i = 0; i < ASSET_CONFIG.length; i++) {
          const { assetId, close, timestamp, quantity, percentage } = ASSET_CONFIG[i];
          const investmentProduct = await buildInvestmentProduct(false, { commonId: assetId });
          await buildIntraDayAssetTicker({
            investmentProduct: investmentProduct.id,
            pricePerCurrency: {
              EUR: close / 2,
              GBP: close / 2,
              USD: close
            },
            timestamp: new Date(timestamp)
          });
          holdings.push({ asset: investmentProduct.id, assetCommonId: assetId, quantity });
          initialHoldingsAllocation.push({ asset: investmentProduct.id, assetCommonId: assetId, percentage });
        }

        // create user & portfolio
        owner = await buildUser({
          email: "<EMAIL>",
          portfolioConversionStatus: "completed",
          currency: "GBP"
        });
        portfolio = await buildPortfolio({
          owner: owner.id,
          initialHoldingsAllocation,
          holdings
        });

        // cache fx rates
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.5,
            GBP: 0.5
          },
          EUR: {
            USD: 2,
            EUR: 1,
            GBP: 1
          },
          GBP: {
            USD: 2,
            GBP: 1,
            EUR: 1
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const dailyPortfolioTickers = await DailyPortfolioTicker.find();
        expect(dailyPortfolioTickers.length).toBe(0);

        await DailyTickerService.createDailyPortfolioTickers();
      });

      it("should not create intra-day portfolio tickers", async () => {
        const dailyPortfolioTickers = await DailyPortfolioTicker.find({ portfolio: portfolio.id });
        expect(dailyPortfolioTickers.length).toBe(0);
      });
    });

    describe.skip("when there are portfolios with portfolio-converted owners and no ticker exists for the day", () => {
      let portfolio: PortfolioDocument;
      let owner: UserDocument;

      beforeEach(async () => {
        // create investment products & daily asset tickers
        const ASSET_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          close: number;
          timestamp: number;
          quantity: number;
          percentage: number;
        }[] = [
          {
            assetId: "equities_apple",
            close: 2000,
            timestamp: new Date().getTime(),
            quantity: 1,
            percentage: 40
          },
          {
            assetId: "equities_microsoft",
            close: 1000,
            timestamp: new Date().getTime() - 1,
            quantity: 1,
            percentage: 30
          },
          {
            assetId: "equities_coinbase",
            close: 1000,
            timestamp: DateUtil.getDateOfDaysAgo(new Date(), 3).getTime(),
            quantity: 1,
            percentage: 30
          }
        ];
        const holdings: HoldingsType[] = [];
        const initialHoldingsAllocation: InitialHoldingsAllocationType[] = [];
        for (let i = 0; i < ASSET_CONFIG.length; i++) {
          const { assetId, close, timestamp, quantity, percentage } = ASSET_CONFIG[i];
          const investmentProduct = await buildInvestmentProduct(false, { commonId: assetId });
          await buildIntraDayAssetTicker({
            investmentProduct: investmentProduct.id,
            pricePerCurrency: {
              EUR: close / 2,
              GBP: close / 2,
              USD: close
            },
            timestamp: new Date(timestamp)
          });
          holdings.push({ asset: investmentProduct.id, assetCommonId: assetId, quantity });
          initialHoldingsAllocation.push({ asset: investmentProduct.id, assetCommonId: assetId, percentage });
        }

        // create user & portfolio
        owner = await buildUser({ portfolioConversionStatus: "completed", currency: "GBP" });
        portfolio = await buildPortfolio({
          owner: owner.id,
          initialHoldingsAllocation,
          holdings
        });

        // cache fx rates
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.5,
            GBP: 0.5
          },
          EUR: {
            USD: 2,
            EUR: 1,
            GBP: 1
          },
          GBP: {
            USD: 2,
            GBP: 1,
            EUR: 1
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const dailyPortfolioTickers = await DailyPortfolioTicker.find();
        expect(dailyPortfolioTickers.length).toBe(0);

        await DailyTickerService.createDailyPortfolioTickers();
      });

      it("should create portfolio tickers", async () => {
        const dailyPortfolioTickers = await DailyPortfolioTicker.find({ portfolio: portfolio.id });
        expect(dailyPortfolioTickers.length).toBe(1);

        const EXPECTED_USD_PRICE = 4000;
        expect(dailyPortfolioTickers[0]).toMatchObject(
          expect.objectContaining({
            pricePerCurrency: expect.objectContaining({
              EUR: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
              GBP: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
              USD: EXPECTED_USD_PRICE
            }),
            currency: owner.currency,
            date: expect.any(Date),
            portfolio: portfolio._id
          })
        );
      });
    });

    describe("when there are portfolios with portfolio-converted owners and tickers exist for the day", () => {
      let portfolio: PortfolioDocument;
      let owner: UserDocument;
      const EXPECTED_USD_PRICE = 4000;

      beforeEach(async () => {
        // create investment products & daily asset tickers
        const ASSET_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          close: number;
          timestamp: number;
          quantity: number;
          percentage: number;
        }[] = [
          {
            assetId: "equities_apple",
            close: 2000,
            timestamp: new Date().getTime(),
            quantity: 1,
            percentage: 40
          },
          {
            assetId: "equities_microsoft",
            close: 1000,
            timestamp: new Date().getTime() - 1,
            quantity: 1,
            percentage: 30
          },
          {
            assetId: "equities_coinbase",
            close: 1000,
            timestamp: DateUtil.getDateOfDaysAgo(new Date(), 3).getTime(),
            quantity: 1,
            percentage: 30
          }
        ];
        const holdings: HoldingsType[] = [];
        const initialHoldingsAllocation: InitialHoldingsAllocationType[] = [];
        for (let i = 0; i < ASSET_CONFIG.length; i++) {
          const { assetId, close, timestamp, quantity, percentage } = ASSET_CONFIG[i];
          const investmentProduct = await buildInvestmentProduct(false, { commonId: assetId });
          await buildIntraDayAssetTicker({
            investmentProduct: investmentProduct.id,
            pricePerCurrency: {
              EUR: close / 2,
              GBP: close / 2,
              USD: close
            },
            timestamp: new Date(timestamp)
          });
          holdings.push({ asset: investmentProduct.id, assetCommonId: assetId, quantity });
          initialHoldingsAllocation.push({ asset: investmentProduct.id, assetCommonId: assetId, percentage });
        }

        // create user & portfolio
        owner = await buildUser({ portfolioConversionStatus: "completed", currency: "GBP" });
        portfolio = await buildPortfolio({
          owner: owner.id,
          initialHoldingsAllocation,
          holdings
        });
        await buildDailyPortfolioTicker({
          currency: owner.currency,
          portfolio: portfolio.id,
          pricePerCurrency: {
            EUR: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
            GBP: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
            USD: EXPECTED_USD_PRICE
          },
          date: new Date(Date.now() - 10 * 60 * 1000) // 10 minutes ago
        });

        // cache fx rates
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.5,
            GBP: 0.5
          },
          EUR: {
            USD: 2,
            EUR: 1,
            GBP: 1
          },
          GBP: {
            USD: 2,
            GBP: 1,
            EUR: 1
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const dailyPortfolioTickers = await DailyPortfolioTicker.find();
        expect(dailyPortfolioTickers.length).toBe(1);

        await DailyTickerService.createDailyPortfolioTickers();
      });

      it("should update the existing portfolio ticker for the day", async () => {
        const dailyPortfolioTickers = await DailyPortfolioTicker.find({ portfolio: portfolio.id });
        expect(dailyPortfolioTickers.length).toBe(1);

        expect(dailyPortfolioTickers).toMatchObject(
          expect.arrayContaining([
            expect.objectContaining({
              pricePerCurrency: expect.objectContaining({
                EUR: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
                GBP: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
                USD: EXPECTED_USD_PRICE
              }),
              currency: owner.currency,
              date: expect.any(Date),
              portfolio: portfolio._id
            })
          ])
        );
      });
    });
  });

  describe("setDailyPortfolioSavingsTicker", () => {
    let portfolio: PortfolioDocument;
    let savingsProduct: SavingsProductDocument;

    describe("when the user has portfolio savings amount but a savings product ticker doesn't exist", () => {
      beforeAll(async () => {
        jest.clearAllMocks();
        const user = await buildUser();
        [portfolio, savingsProduct] = await Promise.all([
          buildPortfolio({
            owner: user.id,
            savings: new Map([
              [
                "mmf_dist_gbp",
                {
                  amount: 0,
                  currency: "GBP"
                }
              ]
            ])
          }),
          buildSavingsProduct(
            true,
            {
              commonId: "mmf_dist_gbp"
            },
            {
              date: DateUtil.getDateOfDaysAgo(new Date(), 3)
            }
          ),
          buildSubscription({
            owner: user.id
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should throw an error", async () => {
        await expect(
          DailyTickerService.setDailyPortfolioSavingsTicker(
            portfolio,
            {
              amount: 0,
              currency: "GBP"
            },
            savingsProduct
          )
        ).rejects.toThrow();
      });
    });

    describe("when the user has portfolio savings amount and it's weekend", () => {
      let savingsProduct: SavingsProductDocument;
      const TODAY = new Date("2024-03-24"); // Today is Sunday, yesterday is Saturday
      const FRIDAY_TICKER_DATE = new Date("2024-03-22T12:00:00.000Z"); // Yesterday in the middle of the day, to avoid timezone issues
      const SATURDAY_TICKER_DATE = new Date("2024-03-23T12:00:00.000Z");
      const SUNDAY_TICKER_DATE = new Date("2024-03-24T12:00:00.000Z");
      const FIXING_DATE = new Date("2024-03-22");

      beforeAll(async () => {
        await clearDb();
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        const user = await buildUser();

        [portfolio, savingsProduct] = await Promise.all([
          buildPortfolio({
            owner: user.id,
            savings: new Map([
              [
                "mmf_dist_gbp",
                {
                  amount: 0,
                  currency: "GBP"
                }
              ]
            ])
          }),
          buildSavingsProduct(
            true,
            {
              commonId: "mmf_dist_gbp"
            },
            {
              date: DateUtil.getDateOfDaysAgo(new Date(), 3)
            }
          ),
          buildSubscription({
            owner: user.id
          })
        ]);

        await portfolio.populate("owner");

        await Promise.all([
          buildDailySavingsProductTicker({
            savingsProduct: savingsProduct.id,
            date: FRIDAY_TICKER_DATE,
            dailyDistributionFactor: 0.0002,
            fixingDate: FIXING_DATE
          }),

          buildDailySavingsProductTicker({
            savingsProduct: savingsProduct.id,
            date: SATURDAY_TICKER_DATE,
            dailyDistributionFactor: 0.0002,
            fixingDate: FIXING_DATE
          }),
          buildDailySavingsProductTicker({
            savingsProduct: savingsProduct.id,
            date: SUNDAY_TICKER_DATE,
            dailyDistributionFactor: 0.0002,
            fixingDate: FIXING_DATE
          })
        ]);
      });

      afterAll(async () => await clearDb());

      it("should create a portfolio ticker using the proper savings product ticker", async () => {
        await DailyTickerService.setDailyPortfolioSavingsTicker(
          portfolio,
          {
            amount: 0,
            currency: "GBP"
          },
          savingsProduct
        );

        const tickers = await DailyPortfolioSavingsTicker.find({
          portfolio: portfolio.id,
          savingsProduct: savingsProduct.id
        });

        expect(tickers.length).toEqual(1);
        expect(tickers[0].date).toEqual(SATURDAY_TICKER_DATE);
      });
    });
  });
});
