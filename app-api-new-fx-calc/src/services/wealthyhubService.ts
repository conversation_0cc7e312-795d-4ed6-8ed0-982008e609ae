import ContentfulRetrievalService from "../external-services/contentfulRetrievalService";
import { documentToHtmlString } from "@contentful/rich-text-html-renderer";
import { BLOCKS, Document } from "@contentful/rich-text-types";
import { slugify } from "../utils/stringUtil";
import { getCachedDataWithFallback } from "../utils/cacheUtil";
import { Entry, EntryCollection, EntrySkeletonType } from "contentful";
import { UserDocument } from "../models/User";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import PaginationUtil from "../utils/paginationUtil";
import type { PaginatedApiResponse } from "types/apiResponse";
import { ContentfulContentTypeEnum } from "../configs/contentfulConfig";
import * as marked from "marked";
import ContentEntryService from "./contentEntryService";
import { ContentEntry, ContentEntryCategoryEnum, ContentEntryContentTypeEnum } from "../models/ContentEntry";
import { RedisClientService } from "../loaders/redis";
import { LearnNews } from "../models/LearnNews";
import CloudflareService from "../external-services/cloudflareService";

type WealthyhubItemType = {
  key: string;
};

export type AnalystInsightType = WealthyhubItemType & {
  contentType: ContentfulContentTypeEnum.ANALYST_INSIGHTS;
  id: string;
  analystInsightType: ContentEntryContentTypeEnum;
  publishedAt: Date;
  createdAt: Date;
  title: string;
  tags: string[];
  contentHTML: string;
  previewImageURL: string;
  fullImageURL: string;
  bannerImageURL?: string;
  readingTime: string;
};

type LearningGuideType = WealthyhubItemType & {
  contentType: ContentfulContentTypeEnum.LEARNING_GUIDE;
  title: string;
  description: string;
  backgroundColor: string;
  guideIconURL: string;
  mobileCoverImageURL: string;
  webCoverImageURL: string;
  slug: string;
  chapterCount: number;
  chapters?: LearningGuideChapterType[];
};

type LearningGuideChapterType = WealthyhubItemType & {
  title: string;
  body: string;
  slug: string;
};

export type NewsItemType = WealthyhubItemType & {
  contentType: ContentfulContentTypeEnum.NEWS;
  createdAt: Date;
  title: string;
  contentHTML: string;
  previewImageURL: string;
  fullImageURL: string;
  readingTime: string;
  previewTitleMain: string;
  previewTitleSecondary?: string;
  storyImageURL: string;
};

export type GlossaryItemType = WealthyhubItemType & {
  contentType: ContentfulContentTypeEnum.GLOSSARY;
  createdAt: Date;
  title: string;
  definitionHTML: string;
};

type FaqItemType = {
  question: string;
  answer: string;
};

type HelpCentreCategoryType = WealthyhubItemType & {
  title: string;
  subtitle: string;
  faqs: FaqItemType[];
};

export type FaqCategoryType = WealthyhubItemType & {
  title: string;
  locale: string;
  isHelpCentreCategory: boolean;
  subtitle: string;
  order: number;
  faqs: FaqItemType[];
};

type LocaleType = "uk" | "eu";

const ENTITY_TO_CONTENTFUL_LOCALE: Record<entitiesConfig.CompanyEntityEnum, LocaleType> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: "uk",
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: "eu"
};

const WEALTHYHUB_NEWS_LIMIT = 50;

const WEALTHYHUB_CACHING_TTL_SECONDS = 60 * 30; // 30 minutes

const DEFAULT_ANALYST_INSIGHTS_PAGE_SIZE = 50;

const ANALYST_INSIGHT_CACHE_TTL_SECONDS = 60 * 60 * 24; // 24 hours

export default class WealthyhubService {
  /**
   * PUBLIC METHODS
   */
  public static async deleteAnalystInsightsCache(): Promise<void> {
    const totalContentEntries = await ContentEntry.countDocuments();
    const totalAnalystInsightsPages = Math.ceil(totalContentEntries / DEFAULT_ANALYST_INSIGHTS_PAGE_SIZE);

    await Promise.all([
      ...Array.from({ length: totalAnalystInsightsPages }, (_, i) => {
        return RedisClientService.Instance.del(`analystInsights:page_${i + 1}`);
      })
    ]);
  }

  public static async getAnalystInsights(
    paginationConfig: {
      page: number;
    },
    options: { isPaidPlan: boolean }
  ): Promise<PaginatedApiResponse<AnalystInsightType>> {
    const response = await getCachedDataWithFallback(
      `analystInsights:page_${paginationConfig.page}`,
      () => WealthyhubService._getAnalystInsightsPage(paginationConfig.page),
      (_) => ANALYST_INSIGHT_CACHE_TTL_SECONDS
    );

    return {
      pagination: response.pagination,
      data: response.data.map((insight) => ({
        ...insight,
        contentHTML: WealthyhubService._displayAllowedContent(insight.contentHTML, {
          isPaidPlan: options.isPaidPlan
        })
      }))
    };
  }

  public static async getAnalystInsight(
    id: string,
    options: { isPaidPlan: boolean }
  ): Promise<AnalystInsightType> {
    const contentEntry = await ContentEntryService.getAnalystInsightById(id);

    const contentfulResponse = await ContentfulRetrievalService.LearnHubInstance.getEntry(
      contentEntry.providers.contentful.id
    );
    const analystInsight = WealthyhubService._formatAnalystInsightFromContentfulResponse(contentfulResponse);

    return {
      ...analystInsight,
      id: contentEntry.id,
      publishedAt: contentEntry.publishAt,
      contentHTML: WealthyhubService._displayAllowedContent(analystInsight.contentHTML, {
        isPaidPlan: options.isPaidPlan
      })
    };
  }

  public static async getLatestAnalystInsight(
    analystInsightType: ContentEntryContentTypeEnum
  ): Promise<AnalystInsightType> {
    const latestAnalystInsightContentEntry =
      await ContentEntryService.getLatestAnalystInsightEntryByContentType(analystInsightType);

    if (!latestAnalystInsightContentEntry) {
      return;
    }

    const analystInsight: Omit<AnalystInsightType, "id"> = await getCachedDataWithFallback(
      `analystInsight:${latestAnalystInsightContentEntry.id}`,
      async () => {
        const contentfulResponse = await ContentfulRetrievalService.LearnHubInstance.getEntry(
          latestAnalystInsightContentEntry.providers.contentful.id
        );

        return WealthyhubService._formatAnalystInsightFromContentfulResponse(contentfulResponse);
      },
      (_) => ANALYST_INSIGHT_CACHE_TTL_SECONDS
    );

    return { ...analystInsight, id: latestAnalystInsightContentEntry.id };
  }

  public static async getLearningGuideBySlug(
    slug: string,
    options: { isPaidPlan: boolean }
  ): Promise<LearningGuideType> {
    const contentfulGuideResponse = (await getCachedDataWithFallback(
      `learningGuide:${slug}`,
      () =>
        ContentfulRetrievalService.LearnHubInstance.getEntryBySlug(slug, ContentfulContentTypeEnum.LEARNING_GUIDE),
      (_) => WEALTHYHUB_CACHING_TTL_SECONDS
    )) as any;

    const guide = WealthyhubService._formatLearningGuideFromContentfulResponse(contentfulGuideResponse);

    return {
      ...guide,
      chapters: contentfulGuideResponse.fields.chapters.map((chapter: any) => ({
        title: chapter.fields.title,
        body: WealthyhubService._displayAllowedContent(
          WealthyhubService._transformRichTextToHTML(chapter.fields.body),
          {
            isPaidPlan: options.isPaidPlan
          }
        ),
        slug: chapter.fields.slug
      })) as LearningGuideChapterType[]
    };
  }

  public static async getLearningGuideById(
    id: string,
    options: { isPaidPlan: boolean }
  ): Promise<LearningGuideType> {
    const contentEntry = await ContentEntryService.getLearningGuideById(id);

    const contentfulGuideResponse = (await ContentfulRetrievalService.LearnHubInstance.getEntry(
      contentEntry.providers.contentful.id
    )) as any;

    const guide = WealthyhubService._formatLearningGuideFromContentfulResponse(contentfulGuideResponse);

    return {
      ...guide,
      chapters: contentfulGuideResponse.fields.chapters.map((chapter: any) => ({
        title: chapter.fields.title,
        body: WealthyhubService._displayAllowedContent(
          WealthyhubService._transformRichTextToHTML(chapter.fields.body),
          {
            isPaidPlan: options.isPaidPlan
          }
        ),
        slug: chapter.fields.slug
      })) as LearningGuideChapterType[]
    };
  }

  public static async getLearningGuides(): Promise<LearningGuideType[]> {
    const contentfulGuidesResponse = await getCachedDataWithFallback(
      "learningGuides",
      () => ContentfulRetrievalService.LearnHubInstance.getEntries(ContentfulContentTypeEnum.LEARNING_GUIDE),
      (_) => WEALTHYHUB_CACHING_TTL_SECONDS
    );

    return (contentfulGuidesResponse as EntryCollection<EntrySkeletonType>).items
      .sort((entry1: any, entry2: any) => entry1.fields.order - entry2.fields.order)
      .map((entry: any) => WealthyhubService._formatLearningGuideFromContentfulResponse(entry));
  }

  public static async getNews(): Promise<NewsItemType[]> {
    const learnNewsDocs = await LearnNews.find().sort({ date: -1 }).limit(WEALTHYHUB_NEWS_LIMIT);

    return learnNewsDocs.map((doc) => ({
      key: doc.hash,
      contentType: ContentfulContentTypeEnum.NEWS,
      createdAt: doc.date,
      title: doc.title,
      contentHTML: `<body style='font-family: Poppins,serif; font-size: 14px !important;'><style> p { color: #757575; } strong { color: black; font-weight: 500; } b { color: black; font-weight: 500; } h1,h2,h3,h4,h5,h6 { font-weight: 500; font-size: 18px; margin-top: 2em; margin-bottom: 1em; } h1:first-of-type, h2:first-of-type, h3:first-of-type, h4:first-of-type, h5:first-of-type, h6:first-of-type { margin-top: 0em !important; padding-top: 0 !important; }</style><link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">${doc.htmlContent}</body>`,
      previewTitleMain: doc.title,
      readingTime: doc.readingTime,
      fullImageURL: doc.imageUrl,
      previewImageURL: CloudflareService.Instance.getResizedImageURL(doc.imageUrl, { height: 512, width: 288 }),
      storyImageURL: CloudflareService.Instance.getResizedImageURL(doc.imageUrl, { height: 288, width: 288 })
    }));
  }

  public static async getGlossaryItems(): Promise<GlossaryItemType[]> {
    const contentfulGlossaryResponse = await getCachedDataWithFallback(
      "glossaryItems",
      () =>
        ContentfulRetrievalService.LandingPageInstance.getEntries(ContentfulContentTypeEnum.GLOSSARY, {
          limit: 200
        }),
      (_) => WEALTHYHUB_CACHING_TTL_SECONDS
    );

    return WealthyhubService._formatGlossaryItemsFromContentfulResponse(contentfulGlossaryResponse);
  }

  public static async getHelpCentre(user: UserDocument): Promise<HelpCentreCategoryType[]> {
    const allFaqCategories = await WealthyhubService._getFaqCategories({
      isHelpCentreCategory: true,
      locale: ENTITY_TO_CONTENTFUL_LOCALE[user.companyEntity]
    });

    return allFaqCategories.map((category) => {
      return {
        key: category.key,
        title: category.title,
        subtitle: category.subtitle,
        faqs: category.faqs
      };
    });
  }

  /**
   * @returns an HTML string that represents the rich text document.
   * @param richTextDocument
   * @private
   */
  private static _transformRichTextToHTML(richTextDocument: Document): string {
    // We use a special renderer for images that are inside the rich text document, to include them inside the HTML
    // as <img> tags.
    const options = {
      renderNode: {
        [BLOCKS.EMBEDDED_ASSET]: (node: any) => {
          return `<img src='https:${node.data.target.fields.file.url}' alt='${node.data.target.fields.title}' style='position: relative; display: block; margin-left: auto; margin-right: auto; margin-top: 2em; margin-bottom: 2em; width: 100%; border-radius: 12px; border: 1px solid #EEEEEE;'/>`;
        }
      }
    };

    return `<body style='font-family: Poppins,serif; font-size: 14px !important;'><style> p { color: #757575; } strong { color: black; font-weight: 500; } b { color: black; font-weight: 500; } h1,h2,h3,h4,h5,h6 { font-weight: 500; font-size: 18px; margin-top: 2em; margin-bottom: 1em; } h1:first-of-type, h2:first-of-type, h3:first-of-type, h4:first-of-type, h5:first-of-type, h6:first-of-type { margin-top: 0em !important; padding-top: 0 !important; }</style><link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">${documentToHtmlString(
      richTextDocument,
      options
    )}</body>`;
  }

  private static _transformAnalystInsightContentMarkdownToHTML(markdownContent: string): string {
    const DISCLAIMER_ANALYST_INSIGHTS =
      "\n - \n\n Capital at risk. Our analyst insights are for educational purposes only. Wealthyhood does not render investment, financial, legal, tax, or accounting advice.";

    return `<body style='font-family: Poppins,serif; font-size: 14px !important;'><style> p { color: #757575; } strong { color: black; font-weight: 500; } h1,h2,h3,h4,h5,h6 { font-weight: 500; font-size: 18px; margin-top: 2em; margin-bottom: 1em; } h1:first-of-type, h2:first-of-type, h3:first-of-type, h4:first-of-type, h5:first-of-type, h6:first-of-type { margin-top: 0; !important; } img { position: relative; display: block; margin-left: auto; margin-right: auto; margin-top: 2em; margin-bottom: 2em; width: 100%; border-radius: 12px; border: 1px solid #EEEEEE; }</style><link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">${marked.parse(
      markdownContent + DISCLAIMER_ANALYST_INSIGHTS
    )}</body>`;
  }

  /**
   * @descriptionTrims HTML content based on subscription status.
   * If user has subscription, returns full content.
   * If no subscription, trims content to preview length.
   *
   * Handles both plain text and HTML content:
   *
   * Plain text example:
   * Input: "This is a long article about investing strategies..."
   * Output: "This is a long article..." (trimmed to 150 chars)
   *
   * HTML example:
   * Input:
   * "<body>
   *   <p>First paragraph about markets</p>
   *   <p>Second paragraph with analysis</p>
   *   <p>Third paragraph with conclusion</p>
   * </body>"
   *
   * Output:
   * "<body>
   *   <p>First paragraph about markets</p>
   *   <p>Second paragraph with analysis</p>
   *   <p>...</p>
   * </body>"
   *
   * @param htmlContent - The HTML or text content to potentially trim
   * @param options
   * @returns The original or trimmed content based on subscription status
   */
  private static _displayAllowedContent(htmlContent: string, options: { isPaidPlan: boolean }): string {
    if (options.isPaidPlan) {
      return htmlContent;
    }

    // If it's not HTML content, apply simple text trimming
    if (!htmlContent.includes("</p>")) {
      return htmlContent.length > 150 ? `${htmlContent.substring(0, 150)}...` : htmlContent;
    }

    // Extract the body content and tag, preserving style and link tags
    const bodyMatch = htmlContent.match(/(<body[^>]*>)([\s\S]*?)(<\/body>)/i);
    if (!bodyMatch) {
      return htmlContent; // Return original if no body tag found
    }

    const [, bodyOpenTag, bodyContent, bodyCloseTag] = bodyMatch;

    // Find style and link tags
    const styleMatch = bodyContent.match(/<style>[\s\S]*?<\/style>/i);
    const linkMatch = bodyContent.match(/<link[^>]*>/i);
    const styleAndLinkTags = (styleMatch?.[0] || "") + (linkMatch?.[0] || "");

    // Find all paragraph tags after the style and link tags
    const contentWithoutStyleAndLink = bodyContent
      .replace(/<style>[\s\S]*?<\/style>/i, "")
      .replace(/<link[^>]*>/i, "");
    const paragraphs = contentWithoutStyleAndLink.match(/<p[^>]*>[\s\S]*?<\/p>/gi) || [];

    // Keep only the first two paragraphs
    const trimmedParagraphs = paragraphs.slice(0, 3);

    if (trimmedParagraphs.length === 0) {
      return htmlContent; // Return original if no paragraphs found
    }

    // Reconstruct the HTML maintaining the original body tag, style, and link tags
    return `${bodyOpenTag}${styleAndLinkTags}${trimmedParagraphs.join("")}${bodyCloseTag}`;
  }

  private static _formatAnalystInsightFromContentfulResponse(
    entry: Entry<EntrySkeletonType>
  ): Omit<AnalystInsightType, "id"> {
    return {
      key: entry.fields.slug,
      createdAt: new Date(entry.fields.publishedAt as string),
      contentType: ContentfulContentTypeEnum.ANALYST_INSIGHTS,
      title: entry.fields.title,
      contentHTML: WealthyhubService._transformAnalystInsightContentMarkdownToHTML(entry.fields.content as string),
      previewImageURL: entry.fields.headerImage,
      fullImageURL: entry.fields.headerImage,
      bannerImageURL: entry.fields.bannerImage,
      readingTime: entry.fields.readingTime,
      analystInsightType: entry.fields.contentType
    } as AnalystInsightType;
  }

  private static _formatLearningGuideFromContentfulResponse(
    contentfulGuideResponse: any
  ): Omit<LearningGuideType, "chapters"> {
    return {
      key: contentfulGuideResponse.fields.slug,
      contentType: ContentfulContentTypeEnum.LEARNING_GUIDE,
      title: contentfulGuideResponse.fields.title,
      description: contentfulGuideResponse.fields.description,
      backgroundColor: contentfulGuideResponse.fields.backgroundColor,
      guideIconURL: `https:${contentfulGuideResponse.fields.guideIcon.fields.file.url}`,
      mobileCoverImageURL: `https:${contentfulGuideResponse.fields.mobileCoverImage.fields.file.url}`,
      webCoverImageURL: contentfulGuideResponse.fields.webCoverImage?.fields?.file?.url
        ? `https:${contentfulGuideResponse.fields.webCoverImage?.fields?.file?.url}`
        : undefined,
      slug: contentfulGuideResponse.fields.slug,
      chapterCount: contentfulGuideResponse.fields.chapters.length
    };
  }

  private static _formatGlossaryItemsFromContentfulResponse(
    response: EntryCollection<EntrySkeletonType>
  ): GlossaryItemType[] {
    return response.items
      .map((entry: any) => {
        return {
          key: slugify(entry.fields.title),
          createdAt: entry.sys.createdAt,
          contentType: ContentfulContentTypeEnum.GLOSSARY,
          title: entry.fields.title,
          definitionHTML: WealthyhubService._transformRichTextToHTML(entry.fields.definition)
        } as GlossaryItemType;
      })
      .sort(({ key: a }, { key: b }) => a.localeCompare(b));
  }

  private static async _getFaqCategories(filter: {
    isHelpCentreCategory: boolean;
    locale: LocaleType;
  }): Promise<FaqCategoryType[]> {
    return getCachedDataWithFallback(
      "faqCategories",
      async () => {
        const contentfulResponse = await ContentfulRetrievalService.LandingPageInstance.getEntries(
          ContentfulContentTypeEnum.FAQ_CATEGORY,
          {
            limit: 200
          }
        );

        return contentfulResponse.items
          .map(WealthyhubService._formatFaqCategoryFromContentfulResponse)
          .filter(
            (category) =>
              category.isHelpCentreCategory === filter.isHelpCentreCategory && category.locale === filter.locale
          )
          .sort(({ order: a }, { order: b }) => a - b);
      },
      (_) => WEALTHYHUB_CACHING_TTL_SECONDS
    );
  }

  private static _formatFaqCategoryFromContentfulResponse(entry: any): FaqCategoryType {
    return {
      key: entry.fields.wealthyhoodId,
      title: entry.fields.title,
      subtitle: entry.fields.subtitle,
      locale: entry.fields.locale?.fields?.code, // some entries may not have a locale
      order: entry.fields.order,
      isHelpCentreCategory: entry.fields.isHelpCentreCategory,
      faqs: entry.fields.faqs.map((faq: any) => {
        return {
          question: faq.fields.question,
          answer: faq.fields.answer
        };
      })
    };
  }

  private static async _getAnalystInsightsPage(page: number): Promise<PaginatedApiResponse<AnalystInsightType>> {
    const [contentEntries, collectionCount] = await Promise.all([
      ContentEntry.find(
        { category: ContentEntryCategoryEnum.ANALYST_INSIGHTS },
        {},
        {
          limit: DEFAULT_ANALYST_INSIGHTS_PAGE_SIZE,
          skip: (page - 1) * DEFAULT_ANALYST_INSIGHTS_PAGE_SIZE,
          sort: { "providers.finimize.publishedAt": -1 }
        }
      ),
      ContentEntry.countDocuments()
    ]);

    const contentfulResponse = await ContentfulRetrievalService.LearnHubInstance.getEntriesByIds(
      ContentfulContentTypeEnum.CONTENT_ENTRY,
      contentEntries.map((entry) => entry.providers.contentful.id)
    );

    const analystInsights = contentfulResponse.items
      .map((entry) => {
        const contentEntry = contentEntries.find(
          (contentEntry) => contentEntry.providers.contentful.id === entry.sys.id
        );
        const analystInsight = WealthyhubService._formatAnalystInsightFromContentfulResponse(entry);

        return {
          ...analystInsight,
          id: contentEntry.id,
          publishedAt: contentEntry.publishAt
        };
      })
      .sort(({ publishedAt: a }, { publishedAt: b }) => {
        return new Date(b).getTime() - new Date(a).getTime();
      });

    return {
      data: analystInsights,
      pagination: PaginationUtil.getPaginationParametersFor(collectionCount, {
        page,
        pageSize: DEFAULT_ANALYST_INSIGHTS_PAGE_SIZE
      })
    };
  }
}
