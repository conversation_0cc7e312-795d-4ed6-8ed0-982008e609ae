import { Payout, PayoutDocument } from "../models/Payout";
import { IncomingPaymentStatusType } from "../external-services/devengoService";
import { BadRequestError } from "../models/ApiErrors";
import { EventType, GoCardlessPaymentsService } from "../external-services/goCardlessPaymentsService";
import { ProviderEnum } from "../configs/providersConfig";
import { PayoutRepository } from "../repositories/payoutRepository";
import logger from "../external-services/loggerService";

export default class PayoutService {
  /**
   * @description Syncs an existing payout with Devengo data.
   * @public
   * @param payoutId
   * @param data
   */
  public static async updatePayoutDevengoData(
    payoutId: string,
    data: {
      id: string;
      status: IncomingPaymentStatusType;
    }
  ): Promise<PayoutDocument> {
    const payout = await Payout.findById(payoutId);

    if (!payout) {
      throw new BadRequestError(`There is no payout with ID ${payoutId}`);
    } else if (["confirmed", "rejected"].includes(payout?.providers?.devengo?.status)) {
      // We already have a terminal status for this payout, therefore we don't want to update it.
      return;
    }

    return Payout.findOneAndUpdate(
      {
        "providers.devengo.id": payout.providers?.devengo?.id,
        "providers.devengo.status": payout.providers?.devengo?.status
      },
      { "providers.devengo": { id: data.id, status: data.status } },
      {
        runValidators: true,
        upsert: false,
        new: true
      }
    );
  }

  /**
   * @description Processes a GoCardless payout event.
   *
   * If the payout action is 'paid', it means the payout was just created. The payout status becomes 'paid' when the
   * 'fx_rate_confirmed' action is received from GoCardless.
   */
  public static async processGoCardlessPayoutEvent(event: EventType): Promise<void> {
    const goCardlessPayoutId = event.links["payout"];

    const goCardlessPayout = await GoCardlessPaymentsService.Instance.retrievePayout(goCardlessPayoutId);
    if (!goCardlessPayout) {
      throw new Error(`Could not find GoCardless payout for ID ${goCardlessPayoutId}`);
    }

    switch (event.action) {
      case "paid":
        await PayoutRepository.createPayout({
          reference: goCardlessPayout.reference,
          activeProviders: [ProviderEnum.GOCARDLESS],
          providers: {
            gocardless: { id: goCardlessPayout.id, status: goCardlessPayout.status }
          }
        });
        break;
      case "fx_rate_confirmed":
        const payout = await PayoutRepository.getPayoutByGoCardlessId(goCardlessPayoutId);
        if (!payout) {
          throw new Error(`Could not find payout document for GoCardless ID ${goCardlessPayoutId}`);
        }

        if (!payout.hasTerminalGoCardlessStatus) {
          await Payout.updateOne(
            { _id: payout._id },
            {
              "providers.gocardless.status": goCardlessPayout.status
            }
          );
        }

        break;
      default:
        logger.info(`Not processing payout events of action ${event.action}`, {
          module: "PayoutService",
          method: "processGoCardlessPayoutEvent",
          data: {
            action: event.action
          }
        });
        break;
    }
  }
}
