import logger from "../external-services/loggerService";
import MailerService from "../external-services/mailerService";
import UserService from "./userService";
import {
  RewardInvitation,
  RewardInvitationDocument,
  RewardInvitationDTOInterface
} from "../models/RewardInvitation";
import { RewardInvitationsFilter } from "filters";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";

export default class RewardInvitationService {
  /**
   * PUBLIC METHODS
   */
  public static async createRewardInvitation(targetUserEmail: string, referrerId: string): Promise<void> {
    const referrer = await UserService.getUser(referrerId);

    const targetUserExistingRewardInvitations = await RewardInvitationService.getRewardInvitations({
      targetUserEmail
    });

    if (targetUserExistingRewardInvitations.length > 0) {
      logger.warn(`Attempted to invite friend but target user ${targetUserEmail} is already invited}`, {
        module: "RewardInvitationService",
        method: "createRewardInvitation",
        data: {
          targetUser: targetUserEmail,
          referrer: referrer.email
        }
      });
      return;
    }

    const targetUser = await UserService.getUserByEmail(targetUserEmail);
    if (targetUser) {
      logger.warn(`Attempted to invite friend but target user ${targetUser.email} exists already.`, {
        module: "RewardInvitationService",
        method: "createRewardInvitation",
        data: {
          targetUser: targetUserEmail,
          referrer: referrer.email
        }
      });
      return;
    }

    await new RewardInvitation({
      referrer: referrer.id,
      targetUserEmail
    } as RewardInvitationDTOInterface).save();

    MailerService.sendUserInvitation(targetUserEmail, referrer);

    eventEmitter.emit(events.user.friendInvitation.eventId, referrer, { invitedEmail: targetUserEmail });

    logger.info(`Created reward invitation for user ${targetUserEmail}`, {
      module: "RewardInvitationService",
      method: "createRewardInvitation",
      data: {
        targetUser: targetUserEmail,
        referrer: referrer.email
      }
    });
  }

  public static async getRewardInvitations(
    filter: RewardInvitationsFilter = {}
  ): Promise<RewardInvitationDocument[]> {
    const dbFilter = this._createRewardsDbFilter(filter);

    return RewardInvitation.find(dbFilter);
  }

  private static _createRewardsDbFilter(filter: RewardInvitationsFilter) {
    const dbFilter = {
      targetUserEmail: filter.targetUserEmail
    };

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }
}
