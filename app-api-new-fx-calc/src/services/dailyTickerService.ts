import { currenciesConfig, fees as feesConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import { ApiResponse, PaginatedApiResponse } from "apiResponse";
import Decimal from "decimal.js";
import { QueryOptions } from "mongoose";
import {
  DailyPortfolioSavingsTicker,
  DailyPortfolioSavingsTickerDocument,
  DailyPortfolioTicker,
  DailyPortfolioTickerDocument,
  DailySavingsProductTicker,
  DailyTickerDocument
} from "../models/DailyTicker";
import { PortfolioDocument, SavingType } from "../models/Portfolio";
import DbUtil from "../utils/dbUtil";
import PaginationUtil from "../utils/paginationUtil";
import PortfolioService from "./portfolioService";
import DateUtil, { DateOrderingEnum } from "../utils/dateUtil";
import { DailyTickerFilter } from "filters";
import logger from "../external-services/loggerService";
import { captureMessage } from "@sentry/node";
import { DURATIONS_MAP, ONE_WEEK_IN_DAYS, TenorEnum } from "../configs/durationConfig";
import { SavingsProductDocument } from "../models/SavingsProduct";
import BlackrockService, { BlackrockFundDataType } from "../external-services/blackrockService";
import { NotImplementedError } from "../models/ApiErrors";
import { UserDocument, UserPopulationFieldsEnum } from "../models/User";
import { IntraDayPortfolioTickerDocument } from "../models/IntraDayTicker";
import UserService from "./userService";
import { SubscriptionDocument } from "../models/Subscription";
import * as CacheUtil from "../utils/cacheUtil";

const DB_BATCH_SIZE = 1000;
const { SAVINGS_PRODUCT_FEE_RATES } = feesConfig;
const { SavingsProductFixingExclusionScheduleEnum, SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

const NUMBER_OF_TICKERS_TO_CREATE = 5;

export type PortfolioTickerPerformanceDataPointType = {
  date: Date;
  displayDate: string;
  price: number;
};
type PortfolioTickerPerformanceEntryType = {
  startDate: Date;
  endDate: Date;
  data: PortfolioTickerPerformanceDataPointType[];
};
type PortfolioTickerPerformanceByTenorType = Record<TenorEnum, PortfolioTickerPerformanceEntryType>;

const EARLIEST_DAILY_PORTFOLIO_TICKER_CACHE_EXPIRE = 1 * 24 * 60 * 60; // 1 day in seconds

class DailyTickerService {
  /**
   * ===============
   * PUBLIC METHODS
   * ===============
   */

  /**
   * @description Retrieves all the portfolio tickers for a given portfolio by tenor. Also fills with zeroes, any dates
   * that are within the tenor but before the user invested.
   *
   * @param portfolioId
   */
  public static async getPortfolioTickerPerformanceByTenor(
    portfolioId: string
  ): Promise<PortfolioTickerPerformanceByTenorType> {
    const [portfolio, tickersResponse] = await Promise.all([
      PortfolioService.getPortfolio(portfolioId, true),
      DailyTickerService._getPortfolioDailyTickers({ portfolio: portfolioId }, "+date")
    ]);

    const tickers = tickersResponse.data as DailyPortfolioTickerDocument[];
    if (tickers.length === 0) {
      return this._getPortfolioZeroTickerPerformanceByTenor();
    }

    const user = portfolio.owner as UserDocument;

    const portfolioTickerPerformanceByTenorPromises = Object.values(TenorEnum).map((tenor) => {
      const tickersForTenor =
        tenor === TenorEnum.ALL_TIME
          ? DailyTickerService._getPortfolioTickerPerformanceForDateRange(user.currency, tickers)
          : DailyTickerService._getPortfolioTickerPerformanceForDateRange(user.currency, tickers, {
              startDate: DateUtil.getDateOfDaysAgo(new Date(Date.now()), DURATIONS_MAP[tenor])
            });

      if (!tickersForTenor) {
        logger.warn(`Tenors for tenor ${tenor} are empty which should not happen!`, {
          module: "DailyTickerService",
          method: "getPortfolioTickerPerformanceByTenor",
          data: {
            tenor,
            portfolioId
          }
        });
      }

      return [
        tenor,
        {
          startDate: tickersForTenor.at(0)?.date,
          endDate: tickersForTenor.at(-1)?.date,
          data: tickersForTenor
        }
      ];
    });

    return Object.fromEntries(portfolioTickerPerformanceByTenorPromises);
  }

  /**
   * @description Retrieves all the portfolio tickers for a given portfolio, optionally paginated and sorted
   * @param filter
   * @param pageConfig
   * @param sort
   * @returns A list of tickers corresponding to the given portfolio
   */
  public static async getDailyPortfolioTickers(
    filter: DailyTickerFilter = {},
    pageConfig?: { page: number; pageSize: number },
    sort?: string
  ): Promise<ApiResponse<DailyTickerDocument>> {
    if (pageConfig) {
      return DailyTickerService._getPortfolioDailyTickersPaginated(filter, pageConfig, sort);
    } else return DailyTickerService._getPortfolioDailyTickers(filter, sort);
  }

  /**
   * @description Method for creating daily portfolio tickers for all REAL portfolios that have an intra-day
   * portfolio ticker. We create the daily tickers based on the current latest intra-day portfolio ticker price.
   *
   * We create tickers for all portfolios of users that have completed an investment and are not deleted.
   * The iteration happens in batches and for each batch we bulk write on the db to create the tickers.
   */
  public static async createDailyPortfolioTickers(): Promise<void> {
    logger.info("Storing daily portfolio prices", {
      module: "DailyTickerService",
      method: "createDailyPortfolioTickers"
    });

    const { start, end } = DateUtil.getStartAndEndOfToday();

    await UserService.getUsersStreamed(
      { portfolioConversionStatus: "completed", hasRequestedDeletion: false },
      {
        path: "portfolios",
        populate: {
          path: "currentTicker"
        }
      }
    ).eachAsync(
      async (users) => {
        const dbOperations = users
          .map((user) => user.portfolios[0] as PortfolioDocument)
          .filter((portfolio) => !!portfolio.currentTicker)
          .map((portfolio: PortfolioDocument) => {
            const latestIntraDayPortfolioTicker = portfolio.currentTicker as IntraDayPortfolioTickerDocument;

            return {
              updateOne: {
                // We want to avoid having more than one portfolio tickers within the same minute.
                // That would be an indication that the implementation is wrong.
                // So we filter by date.
                filter: { portfolio: portfolio.id, date: { $gt: start, $lt: end } },
                update: {
                  $set: {
                    currency: latestIntraDayPortfolioTicker.currency,
                    pricePerCurrency: latestIntraDayPortfolioTicker.pricePerCurrency,
                    openingPricePerCurrency: latestIntraDayPortfolioTicker.pricePerCurrency,
                    closingPricePerCurrency: latestIntraDayPortfolioTicker.pricePerCurrency,
                    portfolio: portfolio.id,
                    date: new Date()
                  }
                },
                upsert: true
              }
            };
          });

        await DailyPortfolioTicker.bulkWrite(dbOperations);
      },
      { batchSize: DB_BATCH_SIZE }
    );
  }

  /**
   * Returns the earliest daily portfolio ticker from the requested date.
   *
   * @param portfolioId
   * @param requestedDate
   */
  public static async getEarliestDailyPortfolioTickerFromDate(
    portfolioId: string,
    requestedDate: Date
  ): Promise<DailyPortfolioTickerDocument> {
    const startOfDay = DateUtil.getStartOfDay(new Date(requestedDate));
    return DailyPortfolioTicker.findOne({ portfolio: portfolioId, date: { $gte: startOfDay } }).sort({
      date: 1
    });
  }

  public static async getFirstDailyPortfolioTickerDate(portfolioId: string): Promise<Date> {
    const date = await CacheUtil.getCachedDataWithFallback<Date>(
      `daily:portfolio:first:date:${portfolioId}`,
      async (): Promise<Date> => {
        const ticker = await DailyPortfolioTicker.findOne({ portfolio: portfolioId }).sort({
          date: 1
        });
        if (ticker) {
          return DateUtil.getStartOfDay(ticker.date);
        }

        return null;
      },
      (_) => EARLIEST_DAILY_PORTFOLIO_TICKER_CACHE_EXPIRE
    );

    // Cast to Date after fetching from redis
    return date ? new Date(date) : null;
  }

  public static async fetchAndStoreSavingsProductData(savingsProduct: SavingsProductDocument): Promise<void> {
    const latestData = await DailyTickerService.fetchSavingsProductData(savingsProduct.commonId);

    logger.info(`Fetched latest data for ${savingsProduct.commonId} savings product`, {
      module: "DailyTickerService",
      method: "fetchAndStoreSavingsProductData",
      data: {
        latestData,
        savingsProductId: savingsProduct.commonId
      }
    });

    await DailyTickerService._storeSavingsProductData(savingsProduct, latestData);
  }

  public static async getPortfolioSavingsTickers(
    portfolio: PortfolioDocument,
    savingsProduct: SavingsProductDocument,
    options: { startDate: Date; endDate: Date }
  ): Promise<DailyPortfolioSavingsTickerDocument[]> {
    return DailyPortfolioSavingsTicker.find({
      portfolio: portfolio.id,
      savingsProduct: savingsProduct.id,
      date: { $gte: options.startDate, $lte: options.endDate }
    });
  }

  /**
   * @description
   * Calculate Portfolio Savings ticker if:
   * - Ticker doesn't exist
   * - User has at least 1 saving
   *
   * Daily accrual formula:
   * DDF * MoneyInSavings * (1 - (WealthyhoodFee / 365))
   */
  public static async setDailyPortfolioSavingsTicker(
    portfolio: PortfolioDocument,
    savings: SavingType,
    savingsProduct: SavingsProductDocument
  ): Promise<void> {
    const { start: startOfYesterday, end: endOfYesterday } = DateUtil.getStartAndEndOfYesterday();
    const user = portfolio.owner as UserDocument;
    const tickerExists = await DailyPortfolioSavingsTicker.exists({
      portfolio: portfolio.id,
      savingsProduct: savingsProduct.id,
      date: { $gte: startOfYesterday, $lte: endOfYesterday }
    });
    if (tickerExists) {
      logger.info(
        `Savings Ticker already exists for ${portfolio.id} portfolio (${savingsProduct.commonId}) - skipping ticker creation...`,
        {
          module: "DailyTickerService",
          method: "setDailyPortfolioSavingsTicker"
        }
      );
      return;
    }

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION);
    const subscription = user.subscription as SubscriptionDocument;

    const dailyPortfolioSavings = await DailyTickerService._calculateDailyPortfolioSavings(
      subscription,
      savings,
      savingsProduct,
      startOfYesterday
    );
    if (!dailyPortfolioSavings) {
      throw new Error("Did not found yesterday's savings product ticker");
    }

    const { holdingAmount, planFee, dailyAccrual } = dailyPortfolioSavings;

    const ticker = await new DailyPortfolioSavingsTicker({
      portfolio: portfolio.id,
      planPrice: subscription.price,
      savingsProduct: savingsProduct.id,
      date: DateUtil.getDateAfterNHours(startOfYesterday, 12),
      holdingAmount,
      planFee,
      dailyAccrual
    }).save();

    logger.info(`Created portfolio savings ticker for ${portfolio.id} portfolio (${savingsProduct.commonId})`, {
      module: "DailyTickerService",
      method: "setDailyPortfolioSavingsTicker",
      data: {
        planPrice: subscription.price,
        ticker: ticker.toObject()
      }
    });
  }

  /**
   * @description
   * This method uses the same logic as the one we use to create daily portfolio savings ticker to **estimate**
   * what the portfolio savings ticker for today will be based on savings product details of 2 days ago, but today's
   * currently held amount.
   */
  public static async estimateTodayPortfolioSavings(
    user: UserDocument,
    savings: SavingType,
    savingsProduct: SavingsProductDocument
  ): Promise<{ dailyAccrual: number; planFee: any; holdingAmount: number }> {
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION);
    const subscription = user.subscription as SubscriptionDocument;

    return DailyTickerService._calculateDailyPortfolioSavings(
      subscription,
      savings,
      savingsProduct,
      DateUtil.getDateOfDaysAgo(new Date(Date.now()), 2)
    );
  }

  public static async fetchSavingsProductData(
    savingsProductCommonId: savingsUniverseConfig.SavingsProductType
  ): Promise<BlackrockFundDataType> {
    if (savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductCommonId].fundManager === "Blackrock") {
      return BlackrockService.getDistributingMoneyMarketFundData(savingsProductCommonId);
    } else {
      throw new NotImplementedError(`Cannot support savings product data fetching for ${savingsProductCommonId}`);
    }
  }

  /**
   * ===============
   * PRIVATE METHODS
   * ===============
   */
  private static async _calculateDailyPortfolioSavings(
    subscription: SubscriptionDocument,
    savings: SavingType,
    savingsProduct: SavingsProductDocument,
    date: Date = new Date(Date.now())
  ): Promise<{ dailyAccrual: number; planFee: any; holdingAmount: number }> {
    const { start, end } = DateUtil.getStartAndEndOfDay(date);
    const savingsProductTicker = await DailySavingsProductTicker.findOne({
      savingsProduct: savingsProduct.id,
      date: { $gte: start, $lte: end }
    });
    if (!savingsProductTicker) {
      logger.error(`Did not find savings product ticker`, {
        module: "DailyTickerService",
        method: "_calculateDailyPortfolioSavings",
        data: {
          savingsProductId: savingsProduct.commonId,
          start,
          end
        }
      });
      return;
    }

    const annualFee = SAVINGS_PRODUCT_FEE_RATES[savingsProduct.commonId][subscription.plan];
    const oneDayFee = Decimal.div(annualFee, 365);
    const { dailyDistributionFactor } = savingsProductTicker;
    const dailyDistributionFactorAfterWealthyhoodFee = Decimal.sub(dailyDistributionFactor, oneDayFee);

    const currentHeldAmount = savings.amount;
    const dailyAccrualAfterFees = Decimal.mul(
      currentHeldAmount,
      dailyDistributionFactorAfterWealthyhoodFee
    ).toNumber();

    return {
      holdingAmount: savings.amount,
      planFee: annualFee,
      dailyAccrual: dailyAccrualAfterFees
    };
  }

  private static _createDailyTickersDbFilter(filter: DailyTickerFilter) {
    const dbFilter = {
      portfolio: filter.portfolio,
      date: null as any
    };

    if (filter.date) {
      dbFilter["date"] = {
        $gte: filter.date.startDate,
        $lt: filter.date.endDate
      };
    }

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }

  private static async _getPortfolioDailyTickersPaginated(
    filter: DailyTickerFilter = {},
    pageConfig?: { page: number; pageSize: number },
    sort?: string
  ): Promise<PaginatedApiResponse<DailyTickerDocument>> {
    const dbFilter = this._createDailyTickersDbFilter(filter);
    const count = await DailyPortfolioTicker.countDocuments(dbFilter);
    const pageConfigToUse = PaginationUtil.getPaginationParametersFor(count, pageConfig);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    const dailyTickers = await DailyPortfolioTicker.find(dbFilter, null, options)
      .skip((pageConfigToUse.page - 1) * pageConfigToUse.pageSize)
      .limit(pageConfigToUse.pageSize);

    return { pagination: pageConfigToUse, data: dailyTickers };
  }

  private static async _getPortfolioDailyTickers(
    filter: DailyTickerFilter = {},
    sort?: string
  ): Promise<ApiResponse<DailyTickerDocument>> {
    const dbFilter = this._createDailyTickersDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    const dailyTickers = await DailyPortfolioTicker.find(dbFilter, null, options);

    return { data: dailyTickers };
  }

  /**
   * @returns Tickers for the given date range.
   *
   * In contrast to a DB query for tickers based on date range, this method fills dates that do not have tickers
   * with zero-valued tickers. e.g. if a one-week date range period is passed, and the tickers are only available
   * for the last 3 days, the remaining four days are returned as zero-value tickers.
   *
   * If no start date has been passed, we return all tickers UNLESS there are less than one week worth of tickers,
   * in which case return tickers as if one-week tickers were requested.
   *
   * @param userCurrency
   * @param tickers
   * @param options
   * @private
   */
  private static _getPortfolioTickerPerformanceForDateRange(
    userCurrency: currenciesConfig.MainCurrencyType,
    tickers: DailyPortfolioTickerDocument[],
    options?: { startDate: Date }
  ): PortfolioTickerPerformanceDataPointType[] {
    const today = new Date(Date.now());
    const oldestTickerDate = tickers[0].date;

    // If user has not passed options, then we don't set a start date UNLESS the user is invested for less than a
    // week, in which case we set the date one week ago as the starting date.
    let startDate = options?.startDate;
    if (!options && DateUtil.isFutureDate(oldestTickerDate, DateUtil.getDateOfDaysAgo(today, ONE_WEEK_IN_DAYS))) {
      startDate = DateUtil.getDateOfDaysAgo(today, ONE_WEEK_IN_DAYS);
    }

    const tickersForPeriod = tickers
      .filter((ticker) => !startDate || ticker.date >= startDate)
      .map(({ date, getPrice }) => {
        return {
          date,
          price: getPrice(userCurrency)
        };
      });

    let finalTickersForPeriod = tickersForPeriod;
    if (startDate && DateUtil.isFutureDate(oldestTickerDate, startDate)) {
      // We need to backfill with zero-value tickers for the days when there are no tickers.
      const datesToFillWithZeroes = DateUtil.getAllDatesBetweenTwoDates(
        startDate,
        DateUtil.getYesterday(oldestTickerDate),
        {
          order: DateOrderingEnum.LEAST_RECENT_FIRST
        }
      );

      // We add the zero-value tickers in the beginning of the array
      finalTickersForPeriod = datesToFillWithZeroes
        .map((date) => {
          return {
            date,
            price: 0
          };
        })
        .concat(tickersForPeriod);
    }

    return finalTickersForPeriod.map(({ date, price }) => {
      return {
        date,
        displayDate: DateUtil.formatDateToDDMONYYYY(date),
        price
      };
    });
  }

  /**
   * @description
   * This method stores savings product date to a daily ticker with yesterday's date.
   * It throws an error if the received fixing date is not expected.
   */
  private static async _storeSavingsProductData(
    savingsProduct: SavingsProductDocument,
    savingsProductData: BlackrockFundDataType
  ): Promise<void> {
    // If the fixing date is not for yesterday, we should not create tickers.
    // Some common cases where this could happen:
    // 1) We just haven't gotten the updated savings product data for the day.
    // 2) The fixing date is from a previous day, because yesterday was a holiday for
    // the ticker's markets or weekend
    if (!DateUtil.isYesterday(savingsProductData.fixingDate)) {
      logger.info(`Skipping ${savingsProduct.commonId} ticker creation...`, {
        module: "DailyTickerService",
        method: "_storeSavingsProductData",
        data: {
          savingsProductData: savingsProductData,
          savingsProductId: savingsProduct.commonId
        }
      });
      return;
    }

    // Calculate how many days are distributed from the current fixing date.
    const daysDistributedFromCurrentFixing =
      DailyTickerService._calculateDaysDistributedFromFixing(savingsProductData);

    // Create tickers based on the days that are distributed from the current fixing date.
    const dailyDistributionFactor = Decimal.div(
      savingsProductData.dailyDistributionFactor,
      daysDistributedFromCurrentFixing
    )
      .toDecimalPlaces(9)
      .toNumber();
    for (let daysOffset = 0; daysOffset < daysDistributedFromCurrentFixing; daysOffset++) {
      const tickerDate = DateUtil.getDateAfterNdays(savingsProductData.fixingDate, daysOffset);
      const { start, end } = DateUtil.getStartAndEndOfDay(tickerDate);

      const tickerIsCreated = await DailySavingsProductTicker.exists({
        date: { $gte: start, $lte: end },
        savingsProduct: savingsProduct.id
      });

      if (!tickerIsCreated) {
        await new DailySavingsProductTicker({
          savingsProduct: savingsProduct.id,
          dailyDistributionFactor: dailyDistributionFactor,
          oneDayYield: savingsProductData.oneDayYield,
          fixingDate: savingsProductData.fixingDate,
          date: DateUtil.getDateAfterNHours(tickerDate, 12)
        }).save();
      }
    }
  }

  private static _calculateDaysDistributedFromFixing(savingsProductData: BlackrockFundDataType): number {
    const daysThatAreDistributedFromFixing = Decimal.div(
      Decimal.mul(savingsProductData.dailyDistributionFactor, 365).mul(100),
      savingsProductData.oneDayYield
    )
      .round()
      .toNumber();
    return daysThatAreDistributedFromFixing;
  }

  private static _calculateDaysTillTheNextFixing(
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    tickerCreationDate: Date
  ): number {
    const { fixingExclusionSchedule } = SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId];
    let nextFixingDate: Date;
    if (fixingExclusionSchedule === SavingsProductFixingExclusionScheduleEnum.WEEKEND_AND_UK_BANK_HOLIDAYS) {
      nextFixingDate = DateUtil.calculateNextUKWorkDay(tickerCreationDate);
    } else {
      throw new NotImplementedError(`Could not handle fixing date calculation for ${fixingExclusionSchedule}`);
    }

    const noOfDaysTillTheNextFixing = DateUtil.dateDiffInWholeDays(tickerCreationDate, nextFixingDate);

    return noOfDaysTillTheNextFixing;
  }

  /**
   * @description Returns 5 zero valued tickers for all tenors.
   */
  private static _getPortfolioZeroTickerPerformanceByTenor(): PortfolioTickerPerformanceByTenorType {
    return Object.fromEntries(
      Object.entries(DURATIONS_MAP).map(([tenor]) => {
        const endDate = new Date(Date.now());
        const startDate = DateUtil.getDateOfDaysAgo(endDate, NUMBER_OF_TICKERS_TO_CREATE - 1);

        const dateRange: Date[] = DateUtil.getAllDatesBetweenTwoDates(startDate, endDate);

        // Create data points for each date in the range
        const dataPoints = dateRange.map((date) => ({
          date: date,
          displayDate: DateUtil.formatDateToDDMONYYYY(date),
          price: 0
        }));

        return [
          tenor,
          {
            startDate,
            endDate,
            data: dataPoints
          }
        ];
      })
    ) as PortfolioTickerPerformanceByTenorType;
  }
}

export default DailyTickerService;
