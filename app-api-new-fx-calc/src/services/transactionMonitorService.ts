import {
  MonitorType,
  TransactionMonitor,
  TransactionMonitorDTOInterface,
  TransactionMonitorDocument
} from "../models/TransactionMonitor";
import Decimal from "decimal.js";
import { DepositCashTransaction, Transaction, WithdrawalCashTransaction } from "../models/Transaction";
import { UserDocument, UserPopulationFieldsEnum } from "../models/User";
import DateUtil from "../utils/dateUtil";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import DbUtil from "../utils/dbUtil";
import { RiskScoreClassificationEnum } from "../models/RiskAssessment";

const NET_AGGREGATE_AMOUNT_THRESHOLD_FOR_HIGH_RISK_USER = 1000;
const AGGREGATE_AMOUNT_THRESHOLD_FOR_HIGH_RISK_USER = 65000;
const AGGREGATE_AMOUNT_THRESHOLD_FOR_LOW_MEDIUM_RISK_USER = 35000;
const AGGREGATE_AMOUNT_TIME_FRAME_IN_MONTHS = 6;
const HIGH_VOLUME_DEPOSIT_THRESHOLD = 9900;
const HIGH_VOLUME_DEPOSIT_TIME_FRAME_IN_DAYS = 30;
const WITHDRAWAL_AFTER_DEPOSIT_TIME_FRAME_IN_DAYS = 30;

export class TransactionMonitorService {
  public static async checkSuspiciousTransactionActivity(user: UserDocument): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.LATEST_RISK_ASSESSMENT);

    const checkPromises = [
      TransactionMonitorService._checkForHighVolumeDeposits(user),
      TransactionMonitorService._checkTransactionAfterInactivity(user)
    ];

    const isHighRiskUser = user.latestRiskAssessment?.classification === RiskScoreClassificationEnum.HighRisk;
    if (isHighRiskUser) {
      checkPromises.push(
        TransactionMonitorService._checkAggregateAmountForHighRiskUser(user),
        TransactionMonitorService._checkForWithdrawalAfterDeposit(user),
        TransactionMonitorService._checkNetAggregateAmountForHighRiskUser(user)
      );
    }

    const isLowOrMediumRiskUser = [
      RiskScoreClassificationEnum.LowRisk,
      RiskScoreClassificationEnum.MediumRisk
    ].includes(user.latestRiskAssessment?.classification);
    if (isLowOrMediumRiskUser) {
      checkPromises.push(TransactionMonitorService._checkAggregateAmountForLowMediumRiskUser(user));
    }

    await Promise.all(checkPromises);
  }

  private static async _checkNetAggregateAmountForHighRiskUser(user: UserDocument): Promise<void> {
    const [depositsAndWithdrawals, transactionMonitorExists] = await Promise.all([
      Transaction.find({
        owner: user._id,
        category: { $in: ["DepositCashTransaction", "WithdrawalCashTransaction"] }
      }),
      TransactionMonitor.findOne({
        owner: user._id,
        type: MonitorType.NET_AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
      })
    ]);

    const netAggregateAmount = depositsAndWithdrawals
      .filter((transaction) => transaction.status === "Settled")
      .reduce((netAggregateAmount, transaction) => {
        if (transaction.category === "WithdrawalCashTransaction") {
          return netAggregateAmount.sub(transaction.consideration.amount);
        } else if (transaction.category === "DepositCashTransaction") {
          return netAggregateAmount.add(transaction.consideration.amount);
        }

        return netAggregateAmount;
      }, new Decimal(0))
      .div(100);

    if (
      netAggregateAmount.greaterThan(NET_AGGREGATE_AMOUNT_THRESHOLD_FOR_HIGH_RISK_USER) &&
      !transactionMonitorExists
    ) {
      await TransactionMonitorService._create({
        owner: user._id,
        type: MonitorType.NET_AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
      });
      eventEmitter.emit(events.transactionMonitoring.netAggregateAmountForHighRiskUser.eventId, user);
    }
  }

  private static async _checkAggregateAmountForHighRiskUser(user: UserDocument): Promise<void> {
    const aggregateAmountTimeFrame = DateUtil.getDateOfMonthsAgo(
      new Date(Date.now()),
      AGGREGATE_AMOUNT_TIME_FRAME_IN_MONTHS
    );
    const [depositsAndWithdrawals, transactionMonitorExists] = await Promise.all([
      Transaction.find({
        owner: user._id,
        category: { $in: ["DepositCashTransaction", "WithdrawalCashTransaction"] },
        createdAt: {
          $gte: aggregateAmountTimeFrame
        }
      }),
      TransactionMonitor.findOne({
        owner: user._id,
        type: MonitorType.AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER,
        createdAt: {
          $gte: aggregateAmountTimeFrame
        }
      })
    ]);

    const aggregateAmount = depositsAndWithdrawals
      .filter((transaction) => transaction.status === "Settled")
      .reduce((sum, transaction) => sum.plus(transaction.consideration.amount), new Decimal(0))
      .div(100);

    if (aggregateAmount.greaterThan(AGGREGATE_AMOUNT_THRESHOLD_FOR_HIGH_RISK_USER) && !transactionMonitorExists) {
      await TransactionMonitorService._create({
        owner: user._id,
        type: MonitorType.AGGREGATE_AMOUNT_FOR_HIGH_RISK_USER
      });
      eventEmitter.emit(events.transactionMonitoring.aggregateAmountForHighRiskUser.eventId, user);
    }
  }

  private static async _checkAggregateAmountForLowMediumRiskUser(user: UserDocument): Promise<void> {
    const aggregateAmountTimeFrame = DateUtil.getDateOfMonthsAgo(
      new Date(Date.now()),
      AGGREGATE_AMOUNT_TIME_FRAME_IN_MONTHS
    );
    const [depositsAndWithdrawals, transactionMonitorExists] = await Promise.all([
      Transaction.find({
        owner: user._id,
        category: { $in: ["DepositCashTransaction", "WithdrawalCashTransaction"] },
        createdAt: {
          $gte: aggregateAmountTimeFrame
        }
      }),
      TransactionMonitor.findOne({
        owner: user._id,
        type: MonitorType.AGGREGATE_AMOUNT_FOR_LOW_RISK_USER,
        createdAt: {
          $gte: aggregateAmountTimeFrame
        }
      })
    ]);

    const aggregateAmount = depositsAndWithdrawals
      .filter((transaction) => transaction.status === "Settled")
      .reduce((sum, transaction) => sum.plus(transaction.consideration.amount), new Decimal(0))
      .div(100);

    if (
      aggregateAmount.greaterThan(AGGREGATE_AMOUNT_THRESHOLD_FOR_LOW_MEDIUM_RISK_USER) &&
      !transactionMonitorExists
    ) {
      await TransactionMonitorService._create({
        owner: user._id,
        type: MonitorType.AGGREGATE_AMOUNT_FOR_LOW_RISK_USER
      });
      eventEmitter.emit(events.transactionMonitoring.aggregateAmountForLowMediumRiskUser.eventId, user);
    }
  }

  /**
   * @description
   * Check if a dormant account (1 year of transaction inactivity) is used to make a deposit or withdrawal.
   *
   * More specifically, check if a user has made a deposit/withdrawal within 1 month after a year of inactivity.
   */
  private static async _checkTransactionAfterInactivity(user: UserDocument): Promise<void> {
    // 1. Check that a transaction was made more than a year ago.
    // and a transaction monitor for this type does not already exist.
    const oneYearAgo = DateUtil.getDateOfYearsAgo(new Date(Date.now()), 1);
    const [lastTransactionBeforeOneYear, transactionMonitorExists] = await Promise.all([
      Transaction.findOne({
        owner: user._id,
        createdAt: { $lt: oneYearAgo }
      }).sort({ createdAt: -1 }),
      TransactionMonitor.exists({
        owner: user._id,
        type: MonitorType.TRANSACTION_AFTER_1_YEAR,
        createdAt: { $gte: oneYearAgo }
      })
    ]);
    if (!lastTransactionBeforeOneYear || transactionMonitorExists) {
      return;
    }

    // 2. Check no transaction were created one year after the last transaction before one year.
    const oneYearAfterPottentialInactivity = DateUtil.getDateOfYearsAgo(
      lastTransactionBeforeOneYear.createdAt,
      -1
    );
    const transactionsInBetween = await Transaction.countDocuments({
      owner: user._id,
      createdAt: {
        $gt: lastTransactionBeforeOneYear.createdAt,
        $lt: oneYearAfterPottentialInactivity
      }
    });
    if (transactionsInBetween > 0) {
      return;
    }

    // 3. Check if the user has made a deposit or withdrawal within 1 month after the inactivity period.
    const depositOrWithdrawalExistsWithin1MonthAfterInactivity = await Transaction.exists({
      owner: user._id,
      category: { $in: ["DepositCashTransaction", "WithdrawalCashTransaction"] },
      createdAt: {
        $gte: oneYearAfterPottentialInactivity,
        $lt: DateUtil.getDateAfterNMonths(oneYearAfterPottentialInactivity, 1)
      }
    });
    if (depositOrWithdrawalExistsWithin1MonthAfterInactivity) {
      await TransactionMonitorService._create({
        owner: user._id,
        type: MonitorType.TRANSACTION_AFTER_1_YEAR
      });
      eventEmitter.emit(events.transactionMonitoring.transactionAfterAccountInactivity.eventId, user);
    }
  }

  /**
   * @description
   * Check if a user has made multiple deposits over a certain threshold within the last 30 days.
   */
  private static async _checkForHighVolumeDeposits(user: UserDocument): Promise<void> {
    const highVolumeDepositTimeFrame = DateUtil.getDateOfDaysAgo(
      new Date(Date.now()),
      HIGH_VOLUME_DEPOSIT_TIME_FRAME_IN_DAYS
    );

    const [deposits, transactionMonitorExists] = await Promise.all([
      DepositCashTransaction.find({
        owner: user._id,
        status: "Settled",
        "consideration.amount": { $gt: Decimal.mul(HIGH_VOLUME_DEPOSIT_THRESHOLD, 100).toNumber() },
        createdAt: { $gte: highVolumeDepositTimeFrame }
      }),
      TransactionMonitor.exists({
        owner: user._id,
        type: MonitorType.HIGH_VOLUME_DEPOSITS,
        createdAt: { $gte: highVolumeDepositTimeFrame }
      })
    ]);

    if (deposits.length >= 2 && !transactionMonitorExists) {
      await TransactionMonitorService._create({
        owner: user._id,
        type: MonitorType.HIGH_VOLUME_DEPOSITS
      });
      eventEmitter.emit(events.transactionMonitoring.highVolumeDeposits.eventId, user);
    }
  }

  /**
   * @description
   * Check if a user has made a withdrawal after a deposit within the last 30 days.
   */
  private static async _checkForWithdrawalAfterDeposit(user: UserDocument): Promise<void> {
    const withdrawalAfterDepositTimeFrame = DateUtil.getDateOfDaysAgo(
      new Date(Date.now()),
      WITHDRAWAL_AFTER_DEPOSIT_TIME_FRAME_IN_DAYS
    );

    // 1. Check that a deposit exists within the time frame.
    const lastDeposit = await DepositCashTransaction.findOne({
      owner: user._id,
      createdAt: { $gte: withdrawalAfterDepositTimeFrame }
    }).sort({ createdAt: -1 });
    if (!lastDeposit) return;

    const lastWithdrawal = await WithdrawalCashTransaction.findOne({
      owner: user._id,
      createdAt: {
        $gte: lastDeposit.createdAt
      }
    }).sort({ createdAt: -1 });

    // 2. If a withdrawal exists after the last deposit, create a transaction monitor.
    if (lastWithdrawal && lastWithdrawal.createdAt > lastDeposit.createdAt) {
      const transactionMonitorExists = await TransactionMonitor.exists({
        owner: user._id,
        type: MonitorType.WITHDRAWAL_AFTER_DEPOSIT_FOR_HIGH_RISK_USER,
        createdAt: { $gte: withdrawalAfterDepositTimeFrame }
      });

      if (!transactionMonitorExists) {
        await TransactionMonitorService._create({
          owner: user._id,
          type: MonitorType.WITHDRAWAL_AFTER_DEPOSIT_FOR_HIGH_RISK_USER
        });
        eventEmitter.emit(events.transactionMonitoring.withdrawalAfterDeposit.eventId, user);
      }
    }
  }

  private static async _create(
    transactionMonitorData: TransactionMonitorDTOInterface
  ): Promise<TransactionMonitorDocument> {
    return new TransactionMonitor({ ...transactionMonitorData, createdAt: new Date(Date.now()) }).save();
  }
}
