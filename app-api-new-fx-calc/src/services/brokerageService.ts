import { countriesConfig, currenciesConfig } from "@wealthyhood/shared-configs";
import {
  BANK_ACCOUNT_NAME_CHAR_LIMIT,
  CurrencyEnum,
  DocumentCodesType,
  EmploymentInfoType,
  EmploymentStatusType,
  IndustryType,
  SourceOfWealthType,
  WealthkernelAccountRegionEnum,
  WealthkernelService
} from "../external-services/wealthkernelService";
import { UserDocument } from "../models/User";
import { BankAccountDocument } from "../models/BankAccount";
import { AddressDocument } from "../models/Address";
import { MainCurrencyToWealthkernelCurrency } from "../configs/currenciesConfig";
import { AccountDocument } from "../models/Account";
import { PortfolioDocument } from "../models/Portfolio";
import {
  DepositCashTransactionDocument,
  TransactionPopulationFieldsEnum,
  WithdrawalCashTransactionDocument
} from "../models/Transaction";
import Decimal from "decimal.js";
import DbUtil from "../utils/dbUtil";
import { DevengoAccountConfig } from "../configs/devengoConfig";
import { getEnvironment } from "../utils/environmentUtil";
import { CreditTicketDocument } from "../models/CreditTicket";

export const InternalTransferStatusArray = ["Requested", "Accepted", "Rejected", "Completed"] as const;
export type InternalTransferStatusType = (typeof InternalTransferStatusArray)[number];

export type InternalTransferDataType = {
  fromPortfolioId: string;
  toPortfolioId: string;
  consideration: {
    currency: CurrencyEnum;
    amount: number;
  };
  clientReference?: string;
};

/**
 * DATA TYPES
 */
type AccountDataType = {
  type: PortfolioWrapperTypeEnum;
  clientReference?: string;
  name: string;
  productId: string;
  owner: string;
  currency: currenciesConfig.MainCurrencyType;
};
type BonusDataType = {
  destinationPortfolio: string;
  consideration: {
    currency: string;
    amount: number;
  };
  clientReference?: string;
};
type AddressDataType = {
  partyId: string;
  clientReference?: string;
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  region?: string;
  countryCode: countriesConfig.CountryCodesType; // Must be ISO3166 two-letter country code.
  postalCode: string;
  startDate?: Date;
  endDate?: Date;
};
type BankAccountDataType = {
  partyId: string;
  clientReference?: string;
  name: string;
  accountNumber: string;
  sortCode: string;
  currency: CurrencyEnum; // Currently only 'GBP' is supported
  countryCode: countriesConfig.CountryCodesType; // Currently only 'GB' is supported
};
type IdentifierType = {
  issuer: countriesConfig.CountryCodesType;
  type: DocumentCodesType;
  value: string;
};
type W8BenFormDataType = {
  partyId: string;
};
type W8BenFormCompletedDataType = {
  completedAt: string; // Date in ISO-8601 format
};

export enum PortfolioWrapperTypeEnum {
  GIA = "GIA",
  ISA = "ISA",
  JISA = "JISA",
  SIPP = "SIPP"
}
const PortfolioMandateArray = ["ExecutionOnlyMandate"];
type PortfolioMandateType = (typeof PortfolioMandateArray)[number];
export type PortfolioDataType = {
  accountId: string;
  clientReference?: string;
  name: string;
  currency: CurrencyEnum;
  mandate: {
    type: PortfolioMandateType;
  };
};
export type CashValuationType = {
  currency: string;
  value: MoneyType;
  amount: MoneyType;
  fxRate: number;
};

interface CreatePartyDataType {
  firstName: string;
  lastName: string;
  email: string;
  dateOfBirth: Date;
  nationalities: countriesConfig.CountryCodesType[];
  taxResidency: {
    countryCode: countriesConfig.CountryCodesType;
    value: string;
  };
  employmentInfo: {
    annualIncome: {
      amount: number;
      currency: keyof typeof MainCurrencyToWealthkernelCurrency;
    };
    employmentStatus: EmploymentStatusType;
    sourcesOfWealth: SourceOfWealthType[];
    industry?: IndustryType;
  };
}

export type HoldingValuationType = {
  isin: string;
  quantity: number;
  price: MoneyType;
  value: MoneyType;
  fxRate: number;
};
type MoneyType = {
  currency: CurrencyEnum;
  amount: number;
};
export type OrderDataType = {
  portfolioId: string;
  isin: string;
  settlementCurrency: CurrencyEnum;
  side: OrderSideType;
  consideration?: {
    currency: CurrencyEnum;
    amount: number;
  };
  clientReference?: string;
  quantity?: number;
};

export type PartyDataType = {
  clientReference?: string;
  type: "Person";
  title?: string;
  forename: string;
  middlename?: string;
  surname: string;
  previousSurname?: string;
  emailAddress: string;
  telephoneNumber?: string;
  dateOfBirth: string;
  taxResidencies: countriesConfig.CountryCodesType[];
  nationalities: countriesConfig.CountryCodesType[];
  identifiers: IdentifierType[];
} & EmploymentInfoType;

export const WithdrawalRequestArray = ["SpecifiedAmount", "Full"] as const;
export type WithdrawalRequestType = (typeof WithdrawalRequestArray)[number];

export type WithdrawalDataType = {
  type: WithdrawalRequestType;
  portfolioId: string;
  bankAccountId?: string;
  consideration?: {
    currency: CurrencyEnum;
    amount: number;
  };
  reference: string;
};
export type WithdrawalType = {
  id: string;
  type: WithdrawalRequestType;
  portfolioId: string;
  bankAccountId: string;
  consideration?: {
    currency: CurrencyEnum;
    amount: number;
  };
  paidOut?: {
    currency: CurrencyEnum;
    amount: number;
  };
  reference: string;
  status: WithdrawalStatusType;
  requestedAt: Date;
};

export type ChargeDataType = {
  clientReference?: string;
  portfolioId: string;
  consideration?: {
    currency: CurrencyEnum;
    amount: number;
  };
  narrative: string;
};

/**
 * ENTITY TYPES
 */
export const AccountStatusArray = ["Pending", "Active", "Suspended", "Closing", "Closed"] as const;
export type AccountStatusType = (typeof AccountStatusArray)[number];
export type AccountType = AccountDataType & {
  status: AccountStatusType;
  addedAt: Date;
  id: string;
};
export type AddressType = AddressDataType & {
  id: string;
};
export type BalancesType = BalanceType[];
export type BalanceType = {
  value?: MoneyType;
  quantity?: number;
  isin?: string;
};

export const BankAccountStatusArray = ["Active", "Inactive", "Pending", "Suspended"] as const;
export type BankAccountStatusType = (typeof BankAccountStatusArray)[number];
export type BankAccountType = BankAccountDataType & {
  id: string;
  status: BankAccountStatusType;
  activatedAt: Date;
  deactivatedAt: Date;
};
export const DepositStatusArray = ["Created", "Settled", "Cancelled", "Rejected", "Cancelling"] as const;
export type DepositStatusType = (typeof DepositStatusArray)[number];
export type DepositType = {
  id: string;
  portfolioId: string;
  accountId: string;
  consideration: {
    currency: string;
    amount: number;
  };
  reference: string;
  status: DepositStatusType;
  createdAt: string;
};

export const BonusStatusArray = ["Created", "Rejected", "Settled"] as const;
export type BonusStatusType = (typeof BonusStatusArray)[number];
export type BonusType = BonusDataType & {
  id: string;
  status: BonusStatusType;
};

export type FillType = {
  transactionId: string;
  price: MoneyType;
  consideration: {
    currency: CurrencyEnum;
    amount: number;
  };
  quantity: number;
  status: "Matched" | "Cancelled";
  filledAt: Date;
  settlementDate: Date;
  exchangeRate: number;
  baseExchangeRate: number;
};
export const OrderSideArray = ["Buy", "Sell"] as const;
export type OrderSideType = (typeof OrderSideArray)[number];

export const OrderStatusArray = ["Pending", "Open", "Matched", "Rejected", "Cancelling", "Cancelled"] as const;
export type OrderStatusType = (typeof OrderStatusArray)[number];
export type OrderType = OrderDataType & {
  fills: FillType[];
  reason: string;
  receivedAt: Date;
  id: string;
  status: OrderStatusType;
};

export type PartyType = PartyDataType & {
  addedAt: Date;
  id: string;
};

export const PortfolioStatusArray = ["Created", "Active", "Closing", "Closed"] as const;
export type PortfolioStatusType = (typeof PortfolioStatusArray)[number];
export type PortfolioType = PortfolioDataType & { createdAt: Date; id: string; status: PortfolioStatusType };
export const TransactionStatusArray = ["Matched", "Settled", "Cancelled"] as const;
export type TransactionStatusType = (typeof TransactionStatusArray)[number];
export const TransactionTypeArray = [
  "Adjustment",
  "Buy",
  "Sell",
  "Deposit",
  "Withdrawal",
  "Charge",
  "ConsolidationIn",
  "ConsolidationOut",
  "Dividend",
  "FxIn",
  "FxOut",
  "CashTransferIn",
  "CashTransferOut",
  "InternalCashTransferIn",
  "InternalCashTransferOut",
  "InternalTransferIn",
  "InternalTransferOut",
  "TransferIn",
  "TransferOut",
  "Redemption"
] as const;
type TransactionTypeType = (typeof TransactionTypeArray)[number];
export type TransactionType = {
  id: string;
  portfolioId: string;
  isin?: string;
  type: TransactionTypeType;
  status: TransactionStatusType;
  price?: MoneyType;
  quantity?: number;
  consideration: MoneyType;
  charges: MoneyType;
  date: string;
  timestamp: string;
  settledOn: string;
  updatedAt: string;
  bookCost?: MoneyType;
};

export type InternalTransferType = InternalTransferDataType & {
  id: string;
  status: InternalTransferStatusType;
};

export const WithdrawalStatusArray = ["Pending", "Active", "Settled", "Cancelling", "Cancelled", "Rejected"];
export type WithdrawalStatusType = (typeof WithdrawalStatusArray)[number];

export type ValuationType = {
  portfolioId: string;
  date: Date;
  value: MoneyType;
  cash: CashValuationType[];
  holdings: HoldingValuationType[];
  changedAt: Date;
};

export type ResponseType = { id: string };

export interface BrokerageServiceInterface {
  createAccount(user: UserDocument, account: AccountDocument): Promise<ResponseType>;
  retrieveAccount(accountId: string): Promise<AccountType>;
  closeAccount(accountId: string): Promise<ResponseType>;
  retrieveAccounts(partyId: string, clientReference: string): Promise<AccountType[]>;
  addAddress(user: UserDocument, address: AddressDocument): Promise<ResponseType>;
  listAddresses(partyId: string): Promise<AddressType[]>;
  listCashBalances(portfolioId: string): Promise<BalancesType>;
  addBankAccount(user: UserDocument, bankAccount: BankAccountDocument): Promise<ResponseType>;
  retrieveBankAccount(bankAccountId: string): Promise<BankAccountType>;
  retrieveBankAccounts(partyId: string, sortCode: string, accountNumber: string): Promise<BankAccountType[]>;
  createDeposit(deposit: DepositCashTransactionDocument): Promise<ResponseType>;
  retrieveDeposit(depositId: string): Promise<DepositType>;
  createOrder(
    orderData: OrderDataType,
    idempotencyKey?: string,
    options?: { realtime: boolean }
  ): Promise<ResponseType>;
  retrieveOrder(orderId: string): Promise<OrderType>;
  retrieveParties(email: string): Promise<PartyType[]>;
  createParty(createPartyData: CreatePartyDataType): Promise<ResponseType>;
  createPortfolio(account: AccountDocument, user: UserDocument): Promise<ResponseType>;
  retrievePortfolio(portfolioId: string): Promise<PortfolioType>;
  retrievePortfolios(accountId: string): Promise<PortfolioType[]>;
  closePortfolio(portfolioId: string): Promise<PortfolioType>;
  retrieveTransaction(transactionId: string): Promise<TransactionType>;
  retrieveLatestValuationByPortfolio(portfolioId: string): Promise<ValuationType>;
  retrieveBonus(bonusId: string): Promise<BonusType>;
  createBonus(bonusData: BonusDataType): Promise<ResponseType>;
  requestWithdrawal(withdrawal: WithdrawalCashTransactionDocument): Promise<ResponseType>;
  retrieveWithdrawal(withdrawalId: string): Promise<WithdrawalType>;
  createCharge(chargeData: ChargeDataType, idempotencyKey: string): Promise<ResponseType>;
  createW8BenForm(w8BenFormData: W8BenFormDataType): Promise<ResponseType>;
  completeW8BenForm(w8BenFormId: string, w8BenFormData: W8BenFormCompletedDataType): Promise<ResponseType>;
  createInternalTransfer(internalTransferData: InternalTransferDataType): Promise<ResponseType>;
  retrieveInternalTransfer(internalTransferId: string): Promise<InternalTransferType>;
}

export class WealthkernelBasedBrokerageService implements BrokerageServiceInterface {
  _wealthkernelService: WealthkernelService;

  constructor(options: { region: WealthkernelAccountRegionEnum }) {
    if (options?.region === WealthkernelAccountRegionEnum.EU) {
      this._wealthkernelService = WealthkernelService.EUInstance;
    } else {
      this._wealthkernelService = WealthkernelService.UKInstance;
    }
  }

  public async createAccount(user: UserDocument, account: AccountDocument): Promise<ResponseType> {
    const accountData = {
      type: account.wrapperType,
      name: account.name,
      productId: "prd-gia",
      owner: user.providers?.wealthkernel?.id,
      clientReference: account.id,
      currency: MainCurrencyToWealthkernelCurrency[user.currency]
    };

    return this._wealthkernelService.createAccount(accountData);
  }

  public async retrieveAccount(accountId: string): Promise<AccountType> {
    return this._wealthkernelService.retrieveAccount(accountId);
  }

  public async closeAccount(accountId: string): Promise<ResponseType> {
    return this._wealthkernelService.closeAccount(accountId);
  }

  /**
   * Retrieves accounts based on party ID. Client reference is only used in anonymous WK implementation.
   * @param partyId
   * @param clientReference
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async retrieveAccounts(partyId: string, clientReference: string): Promise<AccountType[]> {
    return this._wealthkernelService.retrieveAccounts({ owner: partyId });
  }

  public async addAddress(user: UserDocument, address: AddressDocument): Promise<ResponseType> {
    const addressData = {
      partyId: user.providers.wealthkernel.id,
      line1: address.line1,
      line2: address.line2,
      line3: address.line3,
      city: address.city,
      countryCode: address.countryCode,
      postalCode: address.postalCode
    };

    return this._wealthkernelService.addAddress(addressData);
  }

  public async listAddresses(partyId: string): Promise<AddressType[]> {
    return this._wealthkernelService.listAddresses({ partyId });
  }

  public async listCashBalances(portfolioId: string): Promise<BalancesType> {
    return this._wealthkernelService.listCashBalances(portfolioId);
  }

  public async addBankAccount(user: UserDocument, bankAccount: BankAccountDocument): Promise<ResponseType> {
    const bankAccountData = {
      partyId: user.providers.wealthkernel.id,
      name: user.fullName.substring(0, BANK_ACCOUNT_NAME_CHAR_LIMIT),
      accountNumber: bankAccount.number,
      sortCode: bankAccount.sortCode,
      currency: CurrencyEnum.GBP, // Currently only 'GBP' is supported
      countryCode: "GB" as countriesConfig.CountryCodesType // Currently only 'GB' is supported
    };

    return this._wealthkernelService.addBankAccount(bankAccountData);
  }

  public async retrieveBankAccount(bankAccountId: string): Promise<BankAccountType> {
    return this._wealthkernelService.retrieveBankAccount(bankAccountId);
  }

  public async retrieveBankAccounts(
    partyId: string,
    sortCode: string,
    accountNumber: string
  ): Promise<BankAccountType[]> {
    return this._wealthkernelService.retrieveBankAccounts({ partyId, sortCode, accountNumber });
  }

  public async createDeposit(deposit: DepositCashTransactionDocument): Promise<ResponseType> {
    const bankAccount = deposit.bankAccount as BankAccountDocument;

    if (!bankAccount.isProviderActive) {
      return;
    }

    const data = {
      portfolioId: (deposit.portfolio as PortfolioDocument).providers?.wealthkernel?.id,
      bankAccountId: bankAccount?.providers?.wealthkernel?.id,
      useDefaultAccount: false,
      consideration: {
        amount: Decimal.div(deposit.consideration.amount, 100).toNumber(),
        currency: deposit.consideration.currency
      },
      reference: deposit.bankReference
    };

    return this._wealthkernelService.createDeposit(data);
  }

  public async retrieveDeposit(depositId: string): Promise<DepositType> {
    return this._wealthkernelService.retrieveDeposit(depositId);
  }

  public async createOrder(
    orderData: OrderDataType,
    idempotencyKey?: string,
    options: { realtime: boolean } = { realtime: false }
  ): Promise<ResponseType> {
    const wealthkernelOrderData = {
      ...orderData,
      aggregate: !options.realtime
    };

    return this._wealthkernelService.createOrder(wealthkernelOrderData, idempotencyKey);
  }

  public async retrieveOrder(orderId: string): Promise<OrderType> {
    return this._wealthkernelService.retrieveOrder(orderId);
  }

  public async retrieveParties(email: string): Promise<PartyType[]> {
    return this._wealthkernelService.retrieveParties({ emailAddress: email });
  }

  public async createParty(createPartyData: CreatePartyDataType): Promise<ResponseType> {
    const partyData: PartyDataType = {
      type: "Person",
      forename: createPartyData.firstName,
      surname: createPartyData.lastName,
      emailAddress: createPartyData.email,
      dateOfBirth: WealthkernelService.formatDate(createPartyData.dateOfBirth),
      nationalities: createPartyData.nationalities.map(
        (nationality) => nationality as countriesConfig.CountryCodesType
      ),
      taxResidencies: [createPartyData.taxResidency.countryCode as countriesConfig.CountryCodesType],
      identifiers: [
        {
          type: "NINO" as DocumentCodesType,
          value: createPartyData.taxResidency.value,
          issuer: "GB" as countriesConfig.CountryCodesType
        }
      ],
      annualIncome: {
        amount: createPartyData.employmentInfo.annualIncome.amount,
        currency: MainCurrencyToWealthkernelCurrency[createPartyData.employmentInfo.annualIncome.currency]
      },
      employmentStatus: createPartyData.employmentInfo.employmentStatus,
      sourcesOfWealth: createPartyData.employmentInfo.sourcesOfWealth,
      industry: createPartyData.employmentInfo?.industry
    };

    return this._wealthkernelService.createParty(partyData);
  }

  public async createPortfolio(account: AccountDocument, user: UserDocument): Promise<ResponseType> {
    const portfolioData = {
      accountId: account.providers.wealthkernel.id,
      name: "General Investment Portfolio",
      currency: MainCurrencyToWealthkernelCurrency[user.currency],
      mandate: {
        type: "ExecutionOnlyMandate"
      }
    };

    return this._wealthkernelService.createPortfolio(portfolioData);
  }

  public async retrievePortfolio(portfolioId: string): Promise<PortfolioType> {
    return this._wealthkernelService.retrievePortfolio(portfolioId);
  }

  public async retrievePortfolios(accountId: string): Promise<PortfolioType[]> {
    return this._wealthkernelService.retrievePortfolios({ accountId });
  }

  public async closePortfolio(portfolioId: string): Promise<PortfolioType> {
    return this._wealthkernelService.closePortfolio(portfolioId);
  }

  public async retrieveTransaction(transactionId: string): Promise<TransactionType> {
    return this._wealthkernelService.retrieveTransaction(transactionId);
  }

  public async retrieveLatestValuationByPortfolio(portfolioId: string): Promise<ValuationType> {
    return this._wealthkernelService.retrieveLatestValuationByPortfolio(portfolioId);
  }

  public async retrieveBonus(bonusId: string): Promise<BonusType> {
    return this._wealthkernelService.retrieveBonus(bonusId);
  }

  public async createBonus(bonusData: BonusDataType): Promise<ResponseType> {
    return this._wealthkernelService.createBonus(bonusData);
  }

  public async requestWithdrawal(withdrawal: WithdrawalCashTransactionDocument): Promise<ResponseType> {
    // Withdrawals to bank accounts with status Pending are instantly rejected by Wealthkernel
    await DbUtil.populateIfNotAlreadyPopulated(withdrawal, TransactionPopulationFieldsEnum.BANK_ACCOUNT);
    const bankAccount = withdrawal.bankAccount as BankAccountDocument;

    if (!bankAccount.isProviderActive) {
      return;
    }

    const withdrawalData: WithdrawalDataType = {
      type: withdrawal.withdrawalRequestType,
      portfolioId: (withdrawal.portfolio as PortfolioDocument).providers.wealthkernel.id,
      reference: withdrawal.bankReference ?? withdrawal.id,
      bankAccountId: (withdrawal.bankAccount as BankAccountDocument)?.providers?.wealthkernel?.id
    };

    // We only include the consideration amount if the withdrawal is of specified amount
    const withdrawalAmount = Decimal.div(withdrawal.consideration.amount, 100).toNumber();
    if (withdrawal.withdrawalRequestType === "SpecifiedAmount") {
      withdrawalData.consideration = {
        currency: MainCurrencyToWealthkernelCurrency[withdrawal.consideration.currency],
        amount: withdrawalAmount
      };
    }

    return this._wealthkernelService.requestWithdrawal(withdrawalData);
  }

  public async retrieveWithdrawal(withdrawalId: string): Promise<WithdrawalType> {
    return this._wealthkernelService.retrieveWithdrawal(withdrawalId);
  }

  public async createCharge(chargeData: ChargeDataType, idempotencyKey: string): Promise<ResponseType> {
    return this._wealthkernelService.createCharge(chargeData, idempotencyKey);
  }

  public async createW8BenForm(w8BenFormData: W8BenFormDataType): Promise<ResponseType> {
    return this._wealthkernelService.createW8BenForm(w8BenFormData);
  }

  public async completeW8BenForm(
    w8BenFormId: string,
    w8BenFormData: W8BenFormCompletedDataType
  ): Promise<ResponseType> {
    return this._wealthkernelService.completeW8BenForm(w8BenFormId, w8BenFormData);
  }

  public async createInternalTransfer(internalTransferData: InternalTransferDataType): Promise<ResponseType> {
    return this._wealthkernelService.createInternalTransfer(internalTransferData);
  }

  public async retrieveInternalTransfer(internalTransferId: string): Promise<InternalTransferType> {
    return this._wealthkernelService.retrieveInternalTransfer(internalTransferId);
  }
}

export class DefaultWealthkernelBasedBrokerageService extends WealthkernelBasedBrokerageService {
  private static _instance: DefaultWealthkernelBasedBrokerageService;

  public static getInstance(options: {
    region: WealthkernelAccountRegionEnum;
  }): DefaultWealthkernelBasedBrokerageService {
    return this._instance || (this._instance = new this(options));
  }
}

export class AnonymousWealthkernelBasedBrokerageService extends WealthkernelBasedBrokerageService {
  private static _instance: DefaultWealthkernelBasedBrokerageService;

  public static getInstance(options: {
    region: WealthkernelAccountRegionEnum;
  }): DefaultWealthkernelBasedBrokerageService {
    return this._instance || (this._instance = new this(options));
  }

  public async addAddress(): Promise<ResponseType> {
    throw new Error("Not supported in anonymous Wealthkernel-based brokerage service!");
  }

  public async addBankAccount(): Promise<ResponseType> {
    throw new Error("Not supported in anonymous Wealthkernel-based brokerage service!");
  }

  public async createParty(): Promise<ResponseType> {
    throw new Error("Not supported in anonymous Wealthkernel-based brokerage service!");
  }

  public async listAddresses(): Promise<AddressType[]> {
    throw new Error("Not supported in anonymous Wealthkernel-based brokerage service!");
  }

  public async retrieveBankAccount(): Promise<BankAccountType> {
    throw new Error("Not supported in anonymous Wealthkernel-based brokerage service!");
  }

  public async retrieveBankAccounts(): Promise<BankAccountType[]> {
    throw new Error("Not supported in anonymous Wealthkernel-based brokerage service!");
  }

  public async createDeposit(deposit: DepositCashTransactionDocument): Promise<ResponseType> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(deposit, TransactionPopulationFieldsEnum.CREDIT_TICKET),
      DbUtil.populateIfNotAlreadyPopulated(deposit, TransactionPopulationFieldsEnum.PORTFOLIO)
    ]);
    const creditTicket = deposit.linkedCreditTicket as CreditTicketDocument;
    const portfolio = deposit.portfolio as PortfolioDocument;

    // We cannot create an expectation if the deposit has a linked credit ticket that is not credited or rejected, as
    // we don't know yet whether we'll have to send the amount to the user's account or the Wealthyhood funding account.
    if (creditTicket.status === "Pending") {
      return;
    }

    const data = {
      portfolioId: this._getWealthkernelPortfolioForDepositExpectation(creditTicket, portfolio),
      bankAccountId: DevengoAccountConfig[getEnvironment()][deposit.depositMethod].wealthkernelBankAccountId,
      useDefaultAccount: false,
      consideration: {
        amount: Decimal.div(deposit.consideration.amount, 100).toNumber(),
        currency: deposit.consideration.currency
      },
      reference: deposit.bankReference
    };

    return this._wealthkernelService.createDeposit(data);
  }

  public async retrieveParties(): Promise<PartyType[]> {
    return this._wealthkernelService.retrieveParties({ partyId: process.env.WEALTHYHOOD_EUROPE_WK_PARTY });
  }

  public async createAccount(user: UserDocument, account: AccountDocument): Promise<ResponseType> {
    const accountData = {
      type: account.wrapperType,
      name: account.name,
      productId: "prd-gia-corporate",
      owner: user.providers?.wealthkernel?.id,
      clientReference: account.id,
      currency: MainCurrencyToWealthkernelCurrency[user.currency]
    };

    return this._wealthkernelService.createAccount(accountData);
  }

  public async retrieveAccounts(partyId: string, clientReference: string): Promise<AccountType[]> {
    return this._wealthkernelService.retrieveAccounts({ owner: partyId, clientReference });
  }

  public async createW8BenForm(): Promise<ResponseType> {
    throw new Error("Not supported in anonymous Wealthkernel-based brokerage service!");
  }

  public async completeW8BenForm(): Promise<ResponseType> {
    throw new Error("Not supported in anonymous Wealthkernel-based brokerage service!");
  }

  /**
   * In contrast to the default Wealthkernel-based brokerage service, the anonymous implementation requests
   * withdrawals to the single Devengo-based Wealthyhood bank account ID.
   * @param withdrawal
   */
  public async requestWithdrawal(withdrawal: WithdrawalCashTransactionDocument): Promise<ResponseType> {
    const withdrawalData: WithdrawalDataType = {
      type: withdrawal.withdrawalRequestType,
      portfolioId: (withdrawal.portfolio as PortfolioDocument).providers.wealthkernel.id,
      reference: withdrawal.bankReference ?? withdrawal.id,
      bankAccountId: process.env.WEALTHYHOOD_EUROPE_WK_BANK_ACCOUNT_ID
    };

    // We only include the consideration amount if the withdrawal is of specified amount
    const withdrawalAmount = Decimal.div(withdrawal.consideration.amount, 100).toNumber();
    if (withdrawal.withdrawalRequestType === "SpecifiedAmount") {
      withdrawalData.consideration = {
        currency: MainCurrencyToWealthkernelCurrency[withdrawal.consideration.currency],
        amount: withdrawalAmount
      };
    }

    return this._wealthkernelService.requestWithdrawal(withdrawalData);
  }

  public async createInternalTransfer(internalTransferData: InternalTransferDataType): Promise<ResponseType> {
    return this._wealthkernelService.createInternalTransfer(internalTransferData);
  }

  public async retrieveInternalTransfer(internalTransferId: string): Promise<InternalTransferType> {
    return this._wealthkernelService.retrieveInternalTransfer(internalTransferId);
  }

  /**
   * In the anonymous implementation, we don't use the charges API. Instead, we take advantage of internal transfers
   * to move money from the user's portfolio to the Wealthyhood custody/commission fees portfolios.
   * @param chargeData
   * @param idempotencyKey
   */
  public async createCharge(chargeData: ChargeDataType, idempotencyKey: string): Promise<ResponseType> {
    const data = {
      fromPortfolioId: chargeData.portfolioId,
      toPortfolioId:
        chargeData.narrative === "custody"
          ? process.env.WEALTHKERNEL_WH_CUSTODY_FEES_PORTFOLIO_ID_EU
          : process.env.WEALTHKERNEL_WH_COMMISSION_FEES_PORTFOLIO_ID_EU,
      consideration: chargeData.consideration,
      clientReference: chargeData.clientReference
    };

    return this._wealthkernelService.createInternalTransfer(data, idempotencyKey);
  }

  private _getWealthkernelPortfolioForDepositExpectation(
    creditTicket: CreditTicketDocument,
    portfolio: PortfolioDocument
  ): string {
    if (creditTicket.status !== "Rejected" && creditTicket.status !== "Credited") {
      throw new Error(`Cannot create deposit expectation for ${creditTicket.status} credit ticket!`);
    }

    return creditTicket.status === "Credited"
      ? process.env.WEALTHKERNEL_WH_PORTFOLIO_ID_EU
      : portfolio.providers?.wealthkernel?.id;
  }
}
