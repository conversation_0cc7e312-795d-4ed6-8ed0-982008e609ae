import { CreatePaymentDataType, DevengoService } from "../external-services/devengoService";

type CreateBankTransferDepositType = {
  reference: string;
  amount: number;
  linkedTransactionId: string;
  iban: string;
  providerData: {
    accountId: string;
  };
};

type CreateBankTransferDepositResponseType = {
  id: string;
};

export interface BankTransferPaymentServiceInterface {
  createBankTransferPayment(data: CreateBankTransferDepositType): Promise<CreateBankTransferDepositResponseType>;
}

export class BaseBankTransferPaymentService implements BankTransferPaymentServiceInterface {
  private static _instance: BaseBankTransferPaymentService;

  public static get Instance(): BaseBankTransferPaymentService {
    return this._instance || (this._instance = new this());
  }

  public async createBankTransferPayment(): Promise<CreateBankTransferDepositResponseType> {
    throw new Error("Bank transfers are not supported!");
  }
}

export class DevengoBasedSingleDepositPaymentService implements BankTransferPaymentServiceInterface {
  private static _instance: DevengoBasedSingleDepositPaymentService;

  public static get Instance(): DevengoBasedSingleDepositPaymentService {
    return this._instance || (this._instance = new this());
  }

  public async createBankTransferPayment(
    data: CreateBankTransferDepositType
  ): Promise<CreateBankTransferDepositResponseType> {
    const paymentData: CreatePaymentDataType = {
      destination: {
        iban: data.iban
      },
      amount: {
        cents: data.amount,
        currency: "EUR"
      },
      account_id: data.providerData.accountId,
      recipient: "Wealthkernel Ltd",
      company_reference: data.linkedTransactionId,
      description: data.reference
    };

    return (await DevengoService.Instance.createPayment(paymentData)).payment;
  }
}
