import { captureException } from "@sentry/node";
import { fees, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { ApiResponse, PaginatedRewardsResponse } from "apiResponse";
import Decimal from "decimal.js";
import { RewardsFilter } from "filters";
import gaussian from "gaussian";
import { QueryOptions, Types } from "mongoose";
import validator from "validator";
import { MainCurrencyToWealthkernelCurrency } from "../configs/currenciesConfig";
import { ProviderEnum } from "../configs/providersConfig";
import events from "../event-handlers/events";
import logger from "../external-services/loggerService";
import OrderService from "./orderService";
import MailchimpService, { AudienceIdEnum } from "../external-services/mailchimpService";
import { TrackPropertiesType, TrackTransactionInfoType } from "../external-services/segmentAnalyticsService";
import {
  BonusStatusType,
  OrderSideType,
  WealthkernelOrderStatusWithSettledType
} from "../external-services/wealthkernelService";
import eventEmitter from "../loaders/eventEmitter";
import { RedisClientService } from "../loaders/redis";
import { InternalServerError } from "../models/ApiErrors";
import { HoldingsType, Portfolio } from "../models/Portfolio";
import { Reward, RewardDocument, RewardDTOInterface, RewardPopulationFieldsEnum } from "../models/Reward";
import { SubscriptionDocument } from "../models/Subscription";
import { KycStatusEnum, UserDocument } from "../models/User";
import ConfigUtil from "../utils/configUtil";
import DateUtil from "../utils/dateUtil";
import DbUtil from "../utils/dbUtil";
import PaginationUtil from "../utils/paginationUtil";
import InvestmentProductService from "./investmentProductService";
import NotificationService from "./notificationService";
import PortfolioService from "./portfolioService";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import UserService from "./userService";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { MINIMUM_FX_FEE, MINIMUM_COMMISSION_FEE, EXECUTION_SPREAD_RATES } = fees;
const ONE_CENT = 1;
const MIN_REWARD_AMOUNT = 5.07;
const MAX_REWARD_AMOUNT = 11.97;
const REWARD_ASSETS: investmentUniverseConfig.AssetType[] = [
  "equities_apple",
  "equities_airbnb",
  "equities_amazon",
  "equities_blackrock",
  "equities_berkshire_hathaway",
  "equities_coinbase",
  "equities_salesforce",
  "equities_dropbox",
  "equities_disney",
  "equities_alphabet",
  "equities_goldman_sachs",
  "equities_hubspot",
  "equities_coca_cola",
  "equities_mastercard",
  "equities_mcdonalds",
  "equities_meta",
  "equities_microsoft",
  "equities_netflix",
  "equities_nike",
  "equities_nvidia",
  "equities_pfizer",
  "equities_palantir_technologies",
  "equities_paypal",
  "equities_starbucks",
  "equities_snap",
  "equities_block",
  "equities_tesla",
  "equities_uber",
  "equities_visa",
  "equities_exxonmobil",
  "equities_zoom"
];

// Any rewards that have deposits under £1 are not true user rewards
export const MINIMUM_REWARD_DEPOSIT = 100;

export type AssetRecentActivityRewardItemType = {
  type: "reward";
  item: RewardDocument & {
    isCancellable: boolean;
  };
};

export default class RewardService {
  /**
   * PUBLIC METHODS
   */

  public static async createAdminReward(
    referrerEmail: string,
    referralEmail: string,
    targetUserEmail: string,
    asset: investmentUniverseConfig.AssetType,
    considerationAmount: string
  ): Promise<RewardDocument> {
    const isDoubleReward = referrerEmail && referralEmail;

    let referrer: UserDocument;
    let referral: UserDocument;

    if (isDoubleReward) {
      [referrer, referral] = await Promise.all([
        UserService.getUserByEmail(referrerEmail),
        UserService.getUserByEmail(referralEmail)
      ]);

      if (!referrer || !referral) {
        logger.error("Attempted to create rewards for non-existing user(s)", {
          module: "RewardService",
          method: "createAdminReward",
          data: {
            referrerEmail,
            referralEmail
          }
        });
        throw new InternalServerError(
          `Couldn't find referrer ${referrerEmail} or referral ${referralEmail}`,
          "Invalid Request"
        );
      }
    }

    const targetUser = await UserService.getUserByEmail(targetUserEmail);
    if (!targetUser) {
      logger.error("Attempted to create reward for non-existing user", {
        module: "rewardService",
        method: "createAdminReward",
        data: {
          targetUserEmail
        }
      });
      throw new InternalServerError(`Couldn't find target user ${targetUser}`, "Invalid Request");
    }

    const rewardData: RewardDTOInterface = {
      asset,
      isin: ASSET_CONFIG[asset as investmentUniverseConfig.AssetType].isin,
      consideration: {
        currency: targetUser.currency,
        amount: Decimal.mul(considerationAmount, 100).toNumber() // converting to cents
      },
      referrer: isDoubleReward && referrer.id,
      referral: isDoubleReward ? referral.id : targetUser.id,
      targetUser: targetUser.id,
      deposit: {
        activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
      },
      order: {
        activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
      }
    };

    return RewardService._createReward(rewardData);
  }

  /**
   * @description To create all CRM campaign rewards, the steps are the following:
   * 1. We retrieve all users that Mailchimp that are present in the "Pending Reward" segment in Mailchimp.
   * 2. For each eligible user, we create a single reward.
   */
  public static async createAllCRMCampaignRewards(): Promise<void> {
    await MailchimpService.executeForAllPendingRewardMembersPaginated(
      RewardService._createCRMCampaignRewardsForPage
    );
  }

  /**
   * @description To create all referral rewards, the steps are the following:
   * 1. We retrieve all users that have:
   *    A) Signed up in the last 10 days.
   *    B) Have been referred by another user
   *    C) Have settled investments totalling at least £100
   * 2. Create a reward with a random amount/asset and assign it to the referred user
   * 3. Create a reward with a random amount/asset and assign it to the referrer (if they are eligible)
   */
  public static async createAllReferralRewards(): Promise<void> {
    const eligibleUsers = (
      (await UserService.getUsers({
        createdAfter: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 10),
        kycStatus: KycStatusEnum.PASSED,
        referredOnly: true
      })) as ApiResponse<UserDocument>
    ).data;

    for (let i = 0; i < eligibleUsers.length; i++) {
      const user = eligibleUsers[i];
      try {
        await RewardService._processUserReferralReward(user);
      } catch (err) {
        captureException(err);
        logger.error(`Reward creation failed for ${user.email}`, {
          module: "RewardService",
          method: "createAllReferralRewards",
          userEmail: user.email,
          data: { error: err }
        });
      }
    }
  }

  /**
   * @description Creates bonus payment requests to Wealthkernel for the rewards
   * that have been created but don't have payments requested yet.
   */
  public static async createRewardDeposits(): Promise<void> {
    // fetch reward documents
    const rewardsMissingDeposit = await Reward.find({
      "consideration.amount": { $gte: MINIMUM_REWARD_DEPOSIT },
      "deposit.activeProviders": ProviderEnum.WEALTHKERNEL,
      $or: [
        { "deposit.providers.wealthkernel.id": { $exists: false } },
        { "deposit.providers.wealthkernel.id": { $eq: undefined } }
      ],
      accepted: true
    }).populate([
      {
        path: "targetUser",
        populate: {
          path: "portfolios"
        }
      }
    ]);

    for (let i = 0; i < rewardsMissingDeposit.length; i++) {
      const reward = rewardsMissingDeposit[i];
      await RewardService._createRewardDeposit(reward);
    }
  }

  /**
   * @description Creates order requests to Wealthkernel for the reward ETFs, when
   * the corresponding transaction deposit has been settled and enough cash is available
   * to the user's e-wallet.
   */
  public static async createRewardOrders(): Promise<void> {
    // fetch reward documents
    const rewardsPendingOrder: RewardDocument[] = await Reward.find({
      "deposit.providers.wealthkernel.status": { $eq: "Settled" },
      "order.activeProviders": ProviderEnum.WEALTHKERNEL,
      $or: [
        { "order.providers.wealthkernel.id": { $exists: false } },
        { "order.providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate([
      {
        path: "targetUser",
        populate: {
          path: "portfolios"
        }
      }
    ]);

    const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("commonId", false);

    const rewards = rewardsPendingOrder.filter((reward) => investmentProducts[reward.asset].buyLine.active);

    for (let i = 0; i < rewards.length; i++) {
      const reward = rewards[i];
      await RewardService._createRewardOrder(reward);

      const isFirstInvestment = await UserService.userHasSingleInvestment(reward.targetUser.id);

      if (isFirstInvestment) {
        eventEmitter.emit(events.transaction.firstInvestmentCreation.eventId, reward.targetUser);
      }

      const { consideration, fees } = reward;
      const transactionInfo: TrackTransactionInfoType = consideration?.amount
        ? {
            side: "buy",
            category: "reward",
            assetName: ASSET_CONFIG[reward.asset].simpleName,
            amount: new Decimal(consideration.amount).div(100).toNumber(),
            currency: consideration.currency,
            fxFees: fees?.fx?.amount ?? 0,
            commissionFees: fees?.commission?.amount ?? 0,
            executionSpreadFees: fees?.executionSpread?.amount ?? 0
          }
        : {};

      eventEmitter.emit(events.transaction.investmentCreation.eventId, reward.targetUser, {
        isFirst: isFirstInvestment,
        ...transactionInfo
      } as TrackPropertiesType);
    }
  }

  public static async getSettledRewards(ownerId: Types.ObjectId): Promise<RewardDocument[]> {
    return Reward.find({ targetUser: ownerId, status: "Settled" });
  }

  public static async getReward(rewardId: string): Promise<RewardDocument> {
    if (!rewardId || !validator.isMongoId(rewardId)) {
      throw new InternalServerError(`Reward id ${rewardId} is not valid`);
    }

    return Reward.findOne({ _id: rewardId }).populate("referrer referral targetUser");
  }

  public static async getRewards(
    filter: RewardsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate = { portfolio: true },
    sort = "-createdAt"
  ) {
    return pageConfig
      ? RewardService._getRewardsPaginated(filter, pageConfig, populate, sort)
      : { data: await RewardService._getRewards(filter, populate, sort) };
  }

  /**
   * @description Syncs the pending deposits transactions that are made
   * from the Wealthyhood wallet to the investors' e-wallet to have available cash
   * that will later be used to buy the reward ETF.
   */
  public static async syncPendingRewardDeposits(): Promise<void> {
    // For rewards with a wk deposit id, sync wk status
    // that were createdAt at least 15 minutes ago, to act as a fallback to webhook mechanism
    const minimumCreationTime = new Date(Date.now() - 15 * 60 * 1000);

    // fetch reward documents
    const rewardsPendingDeposits: RewardDocument[] = await Reward.find({
      "deposit.providers.wealthkernel.id": { $exists: true, $ne: null },
      "deposit.providers.wealthkernel.status": { $ne: "Settled" },
      createdAt: { $lte: minimumCreationTime }
    }).populate("targetUser");

    for (let i = 0; i < rewardsPendingDeposits.length; i++) {
      const reward = rewardsPendingDeposits[i];
      const user = reward.targetUser as UserDocument;
      try {
        const wkBonus = await ProviderService.getBrokerageService(user.companyEntity).retrieveBonus(
          reward.deposit.providers.wealthkernel.id
        );
        await RewardService.updateRewardDepositStatus(reward, wkBonus.status);
      } catch (err) {
        captureException(err);
        logger.error(`Reward deposit syncing failed for ${reward._id}`, {
          module: "RewardService",
          method: "syncPendingRewardDeposits",
          data: { rewardId: reward._id, bonus: reward.deposit?.providers?.wealthkernel?.id, error: err }
        });
      }
    }
  }

  /**
   * @description Syncs the matched ETF orders to settle the rewards and update the portfolio holdings
   * with the rewarded asset.
   */
  public static async syncPendingRewards(): Promise<void> {
    const pendingRewardsWithMatchedOrder = await Reward.find({
      status: "Pending",
      "order.providers.wealthkernel.id": { $exists: true, $ne: null },
      "order.providers.wealthkernel.status": { $eq: "Matched" }
    });

    for (let i = 0; i < pendingRewardsWithMatchedOrder.length; i++) {
      const reward = pendingRewardsWithMatchedOrder[i];
      try {
        await RewardService._syncPendingReward(reward);
      } catch (err) {
        captureException(err);
        logger.error(`Pending reward syncing failed for ${reward._id}`, {
          module: "RewardService",
          method: "syncPendingRewardOrders",
          data: { rewardId: reward._id, error: err }
        });
      }
    }
  }

  public static async updateReward(
    rewardId: string,
    rewardData: { depositId?: string; hasViewedAppModal?: boolean; accepted?: boolean }
  ): Promise<void> {
    if (!rewardId || !validator.isMongoId(rewardId)) {
      throw new InternalServerError(`Reward id ${rewardId} is not valid`);
    }

    const sanitisedData = Object.fromEntries(
      Object.entries(rewardData).filter(([, value]) => value !== undefined && value !== null)
    );

    if (!Object.keys(sanitisedData).length) {
      throw new InternalServerError("Data cannot be empty");
    }

    const { depositId } = rewardData;
    if (depositId) {
      sanitisedData["deposit.providers.wealthkernel.id"] = depositId;
    }

    await Reward.findOneAndUpdate({ _id: rewardId }, sanitisedData);
  }

  public static async getRewardByDepositId(depositId: string): Promise<RewardDocument> {
    return await Reward.findOne({
      "deposit.providers.wealthkernel.id": depositId
    });
  }

  public static async getRewardByOrderId(orderId: string): Promise<RewardDocument> {
    return await Reward.findOne({
      "order.providers.wealthkernel.id": orderId
    });
  }

  public static async getUserActivityRewards(userId: string, limit?: number) {
    const rewardQuery = {
      targetUser: userId,
      accepted: true
    };

    const rewards = await Reward.find(rewardQuery)
      .sort({ createdAt: -1 }) // Sorting in descending order by createdAt
      .limit(limit || 0) // Limiting the number of results to 5
      .exec();

    return rewards;
  }

  public static async updateRewardDepositStatus(
    reward: RewardDocument,
    newDepositStatus: BonusStatusType
  ): Promise<void> {
    if (newDepositStatus === "Settled") {
      await Reward.findOneAndUpdate({ _id: reward.id }, { "deposit.providers.wealthkernel.status": "Settled" });
    } else if (newDepositStatus === "Rejected") {
      logger.error(`Reward bonus payment was rejected for ${reward.id}`, {
        module: "RewardService",
        method: "updateRewardDepositStatus"
      });
    }
  }

  public static async updateRewardOrderStatus(
    reward: RewardDocument,
    newOrderStatus: WealthkernelOrderStatusWithSettledType
  ): Promise<void> {
    if (OrderService.isOrderWkStatusTerminal(reward.order?.providers?.wealthkernel?.status)) {
      logger.warn(
        `Cannot update WK reward order as it is already ${reward.order?.providers?.wealthkernel?.status}`,
        {
          module: "RewardService",
          method: "updateRewardOrderStatus",
          data: {
            reward
          }
        }
      );
      return;
    }

    logger.info(`Syncing reward order for reward ${reward.id}`, {
      module: "RewardService",
      method: "updateRewardOrderStatus"
    });

    const owner = await UserService.getUser(reward.targetUser.toString(), {
      addresses: false,
      portfolios: true,
      subscription: true
    });

    const wkOrderData = await ProviderService.getBrokerageService(owner.companyEntity).retrieveOrder(
      reward.order?.providers?.wealthkernel?.id
    );
    if (!wkOrderData) {
      throw new Error(`Reward order for reward ${reward.id} could not be retrieved from Wealthkernel.`);
    } else if (wkOrderData.status !== newOrderStatus) {
      throw new Error(
        `Received WK reward order webhook with status ${newOrderStatus}, but order in WK is ${wkOrderData.status}`
      );
    }

    if (newOrderStatus === "Matched") {
      const quantity = OrderService.calculateMatchedOrderQuantity(wkOrderData);
      const unitPrice = OrderService.calculateMatchedOrderUnitPrice(wkOrderData);
      const marketSettledAt = OrderService.calculateMatchedOrderMarketSettledAt(wkOrderData);
      const plan = (owner.subscription as SubscriptionDocument).plan;
      const investmentProduct = await InvestmentProductService.getInvestmentProduct(reward.asset, false);

      // We only store the exchange rate if the order is foreign currency traded.
      let exchangeRate: number;
      if (investmentProduct.tradedCurrency !== "GBP") {
        exchangeRate = await OrderService.calculateMatchedOrderExchangeRateWithSpread(
          wkOrderData,
          owner.currency,
          plan,
          investmentProduct
        );
      }

      await Reward.findOneAndUpdate(
        { _id: reward.id },
        {
          updatedAt: new Date(),
          exchangeRate,
          quantity,
          unitPrice,
          marketSettledAt,
          "order.providers.wealthkernel.status": "Matched"
        }
      );

      // We want to clear the cache to avoid displaying the change in holdings as increase in returns.
      // MWRR & value will be cached when portfolio returns are requested again through dashboard (or at cron).
      const portfolioId = owner.portfolios[0].id;

      await Promise.all([
        RedisClientService.Instance.del(`portfolios:mwrr:${portfolioId}`),
        RedisClientService.Instance.del(`portfolios:value_at_mwrr:${portfolioId}`),
        RedisClientService.Instance.del(`portfolios:up_by:${portfolioId}`),
        RedisClientService.Instance.del(`portfolios:value_at_up_by:${portfolioId}`)
      ]);
    } else if (newOrderStatus === "Rejected") {
      logger.error(`Reward order was rejected for ${reward.id}`, {
        module: "RewardService",
        method: "updateRewardOrderStatus"
      });
    }
  }

  public static async getAssetRecentActivityRewards(
    userId: string,
    assetId: investmentUniverseConfig.AssetType,
    limit?: number
  ): Promise<AssetRecentActivityRewardItemType[]> {
    const assetRewards = limit
      ? (
          (await RewardService.getRewards(
            { targetUser: userId, assetId: assetId, accepted: true },
            { page: 1, pageSize: limit }
          )) as PaginatedRewardsResponse
        ).rewards
      : (
          (await RewardService.getRewards({ targetUser: userId, assetId: assetId, accepted: true })) as {
            data: RewardDocument[];
          }
        ).data;

    return assetRewards.map(
      (reward) =>
        ({
          type: "reward",
          item: { ...reward.toObject(), isCancellable: false }
        }) as AssetRecentActivityRewardItemType
    );
  }

  /**
   * PRIVATE METHODS
   */
  private static async _createCRMCampaignRewardsForPage(users: { email: string }[]): Promise<void> {
    for (let i = 0; i < users.length; i++) {
      const user = users[i];

      try {
        const targetUser = await UserService.getUserByEmail(user.email);
        if (!targetUser) {
          logger.info(`Not creating reward for ${user.email} as they're not signed up!`, {
            module: "RewardService",
            method: "_createCRMCampaignRewardsForPage",
            userEmail: user.email
          });
          continue;
        }
        await RewardService._processUserCRMCampaignReward(targetUser);
      } catch (err) {
        captureException(err);
        logger.error(`Reward creation failed for ${user.email}`, {
          module: "RewardService",
          method: "_createCRMCampaignRewardsForPage",
          userEmail: user.email,
          data: { error: err }
        });
      }
    }
  }

  private static _createRewardsDbFilter(filter: RewardsFilter) {
    const dbFilter = {
      asset: filter.assetId,
      targetUser: filter.targetUser,
      referral: filter.referral,
      hasViewedAppModal: filter.hasViewedAppModal,
      status: filter.status,
      "order.providers.wealthkernel.status": filter.orderStatus,
      unrestrictedAt: null as any,
      updatedAt: null as any,
      createdAt: null as any,
      "order.providers.wealthkernel.submittedAt": filter.orderSubmissionDay
        ? { $gte: filter.orderSubmissionDay, $lt: DateUtil.getDateAfterNdays(filter.orderSubmissionDay, 1) }
        : null,
      accepted: filter.accepted
    };

    if (filter.creationDate) {
      dbFilter["createdAt"] = {
        $gte: filter.creationDate.startDate ?? new Date("1970-01-01T00:00:00Z"),
        $lt: filter.creationDate.endDate ?? new Date(Date.now())
      };
    }

    if (filter.restrictedOnly) {
      dbFilter["unrestrictedAt"] = { $gt: new Date() };
    }

    if (filter.updatedDate) {
      dbFilter["updatedAt"] = {
        $gte: filter.updatedDate.startDate,
        $lt: filter.updatedDate.endDate
      };
    }

    if (filter.orderStatus) {
      dbFilter["order.providers.wealthkernel.status"] = filter.orderStatus;
    }

    return dbFilter
      ? Object.fromEntries(Object.entries(dbFilter).filter(([, value]) => value !== undefined && value !== null))
      : {};
  }

  private static async _getRewards(
    filter?: RewardsFilter,
    populate?: { portfolio?: boolean },
    sort?: string
  ): Promise<RewardDocument[]> {
    const dbFilter = this._createRewardsDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    const rewards = await Reward.find(dbFilter, null, options);

    await RewardService._doPopulations(populate, rewards);

    return rewards;
  }

  private static async _getRewardsPaginated(
    filter: RewardsFilter = {},
    pageConfig: { page: number; pageSize: number },
    populate?: { portfolio?: boolean },
    sort?: string
  ): Promise<PaginatedRewardsResponse> {
    const dbFilter = this._createRewardsDbFilter(filter);
    const count = await Reward.countDocuments(dbFilter);
    const pageConfigToUse = PaginationUtil.getPaginationParametersFor(count, pageConfig);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    const rewards = await Reward.find(dbFilter, null, options)
      .skip((pageConfigToUse.page - 1) * pageConfigToUse.pageSize)
      .limit(pageConfigToUse.pageSize);

    await RewardService._doPopulations(populate, rewards);

    return { pagination: pageConfigToUse, rewards };
  }

  private static async _doPopulations(populate: { portfolio?: boolean }, rewards: RewardDocument[]) {
    if (populate?.portfolio) {
      await Promise.all(
        rewards.map((reward) =>
          reward.populate({
            path: "targetUser",
            populate: {
              path: "portfolios"
            }
          })
        )
      );
    }
  }

  private static async _createRewardDeposit(reward: RewardDocument): Promise<void> {
    logger.info(`Creating reward bonus for doc ${reward.id}`, {
      module: "RewardService",
      method: "_createRewardDeposit"
    });

    await DbUtil.populateIfNotAlreadyPopulated(reward, RewardPopulationFieldsEnum.TARGET_USER);
    const targetUser = reward.targetUser as UserDocument;

    const generalInvestmentPortfolio = await PortfolioService.getGeneralInvestmentPortfolio(targetUser);

    if (!generalInvestmentPortfolio.providers?.wealthkernel?.id) {
      logger.info(`Not creating reward bonus for doc ${reward.id} as the portfolio is not submitted to broker`, {
        module: "RewardService",
        method: "_createRewardDeposit"
      });
      return;
    }

    const bonusPayment = await ProviderService.getBrokerageService(targetUser.companyEntity).createBonus({
      destinationPortfolio: generalInvestmentPortfolio.providers?.wealthkernel?.id,
      consideration: {
        currency: MainCurrencyToWealthkernelCurrency[targetUser.currency],
        amount: Decimal.div(reward.consideration.amount, 100).add(reward.totalFeeAmount).toNumber()
      },
      clientReference: reward.id
    });

    logger.info(`Created reward bonus for doc ${reward.id} with WK id ${bonusPayment.id}`, {
      module: "RewardService",
      method: "_createRewardDeposit"
    });

    await Reward.findByIdAndUpdate(reward.id, {
      "deposit.providers.wealthkernel": {
        status: "Created",
        id: bonusPayment.id,
        submittedAt: new Date(Date.now())
      }
    });
  }

  private static async _createRewardOrder(reward: RewardDocument): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(reward, RewardPopulationFieldsEnum.TARGET_USER);
    const targetUser = reward.targetUser as UserDocument;
    const generalInvestmentPortfolio = await PortfolioService.getGeneralInvestmentPortfolio(targetUser);

    // 1. Create WK order
    const wkOrderData = {
      portfolioId: generalInvestmentPortfolio.providers?.wealthkernel?.id,
      isin: investmentUniverseConfig.ASSET_CONFIG[reward.asset].isin,
      settlementCurrency: MainCurrencyToWealthkernelCurrency[targetUser.currency],
      side: "Buy" as OrderSideType,
      consideration: {
        currency: MainCurrencyToWealthkernelCurrency[targetUser.currency],
        amount: reward.consideration.amount / 100
      }
    };

    const wealthkernelResponse = await ProviderService.getBrokerageService(targetUser.companyEntity).createOrder(
      wkOrderData,
      reward.id
    );
    const orderWealthkernelId = wealthkernelResponse.id;

    // 2. Update order in DB with wealthkernel info
    await Reward.findOneAndUpdate(
      {
        _id: reward.id
      },
      {
        "order.providers.wealthkernel": {
          status: "Pending",
          id: orderWealthkernelId,
          submittedAt: new Date(Date.now())
        }
      }
    );
  }

  private static async _syncPendingReward(reward: RewardDocument): Promise<void> {
    if (reward.orderStatus !== "Settled") {
      return;
    }

    await reward.populate([
      {
        path: "targetUser",
        populate: {
          path: "portfolios"
        }
      },
      { path: "referrer" }
    ]);
    const targetUser = reward.targetUser as UserDocument;

    const wkOrder = await ProviderService.getBrokerageService(targetUser.companyEntity).retrieveOrder(
      reward.order.providers.wealthkernel.id
    );

    const isFirst = await UserService.userHasNoSettledInvestments(targetUser);

    await Reward.findOneAndUpdate(
      { _id: reward.id },
      {
        status: "Settled"
      }
    );

    const { consideration, fees } = reward;
    const transactionInfo: TrackTransactionInfoType = consideration?.amount
      ? {
          side: "buy",
          category: "reward",
          assetName: ASSET_CONFIG[reward.asset].simpleName,
          amount: new Decimal(consideration.amount).div(100).toNumber(),
          currency: consideration.currency,
          fxFees: fees?.fx?.amount ?? 0,
          commissionFees: fees?.commission?.amount ?? 0
        }
      : {};

    eventEmitter.emit(
      events.transaction.investmentSuccess.eventId,
      targetUser,
      {
        isFirst,
        ...transactionInfo
      },
      {
        type: "reward"
      }
    );

    const assetId = reward.asset;

    // Update user portfolio holdings and cash
    const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(reward.targetUser as UserDocument);
    const { holdings } = portfolio;
    const holdingsDict = Object.fromEntries(holdings.map((holding) => [holding.assetCommonId, holding])) as Record<
      investmentUniverseConfig.AssetType,
      HoldingsType
    >;
    if (holdingsDict[assetId]?.quantity) {
      holdingsDict[assetId].quantity = Decimal.add(holdingsDict[assetId].quantity, reward.quantity).toNumber();
    } else {
      holdingsDict[assetId] = { quantity: reward.quantity, assetCommonId: assetId };
    }
    const updatedHoldings = Object.values(holdingsDict);

    const settledAmount = Decimal.mul(OrderService.calculateMatchedOrderAmount(wkOrder), 100);
    const uninvestedCash = new Decimal(reward.consideration.amount).sub(settledAmount);
    const availableCashUpBy = uninvestedCash.greaterThanOrEqualTo(ONE_CENT) ? uninvestedCash.div(100) : 0;
    const availableCash = Decimal.add(
      portfolio?.cash?.[targetUser.currency]?.available ?? 0,
      availableCashUpBy
    ).toNumber();
    await Portfolio.findOneAndUpdate(
      { _id: portfolio.id },
      { holdings: updatedHoldings, "cash.GBP.available": availableCash }
    );

    // Set the user's portfolioConversionStatus to 'completed'
    await UserService.convertUser(targetUser, "completed");

    // Send email to user about successful reward
    await NotificationService.createEmailNotification(
      targetUser.id,
      {
        notificationId: "rewardSuccess",
        properties: new Map(
          Object.entries({
            action_url: `https://wealthyhood.com/referral-dashboard/?wlthd-email=${targetUser.email}`
          })
        )
      },
      { sendImmediately: true }
    );

    eventEmitter.emit(events.referral.rewardSettled.eventId, targetUser);
  }

  private static async _processUserCRMCampaignReward(targetUser: UserDocument): Promise<void> {
    const existingUserRewards = await RewardService._getRewards({
      targetUser: targetUser.id,
      referral: targetUser.id
    });
    if (existingUserRewards.length > 0) {
      logger.info(`Target user ${targetUser.id} already has pending rewards, aborting...`, {
        module: "RewardService",
        method: "_processUserCRMCampaignReward",
        userEmail: targetUser.email
      });

      return;
    }

    const isTargetUserEligible = await UserService.isRewardEligible(targetUser, {
      targetUserEmail: targetUser.email,
      referral: targetUser.id
    });

    if (isTargetUserEligible) {
      const amount = RewardService._getRandomRewardAmount();
      const asset = RewardService._getRandomRewardAsset();

      const reward = {
        asset,
        isin: ASSET_CONFIG[asset].isin,
        consideration: {
          currency: targetUser.currency,
          amount
        },
        referralCampaign: "CRM Campaign",
        referral: targetUser.id,
        targetUser: targetUser.id,
        deposit: {
          activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
        },
        order: {
          activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
        }
      };

      await RewardService._createReward(reward);

      await NotificationService.createEmailNotification(
        targetUser.id,
        {
          notificationId: "referralRewardCreation",
          properties: new Map(
            Object.entries({
              action_url: `https://wealthyhood.com/referral-dashboard/?wlthd-email=${targetUser.email}`
            })
          )
        },
        { sendImmediately: true }
      );

      eventEmitter.emit(events.referral.referralRewardCreation.eventId, targetUser);

      await MailchimpService.updateMember(
        targetUser.email,
        {
          merge_fields: { CAMPLIVE: "Reward settled" }
        },
        AudienceIdEnum.WEALTHYHOOD,
        { silent: true }
      );
    }
  }

  private static async _processUserReferralReward(referral: UserDocument): Promise<void> {
    const existingUserRewards = await RewardService._getRewards({
      targetUser: referral.id,
      referral: referral.id
    });
    if (existingUserRewards.length > 0) {
      logger.info(`Referred user ${referral.id} already has reward, not processing their referral reward...`, {
        module: "RewardService",
        method: "_processUserReferralReward",
        userEmail: referral.email
      });

      return;
    }

    const referrer = await UserService.getUserByEmail(
      referral.referredByEmail,
      { includeDeleted: true },
      { participant: true }
    );

    const isReferredUserEligible = await UserService.isRewardEligible(referral, {
      targetUserEmail: referral.email,
      referral: referral.id,
      referrer: referrer?.id ?? referral.referredByEmail
    });

    if (isReferredUserEligible) {
      const amount = RewardService._getRandomRewardAmount();
      const asset = RewardService._getRandomRewardAsset();

      const reward: RewardDTOInterface = {
        asset,
        isin: ASSET_CONFIG[asset].isin,
        consideration: {
          currency: referral.currency,
          amount
        },
        referral: referral.id,
        targetUser: referral.id,
        deposit: {
          activeProviders: ProviderService.getProviders(referral.companyEntity, [ProviderScopeEnum.BROKERAGE])
        },
        order: {
          activeProviders: ProviderService.getProviders(referral.companyEntity, [ProviderScopeEnum.BROKERAGE])
        }
      };

      if (referrer) {
        reward.referrer = referrer.id;
      } else {
        reward.referralCampaign = referral.referredByEmail;
      }

      await RewardService._createReward(reward);

      await NotificationService.createEmailNotification(
        referral.id,
        {
          notificationId: "referralRewardCreation",
          properties: new Map(
            Object.entries({
              action_url: `https://wealthyhood.com/referral-dashboard/?wlthd-email=${referral.email}`
            })
          )
        },
        { sendImmediately: true }
      );

      eventEmitter.emit(events.referral.referralRewardCreation.eventId, referral);

      if (referrer && !referrer?.participant?.isAmbassador) {
        const isReferrerEligible = await UserService.isRewardEligible(referrer, {
          targetUserEmail: referrer.email,
          referral: referral.id,
          referrer: referrer.id
        });

        if (isReferrerEligible) {
          const amount = RewardService._getRandomRewardAmount();
          const asset = RewardService._getRandomRewardAsset();

          const reward = {
            asset,
            isin: ASSET_CONFIG[asset].isin,
            consideration: {
              currency: referrer.currency,
              amount
            },
            referrer: referrer.id,
            referral: referral.id,
            targetUser: referrer.id,
            deposit: {
              activeProviders: ProviderService.getProviders(referrer.companyEntity, [ProviderScopeEnum.BROKERAGE])
            },
            order: {
              activeProviders: ProviderService.getProviders(referrer.companyEntity, [ProviderScopeEnum.BROKERAGE])
            }
          };

          await RewardService._createReward(reward);

          await NotificationService.createEmailNotification(
            referrer.id,
            {
              notificationId: "referrerRewardCreation",
              properties: new Map(
                Object.entries({
                  action_url: `https://wealthyhood.com/referral-dashboard/?wlthd-email=${referrer.email}`
                })
              )
            },
            { sendImmediately: true }
          );

          eventEmitter.emit(events.referral.referrerRewardCreation.eventId, referrer);
        }
      }
    }
  }

  private static async _createReward(rewardData: RewardDTOInterface): Promise<RewardDocument> {
    const targetUser = await UserService.getUser(rewardData.targetUser.toString(), {
      addresses: false,
      portfolios: false,
      subscription: true
    });

    // If the target user does not have a subscription, we treat them as a free user
    const PRICE_CONFIG = ConfigUtil.getPricing(targetUser.companyEntity);
    const plan = PRICE_CONFIG[(targetUser.subscription as SubscriptionDocument)?.price ?? "free_monthly"].plan;
    const executionSpreadFee = new Decimal(rewardData.consideration.amount)
      .div(100)
      .mul(EXECUTION_SPREAD_RATES[plan])
      .toDecimalPlaces(2);

    const reward = await new Reward({
      ...rewardData,
      fees: {
        fx: { amount: MINIMUM_FX_FEE, currency: "GBP" },
        commission: { amount: MINIMUM_COMMISSION_FEE, currency: "GBP" },
        executionSpread: { amount: executionSpreadFee, currency: "GBP" }
      }
    }).save();

    logger.info(`Created reward for user ${rewardData.targetUser}`, {
      module: "RewardService",
      method: "createReward"
    });

    return reward;
  }

  /**
   * @description The amount we reward follows a normal (gaussian) distribution
   * with a mean of 12.5 and a std deviation of 1.5 (variance: 1.5 ^ 2 => 2.25).
   * @private
   */
  private static _getRandomRewardAmount(): number {
    let amount = new Decimal(0);

    while (amount.lessThan(MIN_REWARD_AMOUNT) || amount.greaterThan(MAX_REWARD_AMOUNT)) {
      amount = new Decimal(gaussian(7.5, 2.25).random(1)[0]);
    }

    return amount.mul(100).floor().toNumber(); // Convert to cents
  }

  /**
   * @description For order management reasons, we don't want to give out more
   * than one asset per day. Therefore, depending on the date we determine what
   * asset to give out by modding it with the length of assets i.e. on the 25th
   * we're going to give out asset with index 25 % 5 = 0 -> equities_global
   * @private
   */
  private static _getRandomRewardAsset(): investmentUniverseConfig.AssetType {
    return REWARD_ASSETS[new Decimal(new Date(Date.now()).getDate()).mod(REWARD_ASSETS.length).toNumber()];
  }
}
