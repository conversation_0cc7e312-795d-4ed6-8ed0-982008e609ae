import { Requisition, RequisitionDocument, RequisitionDTOInterface } from "../models/Requisition";

export default class RequisitionService {
  public static async createRequisition(requisitionData: RequisitionDTOInterface): Promise<RequisitionDocument> {
    return new Requisition(requisitionData).save();
  }

  public static async getRequisitionByReference(reference: string): Promise<RequisitionDocument> {
    return Requisition.findOne({ reference });
  }
}
