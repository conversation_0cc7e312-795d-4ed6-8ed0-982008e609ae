import logger from "../external-services/loggerService";
import {
  ContentEntry,
  ContentEntryCategoryEnum,
  ContentEntryContentTypeEnum,
  ContentEntryDocument,
  ContentEntryDTOInterface
} from "../models/ContentEntry";
import { ProviderEnum } from "../configs/providersConfig";
import ContentEntryRepository from "../repositories/contentEntryRepository";
import { NotFoundError } from "../models/ApiErrors";

export default class ContentEntryService {
  public static async createContentEntry(contentEntry: ContentEntryDTOInterface): Promise<ContentEntryDocument> {
    return new ContentEntry(contentEntry).save();
  }

  public static async getLearningGuideById(id: string): Promise<ContentEntryDocument> {
    const contentEntry = await ContentEntryRepository.getById(id);
    if (!contentEntry) {
      throw new NotFoundError(`Content entry with id ${id} not found`);
    }
    if (contentEntry.category !== ContentEntryCategoryEnum.GUIDES) {
      throw new NotFoundError(`Content entry with id ${id} is not a learning guide`);
    }
    return contentEntry;
  }

  public static async getAnalystInsightById(id: string): Promise<ContentEntryDocument> {
    const contentEntry = await ContentEntryRepository.getById(id);
    if (!contentEntry) {
      throw new NotFoundError(`Content entry with id ${id} not found`);
    }
    if (contentEntry.category !== ContentEntryCategoryEnum.ANALYST_INSIGHTS) {
      throw new NotFoundError(`Content entry with id ${id} is not an analyst insight`);
    }
    return contentEntry;
  }

  /**
   * @description Creates a learning guide content entry if it doesn't already exist.
   * @param title - The title of the learning guide.
   * @param contentfulConfig - The contentful configuration.
   * @returns The created or existing learning guide content entry.
   */
  public static async createLearningGuideContentEntry({
    title,
    contentfulConfig
  }: {
    title: string;
    contentfulConfig: { id: string; spaceId: string; environmentId: string };
  }): Promise<ContentEntryDocument> {
    const existingContentEntry = await ContentEntryRepository.getByTitle(title);
    if (existingContentEntry) {
      logger.warn("Learning guide content entry already exists", {
        module: "ContentEntryService",
        method: "createLearningGuideContentEntry",
        data: {
          title
        }
      });
      return existingContentEntry;
    }

    const contentEntry: ContentEntryDTOInterface = {
      contentType: ContentEntryContentTypeEnum.GUIDE,
      category: ContentEntryCategoryEnum.GUIDES,
      title,
      activeProviders: [ProviderEnum.CONTENTFUL],
      shouldNotifyUsers: true,
      // publishAt is set to now
      publishAt: new Date(),
      providers: {
        contentful: {
          id: contentfulConfig.id,
          spaceId: contentfulConfig.spaceId,
          environmentId: contentfulConfig.environmentId
        }
      }
    };

    return ContentEntryService.createContentEntry(contentEntry);
  }

  public static async getLatestAnalystInsightEntryByContentType(
    analystInsightType: ContentEntryContentTypeEnum
  ): Promise<ContentEntryDocument> {
    return ContentEntry.findOne({
      contentType: analystInsightType,
      category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
      "providers.contentful.id": { $exists: true }
    }).sort({ "providers.finimize.publishedAt": -1 });
  }
}
