import { QueryOptions } from "mongoose";
import { PaymentMethodsFilter } from "filters";
import DbUtil from "../utils/dbUtil";
import { PaymentMethod, PaymentMethodDocument, PaymentMethodTypeEnum } from "../models/PaymentMethod";
import { InternalServerError } from "../models/ApiErrors";
import { StripeService } from "../external-services/stripeService";
import { UserDocument } from "../models/User";
import logger from "../external-services/loggerService";

type InitiateStripeType = {
  clientSecret: string;
  setupIntentId: string;
  ephemeralKey: string;
};

export default class PaymentMethodService {
  /**
   * PUBLIC METHODS
   */
  public static async getPaymentMethods(filter: PaymentMethodsFilter = {}, sort = "-createdAt") {
    const dbFilter = this._createPaymentMethodsDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    return PaymentMethod.find(dbFilter, null, options);
  }

  public static async getPaymentMethod(id: string): Promise<PaymentMethodDocument> {
    return PaymentMethod.findById(id);
  }

  public static async getPaymentMethodByStripeId(id: string): Promise<PaymentMethodDocument> {
    return PaymentMethod.findOne({ "providers.stripe.id": id });
  }

  public static async initiateStripe(user: UserDocument): Promise<InitiateStripeType> {
    const customerId = user.providers?.stripe?.id;
    if (!customerId) {
      throw new InternalServerError("User does not have Stripe customer ID!");
    }

    const { client_secret, id } = await StripeService.Instance.createSetupIntent(customerId);
    if (!client_secret || !id) {
      logger.error("Stripe did not return client secret or setup intent ID in payment method initiation", {
        module: "PaymentMethodService",
        method: "initiateStripe",
        data: { client_secret, id, customerId }
      });
    }

    const { secret } = await StripeService.Instance.createEphemeralKey(customerId);
    if (!secret) {
      logger.error("Stripe did not return secret in payment method initiation", {
        module: "PaymentMethodService",
        method: "initiateStripe",
        data: {
          customerId
        }
      });
    }

    return { clientSecret: client_secret, setupIntentId: id, ephemeralKey: secret };
  }

  public static async completeStripe(userId: string, setupIntentId: string): Promise<PaymentMethodDocument> {
    const setupIntent = await StripeService.Instance.retrieveSetupIntent(setupIntentId);
    const stripePaymentMethod = await StripeService.Instance.retrievePaymentMethod(
      setupIntent.payment_method as string
    );

    if (stripePaymentMethod.type !== PaymentMethodTypeEnum.CARD) {
      throw new InternalServerError("We do not allow non-card payment methods!");
    }

    const existingPaymentMethods = await PaymentMethodService.getPaymentMethods({
      owner: userId
    });

    // If a user already has a payment method with the same fingerprint, then we return that to the client instead of
    // creating a duplicate document.
    const duplicatePaymentMethod = existingPaymentMethods.find(
      (paymentMethod) => paymentMethod.fingerprint === stripePaymentMethod.card.fingerprint
    );
    if (duplicatePaymentMethod) {
      return duplicatePaymentMethod;
    }

    const paymentMethod = await new PaymentMethod({
      owner: userId,
      lastFourDigits: stripePaymentMethod.card.last4,
      fingerprint: stripePaymentMethod.card.fingerprint,
      brand: stripePaymentMethod.card.brand,
      type: PaymentMethodTypeEnum.CARD,
      wallet: stripePaymentMethod.card?.wallet?.type,
      providers: {
        stripe: {
          id: stripePaymentMethod.id
        }
      }
    }).save();

    return paymentMethod;
  }

  /**
   * PRIVATE METHODS
   */
  private static _createPaymentMethodsDbFilter(filter: PaymentMethodsFilter) {
    const dbFilter = {
      owner: filter.owner
    };

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }
}
