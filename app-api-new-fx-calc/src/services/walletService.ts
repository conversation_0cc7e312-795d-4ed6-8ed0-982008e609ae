import { captureException } from "@sentry/node";
import { Wallet, WalletDocument } from "../models/Wallet";
import { UserDocument } from "../models/User";
import { ProviderEnum } from "../configs/providersConfig";
import { DevengoService } from "../external-services/devengoService";
import logger from "../external-services/loggerService";

export default class WalletService {
  public static async createDevengoEntry(user: UserDocument): Promise<{ createdNewDevengoAccount: boolean }> {
    const wallet = await Wallet.findOne({ owner: user.id });

    if (!wallet) {
      return { createdNewDevengoAccount: false };
    } else if (!wallet.activeProviders.includes(ProviderEnum.DEVENGO)) {
      return { createdNewDevengoAccount: false };
    } else if (wallet.providers?.devengo?.id) {
      return { createdNewDevengoAccount: false };
    }

    try {
      logger.info(`Attempting to create Devengo entry for wallet ${wallet.id}`, {
        module: "WalletService",
        method: "createDevengoEntry"
      });

      const { account } = await DevengoService.Instance.createAccount({
        name: user.id,
        currency: "EUR",
        metadata: {
          wealthyhoodId: wallet.id
        }
      });

      await Wallet.findByIdAndUpdate(
        wallet.id,
        {
          iban: account.identifiers.find((identifier) => identifier.type === "iban")?.iban,
          "providers.devengo": { id: account.id, status: account.status }
        },
        { new: true }
      );

      return { createdNewDevengoAccount: true };
    } catch (err) {
      captureException(err);
      logger.error(`Creating a Devengo entry failed for wallet ${wallet.id}`, {
        module: "WalletService",
        method: "createDevengoEntry",
        data: { error: err }
      });
    }
  }

  public static async syncDevengoEntry(wallet: WalletDocument): Promise<WalletDocument> {
    if (wallet.hasTerminalDevengoStatus) {
      return;
    }

    try {
      logger.info(`Attempting to sync Devengo entry for wallet ${wallet.id}`, {
        module: "WalletService",
        method: "syncDevengoEntry"
      });

      const { account } = await DevengoService.Instance.getAccount(wallet.providers.devengo.id);

      if (account.status !== wallet.providers.devengo.status) {
        return Wallet.findByIdAndUpdate(
          wallet.id,
          {
            iban: account.identifiers.find((identifier) => identifier.type === "iban")?.iban,
            "providers.devengo.status": account.status
          },
          { new: true }
        );
      }

      return wallet;
    } catch (err) {
      captureException(err);
      logger.error(`Syncing Devengo entry failed for wallet ${wallet.id}`, {
        module: "WalletService",
        method: "syncDevengoEntry",
        data: { error: err }
      });
    }
  }
}
