import { Address, AddressDocument, AddressDTOInterface } from "../models/Address";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import { UserDocument } from "../models/User";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";
import DateUtil from "../utils/dateUtil";
import PostCodeFetchifyService from "../external-services/postCodeFetchifyService";
import UserService from "./userService";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import ConfigUtil from "../utils/configUtil";
import { ProviderEnum } from "../configs/providersConfig";
import { envIsProd } from "../utils/environmentUtil";

const WEALTHKERNEL_SUBMISSION_BATCH_SIZE = 10;

export default class AddressService {
  public static async createOrUpdateAddress(ownerId: string, addressData: Partial<AddressDTOInterface>) {
    await AddressService._validateAddressData(ownerId, addressData);

    const currentUser = await UserService.getUser(ownerId, {
      addresses: true
    });

    const companyEntity = ConfigUtil.getDefaultCompanyEntity(addressData.countryCode);
    const address = await Address.findOneAndUpdate(
      { owner: ownerId },
      {
        ...addressData,
        activeProviders: ProviderService.getProviders(companyEntity, [ProviderScopeEnum.ADDRESSES])
      },
      {
        runValidators: true,
        setDefaultsOnInsert: true,
        upsert: true,
        new: true
      }
    ).populate("owner");

    /**
     * When user submits an address in a different country than his residency country,
     * we should override it.
     */
    if (currentUser.residencyCountry !== address.countryCode) {
      await UserService.updateUser(currentUser.id, {
        residencyCountry: address.countryCode,
        currency: ConfigUtil.getDefaultUserCurrency(address.countryCode),
        companyEntity,
        activeProviders: ProviderService.getProviders(companyEntity, [
          ProviderScopeEnum.BROKERAGE,
          ProviderScopeEnum.CARD_SUBSCRIPTION_PAYMENTS,
          ProviderScopeEnum.KYC
        ]),
        w8BenForm: {
          activeProviders: ProviderService.getProviders(companyEntity, [ProviderScopeEnum.W_8BEN])
        }
      });
    }

    if (!currentUser.addressSubmitted) {
      eventEmitter.emit(events.user.addressSubmission.eventId, address.owner);
    }
  }

  public static async createAllWkAddresses(): Promise<void> {
    await Address.find({
      activeProviders: ProviderEnum.WEALTHKERNEL,
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.id": { $eq: undefined } }
      ]
    })
      .populate("owner")
      .cursor()
      .addCursorFlag("noCursorTimeout", envIsProd())
      .eachAsync(
        async (addresses: AddressDocument[]) => {
          const dbOperations = addresses.map((address) => {
            const user = address.owner as UserDocument;

            if (
              user?.providers?.wealthkernel?.id &&
              user.submittedRequiredInfoAt < DateUtil.getDateOfMinutesAgo(10)
            ) {
              try {
                return AddressService.createOrSyncBrokerageAddress(user, address);
              } catch (err) {
                captureException(err);
                logger.error(`Creating a WK entry failed for address ${address.id}`, {
                  module: "AddressService",
                  method: "createAllWkAddresses",
                  data: { address: address.id, error: err }
                });
              }
            }
          });

          await Promise.all(dbOperations.filter((operation) => !!operation));
        },
        { batchSize: WEALTHKERNEL_SUBMISSION_BATCH_SIZE }
      );
  }

  /**
   * @description Creates wealthkernel address for given user if there is not one already.
   * If address already exists in Wealthkernel but our document does not have a reference
   * to it, then we sync our model to include one.
   *
   * @returns a boolean value representing whether a new address was created in WK or not.
   */
  public static async createOrSyncBrokerageAddress(
    user: UserDocument,
    address: AddressDocument
  ): Promise<boolean> {
    if (address?.providers?.wealthkernel?.id) {
      // This user already has an address created and our database is synced!
      return false;
    } else if (!address.activeProviders.includes(ProviderEnum.WEALTHKERNEL)) {
      return false;
    }

    const partyId = user?.providers?.wealthkernel?.id;
    if (!partyId) {
      logger.error(
        `Attempted to submit address to Wealthkernel for user ${user.email}, but user has no Wealthkernel party`,
        {
          module: "AddressService",
          method: "createOrSyncBrokerageAddress",
          data: { address: address.id, user: user.id }
        }
      );
      throw new Error("User does not have a party ID!");
    }

    // 1. Update wealthkernel address details if an address exists already
    const existingAddresses = await ProviderService.getBrokerageService(user.companyEntity).listAddresses(partyId);
    if (existingAddresses.length > 0) {
      // This user already has a party created - populate partyId & abort.
      logger.warn(`Wealthkernel address for user ${user.email} exists already - syncing`, {
        module: "AddressService",
        method: "createOrSyncBrokerageAddress"
      });

      const wkAddress = existingAddresses[0];
      await Address.findOneAndUpdate(
        { _id: address._id },
        {
          providers: {
            wealthkernel: { id: wkAddress.id }
          }
        },
        { runValidators: true, upsert: false }
      );
      return false;
    }

    // 2. Create wealthkernel address
    const addressWkResponse = await ProviderService.getBrokerageService(user.companyEntity).addAddress(
      user,
      address
    );

    // 3. Update address document with wealthkernel info
    await Address.findOneAndUpdate(
      { _id: address.id },
      {
        providers: {
          wealthkernel: { id: addressWkResponse.id }
        }
      }
    );

    return true;
  }

  private static async _validateAddressData(
    ownerId: string,
    addressData: Partial<AddressDTOInterface>
  ): Promise<void> {
    if (addressData.countryCode === "GB") {
      await PostCodeFetchifyService.validateUKPostCode(ownerId, addressData.postalCode.trim().toLowerCase());
    }
  }
}
