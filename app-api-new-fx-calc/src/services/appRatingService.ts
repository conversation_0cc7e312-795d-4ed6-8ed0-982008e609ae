import events from "../event-handlers/events";
import logger from "../external-services/loggerService";
import { TrackAppRatingPropertiesType } from "../external-services/segmentAnalyticsService";
import eventEmitter from "../loaders/eventEmitter";
import { AppRating, AppRatingDocument, AppRatingStatusEnum } from "../models/AppRating";
import { TopUpAutomation } from "../models/Automation";
import { AssetTransaction } from "../models/Transaction";
import { UserDocument, UserPopulationFieldsEnum } from "../models/User";
import DateUtil from "../utils/dateUtil";
import DbUtil from "../utils/dbUtil";

const BAD_REVIEW_REPROMPT_MONTHS_INTERVAL = 2;
const INCOMPLETE_REVIEW_REPROMPT_MONTHS_INTERVAL = 1;

export type AppRatingPromptType = {
  appRatingId: string;
};

export default class AppRatingService {
  public static async getAppRating(id: string): Promise<AppRatingDocument> {
    return AppRating.findById(id);
  }

  public static async getPrompt(user: UserDocument): Promise<AppRatingPromptType> {
    const [hasActiveTopUpAutomation, hasAssetTransaction] = await Promise.all([
      TopUpAutomation.exists({ owner: user.id, active: true }),
      AssetTransaction.exists({ owner: user.id, status: { $nin: ["Cancelled", "Rejected", "DepositFailed"] } }),
      DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.LATEST_APP_RATING)
    ]);

    let showAppRating = false;
    if (!user.latestAppRating) {
      showAppRating = !!hasActiveTopUpAutomation || !!hasAssetTransaction;
    } else if (
      user.latestAppRating.status === AppRatingStatusEnum.COMPLETED &&
      user.latestAppRating.starRating < 5 &&
      DateUtil.dateDiffInMonths(user.latestAppRating.createdAt, new Date(Date.now())) >=
        BAD_REVIEW_REPROMPT_MONTHS_INTERVAL
    ) {
      showAppRating = true;
    } else if (
      user.latestAppRating.status === AppRatingStatusEnum.INCOMPLETE &&
      DateUtil.dateDiffInMonths(user.latestAppRating.createdAt, new Date(Date.now())) >=
        INCOMPLETE_REVIEW_REPROMPT_MONTHS_INTERVAL
    ) {
      showAppRating = true;
    }

    if (showAppRating) {
      const appRating = await this._createAppRating(user.id);
      logger.info(`Created app rating ${appRating.id} for user ${user.id}`, {
        module: "AppRatingService",
        method: "getPrompt"
      });
      return { appRatingId: appRating.id };
    }
  }

  public static async submitAppRating(
    appRatingId: string,
    data: {
      feedback?: string;
      starRating: number;
    }
  ): Promise<void> {
    const appRating = await AppRating.findOneAndUpdate(
      {
        _id: appRatingId,
        status: AppRatingStatusEnum.INCOMPLETE
      },
      {
        status: AppRatingStatusEnum.COMPLETED,
        feedback: data.feedback,
        starRating: data.starRating
      },
      { new: true, runValidators: true }
    ).populate("owner");

    if (appRating) {
      logger.info(`User ${appRating.owner.id} submitted ${appRatingId} app rating`, {
        module: "AppRatingService",
        method: "submitAppRating",
        data
      });
      eventEmitter.emit(
        events.appRating.appRatingSubmitted.eventId,
        appRating.owner as UserDocument,
        data as TrackAppRatingPropertiesType
      );
    } else {
      logger.warn(`AppRating with ID ${appRatingId} was already completed or does not exist.`, {
        module: "AppRatingService",
        method: "submitAppRating"
      });
    }
  }

  private static async _createAppRating(owner: string): Promise<AppRatingDocument> {
    return new AppRating({ owner }).save();
  }
}
