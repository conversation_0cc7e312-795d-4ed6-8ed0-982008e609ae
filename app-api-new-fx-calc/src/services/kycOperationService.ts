import { BadRequestError, ConflictError, NotFoundError } from "../models/ApiErrors";
import {
  KycOperation,
  KycOperationDocument,
  KycOperationDTOInterface,
  SUMSUB_ANSWER_TO_KYC_STATUS
} from "../models/KycOperation";
import { UserDocument, UserPopulationFieldsEnum } from "../models/User";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import DbUtil from "../utils/dbUtil";
import UserService from "./userService";
import { PassportDetails, WORKFLOW_TIMEOUT_MINUTES } from "./kycService";
import { titleCaseString } from "../utils/stringUtil";
import events from "../event-handlers/events";
import eventEmitter from "../loaders/eventEmitter";
import { ProviderEnum } from "../configs/providersConfig";
import DateUtil from "../utils/dateUtil";
import { captureException } from "@sentry/node";
import logger from "../external-services/loggerService";
import { ReviewAnswerType, ReviewStatusType } from "../external-services/sumsubService";

export type KycInitiationCredentials = {
  sdkToken: string;
};

export default class KycOperationService {
  public static async initiateKycOperation(owner: UserDocument): Promise<KycInitiationCredentials> {
    await DbUtil.populateIfNotAlreadyPopulated(owner, UserPopulationFieldsEnum.KYC_OPERATION);
    const kycOperation = owner?.kycOperation as KycOperationDocument;

    if (kycOperation?.isJourneyCompleted) {
      throw new ConflictError(
        "A KYC session has already been processed for this user. You cannot initiate a new session."
      );
    }

    const { sdkToken } = await ProviderService.getKycService(owner.companyEntity).createKycWorkflow(owner.id);

    // We only create KYC operation if one does not exist already.
    if (!kycOperation) {
      await KycOperationService._createKycOperation({
        owner: owner.id,
        activeProviders: ProviderService.getProviders(owner.companyEntity, [ProviderScopeEnum.KYC]),
        status: "Pending",
        providers: {
          sumsub: {
            submittedAt: new Date(Date.now())
          }
        }
      });

      eventEmitter.emit(events.user.sumsubKycStarted.eventId, owner);
    } else {
      await KycOperationService._updateKycOperationSubmittedAt(kycOperation.id, new Date(Date.now()));
    }

    return { sdkToken };
  }

  public static async retrieveKycOperation(owner: UserDocument): Promise<KycOperationDocument> {
    await DbUtil.populateIfNotAlreadyPopulated(owner, UserPopulationFieldsEnum.KYC_OPERATION);

    const kycOperation = owner.kycOperation as KycOperationDocument;
    if (!kycOperation) {
      throw new NotFoundError(`Did not found KYC operation for user ${owner.id}`);
    }

    return kycOperation;
  }

  public static async syncAllSumsubKycOperations(): Promise<void> {
    const minimumSubmissionTime = DateUtil.getDateOfMinutesAgo(WORKFLOW_TIMEOUT_MINUTES);
    const kycOperations = await KycOperation.find({
      activeProviders: ProviderEnum.SUMSUB,
      "providers.sumsub.id": { $exists: true },
      "providers.sumsub.submittedAt": { $lte: minimumSubmissionTime },
      "providers.sumsub.status": { $ne: "completed" }
    }).populate("owner");

    for (let i = 0; i < kycOperations.length; i++) {
      const kycOperation = kycOperations[i];
      const user = kycOperation.owner as UserDocument;
      try {
        await KycOperationService.syncSumsubKycOperation(user);
      } catch (err) {
        captureException(err);
        logger.error(`KYC operation syncing failed for ${kycOperation._id}`, {
          module: "KycOperationService",
          method: "syncAllSumsubKycOperations",
          data: { kycOperationId: kycOperation._id, userId: user._id, error: err }
        });
      }
    }
  }

  public static async syncSumsubKycOperation(user: UserDocument): Promise<KycOperationDocument> {
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.KYC_OPERATION);
    const kycOperation = user.kycOperation as KycOperationDocument;

    if (!kycOperation) {
      throw new Error(`KYC operation not found for user ${user.id}`);
    }

    const applicant = await ProviderService.getKycService(user.companyEntity).retrieveApplicant(user.id);

    const formattedPassportDetails = KycOperationService._formatExtractedPassportDetails(
      applicant.passportDetails
    );
    await UserService.updateKycProviderDataIfMissing(user.id, applicant.id, formattedPassportDetails);

    const updatedKycOperation = await KycOperationService._updateSumsubDetails(
      kycOperation,
      applicant.reviewId,
      applicant.status,
      applicant.decision
    );

    if (
      !user.kycOperation.providers.sumsub.decision &&
      updatedKycOperation.isProcessed &&
      updatedKycOperation.providers.sumsub.decision
    ) {
      eventEmitter.emit(events.user.sumsubKycFinished.eventId, user, updatedKycOperation);
    }

    if (updatedKycOperation.status === "Passed") {
      await UserService.setPassedKycStatusIfEligible(user.id);
    } else if (updatedKycOperation.status === "Failed") {
      await UserService.setFailedKycStatus(user);
    }

    return updatedKycOperation;
  }

  public static async overrideFailedKycDecision(userId: string): Promise<void> {
    logger.info(`Verifying user ${userId} that failed Sumsub ID verification`, {
      module: "KycOperationService",
      method: "overrideFailedKycDecision"
    });

    const user = await UserService.getUser(userId, {
      kycOperation: true,
      addresses: true
    });
    if (user.kycOperation?.status !== "Failed") {
      throw new BadRequestError("User's latest kyc operation is not failed.");
    }

    await user.kycOperation.updateOne({ status: "ManuallyPassed" });
    await Promise.all([
      UserService.setPassedKycStatusIfEligible(userId),
      ProviderService.getKycService(user.companyEntity).runAMLCheck(user?.providers?.sumsub?.id)
    ]);

    await user.kycOperation.updateOne({ isManualAmlWorkflowSubmitted: true });

    logger.info(`Successfully verified user ${userId} that failed Sumsub ID verification`, {
      module: "KycOperationService",
      method: "overrideFailedKycDecision"
    });
  }

  /**
   * PRIVATE METHODS
   */

  private static async _createKycOperation(data: KycOperationDTOInterface) {
    await new KycOperation(data).save();
  }

  private static async _updateKycOperationSubmittedAt(id: string, submittedAt: Date) {
    await KycOperation.findByIdAndUpdate(id, { "providers.sumsub.submittedAt": submittedAt });
  }

  private static async _updateSumsubDetails(
    kycOperation: KycOperationDocument,
    reviewId: string,
    status: ReviewStatusType,
    decision: ReviewAnswerType
  ): Promise<KycOperationDocument> {
    let newStatus = kycOperation.status;
    if (newStatus !== "ManuallyPassed") {
      newStatus = SUMSUB_ANSWER_TO_KYC_STATUS[decision] ?? "Pending";
    }

    return KycOperation.findByIdAndUpdate(
      kycOperation._id,
      {
        "providers.sumsub.id": reviewId,
        "providers.sumsub.status": status,
        "providers.sumsub.decision": decision,
        status: newStatus
      },
      {
        runValidators: true,
        new: true
      }
    );
  }

  private static _formatExtractedPassportDetails(passportDetails: PassportDetails): PassportDetails {
    return {
      firstName: titleCaseString(passportDetails.firstName),
      lastName: titleCaseString(passportDetails.lastName),
      dateOfBirth: passportDetails.dateOfBirth,
      nationality: passportDetails.nationality
    };
  }
}
