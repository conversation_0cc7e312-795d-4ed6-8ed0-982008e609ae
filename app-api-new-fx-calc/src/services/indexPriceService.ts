import { QueryOptions } from "mongoose";
import { IndexPriceFilter } from "filters";
import DbUtil from "../utils/dbUtil";
import { IndexPrice, IndexPriceDocument } from "../models/IndexPrice";

export default class IndexPriceService {
  /**
   * PUBLIC METHODS
   */
  public static async getIndexPrices(filter: IndexPriceFilter = {}, sort?: string): Promise<IndexPriceDocument[]> {
    const dbFilter = this._createIndexPriceDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    return IndexPrice.find(dbFilter, null, options);
  }

  /**
   * PRIVATE METHODS
   */
  private static _createIndexPriceDbFilter(filter: IndexPriceFilter) {
    const dbFilter = {
      date: null as any
    };

    if (filter.date) {
      dbFilter["date"] = {
        $gte: filter.date.startDate,
        $lt: filter.date.endDate
      };
    }

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }
}
