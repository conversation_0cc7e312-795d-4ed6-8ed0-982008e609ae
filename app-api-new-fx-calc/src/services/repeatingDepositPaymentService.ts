import { countriesConfig } from "@wealthyhood/shared-configs";
import {
  GoCardlessPaymentsService,
  MandateStatusType as GoCardlessMandateStatusType,
  PaymentStatusType as GoCardlessPaymentStatusType
} from "../external-services/goCardlessPaymentsService";
import {
  DirectDebitPaymentStatusType as WealthkernelDirectDebitPaymentStatusType,
  WealthkernelMandateStatusType,
  WealthkernelService
} from "../external-services/wealthkernelService";
import Decimal from "decimal.js";
import DateUtil from "../utils/dateUtil";

type CreateDirectDebitMandateType = {
  user: {
    providers: {
      wealthkernel?: { id: string };
      gocardless?: { id: string };
    };
  };
  address?: {
    line1: string;
    city: string;
    postalCode: string;
    countryCode: countriesConfig.CountryCodesType;
  };
  bankAccount: {
    providers: {
      wealthkernel?: { id: string };
      gocardless?: { id: string };
    };
  };
  metadata: {
    wealthyhoodId: string;
  };
};

type DirectDebitMandateStatusType = WealthkernelMandateStatusType | GoCardlessMandateStatusType;

type DirectDebitMandateType = {
  bankAccountId?: string;
  partyId?: string;
  nextPossibleChargeDate?: Date;
  status: DirectDebitMandateStatusType;
};

type CreateDirectDebitPaymentType = {
  consideration: {
    currency: string;
    amount: number;
  };
  mandate: {
    providers: {
      gocardless?: { id: string };
      wealthkernel?: { id: string };
    };
  };
  portfolio?: {
    providers: {
      wealthkernel: {
        id: string;
      };
    };
  };
  collectionDate: Date;
  metadata: {
    wealthyhoodId: string;
  };
};

type DirectDebitPaymentStatusType = WealthkernelDirectDebitPaymentStatusType;

type DirectDebitPaymentType = {
  id: string;
  status: DirectDebitPaymentStatusType;
  amount: {
    currency: string;
    amount: number;
  };
};

type MandateResponsetype = {
  providerData: GoCardlessMandateDataType | WealthkernelMandateDataType;
};

type GoCardlessMandateDataType = {
  gocardless: {
    id: string;
    status: GoCardlessMandateStatusType;
  };
};

type WealthkernelMandateDataType = {
  wealthkernel: { id: string; status: DirectDebitMandateStatusType };
};

type DirectDebitPaymentResponseType = {
  providerData: GoCardlessDirectDebitPaymentType | WealthkernelDirectDebitPaymentType;
};

type GoCardlessDirectDebitPaymentType = {
  gocardless: {
    id: string;
    status: GoCardlessPaymentStatusType;
  };
};

type WealthkernelDirectDebitPaymentType = {
  wealthkernel: { id: string; status: WealthkernelDirectDebitPaymentStatusType };
};

export interface RepeatingDepositPaymentServiceInterface {
  createDirectDebitMandate(data: CreateDirectDebitMandateType): Promise<MandateResponsetype>;

  getDirectDebitMandate(mandateId: string): Promise<DirectDebitMandateType>;

  cancelDirectDebitMandate(mandateId: string): Promise<void>;

  createDirectDebitDeposit(data: CreateDirectDebitPaymentType): Promise<DirectDebitPaymentResponseType>;

  getDirectDebitDeposit(directDebitPaymentId: string): Promise<DirectDebitPaymentType>;
}

export class GoCardlessBasedRepeatingDepositPaymentService implements RepeatingDepositPaymentServiceInterface {
  private static _instance: GoCardlessBasedRepeatingDepositPaymentService;

  public static get Instance(): GoCardlessBasedRepeatingDepositPaymentService {
    return this._instance || (this._instance = new this());
  }

  public async createDirectDebitMandate(data: CreateDirectDebitMandateType): Promise<MandateResponsetype> {
    if (!data.user.providers?.gocardless?.id) {
      throw new Error("We cannot create mandate for user as they are not submitted to GoCardless!");
    } else if (!data.bankAccount.providers?.gocardless?.id) {
      throw new Error("We cannot create mandate for user's bank account as they are not submitted to GoCardless!");
    } else if (!data.address) {
      throw new Error("We cannot create mandate because address details are missing!");
    }

    const billingRequest = await GoCardlessPaymentsService.Instance.createMandateOnlyBillingRequest({
      customerId: data.user.providers.gocardless.id,
      bankAccountId: data.bankAccount.providers.gocardless.id,
      metadata: { wealthyhoodId: data.metadata.wealthyhoodId }
    });

    await GoCardlessPaymentsService.Instance.collectCustomerDetails(billingRequest.id, {
      customer_billing_detail: {
        address_line1: data.address.line1,
        city: data.address.city,
        postal_code: data.address.postalCode,
        country_code: data.address.countryCode
      }
    });

    await GoCardlessPaymentsService.Instance.confirmPayerDetails(billingRequest.id);

    const fulfiledBillingRequest = await GoCardlessPaymentsService.Instance.fulfilBillingRequest(
      billingRequest.id
    );

    const mandate = await GoCardlessPaymentsService.Instance.retrieveMandate(
      fulfiledBillingRequest.mandate_request.links.mandate
    );

    return {
      providerData: {
        gocardless: { id: mandate.id, status: mandate.status }
      }
    };
  }

  public async getDirectDebitMandate(mandateId: string): Promise<DirectDebitMandateType> {
    const mandate = await GoCardlessPaymentsService.Instance.retrieveMandate(mandateId);

    return {
      status: mandate.status,
      nextPossibleChargeDate: new Date(mandate.next_possible_charge_date)
    };
  }

  public async cancelDirectDebitMandate(mandateId: string): Promise<void> {
    await GoCardlessPaymentsService.Instance.cancelMandate(mandateId);
  }

  public async createDirectDebitDeposit(
    data: CreateDirectDebitPaymentType
  ): Promise<DirectDebitPaymentResponseType> {
    if (!data.mandate.providers?.gocardless?.id) {
      throw new Error("We cannot create direct-debit payment for mandate that is not submitted to GoCardless!");
    }

    const mandate = await this.getDirectDebitMandate(data.mandate.providers.gocardless.id);

    let collectionDate: string;
    if (data.collectionDate && DateUtil.isSameOrFutureDate(data.collectionDate, mandate.nextPossibleChargeDate)) {
      collectionDate = GoCardlessPaymentsService.formatDate(data.collectionDate);
    }

    const gocardlessPayment = await GoCardlessPaymentsService.Instance.createPayment(
      {
        currency: data.consideration.currency,
        amount: Decimal.mul(data.consideration.amount, 100).toNumber(),
        charge_date: collectionDate,
        links: {
          mandate: data.mandate.providers.gocardless.id
        },
        metadata: {
          wealthyhoodId: data.metadata.wealthyhoodId
        }
      },
      data.metadata.wealthyhoodId
    );

    return {
      providerData: {
        gocardless: {
          id: gocardlessPayment.id,
          status: gocardlessPayment.status
        }
      }
    };
  }

  public getDirectDebitDeposit(): Promise<DirectDebitPaymentType> {
    throw new Error("Repeating deposits are not supported!");
  }
}

/**
 * A service for repeating deposit payments using Wealthkernel - note this hard-codes the Wealthkernel instance to be
 * a UK instance as we don't use Wealthkernel for repeating deposits in the EU region.
 */
export class WealthkernelBasedRepeatingDepositPaymentService implements RepeatingDepositPaymentServiceInterface {
  private static _instance: WealthkernelBasedRepeatingDepositPaymentService;

  public static get Instance(): WealthkernelBasedRepeatingDepositPaymentService {
    return this._instance || (this._instance = new this());
  }

  public async createDirectDebitMandate(data: CreateDirectDebitMandateType): Promise<MandateResponsetype> {
    if (!data.user.providers?.wealthkernel?.id) {
      throw new Error("We cannot create mandate for user as they are not submitted to Wealthkernel!");
    } else if (!data.bankAccount.providers?.wealthkernel?.id) {
      throw new Error(
        "We cannot create mandate for user's bank account as they are not submitted to Wealthkernel!"
      );
    }

    const mandate = await WealthkernelService.UKInstance.createMandate(
      {
        partyId: data.user.providers.wealthkernel.id,
        bankAccountId: data.bankAccount.providers.wealthkernel.id
      },
      data.metadata.wealthyhoodId
    );

    return {
      providerData: {
        wealthkernel: {
          id: mandate.id,
          status: "Pending"
        }
      }
    };
  }

  public getDirectDebitMandate(mandateId: string): Promise<DirectDebitMandateType> {
    return WealthkernelService.UKInstance.retrieveMandate(mandateId);
  }

  public cancelDirectDebitMandate(mandateId: string): Promise<void> {
    return WealthkernelService.UKInstance.cancelMandate(mandateId);
  }

  public async createDirectDebitDeposit(
    data: CreateDirectDebitPaymentType
  ): Promise<DirectDebitPaymentResponseType> {
    if (!data.mandate.providers?.wealthkernel?.id) {
      throw new Error("We cannot create direct-debit payment for mandate that is not submitted to Wealthkernel!");
    } else if (!data.portfolio?.providers?.wealthkernel?.id) {
      throw new Error(
        "We cannot create direct-debit payment for portfolio that is not submitted to Wealthkernel!"
      );
    }

    const nextPossiblePaymentDate = await WealthkernelService.UKInstance.retrieveMandateNextPossiblePaymentDate(
      data.mandate.providers.wealthkernel.id
    );

    let collectionDate: string;
    if (
      data.collectionDate &&
      DateUtil.isFutureDate(data.collectionDate, new Date(nextPossiblePaymentDate.date))
    ) {
      collectionDate = WealthkernelService.formatDate(data.collectionDate);
    }

    const directDebitPayment = await WealthkernelService.UKInstance.createDirectDebitPayment({
      amount: {
        currency: data.consideration.currency,
        amount: data.consideration.amount
      },
      mandateId: data.mandate.providers.wealthkernel.id,
      portfolioId: data.portfolio.providers.wealthkernel.id,
      collectionDate: collectionDate
    });

    return {
      providerData: {
        wealthkernel: {
          id: directDebitPayment.id,
          status: "Pending"
        }
      }
    };
  }

  public getDirectDebitDeposit(directDebitPaymentId: string): Promise<DirectDebitPaymentType> {
    return WealthkernelService.UKInstance.retrieveDirectDebitPayment(directDebitPaymentId);
  }
}
