import { captureException } from "@sentry/node";
import { KycStatusEnum, UserDocument, UserPopulationFieldsEnum } from "../models/User";
import UserService from "./userService";
import logger from "../external-services/loggerService";
import DbUtil from "../utils/dbUtil";
import {
  AmlScreeningResultEnum,
  employmentRiskMapping,
  riskScoreMapping,
  sourceOfWealthRiskMapping
} from "../configs/riskAssessmentConfig";
import {
  RiskAssessment,
  RiskAssessmentAmlScreening,
  RiskAssessmentDTOInterface,
  RiskAssessmentEmploymentStatus,
  RiskAssessmentNationality,
  RiskAssessmentSourcesOfFunds,
  RiskAssessmentSourcesOfWealth,
  RiskAssessmentVolumeOfTransactions,
  RiskScoreClassificationEnum,
  SourceOfFundsEnum
} from "../models/RiskAssessment";
import Decimal from "decimal.js";
import DateUtil from "../utils/dateUtil";
import events from "../event-handlers/events";
import eventEmitter from "../loaders/eventEmitter";

const REQUIRED_INFO_ADDED_AT = new Date("2024-01-11");

const HIGH_RISK_SCORES = [RiskScoreClassificationEnum.HighRisk, RiskScoreClassificationEnum.Prohibited];

export default class RiskAssessmentService {
  /**
   * @description Method for calculating risk assessment for all eligible users.
   */
  public static async createRiskAssessments(): Promise<void> {
    await UserService.getUsersStreamed({ kycStatus: KycStatusEnum.PASSED }, "latestRiskAssessment").eachAsync(
      async (users) => {
        const promises = users.map(async (user) => {
          try {
            const createdAtDate = new Date(user.latestRiskAssessment?.createdAt);
            const { start, end } = DateUtil.getStartAndEndOfToday();

            if (createdAtDate >= start && createdAtDate <= end) {
              return;
            }

            if (user.email.startsWith("deleted")) {
              return;
            }

            await RiskAssessmentService.createRiskAssessment(user);
          } catch (err) {
            captureException(err);
            logger.error(`Failed while creating risk assessment for user ${user.email}`, {
              module: "RiskAssessmentService",
              method: "createRiskAssessments",
              data: { user: user.id }
            });
          }
        });

        await Promise.all(promises);
      },
      { batchSize: 20 }
    );
  }

  public static async createRiskAssessment(user: UserDocument) {
    if (user.kycStatus !== "passed") {
      throw new Error("To calculate risk assessment user must be verified");
    }
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.LATEST_RISK_ASSESSMENT);
    const latestRiskAssessment = user.latestRiskAssessment;

    const riskAssessment = await RiskAssessmentService._calculateRiskAssessment(user);

    const riskAssessmentDocument = await new RiskAssessment(riskAssessment).save();
    eventEmitter.emit(events.user.riskAssessmentCreated.eventId, user, riskAssessmentDocument);

    const isLastRiskAssessmentHighRisk = HIGH_RISK_SCORES.includes(latestRiskAssessment?.classification);
    const isCurrentRiskAssessmentHighRisk = HIGH_RISK_SCORES.includes(riskAssessmentDocument.classification);
    if (!isLastRiskAssessmentHighRisk && isCurrentRiskAssessmentHighRisk) {
      logger.info(`High risk assessment detected for user ${user.id}`, {
        module: "RiskAssessmentService",
        method: "createRiskAssessment"
      });
      eventEmitter.emit(events.user.highRiskAssessmentDetected.eventId, user);
    }
  }

  private static async _calculateRiskAssessment(user: UserDocument): Promise<RiskAssessmentDTOInterface> {
    const nationality = this._getNationalityRiskScore(user);
    const employmentStatus = this._getEmploymentStatusRiskScore(user);
    const sourcesOfWealth = RiskAssessmentService._getSourceOfWealthRiskScore(user);
    const sourcesOfFunds = RiskAssessmentService._getSourcesOfFundsRiskScore();
    const volumeOfTransactions = await RiskAssessmentService._getVolumeTransactionRiskScore(user);
    const amlScreening = RiskAssessmentService._getAMLScreeningRiskScore(user);

    const totalScore =
      nationality.score +
      employmentStatus.score +
      sourcesOfWealth.score +
      sourcesOfFunds.score +
      volumeOfTransactions.score +
      amlScreening.score;

    return {
      createdAt: new Date(),
      owner: user._id,
      nationality,
      employmentStatus,
      sourcesOfFunds,
      volumeOfTransactions,
      amlScreening,
      sourcesOfWealth,
      totalScore
    };
  }

  private static _getNationalityRiskScore(user: UserDocument): RiskAssessmentNationality {
    return {
      value: user.nationalities[0],
      score: riskScoreMapping[user.nationalities[0]]
    };
  }

  private static _getSourceOfWealthRiskScore(user: UserDocument): RiskAssessmentSourcesOfWealth {
    if (RiskAssessmentService._isUserEmploymentInfoMock(user.submittedRequiredInfoAt)) {
      return {
        value: user.employmentInfo.sourcesOfWealth,
        score: 0
      };
    }

    return {
      value: user.employmentInfo.sourcesOfWealth,
      score: user.employmentInfo.sourcesOfWealth.reduce(
        (sum, sourcesOfWealth) => sum + sourceOfWealthRiskMapping[sourcesOfWealth],
        0
      )
    };
  }

  private static _getEmploymentStatusRiskScore(user: UserDocument): RiskAssessmentEmploymentStatus {
    if (RiskAssessmentService._isUserEmploymentInfoMock(user.submittedRequiredInfoAt)) {
      return {
        value: user.employmentInfo.employmentStatus,
        score: 0
      };
    }

    return {
      value: user.employmentInfo.employmentStatus,
      score: employmentRiskMapping[user.employmentInfo.employmentStatus]
    };
  }

  private static _getSourcesOfFundsRiskScore(): RiskAssessmentSourcesOfFunds {
    // Because all of our users have a linkedUK bank account we score them 0
    return {
      value: [SourceOfFundsEnum.LinkedUKBankAccount],
      score: 0
    };
  }

  private static async _getVolumeTransactionRiskScore(
    user: UserDocument
  ): Promise<RiskAssessmentVolumeOfTransactions> {
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.LATEST_RISK_ASSESSMENT);

    let volumeOfTransactions: Decimal;

    // If the user has a previous risk assessment we add the existing volumeOfBuyTransactions after that date.
    // If not we calculate it for all the buy transactions
    if (user.latestRiskAssessment) {
      const newVolume = await UserService.getVolumeOfBuyTransactionsForUser(
        user,
        user.latestRiskAssessment.createdAt
      );
      const previousAssessmentVolumeOfTransactions = user.latestRiskAssessment.volumeOfTransactions.value;
      volumeOfTransactions = Decimal.add(previousAssessmentVolumeOfTransactions, new Decimal(newVolume));
    } else {
      volumeOfTransactions = await UserService.getVolumeOfBuyTransactionsForUser(user);
    }
    let score;

    // Direct comparison using Decimal values
    if (volumeOfTransactions.gte(0) && volumeOfTransactions.lte(1000)) {
      score = 0;
    } else if (volumeOfTransactions.gt(1000) && volumeOfTransactions.lte(5000)) {
      score = 2;
    } else if (volumeOfTransactions.gt(5000) && volumeOfTransactions.lte(10000)) {
      score = 5;
    } else if (volumeOfTransactions.gt(10000) && volumeOfTransactions.lte(20000)) {
      score = 8;
    } else if (volumeOfTransactions.gt(20000)) {
      score = 10;
    }

    return {
      value: volumeOfTransactions.toNumber(),
      score
    };
  }

  private static _getAMLScreeningRiskScore(user: UserDocument): RiskAssessmentAmlScreening {
    let score;
    switch (user.amlScreening) {
      case AmlScreeningResultEnum.NoHit:
        score = 0;
        break;
      case AmlScreeningResultEnum.ImmaterialAdverseMedia:
        score = 5;
        break;
      case AmlScreeningResultEnum.MaterialAdverseMediaPEP:
        if (user.nationalities.includes("GB")) {
          score = 10;
        } else {
          score = 15;
        }
        break;
      case AmlScreeningResultEnum.SanctionsListHit:
        score = 100;
        break;
    }

    return {
      value: user.amlScreening,
      score
    };
  }

  /**
   * @description Inform us if a user's emplyment data is mocked.
   * This happens when the user submittedRequiredInfoAt date is before we added employment data.
   */
  private static _isUserEmploymentInfoMock(userSubmittedRequiredInfoAt: Date) {
    if (userSubmittedRequiredInfoAt < REQUIRED_INFO_ADDED_AT) {
      return true;
    }
    return false;
  }
}
