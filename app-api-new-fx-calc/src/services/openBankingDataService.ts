import { banksConfig, countriesConfig } from "@wealthyhood/shared-configs";
import { GoCardlessDataService } from "../external-services/goCardlessDataService";
import { PlatformEnum } from "../configs/platformConfig";
import ConfigUtil from "../utils/configUtil";
import BanksUtil, { UNKNOWN_LOGO_URL } from "../utils/banksUtil";

export type OpenBankingDataProvider = {
  id: banksConfig.BankType;
  providerId: string;
  name: string;
  logo: string;
};

type OpenBankingLinkingInitiationType = {
  redirectUri: string;
  reference: string;
  id: string;
};

type OpenBankingRequisitionType = {
  id: string;
  accounts: string[];
};

type OpenBankingAccountType = {
  id: string;
  institution_id: string;
  iban: string;
  owner: string;
};

type OpenBankingAccountDetailsType = {
  currency: string;
};

export interface OpenBankingDataServiceInterface {
  getAvailableBanks(residencyCountry: countriesConfig.CountryCodesType): OpenBankingDataProvider[];
  getAllBanks(residencyCountry: countriesConfig.CountryCodesType): OpenBankingDataProvider[];
  getRequisition(requisitionId: string): Promise<OpenBankingRequisitionType>;
  getAccount(accountId: string): Promise<OpenBankingAccountType>;
  getAccountDetails(accountId: string): Promise<OpenBankingAccountDetailsType>;
  initiateBankLinking(
    ownerId: string,
    bankId: banksConfig.BankType,
    platform: PlatformEnum,
    redirectUriState?: string
  ): Promise<OpenBankingLinkingInitiationType>;
}

export class TruelayerBasedOpenBankingDataService implements OpenBankingDataServiceInterface {
  private static _instance: TruelayerBasedOpenBankingDataService;

  public static get Instance(): TruelayerBasedOpenBankingDataService {
    return this._instance || (this._instance = new this());
  }

  public getAvailableBanks(residencyCountry: countriesConfig.CountryCodesType): OpenBankingDataProvider[] {
    return this.getAllBanks().filter(({ id }) =>
      BanksUtil.getTruelayerAvailableProviders(residencyCountry).includes(id)
    );
  }

  public getAllBanks(): OpenBankingDataProvider[] {
    return Object.entries(banksConfig.BANKS_CONFIG).map(([id, config]) => {
      return {
        id: id as banksConfig.BankType,
        providerId: config.truelayerInstitutionId,
        name: config.name,
        logo: config.logo ?? UNKNOWN_LOGO_URL
      };
    });
  }

  public async initiateBankLinking(): Promise<OpenBankingLinkingInitiationType> {
    throw new Error("Not implemented for Truelayer!");
  }

  public async getRequisition(): Promise<OpenBankingRequisitionType> {
    throw new Error("Not implemented for Truelayer!");
  }

  public async getAccount(): Promise<OpenBankingAccountType> {
    throw new Error("Not implemented for Truelayer!");
  }

  public async getAccountDetails(): Promise<OpenBankingAccountDetailsType> {
    throw new Error("Not implemented for Truelayer!");
  }
}

export class GoCardlessBasedOpenBankingDataService implements OpenBankingDataServiceInterface {
  private static _instance: GoCardlessBasedOpenBankingDataService;

  public static get Instance(): GoCardlessBasedOpenBankingDataService {
    return this._instance || (this._instance = new this());
  }

  public getAvailableBanks(residencyCountry: countriesConfig.CountryCodesType): OpenBankingDataProvider[] {
    return this.getAllBanks().filter(({ id }) =>
      banksConfig.AVAILABLE_PROVIDERS[ConfigUtil.getRegion(residencyCountry)][
        banksConfig.BankProviderScopeEnum.DATA
      ].includes(id)
    );
  }

  public getAllBanks(): OpenBankingDataProvider[] {
    return Object.entries(banksConfig.BANKS_CONFIG).map(([id, config]) => {
      return {
        id: id as banksConfig.BankType,
        providerId: config.goCardlessDataInstitutionId,
        name: config.name,
        logo: config.logo ?? UNKNOWN_LOGO_URL
      };
    });
  }

  public async initiateBankLinking(
    ownerId: string,
    bankId: banksConfig.BankType,
    platform: PlatformEnum,
    redirectUriState?: string
  ): Promise<OpenBankingLinkingInitiationType> {
    const endUserAgreement = await GoCardlessDataService.Instance.createEndUserAgreement(
      banksConfig.BANKS_CONFIG[bankId].goCardlessDataInstitutionId
    );

    const requisition = await GoCardlessDataService.Instance.createRequisition(
      ownerId,
      platform,
      banksConfig.BANKS_CONFIG[bankId].goCardlessDataInstitutionId,
      endUserAgreement.id,
      redirectUriState
    );

    return {
      redirectUri: requisition.link,
      reference: requisition.reference,
      id: requisition.id
    };
  }

  public async getRequisition(requisitionId: string): Promise<OpenBankingRequisitionType> {
    return GoCardlessDataService.Instance.getRequisition(requisitionId);
  }

  public async getAccount(accountId: string): Promise<OpenBankingAccountType> {
    const account = await GoCardlessDataService.Instance.getAccount(accountId);

    return {
      id: account.id,
      institution_id: account.institution_id,
      iban: account.iban,
      owner: account.owner_name
    };
  }

  public async getAccountDetails(accountId: string): Promise<OpenBankingAccountDetailsType> {
    const details = await GoCardlessDataService.Instance.getAccountDetails(accountId);

    return {
      currency: details.account.currency
    };
  }
}
