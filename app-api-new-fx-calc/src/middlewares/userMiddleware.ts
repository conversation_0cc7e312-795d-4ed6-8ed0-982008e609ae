import { NextFunction, Response } from "express";
import { CustomRequest } from "custom";
import { BadRequestError } from "../models/ApiErrors";

export default class UserMiddleware {
  public static userHasPassedKyc = (req: CustomRequest, res: Response, next: NextFunction): void => {
    const user = req.user;

    if (!user.hasPassedKyc) {
      throw new BadRequestError("User has not passed kyc");
    }

    next();
  };

  public static userHasConvertedPortfolio = (req: CustomRequest, res: Response, next: NextFunction): void => {
    const user = req.user;

    if (!user.hasConvertedPortfolio) {
      throw new BadRequestError("User has not converted his portfolio yet");
    }

    next();
  };

  public static userHasSubmittedRequiredInfo = async (
    req: CustomRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const user = req.user;

    if (!user.populated("addresses")) {
      await user.populate("addresses");
    }

    if (!user.submittedRequiredInfo) {
      throw new BadRequestError("User has not submitted all required information");
    }

    next();
  };
}
