import { CustomRequest } from "../../types/custom";
import { NextFunction, Response } from "express";
import { ApiErrorResponse } from "../../models/ApiErrorResponse";
import { PlatformType, User } from "../../models/User";

// jwt middlewawre produces errors of the following type
// base on documentation
type JwtErrorType = {
  name: string;
  message: string;
  code: string;
  status: number;
};

/**
 *
 * @description Mock implementation for the AuthMiddleware class
 *
 */
export default class AuthMiddleware {
  private static readonly REQ_PROP_ACCESS_TOKEN = "accessTokenPayload";
  private static readonly REQ_PROP_IDENTITY_TOKEN = "identityTokenPayload";

  /**
   * @description Mock implementation of method, in order to ignore validation of jwt access token
   */
  public static readonly validateAccessToken = (req: CustomRequest, res: Response, next: NextFunction) => {
    next();
  };

  /**
   * @description Mock implementation of method, in order to ignore validation of jwt identity token
   */
  public static readonly validateIdentityToken = (req: CustomRequest, res: Response, next: NextFunction) => {
    next();
  };

  public static readonly findUserUsingIdToken = async (req: CustomRequest, res: Response, next: NextFunction) => {
    return next();
  };

  public static readonly setUserCreatedAt = (req: CustomRequest, res: Response, next: NextFunction) => {
    next();
  };

  public static readonly setPlatform = (req: CustomRequest, res: Response, next: NextFunction) => {
    req.platform = req.headers["platform"] as PlatformType;
    next();
  };

  /**
   * @description This middleware reads the roles of user (if any) from the default property of provider
   * and copies them in the defined property 'roles' of access token payload model.
   * @param req
   * @param res
   * @param next
   */
  public static readonly setUserRoles = (req: CustomRequest, res: Response, next: NextFunction) => {
    next();
  };

  /**
   * @description Mock implementation of method, in order to ignore scope access management check
   */
  public static readonly canAccessScope =
    (scope: string) => (req: CustomRequest, res: Response, next: NextFunction) => {
      next();
    };

  /**
   * @description This middleware handles errors occured and transforms them into json responses.
   * @param err
   * @param req
   * @param res
   * @param next
   * @returns
   */
  public static readonly handleErrors = (
    err: JwtErrorType,
    req: CustomRequest,
    res: Response,
    next: NextFunction
  ) => {
    const { status, code: message, message: description } = err;
    return res.status(err.status).json(new ApiErrorResponse({ status, message, description }));
  };

  /**
   * @description This middleware searches in database for a user with the resolved auth0 id from the access token payload (property 'sub').
   * If user exists is being injected to the request at property 'user' otherwise an error is being produced.
   * @param req
   * @param res
   * @param next
   */
  public static readonly findUserByAuth0Id = async (req: CustomRequest, res: Response, next: NextFunction) => {
    return next();
  };

  /**
   * @description This middleware searches the database for a user with the resolved id from the request header 'external-user-id'.
   * If user exists is being injected to the request at property 'user' otherwise an error is being produced.
   * @param req
   * @param res
   * @param next
   */
  public static readonly findExternalUserById = async (req: CustomRequest, res: Response, next: NextFunction) => {
    const errorNext = () => {
      return next({
        code: "invalid_token",
        status: 401,
        name: "UserError",
        message: "User not found"
      } as JwtErrorType);
    };
    try {
      const userId = req.headers["external-user-id"];
      const user = await User.findById(userId);
      if (user) {
        req.user = user;
        next();
      } else {
        errorNext();
      }
    } catch (err) {
      errorNext();
    }
  };
}
