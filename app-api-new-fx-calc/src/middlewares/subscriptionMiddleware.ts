import { CustomRequest } from "custom";
import { NextFunction, Response } from "express";
import SubscriptionService from "../services/subscriptionService";

export default class SubscriptionMiddleware {
  /**
   * Middleware to load and populate the subscription for the logged-in user
   */
  public static loadSubscription = async (
    req: CustomRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const user = req.user;

      res.locals.subscription = await SubscriptionService.getSubscription(user.id);

      next();
    } catch (error) {
      next(error);
    }
  };
}
