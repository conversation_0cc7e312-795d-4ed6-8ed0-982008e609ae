import { NextFunction } from "express";
import { CustomRequest, CustomResponse } from "custom";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { ForbiddenError, NotFoundError } from "../models/ApiErrors";
import RestUtil from "../utils/restUtil";

export default class AppRatingMiddleware {
  public static loadAppRating = async (
    req: CustomRequest,
    res: CustomResponse,
    next: NextFunction
  ): Promise<void> => {
    const appRatingId = req.params.id;

    ParamsValidationUtil.isObjectIdParamValid("id", appRatingId, {
      responseMessage: "App Rating ID is invalid"
    });

    res.locals.appRating = await RestUtil.getAppRatingFromResponse(req, res);

    next();
  };

  public static appRatingBelongsToUser = async (
    req: CustomRequest,
    res: CustomResponse,
    next: NextFunction
  ): Promise<void> => {
    const user = req.user;
    const appRating = await RestUtil.getAppRatingFromResponse(req, res);

    if (!appRating) throw new NotFoundError("App Rating does not exist");
    else if (appRating.owner._id.toString() !== user.id)
      throw new ForbiddenError("App Rating does not belong to user");

    next();
  };
}
