import { NextFunction, Response } from "express";
import { BadRequestError, ForbiddenError } from "../models/ApiErrors";
import { Portfolio, PortfolioModeEnum } from "../models/Portfolio";
import { CustomRequest, CustomResponse } from "custom";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import RestUtil from "../utils/restUtil";

export default class PortfolioMiddleware {
  public static portfolioPathParamBelongsToUser = async (
    req: CustomRequest,
    res: CustomResponse,
    next: NextFunction
  ): Promise<void> => {
    const user = req.user;
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res);

    if (user.id !== portfolio.owner.toString()) throw new ForbiddenError("Portfolio does not belong to user");

    next();
  };

  public static portfolioQueryParamBelongsToUser = async (
    req: CustomRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const user = req.user;
    const portfolioId = req.query.portfolio as string;

    ParamsValidationUtil.isObjectIdParamValid("id", portfolioId, { responseMessage: "Portfolio does not exist" });

    const portfolio = await Portfolio.findOne({ _id: portfolioId, owner: user.id });
    if (!portfolio) throw new ForbiddenError("Portfolio does not belong to user");

    res.locals.portfolio = portfolio;

    next();
  };

  public static portfolioIsReal = async (
    req: CustomRequest,
    res: CustomResponse,
    next: NextFunction
  ): Promise<void> => {
    const portfolio = await RestUtil.getPortfolioFromResponse(req, res);

    if (portfolio.mode !== PortfolioModeEnum.REAL) throw new BadRequestError("Portfolio given is not real");

    next();
  };

  public static loadPortfolio = async (
    req: CustomRequest,
    res: CustomResponse,
    next: NextFunction
  ): Promise<void> => {
    const portfolioId = req.params.id;

    ParamsValidationUtil.isObjectIdParamValid("id", portfolioId, { responseMessage: "Portfolio does not exist" });

    res.locals.portfolio = await Portfolio.findOne({ _id: portfolioId });

    next();
  };
}
