import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { NextFunction, Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { BadRequestError } from "../models/ApiErrors";
import { CustomRequest } from "custom";
import { PendingOrdersType } from "../services/portfolioService";
import * as InvestmentUniverseUtil from "../utils/investmentUniverseUtil";
import validator from "validator";

export default class ValidationMiddleware {
  public static pathUserIdHeaderMatch = (req: CustomRequest, res: Response, next: NextFunction): void => {
    const headerUserId = req.headers["external-user-id"] as string;
    const userId = req.params.id as string;

    if (headerUserId != userId) {
      throw new BadRequestError("User id in header should match id in path", "Invalid parameter");
    }

    ParamsValidationUtil.isObjectIdParamValid("id", userId);

    next();
  };

  public static hasValidPendingOrder = (req: CustomRequest, res: Response, next: NextFunction): void => {
    if (!req.body.pendingOrders) {
      throw new BadRequestError("body param 'pendingOrders' is required");
    }
    const pendingOrders: PendingOrdersType = req.body.pendingOrders;

    if (Object.entries(pendingOrders).length > 1) {
      throw new BadRequestError("Endpoint does not support multiple pending orders", "Operation failed");
    }
    const [asset, pendingOrder] = Object.entries(pendingOrders)[0];

    if (!InvestmentUniverseUtil.isAssetActive(asset as investmentUniverseConfig.AssetType)) {
      throw new BadRequestError(`Invalid asset key '${asset}'`, "Invalid parameter 'pendingOrders'");
    }

    if (!["sell", "buy"].includes(pendingOrder.side)) {
      throw new BadRequestError(
        `Invalid side value '${pendingOrder.side}' for asset '${asset}'`,
        "Invalid parameter 'pendingOrders'"
      );
    }
    if (pendingOrder.side == "sell" && !validator.isNumeric(pendingOrder.quantity?.toString())) {
      throw new BadRequestError(
        `Invalid quantity value '${pendingOrder.quantity}' for ${pendingOrder.side} order for asset '${asset}'`,
        "Invalid parameter 'pendingOrders'"
      );
    }
    if (pendingOrder.side == "buy" && !validator.isNumeric(pendingOrder.money?.toString())) {
      throw new BadRequestError(
        `Invalid money value '${pendingOrder.money}' for ${pendingOrder.side} order for asset '${asset}'`,
        "Invalid parameter 'pendingOrders'"
      );
    }

    next();
  };
}
