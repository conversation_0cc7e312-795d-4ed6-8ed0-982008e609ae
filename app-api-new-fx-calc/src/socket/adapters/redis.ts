import { Redis } from "ioredis";
import { createAdapter, RedisAdapter } from "@socket.io/redis-adapter";

export class RedisAdapterService {
  private readonly _pubClient: Redis;
  private readonly _subClient: Redis;

  private static _instance: RedisAdapterService;

  private constructor() {
    this._pubClient = new Redis(
      `rediss://default:${process.env.REDIS_TOKEN}@${process.env.REDIS_URL.replace("https://", "")}`
    );
    this._subClient = this._pubClient.duplicate();
  }

  public static get Instance(): RedisAdapterService {
    return RedisAdapterService._instance || (RedisAdapterService._instance = new RedisAdapterService());
  }

  public get adapter(): (_: any) => RedisAdapter {
    return createAdapter(this._pubClient, this._subClient);
  }
}
