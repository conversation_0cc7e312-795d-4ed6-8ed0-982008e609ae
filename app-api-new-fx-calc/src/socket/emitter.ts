import { Emitter } from "@socket.io/redis-emitter";
import { Redis } from "ioredis";
import logger from "../external-services/loggerService";
import { RoomEnum } from "./rooms";

class SocketEmitter {
  private readonly _emitter: Emitter;

  private static _instance: SocketEmitter;

  private constructor() {
    const redisClient = new Redis(
      `rediss://default:${process.env.REDIS_TOKEN}@${process.env.REDIS_URL.replace("https://", "")}`
    );

    this._emitter = new Emitter(redisClient);
  }

  public static get Instance(): SocketEmitter {
    return SocketEmitter._instance || (SocketEmitter._instance = new SocketEmitter());
  }

  public emit(data: { key: string; value?: string }, room: RoomEnum): void {
    logger.info("Emitting event", {
      module: "socket:emitter",
      data: {
        key: data.key,
        room,
        value: data.value
      }
    });

    this._emitter.to(room).emit(data.key, data.value);
  }
}

export default SocketEmitter;
