import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AutomationController from "../controllers/automationController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(AutomationController.getAutomations));

/**
 * POST REQUESTS
 */
router.post("/", ErrorMiddleware.catchAsyncErrors(AutomationController.setupAutomation));
router.post("/:id/cancel", ErrorMiddleware.catchAsyncErrors(AutomationController.cancelAutomation));

export default router;
