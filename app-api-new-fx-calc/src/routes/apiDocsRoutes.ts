import express from "express";
import swaggerUi from "swagger-ui-express";
import YAML from "yamljs";

const router = express.Router();

router.use(
  "/docs",
  swaggerUi.serveFiles(YAML.load("./api.yml"), {}),
  swaggerUi.setup(YAML.load("./api.yml"), {
    explorer: false,
    customCssUrl: `${process.env.DOMAIN_URL}/static/theme-feeling-blue.css`
  })
);

router.use(
  "/docs-admin",
  swaggerUi.serveFiles(YAML.load("./api-admin.yml"), {}),
  swaggerUi.setup(YAML.load("./api-admin.yml"), {
    explorer: false,
    customCssUrl: `${process.env.DOMAIN_URL}/static/theme-feeling-blue.css`
  })
);

export default router;
