import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AdminOrderController from "../controllers/adminOrderController";

const router = express.Router();

router.get("/", ErrorMiddleware.catchAsyncErrors(AdminOrderController.getOrders));
router.get("/analytics", ErrorMiddleware.catchAsyncErrors(AdminOrderController.getAnalytics));
router.get(
  "/analytics/unsubmitted",
  ErrorMiddleware.catchAsyncErrors(AdminOrderController.getAnalyticsForUnsubmittedOrders)
);
router.post(
  "/create-wealthkernel",
  ErrorMiddleware.catchAsyncErrors(AdminOrderController.createMissingWealthkernelOrders)
);

export default router;
