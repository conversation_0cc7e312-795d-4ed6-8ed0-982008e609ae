import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AdminRewardController from "../controllers/adminRewardController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(AdminRewardController.getRewards));
router.get("/:id", ErrorMiddleware.catchAsyncErrors(AdminRewardController.getReward));

/**
 * POST REQUESTS
 */

router.post("/create-orders", ErrorMiddleware.catchAsyncErrors(AdminRewardController.createRewardOrders));
router.post("/create-deposits", ErrorMiddleware.catchAsyncErrors(AdminRewardController.createRewardDeposits));
router.post("/sync-deposits", ErrorMiddleware.catchAsyncErrors(AdminRewardController.syncPendingRewardDeposits));
router.post("/sync-orders", ErrorMiddleware.catchAsyncErrors(AdminRewardController.syncPendingRewardOrders));
router.post("/", ErrorMiddleware.catchAsyncErrors(AdminRewardController.createReward));
router.post("/:id", ErrorMiddleware.catchAsyncErrors(AdminRewardController.updateReward));

export default router;
