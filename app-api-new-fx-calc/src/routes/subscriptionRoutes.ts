import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import SubscriptionController from "../controllers/subscriptionController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(SubscriptionController.getSubscription));

/**
 * POST REQUESTS
 */
router.post("/initiate-stripe", ErrorMiddleware.catchAsyncErrors(SubscriptionController.initiateStripe));
router.post("/complete-stripe", ErrorMiddleware.catchAsyncErrors(SubscriptionController.completeStripe));
router.post("/payment-method", ErrorMiddleware.catchAsyncErrors(SubscriptionController.updatePaymentMethod));
router.post("/", ErrorMiddleware.catchAsyncErrors(SubscriptionController.createSubscription));
router.post("/:id", ErrorMiddleware.catchAsyncErrors(SubscriptionController.updateSubscription));
router.post("/:id/renew", ErrorMiddleware.catchAsyncErrors(SubscriptionController.renewSubscription));

export default router;
