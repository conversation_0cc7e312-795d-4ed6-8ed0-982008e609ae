import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import WealthyhubController from "../controllers/wealthyhubController";
import SubscriptionMiddleware from "../middlewares/subscriptionMiddleware";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get(
  "/analyst-insights",
  SubscriptionMiddleware.loadSubscription,
  ErrorMiddleware.catchAsyncErrors(WealthyhubController.getAnalystInsights)
);
router.get(
  "/analyst-insights/:id",
  SubscriptionMiddleware.loadSubscription,
  ErrorMiddleware.catchAsyncErrors(WealthyhubController.getAnalystInsight)
);
router.get(
  "/learning-guides",
  SubscriptionMiddleware.loadSubscription,
  ErrorMiddleware.catchAsyncErrors(WealthyhubController.getLearningGuides)
);
router.get(
  "/learning-guides/slug/:slug",
  SubscriptionMiddleware.loadSubscription,
  ErrorMiddleware.catchAsyncErrors(WealthyhubController.getLearningGuideBySlug)
);
router.get(
  "/learning-guides/:id",
  SubscriptionMiddleware.loadSubscription,
  ErrorMiddleware.catchAsyncErrors(WealthyhubController.getLearningGuideById)
);
router.get("/news", ErrorMiddleware.catchAsyncErrors(WealthyhubController.getNews));
router.get("/glossary", ErrorMiddleware.catchAsyncErrors(WealthyhubController.getGlossary));
router.get("/me/help-centre", ErrorMiddleware.catchAsyncErrors(WealthyhubController.getHelpCentre));

export default router;
