import express from "express";
import addressRoutes from "./addressRoutes";
import investmentProductRoutes from "./investmentProductRoutes";
import portfolioRoutes from "./portfolioRoutes";
import transactionRoutes from "./transactionRoutes";
import dailyPortfolioTickerRoutes from "./dailyPortfolioTickerRoutes";
import rewardRoutes from "./rewardRoutes";
import statisticsRoutes from "./statisticsRoutes";
import userRoutes from "./userRoutes";
import UserController from "../controllers/userController";
import AuthMiddleware from "../middlewares/authMiddleware";
import bankAccountRoutes from "./bankAccountRoutes";
import subscriptionRoutes from "./subscriptionRoutes";
import userDataRequestRoutes from "./userDataRequestRoutes";
import giftRoutes from "./giftRoutes";
import mandateRoutes from "./mandateRoutes";
import automationRoutes from "./automationRoutes";
import rewardInvitationRoutes from "./rewardInvitationRoutes";
import referralCodeRoutes from "./referralCodeRoutes";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import wealthyhubRoutes from "./wealthyhubRoutes";
import paymentMethodRoutes from "./paymentMethodRoutes";
import kycOperationRoutes from "./kycOperationRoutes";
import orderRoutes from "./orderRoutes";
import savingsProductRoutes from "./savingsProductRoutes";
import assetNewsRoutes from "./assetNewsRoutes";
import appRatingRoutes from "./appRatingRoutes";
import notificationSettingsRoutes from "./notificationSettingsRoutes";

/**
 * Non-Authenticated C2M Routers
 */
const emptyUserC2mRoutes = express.Router();
emptyUserC2mRoutes.post(
  "/auth",
  AuthMiddleware.validateAccessToken,
  AuthMiddleware.canAccessScope("openid email"),
  AuthMiddleware.validateIdentityToken,
  AuthMiddleware.setUserCreatedAt,
  AuthMiddleware.setPlatform,
  AuthMiddleware.handleErrors,
  ErrorMiddleware.catchAsyncErrors(UserController.createOrUpdateUser)
);

/**
 * Authenticated C2M Routers
 */
const authC2mRoutes = express.Router();
authC2mRoutes.use("/portfolios", portfolioRoutes);
authC2mRoutes.use("/investment-products", investmentProductRoutes);
authC2mRoutes.use("/rewards", rewardRoutes);
authC2mRoutes.use("/reward-invitations", rewardInvitationRoutes);
authC2mRoutes.use("/gifts", giftRoutes);
authC2mRoutes.use("/payment-methods", paymentMethodRoutes);
authC2mRoutes.use("/transactions", transactionRoutes);
authC2mRoutes.use("/daily-portfolio-tickers", dailyPortfolioTickerRoutes);
authC2mRoutes.use("/addresses", addressRoutes);
authC2mRoutes.use("/subscriptions", subscriptionRoutes);
authC2mRoutes.use("/users", userRoutes);
authC2mRoutes.use("/user-data-requests", userDataRequestRoutes);
authC2mRoutes.use("/bank-accounts", bankAccountRoutes);
authC2mRoutes.use("/statistics", statisticsRoutes);
authC2mRoutes.use("/mandates", mandateRoutes);
authC2mRoutes.use("/orders", orderRoutes);
authC2mRoutes.use("/automations", automationRoutes);
authC2mRoutes.use("/referral-codes", referralCodeRoutes);
authC2mRoutes.use("/wealthyhub", wealthyhubRoutes);
authC2mRoutes.use("/kyc-operations", kycOperationRoutes);
authC2mRoutes.use("/savings-products", savingsProductRoutes);
authC2mRoutes.use("/asset-news", assetNewsRoutes);
authC2mRoutes.use("/app-ratings", appRatingRoutes);
authC2mRoutes.use("/notification-settings", notificationSettingsRoutes);

/**
 * Combined C2M Routers
 */
const c2mRouter = express.Router();
// NOTE: it's important the emptyUserC2mRoutes to be used by the router before
// the authC2mRoutes, otherwise the validation will be applied to both routes.
c2mRouter.use("/", emptyUserC2mRoutes);
c2mRouter.use(
  "/",
  AuthMiddleware.validateAccessToken,
  AuthMiddleware.canAccessScope("openid email"),
  AuthMiddleware.validateIdentityToken,
  AuthMiddleware.findUserUsingIdToken,
  AuthMiddleware.setPlatform,
  AuthMiddleware.handleErrors,
  authC2mRoutes
);

export default c2mRouter;
