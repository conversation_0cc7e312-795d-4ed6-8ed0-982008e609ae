import express from "express";
import BankAccountController from "../controllers/bankAccountController";
import ErrorMiddleware from "../middlewares/errorMiddleware";

const router = express.Router();

router.get("/banks", ErrorMiddleware.catchAsyncErrors(BankAccountController.getAvailableBanks));

router.post("/", ErrorMiddleware.catchAsyncErrors(BankAccountController.createBankAccount));
router.post("/:id/deactivate", ErrorMiddleware.catchAsyncErrors(BankAccountController.deactivateBankAccount));
router.post("/initiate-bank-linking", ErrorMiddleware.catchAsyncErrors(BankAccountController.initiateBankLinking));

export default router;
