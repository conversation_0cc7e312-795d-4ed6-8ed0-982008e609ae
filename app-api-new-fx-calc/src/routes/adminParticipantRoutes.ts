import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AdminParticipantController from "../controllers/adminParticipantController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(AdminParticipantController.getParticipants));

/**
 * POST REQUESTS
 */
router.post("/", ErrorMiddleware.catchAsyncErrors(AdminParticipantController.createParticipant));

export default router;
