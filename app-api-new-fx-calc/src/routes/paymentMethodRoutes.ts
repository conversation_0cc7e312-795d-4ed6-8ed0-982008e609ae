import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import PaymentMethodController from "../controllers/paymentMethodController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(PaymentMethodController.getPaymentMethods));

/**
 * POST REQUESTS
 */
router.post("/initiate-stripe", ErrorMiddleware.catchAsyncErrors(PaymentMethodController.initiateStripe));
router.post("/complete-stripe", ErrorMiddleware.catchAsyncErrors(PaymentMethodController.completeStripe));

export default router;
