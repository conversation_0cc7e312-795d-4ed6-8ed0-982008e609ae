import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AppRatingController from "../controllers/appRatingController";
import AppRatingMiddleware from "../middlewares/appRatingMiddleware";

const router = express.Router();

/**
 * POST
 */
router.post(
  "/:id",
  ErrorMiddleware.catchAsyncErrors(AppRatingMiddleware.loadAppRating),
  ErrorMiddleware.catchAsyncErrors(AppRatingMiddleware.appRatingBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(AppRatingController.submitAppRating)
);

export default router;
