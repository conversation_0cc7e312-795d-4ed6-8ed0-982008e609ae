import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import NotificationSettingsController from "../controllers/notificationSettingsController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/me", ErrorMiddleware.catchAsyncErrors(NotificationSettingsController.getNotificationSettings));

/**
 * POST REQUESTS
 */
router.post("/me", ErrorMiddleware.catchAsyncErrors(NotificationSettingsController.updateNotificationSettings));
router.post(
  "/me/device-settings",
  ErrorMiddleware.catchAsyncErrors(NotificationSettingsController.updateDeviceNotificationSettings)
);

export default router;
