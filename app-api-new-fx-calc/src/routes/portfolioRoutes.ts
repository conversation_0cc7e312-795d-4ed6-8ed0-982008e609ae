import express from "express";
import PortfolioController from "../controllers/portfolioController";
import PortfolioMiddleware from "../middlewares/portfolioMiddleware";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import UserMiddleware from "../middlewares/userMiddleware";
import ValidationMiddleware from "../middlewares/validationMiddleware";

const router = express.Router();

/**
 * GET REQUESTS
 */

router.get("/", ErrorMiddleware.catchAsyncErrors(PortfolioController.getPortfolios));
router.get(
  "/:id",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.getPortfolio)
);
router.get(
  "/:id/prices-by-tenor",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.getPortfolioPricesByTenor)
);
router.get(
  "/:id/with-returns-by-tenor",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.getPortfolioWithReturnsByTenor)
);
router.get(
  "/:id/available-holdings",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.getAvailableHoldings)
);
router.get(
  "/:id/is-imbalanced",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.isPortfolioImbalanced)
);
router.get(
  "/:id/pending-cash-flows",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.getPendingCashFlows)
);
router.get(
  "/:id/asset-restriction",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.getAssetRestrictionDetails)
);
router.get(
  "/:id/restricted-holdings",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.getRestrictedHoldings)
);

/**
 * POST REQUESTS
 */
router.post(
  "/:id/submit-orders",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ValidationMiddleware.hasValidPendingOrder,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.submitOrder)
);
router.post(
  "/:id/buy-asset-pending-deposit",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ValidationMiddleware.hasValidPendingOrder,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.submitOrderPendingDeposit)
);
router.post(
  "/:id/buy",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.buyPortfolio)
);
router.post(
  "/:id/invest-pending-deposit",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.buyPortfolioPendingDeposit)
);
router.post(
  "/:id/sell",
  UserMiddleware.userHasPassedKyc,
  UserMiddleware.userHasConvertedPortfolio,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.sellPortfolio)
);
router.post(
  "/:id/sell/all",
  UserMiddleware.userHasPassedKyc,
  UserMiddleware.userHasConvertedPortfolio,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.sellWholePortfolio)
);
router.post(
  "/:id/withdraw",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.withdraw)
);
router.post(
  "/:id/allocation",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.createOrUpdatePortfolioAllocation)
);
router.post(
  "/:id/personalisation-preferences",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.submitPersonalisationPreferences)
);
router.post(
  "/:id/rebalance",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.rebalancePortfolio)
);
router.post(
  "/:id/topup-savings",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.topupSavings)
);
router.post(
  "/:id/withdraw-savings",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.loadPortfolio),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioPathParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioIsReal),
  ErrorMiddleware.catchAsyncErrors(PortfolioController.withdrawSavings)
);

export default router;
