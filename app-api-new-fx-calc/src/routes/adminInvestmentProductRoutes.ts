import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import { AdminInvestmentProductController } from "../controllers/adminInvestmentProductController";

const router = express.Router();

router.post("/:id/pause", ErrorMiddleware.catchAsyncErrors(AdminInvestmentProductController.pauseOrders));
router.post("/:id/resume", ErrorMiddleware.catchAsyncErrors(AdminInvestmentProductController.resumeOrders));

export default router;
