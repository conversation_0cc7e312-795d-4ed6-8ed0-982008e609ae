import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AdminGiftController from "../controllers/adminGiftController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(AdminGiftController.getGifts));
router.get("/:id", ErrorMiddleware.catchAsyncErrors(AdminGiftController.getGift));

export default router;
