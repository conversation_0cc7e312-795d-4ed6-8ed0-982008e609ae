import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import RewardController from "../controllers/rewardController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(RewardController.getRewards));

/**
 * POST REQUESTS
 */
router.post("/:id", ErrorMiddleware.catchAsyncErrors(RewardController.updateReward));

export default router;
