import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import StatisticsController from "../controllers/statisticsController";

const router = express.Router();

router.get("/optimal-allocation", ErrorMiddleware.catchAsyncErrors(StatisticsController.getOptimalAllocation));
router.get(
  "/past-performance",
  ErrorMiddleware.catchAsyncErrors(StatisticsController.getPortfolioPastPerformance)
);
router.get(
  "/future-performance",
  ErrorMiddleware.catchAsyncErrors(StatisticsController.getPortfolioFuturePerformance)
);
router.get(
  "/future-performance-monte-carlo",
  ErrorMiddleware.catchAsyncErrors(StatisticsController.getPortfolioFuturePerformanceMonteCarlo)
);

export default router;
