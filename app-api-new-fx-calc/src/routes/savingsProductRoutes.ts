import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import SavingsProductController from "../controllers/savingsProductController";

const router = express.Router();

router.get("/me", ErrorMiddleware.catchAsyncErrors(SavingsProductController.getUserSavings));
router.get("/activity", ErrorMiddleware.catchAsyncErrors(SavingsProductController.getSavingsProductActivity));
router.get(
  "/fee-details",
  ErrorMiddleware.catchAsyncErrors(SavingsProductController.getUserSavingsProductFeeDetails)
);
router.get("/data", ErrorMiddleware.catchAsyncErrors(SavingsProductController.getSavingsProductData));

export default router;
