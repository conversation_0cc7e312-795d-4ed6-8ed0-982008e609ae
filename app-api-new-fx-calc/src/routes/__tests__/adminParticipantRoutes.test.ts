import request from "supertest";
import { faker } from "@faker-js/faker";
import app from "../../app";
import { Participant, ParticipantDocument } from "../../models/Participant";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildParticipant } from "../../tests/utils/generateModels";

describe("AdminParticipantRoutes", () => {
  beforeAll(async () => await connectDb("AdminParticipantRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /participants", () => {
    let ambassadorParticipant: ParticipantDocument;
    let basicParticipant: ParticipantDocument;
    const GRSF_ID = "db0t5f";

    beforeAll(async () => {
      ambassadorParticipant = await buildParticipant({ participantRole: "AMBASSADOR" });
      basicParticipant = await buildParticipant({ participantRole: "BASIC", grsfId: GRSF_ID });
    });
    afterAll(async () => await clearDb());

    it("should fetch all participants for request to /participants", async () => {
      const response = await request(app).get("/api/admin/m2m/participants").set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const fetchedParticipants = JSON.parse(response.text);
      expect(fetchedParticipants.length).toEqual(2);
    });

    it("should fetch only ambassadors for request to /participants?participantRole=AMBASSADOR", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/participants?participantRole=AMBASSADOR")
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const fetchedParticipants = JSON.parse(response.text);
      expect(fetchedParticipants.length).toEqual(1);
      expect(fetchedParticipants[0].id.toString()).toEqual(ambassadorParticipant.id.toString());
    });

    it("should fetch only basic participants for request to /participants?participantRole=BASIC", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/participants?participantRole=BASIC")
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const fetchedParticipants = JSON.parse(response.text);
      expect(fetchedParticipants.length).toEqual(1);
      expect(fetchedParticipants[0].id.toString()).toEqual(basicParticipant.id.toString());
    });

    it("should fetch the participant specified by email for request to /participants?email=:email", async () => {
      const response = await request(app)
        .get(`/api/admin/m2m/participants?email=${ambassadorParticipant.email}`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const fetchedParticipants = JSON.parse(response.text);
      expect(fetchedParticipants.length).toEqual(1);
      expect(fetchedParticipants[0].id.toString()).toEqual(ambassadorParticipant.id.toString());
    });

    it("should fetch the participant specified by grsf for request to /participants?grsf=:grsfId", async () => {
      const response = await request(app)
        .get(`/api/admin/m2m/participants?grsf=${GRSF_ID}`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const fetchedParticipants = JSON.parse(response.text);
      expect(fetchedParticipants.length).toEqual(1);
      expect(fetchedParticipants[0].id.toString()).toEqual(basicParticipant.id.toString());
    });

    it("should fetch no participants for invalid combination of email and grsf id", async () => {
      const response = await request(app)
        .get(`/api/admin/m2m/participants?email=${basicParticipant.email}&grsf=wrong-${GRSF_ID}`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const fetchedParticipants = JSON.parse(response.text);
      expect(fetchedParticipants.length).toEqual(0);
    });
  });

  describe("POST /participants", () => {
    let referrerParticipant: ParticipantDocument;

    beforeAll(async () => {
      referrerParticipant = await buildParticipant();
    });
    afterAll(async () => await clearDb());

    it("should create a participant with no referrer field if no referrer email or wlthd id are provided", async () => {
      const email = faker.internet.email();
      const participantData = {
        email
      };
      const response = await request(app)
        .post("/api/admin/m2m/participants")
        .send(participantData)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      // should respond with created participant
      const createdParticipantResponse = JSON.parse(response.text);
      expect(createdParticipantResponse.email).toEqual(email.toLowerCase());
      expect(createdParticipantResponse.participantRole).toEqual("BASIC");
      expect(createdParticipantResponse.referrer).toBeUndefined();

      const dbParticipant = (await Participant.findOne({ email: email.toLowerCase() })) as ParticipantDocument;
      expect(JSON.stringify(dbParticipant)).toEqual(JSON.stringify(createdParticipantResponse));
    });

    it("should create an ambassador for request with participantRole AMBASSADOR", async () => {
      const email = faker.internet.email();
      const participantData = {
        email,
        participantRole: "AMBASSADOR"
      };
      const response = await request(app)
        .post("/api/admin/m2m/participants")
        .send(participantData)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      // should respond with created participant
      const createdParticipantResponse = JSON.parse(response.text);
      expect(createdParticipantResponse.email).toEqual(email.toLowerCase());
      expect(createdParticipantResponse.participantRole).toEqual("AMBASSADOR");
      expect(createdParticipantResponse.referrer).toBeUndefined();

      const dbParticipant = (await Participant.findOne({ email: email.toLowerCase() })) as ParticipantDocument;
      expect(JSON.stringify(dbParticipant)).toEqual(JSON.stringify(createdParticipantResponse));
    });

    it("should create a participant with referrer field if referrer email is provided", async () => {
      const email = faker.internet.email();
      const participantData = {
        email,
        referrerEmail: referrerParticipant.email
      };
      const response = await request(app)
        .post("/api/admin/m2m/participants")
        .send(participantData)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      // should respond with created participant
      const createdParticipantResponse = JSON.parse(response.text);
      expect(createdParticipantResponse.email).toEqual(email.toLowerCase());
      expect(createdParticipantResponse.referrer.id.toString()).toEqual(referrerParticipant.id.toString());
      expect(createdParticipantResponse.referrer.email).toEqual(referrerParticipant.email);

      const dbParticipant = (await Participant.findOne({ email: email.toLowerCase() }).populate(
        "referrer"
      )) as ParticipantDocument;
      expect(JSON.stringify(dbParticipant)).toEqual(JSON.stringify(createdParticipantResponse));
    });

    it("should create a participant with referrer field if referrer wlthd is provided", async () => {
      const email = faker.internet.email();
      const participantData = {
        email,
        referrerId: referrerParticipant.wlthdId
      };
      const response = await request(app)
        .post("/api/admin/m2m/participants")
        .send(participantData)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      // should respond with created participant
      const createdParticipantResponse = JSON.parse(response.text);
      expect(createdParticipantResponse.email).toEqual(email.toLowerCase());
      expect(createdParticipantResponse.referrer.id.toString()).toEqual(referrerParticipant.id.toString());
      expect(createdParticipantResponse.referrer.email).toEqual(referrerParticipant.email);

      const dbParticipant = (await Participant.findOne({ email: email.toLowerCase() }).populate(
        "referrer"
      )) as ParticipantDocument;
      expect(JSON.stringify(dbParticipant)).toEqual(JSON.stringify(createdParticipantResponse));
    });

    it("should prioritize the wealthyhood referrer if both a wealthyhood referrer & a finance ads referrer exist", async () => {
      const INFLUENCER_ID = "finance-ads-influencer-id-1";
      const email = faker.internet.email();
      const participantData = {
        email,
        influencerId: INFLUENCER_ID,
        referrerId: referrerParticipant.wlthdId
      };
      const response = await request(app)
        .post("/api/admin/m2m/participants")
        .send(participantData)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      // should respond with created participant
      const createdParticipantResponse = JSON.parse(response.text);
      expect(createdParticipantResponse.email).toEqual(email.toLowerCase());
      expect(createdParticipantResponse.referrer.id.toString()).toEqual(referrerParticipant.id.toString());
      expect(createdParticipantResponse.referrer.email).toEqual(referrerParticipant.email);

      const dbParticipant = (await Participant.findOne({ email: email.toLowerCase() }).populate(
        "referrer"
      )) as ParticipantDocument;
      expect(JSON.stringify(dbParticipant)).toEqual(JSON.stringify(createdParticipantResponse));
    });

    describe("when influencer id field is provided", () => {
      let response: request.Response;
      const INFLUENCER_ID = "finance-ads-influencer-id-1";
      const email = faker.internet.email();

      beforeAll(async () => {
        const participantData = {
          email,
          influencerId: INFLUENCER_ID,
          referrerId: referrerParticipant.wlthdId
        };
        response = await request(app)
          .post("/api/admin/m2m/participants")
          .send(participantData)
          .set("Accept", "application/json");
        expect(response.status).toEqual(200);
      });

      it("should create a participant with financeAds influencerId", async () => {
        // should respond with created participant
        const createdParticipantResponse = JSON.parse(response.text);
        expect(createdParticipantResponse.email).toEqual(email.toLowerCase());
        expect(createdParticipantResponse.referrer.id.toString()).toEqual(referrerParticipant.id.toString());
        expect(createdParticipantResponse.referrer.email).toEqual(referrerParticipant.email);
        expect(createdParticipantResponse.metadata).toMatchObject({ financeAds: { influencerId: INFLUENCER_ID } });

        const dbParticipant = (await Participant.findOne({ email: email.toLowerCase() }).populate(
          "referrer"
        )) as ParticipantDocument;
        expect(JSON.stringify(dbParticipant)).toEqual(JSON.stringify(createdParticipantResponse));
      });

      describe("when a second request is made to create the same participant", () => {
        beforeAll(async () => {
          await request(app).post("/api/admin/m2m/participants").send({ email }).set("Accept", "application/json");
        });

        it("the participant data should not be overwritten", async () => {
          const dbParticipant = (await Participant.findOne({ email: email.toLowerCase() })) as ParticipantDocument;
          expect(dbParticipant.metadata).toMatchObject({ financeAds: { influencerId: INFLUENCER_ID } });
        });
      });
    });
  });
});
