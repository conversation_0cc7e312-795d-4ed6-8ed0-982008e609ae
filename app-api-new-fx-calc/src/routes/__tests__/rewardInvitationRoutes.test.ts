import request from "supertest";
import { faker } from "@faker-js/faker";
import app from "../../app";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildUser } from "../../tests/utils/generateModels";

describe("RewardInvitationRoutes", () => {
  beforeAll(async () => await connectDb("RewardInvitationRoutes"));
  afterAll(async () => await closeDb());

  describe("POST /reward-invitations", () => {
    let referrer: UserDocument;

    beforeAll(async () => {
      referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
    });
    afterAll(async () => await clearDb());

    it("should return 400 if the request body is empty", async () => {
      const response = await request(app)
        .post("/api/m2m/reward-invitations")
        .send({})
        .set("external-user-id", referrer._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Request body cannot be empty"
          }
        })
      );
    });

    it("should return 400 if the request does not include a targetUserEmail field", async () => {
      const response = await request(app)
        .post("/api/m2m/reward-invitations")
        .send({
          someField: 1
        })
        .set("external-user-id", referrer._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'targetUserEmail' is required"
          }
        })
      );
    });

    it("should return 200 with the reward data if successful", async () => {
      const targetUserEmail = faker.internet.email().toLowerCase();

      const response = await request(app)
        .post("/api/m2m/reward-invitations")
        .send({
          targetUserEmail
        })
        .set("external-user-id", referrer._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);
    });
  });
});
