import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildAddress, buildUser } from "../../tests/utils/generateModels";
import mongoose from "mongoose";
import { Address, AddressDTOInterface } from "../../models/Address";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { BadRequestError } from "../../models/ApiErrors";
import PostCodeFetchifyService from "../../external-services/postCodeFetchifyService";
import { ProviderEnum } from "../../configs/providersConfig";

describe("AddressRoutes", () => {
  beforeAll(async () => await connectDb("AddressRoutes"));
  afterAll(async () => await closeDb());

  describe("/addresses", function () {
    describe("POST /addresses", () => {
      let user: UserDocument;
      let validAddressData: AddressDTOInterface;

      beforeEach(async () => {
        jest.clearAllMocks();
        jest.spyOn(PostCodeFetchifyService, "validateUKPostCode").mockImplementation((): any => null);

        user = await buildUser();
        validAddressData = {
          owner: new mongoose.Types.ObjectId(user._id),
          line1: "8 West St",
          city: "Crewkerne",
          countryCode: "GB",
          postalCode: "TA188AX"
        };
      });
      afterEach(async () => await clearDb());

      it("should return 204, emit event and create new address when address data is valid and owner does not already have an address", async () => {
        const existingAddresses = await Address.find({ owner: user._id });
        expect(existingAddresses.length).toEqual(0);

        const actualResponse = await request(app)
          .post("/api/m2m/addresses")
          .send(validAddressData)
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(204);

        const newAddresses = await Address.find({ owner: user._id });
        expect(newAddresses.length).toEqual(1);
        expect(newAddresses[0]).toMatchObject(
          expect.objectContaining({ ...validAddressData, activeProviders: [ProviderEnum.WEALTHKERNEL] })
        );
        expect(eventEmitter.emit).toBeCalledWith(
          events.user.addressSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should return 204, without emitting event and update existing address with specified fields when owner already has an address", async () => {
        await buildAddress({ ...validAddressData });
        const existingAddresses = await Address.find({ owner: user._id });
        expect(existingAddresses.length).toEqual(1);

        const actualResponse = await request(app)
          .post("/api/m2m/addresses")
          .send({ ...validAddressData, line1: "7 West St" })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(204);

        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(1);
        expect(addresses[0]).toMatchObject(expect.objectContaining({ ...validAddressData, line1: "7 West St" }));
        expect(eventEmitter.emit).not.toBeCalledWith(
          events.user.addressSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should remove whitespaces from postalCode submitted for valid address data", async () => {
        validAddressData = {
          owner: new mongoose.Types.ObjectId(user._id),
          line1: "8 West St",
          city: "Crewkerne",
          countryCode: "GB",
          postalCode: "TA18 8AX "
        };

        const actualResponse = await request(app)
          .post("/api/m2m/addresses")
          .send({ ...validAddressData, line1: "7 West St" })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(204);

        const addresses = await Address.find({});
        expect(addresses[0].postalCode).toBe("TA188AX");
      });

      it("should return 400 when required body field is empty", async () => {
        const response = await request(app)
          .post("/api/m2m/addresses")
          .send({ ...validAddressData, line1: "" })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Missing field 'line1'",
              description: "Operation failed"
            }
          })
        );
        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(0);
      });

      it("should return 400 when required body field is undefined", async () => {
        const response = await request(app)
          .post("/api/m2m/addresses")
          .send({ owner: user.id, line1: "line1", countryCode: "GB", postalCode: "EC1R3AL" })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Missing field 'city'",
              description: "Operation failed"
            }
          })
        );
        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(0);
      });

      it("should return 400 when post code provided is invalid", async () => {
        jest.spyOn(PostCodeFetchifyService, "validateUKPostCode").mockImplementation((): any => {
          throw new BadRequestError("Could not validate post code");
        });

        const invalidAddressData = {
          owner: new mongoose.Types.ObjectId(user._id),
          line1: "8 West St",
          city: "Crewkerne",
          countryCode: "GB",
          postalCode: "SOMETHING"
        };

        const actualResponse = await request(app)
          .post("/api/m2m/addresses")
          .send(invalidAddressData)
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(400);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Could not validate post code",
              description: "Operation failed"
            }
          })
        );

        const newAddresses = await Address.find({ owner: user._id });
        expect(newAddresses.length).toEqual(0);
      });

      it("should return 401 if user ID header is missing", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/addresses")
          .send(validAddressData)
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(401);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "User not found", message: "invalid_token" }
          })
        );
        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(0);
      });
    });
  });
});
