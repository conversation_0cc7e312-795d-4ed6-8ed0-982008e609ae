import request from "supertest";
import app from "../../app";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { AppRating, AppRatingDocument, AppRatingStatusEnum } from "../../models/AppRating";
import { buildAppRating, buildUser } from "../../tests/utils/generateModels";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { UserDocument } from "../../models/User";

describe("AppRatingRoutes", () => {
  beforeAll(async () => await connectDb("AppRatingRoutes"));
  afterAll(async () => await closeDb());

  describe("POST /app-ratings/:id", () => {
    describe("when the app rating id is invalid", () => {
      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();
        response = await request(app)
          .post("/api/m2m/app-ratings/invalid")
          .set("external-user-id", user._id)
          .set("Accept", "application/json")
          .send({ feedback: "feedback", starRating: 6 });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "Invalid parameter", message: "App Rating ID is invalid" }
          })
        );
      });
    });

    describe("when the app rating id belongs to another user", () => {
      let user: UserDocument;
      let anotherUser: UserDocument;
      let appRating: AppRatingDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();

        anotherUser = await buildUser();
        appRating = await buildAppRating({ owner: anotherUser._id });

        response = await request(app)
          .post(`/api/m2m/app-ratings/${appRating.id}`)
          .set("external-user-id", user._id)
          .set("Accept", "application/json")
          .send({ feedback: "feedback", starRating: 6 });
      });
      afterAll(async () => await clearDb());

      it("should return 403", async () => {
        expect(response.status).toEqual(403);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Inaccessible resource",
              message: "App Rating does not belong to user"
            }
          })
        );
      });
    });

    describe("when the app rating value is greater than 5", () => {
      let user: UserDocument;
      let appRating: AppRatingDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();
        appRating = await buildAppRating({ owner: user._id });

        response = await request(app)
          .post(`/api/m2m/app-ratings/${appRating.id}`)
          .set("external-user-id", user._id)
          .set("Accept", "application/json")
          .send({ feedback: "feedback", starRating: 6 });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "Operation failed", message: "Invalid star rating" }
          })
        );
      });
    });

    describe("when the app rating value has fraction digits", () => {
      let user: UserDocument;
      let appRating: AppRatingDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();
        appRating = await buildAppRating({ owner: user._id });

        response = await request(app)
          .post(`/api/m2m/app-ratings/${appRating.id}`)
          .set("external-user-id", user._id)
          .set("Accept", "application/json")
          .send({ feedback: "feedback", starRating: 3.5 });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "Operation failed", message: "Invalid star rating" }
          })
        );
      });
    });

    describe("when the request is valid", () => {
      let user: UserDocument;
      let appRating: AppRatingDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();
        appRating = await buildAppRating({ owner: user._id });

        response = await request(app)
          .post(`/api/m2m/app-ratings/${appRating.id}`)
          .set("external-user-id", user._id)
          .set("Accept", "application/json")
          .send({ feedback: "feedback", starRating: 5 });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and update the app rating document", async () => {
        expect(response.status).toEqual(204);

        const updatedAppRating = await AppRating.findOne({ owner: user._id });

        expect(updatedAppRating.status).toEqual(AppRatingStatusEnum.COMPLETED);
        expect(updatedAppRating.feedback).toEqual("feedback");
        expect(updatedAppRating.starRating).toEqual(5);
      });

      it("should should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.appRating.appRatingSubmitted.eventId,
          expect.objectContaining({ email: user.email }),
          { feedback: "feedback", starRating: 5 }
        );
      });
    });

    describe("when the request is valid but called concurrently 3 times", () => {
      let user: UserDocument;
      let appRating: AppRatingDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();
        appRating = await buildAppRating({ owner: user._id });

        [response] = await Promise.all([
          request(app)
            .post(`/api/m2m/app-ratings/${appRating.id}`)
            .set("external-user-id", user._id)
            .set("Accept", "application/json")
            .send({ feedback: "feedback", starRating: 5 }),
          request(app)
            .post(`/api/m2m/app-ratings/${appRating.id}`)
            .set("external-user-id", user._id)
            .set("Accept", "application/json")
            .send({ feedback: "feedback", starRating: 5 }),
          request(app)
            .post(`/api/m2m/app-ratings/${appRating.id}`)
            .set("external-user-id", user._id)
            .set("Accept", "application/json")
            .send({ feedback: "feedback", starRating: 5 })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return 200 and update the app rating document", async () => {
        expect(response.status).toEqual(204);

        const updatedAppRating = await AppRating.findOne({ owner: user._id });

        expect(updatedAppRating.status).toEqual(AppRatingStatusEnum.COMPLETED);
        expect(updatedAppRating.feedback).toEqual("feedback");
        expect(updatedAppRating.starRating).toEqual(5);
      });

      it("should should emit an app rating event once", async () => {
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          1,
          events.appRating.appRatingSubmitted.eventId,
          expect.objectContaining({ email: user.email }),
          { feedback: "feedback", starRating: 5 }
        );
      });
    });
  });
});
