import request from "supertest";
import app from "../../app";
import { PortfolioDocument } from "../../models/Portfolio";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildDailyPortfolioTicker, buildPortfolio, buildUser } from "../../tests/utils/generateModels";
import DateUtil from "../../utils/dateUtil";

describe("DailyTickerRoutes", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("DailyTickerRoutes"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("/daily-portfolio-tickers", function () {
    describe("GET /daily-portfolio-tickers/by-tenor", () => {
      describe("when user has not passed portfolio ID", () => {
        let owner: UserDocument;

        beforeAll(async () => {
          owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        });
        afterAll(async () => await clearDb());

        it("should return 400", async () => {
          const response = await request(app)
            .get("/api/m2m/daily-portfolio-tickers/by-tenor")
            .set("external-user-id", owner._id)
            .set("Accept", "application/json");

          expect(response.status).toBe(400);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              error: {
                description: "Invalid parameter",
                message: expect.stringContaining("Param 'id' is required")
              }
            })
          );
        });
      });

      describe("when user has passed portfolio ID that does not exist", () => {
        let owner: UserDocument;
        let portfolio: PortfolioDocument;

        const TODAY = new Date("2023-08-23");

        beforeAll(async () => {
          Date.now = jest.fn(() => TODAY.valueOf());

          owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          portfolio = await buildPortfolio({ owner: owner.id });
        });
        afterAll(async () => await clearDb());

        it("should return 200", async () => {
          const response = await request(app)
            .get(`/api/m2m/daily-portfolio-tickers/by-tenor?portfolio=${owner.id}`)
            .set("external-user-id", owner._id)
            .set("Accept", "application/json");

          expect(response.status).toEqual(403);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              error: { description: "Inaccessible resource", message: "Portfolio does not belong to user" }
            })
          );
        });
      });

      describe("when user is not invested", () => {
        let owner: UserDocument;
        let portfolio: PortfolioDocument;

        const TODAY = new Date("2023-08-23");
        const data = [
          { date: "2023-08-19T00:00:00.000Z", displayDate: "19 Aug 2023", price: 0 },
          { date: "2023-08-20T00:00:00.000Z", displayDate: "20 Aug 2023", price: 0 },
          { date: "2023-08-21T00:00:00.000Z", displayDate: "21 Aug 2023", price: 0 },
          { date: "2023-08-22T00:00:00.000Z", displayDate: "22 Aug 2023", price: 0 },
          { date: "2023-08-23T00:00:00.000Z", displayDate: "23 Aug 2023", price: 0 }
        ];
        beforeAll(async () => {
          Date.now = jest.fn(() => TODAY.valueOf());

          owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          portfolio = await buildPortfolio({ owner: owner.id });
        });
        afterAll(async () => await clearDb());

        it("should return 200", async () => {
          const response = await request(app)
            .get(`/api/m2m/daily-portfolio-tickers/by-tenor?portfolio=${portfolio.id}`)
            .set("external-user-id", owner._id)
            .set("Accept", "application/json");

          expect(response.status).toEqual(200);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              "1w": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-19T00:00:00.000Z",
                data: data
              },
              "1m": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-19T00:00:00.000Z",
                data: data
              },
              "3m": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-19T00:00:00.000Z",
                data: data
              },
              "6m": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-19T00:00:00.000Z",
                data: data
              },
              "1y": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-19T00:00:00.000Z",
                data: data
              },
              max: {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-19T00:00:00.000Z",
                data: data
              }
            })
          );
        });
      });

      describe("when user is invested for the last 10 days", () => {
        let owner: UserDocument;
        let portfolio: PortfolioDocument;

        const TODAY = new Date("2023-08-23");

        beforeAll(async () => {
          Date.now = jest.fn(() => TODAY.valueOf());

          owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          portfolio = await buildPortfolio({ owner: owner.id });

          await Promise.all(
            [...Array(10).keys()].map(async (daysAgo) => {
              const date = DateUtil.getDateOfDaysAgo(TODAY, daysAgo);

              // Ticker for 16th is missing because it was a holiday
              if (!DateUtil.datesAreEqual(date, new Date("2023-08-16"))) {
                await buildDailyPortfolioTicker({
                  portfolio: portfolio._id,
                  date: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                  pricePerCurrency: { GBP: 10 }
                });
              }
            })
          );
        });
        afterAll(async () => await clearDb());

        it("should return 200", async () => {
          const response = await request(app)
            .get(`/api/m2m/daily-portfolio-tickers/by-tenor?portfolio=${portfolio.id}`)
            .set("external-user-id", owner._id)
            .set("Accept", "application/json");

          // Since the user is invested for 10 days, the 1w tenor will be full of data, and the rest will be filled
          // partially with zeroes.
          const oneWeekData = [
            // Ticker for 16th is missing because it was a holiday
            { date: "2023-08-17T00:00:00.000Z", displayDate: "17 Aug 2023", price: 10 },
            { date: "2023-08-18T00:00:00.000Z", displayDate: "18 Aug 2023", price: 10 },
            { date: "2023-08-19T00:00:00.000Z", displayDate: "19 Aug 2023", price: 10 },
            { date: "2023-08-20T00:00:00.000Z", displayDate: "20 Aug 2023", price: 10 },
            { date: "2023-08-21T00:00:00.000Z", displayDate: "21 Aug 2023", price: 10 },
            { date: "2023-08-22T00:00:00.000Z", displayDate: "22 Aug 2023", price: 10 },
            { date: "2023-08-23T00:00:00.000Z", displayDate: "23 Aug 2023", price: 10 }
          ];
          const fullData = [
            { date: "2023-08-14T00:00:00.000Z", displayDate: "14 Aug 2023", price: 10 },
            { date: "2023-08-15T00:00:00.000Z", displayDate: "15 Aug 2023", price: 10 },
            ...oneWeekData
          ];

          expect(response.status).toBe(200);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              "1w": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-17T00:00:00.000Z",
                data: oneWeekData
              },
              "1m": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-07-24T00:00:00.000Z",
                data: expect.arrayContaining([
                  ...fullData,
                  expect.objectContaining({ displayDate: "24 Jul 2023", price: 0 })
                ])
              },
              "3m": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-05-24T00:00:00.000Z",
                data: expect.arrayContaining([
                  ...fullData,
                  expect.objectContaining({ displayDate: "24 Jul 2023", price: 0 }),
                  expect.objectContaining({ displayDate: "24 May 2023", price: 0 })
                ])
              },
              "6m": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-02-22T00:00:00.000Z",
                data: expect.arrayContaining([
                  ...fullData,
                  expect.objectContaining({ displayDate: "24 Jul 2023", price: 0 }),
                  expect.objectContaining({ displayDate: "24 May 2023", price: 0 }),
                  expect.objectContaining({ displayDate: "22 Feb 2023", price: 0 })
                ])
              },
              "1y": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2022-08-23T00:00:00.000Z",
                data: expect.arrayContaining([
                  ...fullData,
                  expect.objectContaining({ displayDate: "24 Jul 2023", price: 0 }),
                  expect.objectContaining({ displayDate: "24 May 2023", price: 0 }),
                  expect.objectContaining({ displayDate: "22 Feb 2023", price: 0 }),
                  expect.objectContaining({ displayDate: "23 Aug 2022", price: 0 })
                ])
              },
              max: {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-14T00:00:00.000Z",
                data: fullData
              }
            })
          );
        });
      });

      describe("when user is invested for one day", () => {
        let owner: UserDocument;
        let portfolio: PortfolioDocument;

        const TODAY = new Date("2023-08-23");

        beforeAll(async () => {
          Date.now = jest.fn(() => TODAY.valueOf());

          owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          portfolio = await buildPortfolio({ owner: owner.id });

          await buildDailyPortfolioTicker({
            portfolio: portfolio._id,
            date: TODAY,
            pricePerCurrency: { GBP: 10 }
          });
        });
        afterAll(async () => await clearDb());

        it("should return 200 and the max tenor should be equal to the 1w tenor", async () => {
          const response = await request(app)
            .get(`/api/m2m/daily-portfolio-tickers/by-tenor?portfolio=${portfolio.id}`)
            .set("external-user-id", owner._id)
            .set("Accept", "application/json");

          // Since the user is invested for 1 day, the 1w & the max tenor will be filled with zeroes for the
          // non-invested days.
          const oneWeekData = [
            { date: "2023-08-16T00:00:00.000Z", displayDate: "16 Aug 2023", price: 0 },
            { date: "2023-08-17T00:00:00.000Z", displayDate: "17 Aug 2023", price: 0 },
            { date: "2023-08-18T00:00:00.000Z", displayDate: "18 Aug 2023", price: 0 },
            { date: "2023-08-19T00:00:00.000Z", displayDate: "19 Aug 2023", price: 0 },
            { date: "2023-08-20T00:00:00.000Z", displayDate: "20 Aug 2023", price: 0 },
            { date: "2023-08-21T00:00:00.000Z", displayDate: "21 Aug 2023", price: 0 },
            { date: "2023-08-22T00:00:00.000Z", displayDate: "22 Aug 2023", price: 0 },
            { date: "2023-08-23T00:00:00.000Z", displayDate: "23 Aug 2023", price: 10 }
          ];

          expect(response.status).toBe(200);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              "1w": {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-16T00:00:00.000Z",
                data: oneWeekData
              },
              max: {
                endDate: "2023-08-23T00:00:00.000Z",
                startDate: "2023-08-16T00:00:00.000Z",
                data: oneWeekData
              }
            })
          );
        });
      });
    });
  });
});
