import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildPaymentMethod, buildUser } from "../../tests/utils/generateModels";
import { PaymentMethod, PaymentMethodDocument } from "../../models/PaymentMethod";
import { faker } from "@faker-js/faker";
import { StripeService } from "../../external-services/stripeService";
import mongoose from "mongoose";

describe("PaymentMethodRoutes", () => {
  beforeAll(async () => await connectDb("PaymentMethodRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /payment-methods", () => {
    describe("when user does not have a payment method", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 200 with an empty data array", async () => {
        const response = await request(app)
          .get("/api/m2m/payment-methods")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({ data: [] });
      });
    });

    describe("when there is a payment method for the user", () => {
      let owner: UserDocument;
      let paymentMethod: PaymentMethodDocument;

      beforeAll(async () => {
        owner = await buildUser();
        paymentMethod = await buildPaymentMethod({
          owner: owner.id,
          providers: {
            stripe: {
              id: "pm_xxx"
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 with a single payment method", async () => {
        const response = await request(app)
          .get("/api/m2m/payment-methods")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              owner: owner.id,
              _id: paymentMethod.id
            })
          ]
        });
      });
    });
  });

  describe("POST /payment-methods/initiate-stripe", () => {
    describe("when user does not have a Stripe customer", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        const response = await request(app)
          .post("/api/m2m/payment-methods/initiate-stripe")
          .set("external-user-id", owner.id)
          .set("Accept", "application/json");
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: "User does not have Stripe customer ID!"
            }
          })
        );
      });
    });

    describe("when user has a Stripe customer", () => {
      const STRIPE_CLIENT_SECRET = faker.string.uuid();
      const STRIPE_SETUP_INTENT_ID = faker.string.uuid();
      const STRIPE_EPHEMERAL_KEY = faker.string.uuid();

      let owner: UserDocument;

      beforeAll(async () => {
        jest
          .spyOn(StripeService.Instance, "createSetupIntent")
          .mockResolvedValue({ client_secret: STRIPE_CLIENT_SECRET, id: STRIPE_SETUP_INTENT_ID } as any);
        jest
          .spyOn(StripeService.Instance, "createEphemeralKey")
          .mockResolvedValue({ secret: STRIPE_EPHEMERAL_KEY } as any);

        owner = await buildUser({
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 with a client secret and an ephemeral key", async () => {
        const response = await request(app)
          .post("/api/m2m/payment-methods/initiate-stripe")
          .set("external-user-id", owner.id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({
          clientSecret: STRIPE_CLIENT_SECRET,
          setupIntentId: STRIPE_SETUP_INTENT_ID,
          ephemeralKey: STRIPE_EPHEMERAL_KEY
        });
      });
    });
  });

  describe("POST /payment-methods/complete-stripe", () => {
    describe("when client has not passed a setup intent ID", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/payment-methods/complete-stripe")
          .send({})
          .set("external-user-id", owner.id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Setup intent ID has not been passed!"
            }
          })
        );
      });
    });

    describe("when client is creating a payment method that already exists", () => {
      let owner: UserDocument;
      let existingPaymentMethod: PaymentMethodDocument;
      let response: request.Response;

      const STRIPE_PAYMENT_METHOD = faker.string.uuid();
      const STRIPE_FINGERPRINT = faker.string.uuid();

      beforeAll(async () => {
        jest
          .spyOn(StripeService.Instance, "retrieveSetupIntent")
          .mockResolvedValue({ payment_method: STRIPE_PAYMENT_METHOD } as any);
        jest
          .spyOn(StripeService.Instance, "retrievePaymentMethod")
          .mockImplementation((paymentMethodId: string) => {
            if (paymentMethodId === STRIPE_PAYMENT_METHOD) {
              return Promise.resolve({
                type: "card",
                card: {
                  fingerprint: STRIPE_FINGERPRINT,
                  last4: "4242",
                  brand: "visa"
                }
              } as any);
            } else throw new Error(`Should have passed ${STRIPE_PAYMENT_METHOD}`);
          });

        owner = await buildUser();

        existingPaymentMethod = await buildPaymentMethod({
          owner: owner.id,
          fingerprint: STRIPE_FINGERPRINT
        });
        const paymentMethods = await PaymentMethod.find({ owner: owner.id });
        expect(paymentMethods.length).toBe(1);

        response = await request(app)
          .post("/api/m2m/payment-methods/complete-stripe")
          .send({ setupIntentId: faker.string.uuid() })
          .set("external-user-id", owner.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the original payment method as the response body", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          owner: owner.id,
          _id: existingPaymentMethod.id
        });
      });

      it("should NOT create a payment method", async () => {
        const paymentMethods = await PaymentMethod.find({ owner: owner.id });
        expect(paymentMethods.length).toBe(1);
      });
    });

    describe("when client has passed a valid setup intent ID", () => {
      let owner: UserDocument;
      let response: request.Response;

      const STRIPE_PAYMENT_METHOD = faker.string.uuid();

      beforeAll(async () => {
        jest
          .spyOn(StripeService.Instance, "retrieveSetupIntent")
          .mockResolvedValue({ payment_method: STRIPE_PAYMENT_METHOD } as any);
        jest
          .spyOn(StripeService.Instance, "retrievePaymentMethod")
          .mockImplementation((paymentMethodId: string) => {
            if (paymentMethodId === STRIPE_PAYMENT_METHOD) {
              return Promise.resolve({
                type: "card",
                card: {
                  fingerprint: faker.string.uuid(),
                  last4: "4242",
                  brand: "visa"
                }
              } as any);
            } else throw new Error(`Should have passed ${STRIPE_PAYMENT_METHOD}`);
          });

        owner = await buildUser();

        response = await request(app)
          .post("/api/m2m/payment-methods/complete-stripe")
          .send({ setupIntentId: faker.string.uuid() })
          .set("external-user-id", owner.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the payment method as the response body", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          owner: owner.id,
          type: "card",
          brand: "visa",
          lastFourDigits: "4242"
        });
      });

      it("should create a payment method", async () => {
        const paymentMethods = await PaymentMethod.find({ owner: owner.id });
        expect(paymentMethods.length).toBe(1);
        expect(paymentMethods[0]).toMatchObject(
          expect.objectContaining({
            owner: new mongoose.Types.ObjectId(owner.id),
            type: "card",
            brand: "visa",
            lastFourDigits: "4242"
          })
        );
      });
    });
  });
});
