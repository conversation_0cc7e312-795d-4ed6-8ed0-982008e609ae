import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import request from "supertest";
import supertest from "supertest";
import app from "../../app";
import { faker } from "@faker-js/faker";
import {
  buildAssetTransaction,
  buildBankAccount,
  buildGift,
  buildMandate,
  buildReward,
  buildSubscription,
  buildTopUpAutomation,
  buildUser
} from "../../tests/utils/generateModels";
import { KycStatusEnum, UserDocument } from "../../models/User";
import events from "../../event-handlers/events";
import eventEmitter from "../../loaders/eventEmitter";
import { TruelayerPaymentsClient } from "../../external-services/truelayerService";
import { buildProviderType } from "../../tests/utils/generateTruelayer";
import { buildWealthkernelBankAccountResponse } from "../../tests/utils/generateWealthkernel";
import { BankAccount } from "../../models/BankAccount";
import { ProviderEnum } from "../../configs/providersConfig";
import { GiftDocument } from "../../models/Gift";
import { banksConfig, entitiesConfig } from "@wealthyhood/shared-configs";
import { GoCardlessDataService } from "../../external-services/goCardlessDataService";
import {
  buildGoCardlessEndUserAgreementResponse,
  buildGoCardlessRequisitionResponse
} from "../../tests/utils/generateGoCardless";
import { Requisition } from "../../models/Requisition";
import { WealthkernelService } from "../../external-services/wealthkernelService";

describe("BankAccountRoutes", () => {
  beforeAll(async () => await connectDb("AdminBankAccountRoutes"));
  afterAll(async () => {
    await clearDb();
    await closeDb();
  });

  describe("GET /bank-accounts/banks", () => {
    describe("when user has WEALTHYHOOD_EUROPE company entity and scope is DATA", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();

        const user = await buildUser(
          { residencyCountry: "GR", companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE },
          false
        );

        response = await request(app)
          .get(`/api/m2m/bank-accounts/banks?scope=${banksConfig.BankProviderScopeEnum.DATA}`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should return 200 with a list of available banks", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.arrayContaining([
            expect.objectContaining({
              id: "alphabank",
              providerId: "ALPHABANK_CRBAGRAA",
              name: "Alpha Bank",
              logo: "https://img.wealthyhood.dev/bank-icons/AlphaBank.png"
            })
          ])
        );
      });
    });

    describe("when user has WEALTHYHOOD_EUROPE company entity and scope is not set", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();

        const user = await buildUser(
          { residencyCountry: "GR", companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE },
          false
        );

        response = await request(app)
          .get("/api/m2m/bank-accounts/banks")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should return 200 with a list of available banks", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.arrayContaining([
            expect.objectContaining({
              id: "alphabank",
              providerId: "ALPHABANK_CRBAGRAA",
              name: "Alpha Bank",
              logo: "https://img.wealthyhood.dev/bank-icons/AlphaBank.png"
            })
          ])
        );
      });
    });

    describe("when user has WEALTHYHOOD_EUROPE company entity and scope is PAY", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();

        const user = await buildUser(
          { residencyCountry: "GR", companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE },
          false
        );

        response = await request(app)
          .get(`/api/m2m/bank-accounts/banks?scope=${banksConfig.BankProviderScopeEnum.PAY}`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should return 200 with a list of available banks", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.arrayContaining([
            expect.objectContaining({
              id: "revolut",
              providerId: "revolut_oauth_client_gr",
              name: "Revolut",
              logo: "https://img.wealthyhood.dev/bank-icons/Revolut.png"
            }),
            expect.objectContaining({
              id: "n26",
              providerId: "n26_xs2a_oauth_client_gr",
              name: "N26 Bank",
              logo: "https://img.wealthyhood.dev/bank-icons/N26.png"
            })
          ])
        );
      });
    });

    describe("when user has WEALTHYHOOD_UK company entity and scope is DATA", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();

        const user = await buildUser(
          { residencyCountry: "GB", companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK },
          false
        );

        response = await request(app)
          .get("/api/m2m/bank-accounts/banks")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should return 200 with banks list", async () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: "mock-payments-gb-redirect",
              providerId: "mock-payments-gb-redirect",
              name: "Mock UK Bank",
              logo: "https://img.wealthyhood.dev/bank-icons/Unknown.png"
            }),
            expect.objectContaining({
              id: "mock",
              providerId: "mock",
              name: "Mock Data Linking Bank",
              logo: "https://img.wealthyhood.dev/bank-icons/Unknown.png"
            })
          ])
        );
      });
    });
  });

  describe("POST /bank-accounts", () => {
    const TRUELAYER_PROVIDER = buildProviderType({ provider_id: "mock-payments-gb-redirect" });

    ["name", "number", "sortCode", "truelayerProviderId"].forEach((param) => {
      it(`should returns status 400 with proper message for missing param ${param}`, async () => {
        const user = await buildUser();
        const response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // create body with all parameters required except 'param'
            {
              bankAccount: Object.fromEntries(
                ["name", "number", "sortCode", "truelayerProviderId"]
                  .filter((p) => p != param)
                  .map((p) => [p, faker.string.sample()])
              )
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: `Missing field '${param}'`
            }
          })
        );
      });
    });

    describe("when request body is valid but bank provider is not supported", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const UNSUPPORTED_BANK_PROVIDER = "unsupported-bank-provider";
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: UNSUPPORTED_BANK_PROVIDER
      };

      beforeAll(async () => {
        await clearDb();
        jest.clearAllMocks();
        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );
        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // create body with all parameters required except 'param'
            {
              bankAccount: validBankAccountData,
              bankLinkedFrom: "my_account"
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return error status 400 with message for unsupported provider", () => {
        expect(response.status).toBe(400);
        const parsedResponse = JSON.parse(response.text);
        expect(parsedResponse.error.message).toEqual(`Provider ${UNSUPPORTED_BANK_PROVIDER} is not supported`);
      });

      it("should NOT create a bank account", async () => {
        const bankAccounts = await BankAccount.find({});
        expect(bankAccounts.length).toEqual(0);
      });
    });

    describe("when request body is valid but user is EU-based", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([TRUELAYER_PROVIDER]);

        user = await buildUser(
          {
            firstName: "Paris",
            lastName: "Kolovos",
            providers: { wealthkernel: { id: "WK-PARTY-ID" } },
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
          },
          false
        );
        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // create body with all parameters required except 'param'
            {
              bankAccount: validBankAccountData,
              bankLinkedFrom: "my_account"
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 400", () => {
        expect(response.status).toEqual(400);
      });

      it("should NOT create a bank account document", async () => {
        const newBankAccounts = await BankAccount.find({ owner: user._id });
        expect(newBankAccounts.length).toEqual(0);
      });
    });

    describe("when request body is valid for existing bank account and bank provider is not supported", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: TRUELAYER_PROVIDER.provider_id
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([TRUELAYER_PROVIDER]);

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(0);
        await buildBankAccount({ ...validBankAccountData, owner: user.id });
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(1);

        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send({
            bankAccount: { ...validBankAccountData, name: "Panagiotis Kolovos" }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should NOT create a new bank account", async () => {
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(1);
      });

      it("should update existing bank account", async () => {
        expect(await BankAccount.findOne({ owner: user.id })).toMatchObject(
          expect.objectContaining({ ...validBankAccountData, name: "Panagiotis Kolovos" })
        );
      });
    });

    describe("when request body is valid and user is UK-based", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([TRUELAYER_PROVIDER]);

        user = await buildUser(
          {
            firstName: "Paris",
            lastName: "Kolovos",
            providers: { wealthkernel: { id: "WK-PARTY-ID" } },
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
          },
          false
        );
        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // create body with all parameters required except 'param'
            {
              bankAccount: validBankAccountData,
              bankLinkedFrom: "my_account"
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should create a bank account document", async () => {
        const newBankAccounts = await BankAccount.find({ owner: user._id });
        expect(newBankAccounts.length).toEqual(1);
        expect(newBankAccounts[0].activeProviders.length).toEqual(2);
        expect(newBankAccounts[0]).toMatchObject(
          expect.objectContaining({
            ...validBankAccountData,
            activeProviders: expect.arrayContaining([ProviderEnum.WEALTHKERNEL, ProviderEnum.TRUELAYER]),
            providers: expect.objectContaining({ truelayer: { bankId: validBankAccountData.truelayerProviderId } })
          })
        );
      });

      it("should create a bank account object in Wealthkernel", () => {
        expect(WealthkernelService.UKInstance.addBankAccount).toBeCalledTimes(1);
        expect(WealthkernelService.UKInstance.addBankAccount).toBeCalledWith(
          expect.objectContaining({
            accountNumber: validBankAccountData.number,
            countryCode: "GB",
            currency: "GBP",
            name: validBankAccountData.name,
            partyId: user.providers.wealthkernel.id,
            sortCode: validBankAccountData.sortCode
          })
        );
      });

      it("should emit bankAccountLinking event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.bankAccountLinking.eventId,
          expect.objectContaining({ id: user.id }),
          {
            bankLinkedFrom: "my account",
            isFirst: true,
            hasSettledRewards: false,
            truelayerId: validBankAccountData.truelayerProviderId,
            banks: [validBankAccountData.truelayerProviderId],
            hasUsedGift: false
          }
        );
      });

      it("should return 200", () => {
        expect(response.status).toEqual(200);
      });

      describe("and a second bank account is created with incorrect name", () => {
        const secondValidBankAccountData = {
          name: "Incorect Name",
          number: "********",
          sortCode: "43-21-32",
          truelayerProviderId: "mock-payments-gb-redirect"
        };

        beforeAll(async () => {
          jest.clearAllMocks();
          response = await request(app)
            .post("/api/m2m/bank-accounts")
            .send(
              // create body with all parameters required except 'param'
              {
                bankAccount: secondValidBankAccountData,
                bankLinkedFrom: "my_account"
              }
            )
            .set("external-user-id", user.id)
            .set("Accept", "application/json");
        });

        it("should create a bank account document", async () => {
          const newBankAccounts = await BankAccount.find({ owner: user._id }).sort({ createdAt: -1 });
          expect(newBankAccounts[0].toObject()).toMatchObject(
            expect.objectContaining({ ...secondValidBankAccountData })
          );
        });

        it("should create a bank account object in Wealthkernel", () => {
          // 2 times (for previous account too)
          expect(WealthkernelService.UKInstance.addBankAccount).toBeCalledTimes(1);
          expect(WealthkernelService.UKInstance.addBankAccount).toBeCalledWith(
            expect.objectContaining({
              accountNumber: secondValidBankAccountData.number,
              countryCode: "GB",
              currency: "GBP",
              name: user.fullName,
              partyId: user.providers.wealthkernel.id,
              sortCode: secondValidBankAccountData.sortCode
            })
          );
        });

        it("should emit bankAccountLinking event", () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            1,
            events.user.bankAccountLinking.eventId,
            expect.objectContaining({ id: user.id }),
            {
              bankLinkedFrom: "my account",
              isFirst: false,
              hasSettledRewards: false,
              truelayerId: secondValidBankAccountData.truelayerProviderId,
              banks: [validBankAccountData.truelayerProviderId, secondValidBankAccountData.truelayerProviderId],
              hasUsedGift: false
            }
          );
        });

        it("should exist 2 bank accounts for user", async () => {
          expect(await BankAccount.find({ owner: user.id }).countDocuments()).toEqual(2);
        });

        it("should return 200", () => {
          expect(response.status).toEqual(200);
        });
      });
    });

    describe("when request body is valid, user is gifted and submits a bank account linking event", () => {
      let targetUser: UserDocument;
      let gifted: UserDocument;
      let gift: GiftDocument;

      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        targetUser = await buildUser({
          kycStatus: KycStatusEnum.PASSED
        });

        gifted = await buildUser({
          firstName: "Paris",
          lastName: "Kolovos",
          providers: { wealthkernel: { id: "WK-PARTY-ID" } },
          kycStatus: KycStatusEnum.PASSED
        });

        gift = await buildGift({
          gifter: targetUser.id,
          targetUserEmail: gifted.email,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          }
        });

        await buildAssetTransaction({
          pendingGift: gift.id,
          status: "PendingGift"
        });

        await request(app)
          .post("/api/m2m/bank-accounts")
          .send({
            bankAccount: validBankAccountData,
            bankLinkedFrom: "my_account"
          })
          .set("external-user-id", gifted.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should emit bankAccountLinking event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          1,
          events.user.bankAccountLinking.eventId,
          expect.objectContaining({ id: gifted.id }),
          {
            bankLinkedFrom: "my account",
            isFirst: false,
            hasSettledRewards: false,
            truelayerId: validBankAccountData.truelayerProviderId,
            banks: [undefined, validBankAccountData.truelayerProviderId],
            hasUsedGift: true
          }
        );
      });
    });

    describe("when a bank account with sortCode, number provided exists", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: TRUELAYER_PROVIDER.provider_id
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([TRUELAYER_PROVIDER]);

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(0);
        await buildBankAccount({ ...validBankAccountData, owner: user.id });
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(1);

        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send({
            bankAccount: { ...validBankAccountData, name: "Panagiotis Kolovos" }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should NOT create a new bank account", async () => {
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(1);
      });

      it("should update existing bank account", async () => {
        expect(await BankAccount.findOne({ owner: user.id })).toMatchObject(
          expect.objectContaining({ ...validBankAccountData, name: "Panagiotis Kolovos" })
        );
      });

      describe("and a second request is being made with differnet bank account data", () => {
        const validBankAccountData = {
          name: "Paris Kolovos",
          number: "********",
          sortCode: "32-21-57",
          truelayerProviderId: "mock-payments-gb-redirect"
        };

        beforeAll(async () => {
          expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(1);

          response = await request(app)
            .post("/api/m2m/bank-accounts")
            .send({
              bankAccount: validBankAccountData
            })
            .set("external-user-id", user.id)
            .set("Accept", "application/json");
        });

        it("should return 200", () => {
          expect(response.status).toEqual(200);
        });

        it("should create a second new bank account", async () => {
          expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(2);
          const newBankAccount = await BankAccount.findOne({
            owner: user.id,
            number: validBankAccountData.number,
            sortCode: validBankAccountData.sortCode
          });
          expect(newBankAccount).toMatchObject(expect.objectContaining({ ...validBankAccountData }));
        });

        describe("and a third request is being made with same bank account data as second", () => {
          const validBankAccountData = {
            name: "Paris Kolovos",
            number: "********",
            sortCode: "32-21-57",
            truelayerProviderId: "mock-payments-gb-redirect"
          };

          beforeAll(async () => {
            expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(2);

            response = await request(app)
              .post("/api/m2m/bank-accounts")
              .send({
                bankAccount: validBankAccountData
              })
              .set("external-user-id", user.id)
              .set("Accept", "application/json");
          });

          it("should return 200", () => {
            expect(response.status).toEqual(200);
          });

          it("should NOT create a new bank account", async () => {
            expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(2);
          });

          describe("and a fourth request is being made with same bank account data as second except owner", () => {
            let secondUser: UserDocument;
            const validBankAccountData = {
              name: "Paris Kolovos",
              number: "********",
              sortCode: "32-21-57",
              truelayerProviderId: "mock-payments-gb-redirect"
            };

            beforeAll(async () => {
              secondUser = await buildUser({}, false);
              expect(await BankAccount.countDocuments({ owner: secondUser.id })).toEqual(0);

              response = await request(app)
                .post("/api/m2m/bank-accounts")
                .send({
                  bankAccount: validBankAccountData
                })
                .set("external-user-id", secondUser.id)
                .set("Accept", "application/json");
            });

            it("should return 200", () => {
              expect(response.status).toEqual(200);
            });

            it("should create a new bank account", async () => {
              expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(2);
              expect(await BankAccount.countDocuments({ owner: secondUser.id })).toEqual(1);

              const newBankAccount = await BankAccount.findOne({
                owner: secondUser.id,
                number: validBankAccountData.number,
                sortCode: validBankAccountData.sortCode
              });
              expect(newBankAccount).toMatchObject(expect.objectContaining({ ...validBankAccountData }));
            });

            it("should exist two bank accounts for two users wih same number, sortCode", async () => {
              expect(
                await BankAccount.countDocuments({
                  number: validBankAccountData.number,
                  sortCode: validBankAccountData.sortCode
                })
              ).toEqual(2);
            });
          });
        });
      });
    });

    describe("when user trying to recreate a deactivated account", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([TRUELAYER_PROVIDER]);

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );
        // create a deactivated account for user
        await buildBankAccount({ ...validBankAccountData, owner: user.id, active: false });
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(1);
        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // create body with all parameters required except 'param'
            {
              bankAccount: validBankAccountData,
              bankLinkedFrom: "my_account"
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should reactivate the bank account without creating a new document document", async () => {
        const newBankAccounts = await BankAccount.find({ owner: user._id });
        expect(newBankAccounts.length).toEqual(1);
        expect(newBankAccounts[0]).toMatchObject(
          expect.objectContaining({ ...validBankAccountData, active: true })
        );
      });

      it("should emit bankAccountLinking event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.bankAccountLinking.eventId,
          expect.objectContaining({ id: user.id }),
          {
            bankLinkedFrom: "my account",
            isFirst: false,
            hasSettledRewards: false,
            truelayerId: validBankAccountData.truelayerProviderId,
            banks: [validBankAccountData.truelayerProviderId],
            hasUsedGift: false
          }
        );
      });

      it("should return 200", () => {
        expect(response.status).toEqual(200);
      });
    });

    describe("when user trying to recreate a deactivated account and has an accepted reward", () => {
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([TRUELAYER_PROVIDER]);

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );
        // create a deactivated account for user
        await buildBankAccount({ ...validBankAccountData, owner: user.id, active: false });
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(1);

        await buildReward({
          referral: user.id,
          targetUser: user.id,
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          },
          asset: "equities_uk"
        });

        await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // create body with all parameters required except 'param'
            {
              bankAccount: validBankAccountData,
              bankLinkedFrom: "my_account"
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should emit bankAccountLinking event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.bankAccountLinking.eventId,
          expect.objectContaining({ id: user.id }),
          {
            bankLinkedFrom: "my account",
            isFirst: false,
            hasSettledRewards: true,
            truelayerId: validBankAccountData.truelayerProviderId,
            banks: [validBankAccountData.truelayerProviderId],
            hasUsedGift: false
          }
        );
      });
    });

    describe("when user trying to deactivate one of their bank accounts", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };
      const secondAccountValidBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([TRUELAYER_PROVIDER]);

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );
        await buildBankAccount({
          ...validBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: validBankAccountData.truelayerProviderId } }
        });
        await buildBankAccount({
          ...secondAccountValidBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: secondAccountValidBankAccountData.truelayerProviderId } }
        });
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(2);
        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // create body with all parameters required except 'param'
            {
              bankAccount: { ...validBankAccountData, active: false },
              bankLinkedFrom: "my_account"
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should exist one active bank account", async () => {
        const bankAccounts = await BankAccount.find({ owner: user._id, active: true });
        expect(bankAccounts.length).toEqual(1);
        expect(bankAccounts[0]).toMatchObject(
          expect.objectContaining({ ...secondAccountValidBankAccountData, active: true })
        );
      });

      it("should emit a bank account removed event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.bankAccountRemoval.eventId,
          expect.objectContaining({ id: user.id }),
          {
            truelayerId: validBankAccountData.truelayerProviderId,
            banks: [secondAccountValidBankAccountData.truelayerProviderId]
          }
        );
      });
    });

    describe("when user trying to deactivate his last active account", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );
        // create a deactivated account for user
        await buildBankAccount({
          ...validBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: validBankAccountData.truelayerProviderId } }
        });
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(1);
        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // create body with all parameters required except 'param'
            {
              bankAccount: { ...validBankAccountData, active: false },
              bankLinkedFrom: "my_account"
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 500", () => {
        expect(response.status).toEqual(500);
      });

      it("should exist one active bank account", async () => {
        const bankAccounts = await BankAccount.find({ owner: user._id });
        expect(bankAccounts.length).toEqual(1);
        expect(bankAccounts[0]).toMatchObject(expect.objectContaining({ ...validBankAccountData, active: true }));
      });
    });

    describe("when user trying to deactivate the account they're currently subscribed with", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );

        // Create two bank accounts for the user
        const subscribedBankAccount = await buildBankAccount({
          ...validBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: validBankAccountData.truelayerProviderId } }
        });
        await buildBankAccount({
          owner: user.id,
          active: true
        });

        // User is subscribed with one of the two bank accounts
        const mandate = await buildMandate({ owner: user.id, bankAccount: subscribedBankAccount.id });
        await buildSubscription({
          owner: user.id,
          category: "DirectDebitSubscription",
          mandate: mandate.id,
          price: "free_monthly"
        });

        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // User tries to deactivate the bank account they're currently subscribed with
            {
              bankAccount: { ...validBankAccountData, active: false },
              bankLinkedFrom: "my_account"
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 500", () => {
        expect(response.status).toEqual(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "User is currently subscribed with that bank account!",
              description: "An error occurred"
            }
          })
        );
      });

      it("should not deactivate any of the two bank accounts", async () => {
        const bankAccounts = await BankAccount.find({ owner: user._id });
        expect(bankAccounts.length).toEqual(2);
        expect(bankAccounts).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              ...validBankAccountData,
              active: true
            }),
            expect.objectContaining({
              active: true
            })
          ])
        );
      });
    });

    describe("when user trying to deactivate the account they have a monthly top-up with", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );

        // Create two bank accounts for the user
        const monthlyTopUpBankAccount = await buildBankAccount({
          ...validBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: validBankAccountData.truelayerProviderId } }
        });
        await buildBankAccount({
          owner: user.id,
          active: true
        });

        // User has a monthly top-up with one of the two bank accounts
        const mandate = await buildMandate({ owner: user.id, bankAccount: monthlyTopUpBankAccount.id });
        await buildTopUpAutomation({
          owner: user.id,
          category: "TopUpAutomation",
          mandate: mandate.id
        });

        response = await request(app)
          .post("/api/m2m/bank-accounts")
          .send(
            // User tries to deactivate the bank account they're currently subscribed with
            {
              bankAccount: { ...validBankAccountData, active: false },
              bankLinkedFrom: "my_account"
            }
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 500", () => {
        expect(response.status).toEqual(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "User has a recurring top-up with that bank account!",
              description: "An error occurred"
            }
          })
        );
      });

      it("should not deactivate any of the two bank accounts", async () => {
        const bankAccounts = await BankAccount.find({ owner: user._id });
        expect(bankAccounts.length).toEqual(2);
        expect(bankAccounts).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              ...validBankAccountData,
              active: true
            }),
            expect.objectContaining({
              active: true
            })
          ])
        );
      });
    });
  });

  describe("POST /bank-accounts/:id/deactivate", () => {
    const TRUELAYER_PROVIDER = buildProviderType({ provider_id: "mock-payments-gb-redirect" });

    describe("when user trying to deactivate one of their bank accounts", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };
      const secondAccountValidBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([TRUELAYER_PROVIDER]);

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );
        const bankAccount = await buildBankAccount({
          ...validBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: validBankAccountData.truelayerProviderId } }
        });
        await buildBankAccount({
          ...secondAccountValidBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: secondAccountValidBankAccountData.truelayerProviderId } }
        });
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(2);
        response = await request(app)
          .post(`/api/m2m/bank-accounts/${bankAccount._id.toString()}/deactivate`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should exist one active bank account", async () => {
        const bankAccounts = await BankAccount.find({ owner: user._id, active: true });
        expect(bankAccounts.length).toEqual(1);
        expect(bankAccounts[0]).toMatchObject(
          expect.objectContaining({ ...secondAccountValidBankAccountData, active: true })
        );
      });

      it("should emit a bank account removed event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.bankAccountRemoval.eventId,
          expect.objectContaining({ id: user.id }),
          {
            truelayerId: validBankAccountData.truelayerProviderId,
            banks: [secondAccountValidBankAccountData.truelayerProviderId]
          }
        );
      });
    });

    describe("when user trying to deactivate his last active account", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "addBankAccount").mockResolvedValue(
          buildWealthkernelBankAccountResponse({
            id: faker.string.uuid()
          })
        );
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBankAccounts").mockResolvedValue([]);

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );
        // create a deactivated account for user
        const bankAccount = await buildBankAccount({
          ...validBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: validBankAccountData.truelayerProviderId } }
        });
        expect(await BankAccount.countDocuments({ owner: user.id })).toEqual(1);
        response = await request(app)
          .post(`/api/m2m/bank-accounts/${bankAccount._id.toString()}/deactivate`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 500", () => {
        expect(response.status).toEqual(500);
      });

      it("should exist one active bank account", async () => {
        const bankAccounts = await BankAccount.find({ owner: user._id });
        expect(bankAccounts.length).toEqual(1);
        expect(bankAccounts[0]).toMatchObject(expect.objectContaining({ ...validBankAccountData, active: true }));
      });
    });

    describe("when user trying to deactivate the account they're currently subscribed with", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );

        // Create two bank accounts for the user
        const subscribedBankAccount = await buildBankAccount({
          ...validBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: validBankAccountData.truelayerProviderId } }
        });
        await buildBankAccount({
          owner: user.id,
          active: true
        });

        // User is subscribed with one of the two bank accounts
        const mandate = await buildMandate({ owner: user.id, bankAccount: subscribedBankAccount.id });
        await buildSubscription({
          owner: user.id,
          category: "DirectDebitSubscription",
          mandate: mandate.id,
          price: "free_monthly"
        });

        response = await request(app)
          .post(`/api/m2m/bank-accounts/${subscribedBankAccount._id.toString()}/deactivate`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 500", () => {
        expect(response.status).toEqual(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "User is currently subscribed with that bank account!",
              description: "An error occurred"
            }
          })
        );
      });

      it("should not deactivate any of the two bank accounts", async () => {
        const bankAccounts = await BankAccount.find({ owner: user._id });
        expect(bankAccounts.length).toEqual(2);
        expect(bankAccounts).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              ...validBankAccountData,
              active: true
            }),
            expect.objectContaining({
              active: true
            })
          ])
        );
      });
    });

    describe("when user trying to deactivate the account they have a monthly top-up with", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const validBankAccountData = {
        name: "Paris Kolovos",
        number: "********",
        sortCode: "01-21-32",
        truelayerProviderId: "mock-payments-gb-redirect"
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser(
          { firstName: "Paris", lastName: "Kolovos", providers: { wealthkernel: { id: "WK-PARTY-ID" } } },
          false
        );

        // Create two bank accounts for the user
        const monthlyTopUpBankAccount = await buildBankAccount({
          ...validBankAccountData,
          owner: user.id,
          active: true,
          providers: { truelayer: { bankId: validBankAccountData.truelayerProviderId } }
        });
        await buildBankAccount({
          owner: user.id,
          active: true
        });

        // User has a monthly top-up with one of the two bank accounts
        const mandate = await buildMandate({ owner: user.id, bankAccount: monthlyTopUpBankAccount.id });
        await buildTopUpAutomation({
          owner: user.id,
          category: "TopUpAutomation",
          mandate: mandate.id
        });

        response = await request(app)
          .post(`/api/m2m/bank-accounts/${monthlyTopUpBankAccount._id.toString()}/deactivate`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 500", () => {
        expect(response.status).toEqual(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "User has a recurring top-up with that bank account!",
              description: "An error occurred"
            }
          })
        );
      });

      it("should not deactivate any of the two bank accounts", async () => {
        const bankAccounts = await BankAccount.find({ owner: user._id });
        expect(bankAccounts.length).toEqual(2);
        expect(bankAccounts).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              ...validBankAccountData,
              active: true
            }),
            expect.objectContaining({
              active: true
            })
          ])
        );
      });
    });
  });

  describe("POST /bank-accounts/initiate-bank-linking", () => {
    describe("when user requests initiation of bank linking without passing a bankId", () => {
      const REDIRECT_URI = "https://some-redirect-uri";
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();

        jest
          .spyOn(GoCardlessDataService.Instance, "createEndUserAgreement")
          .mockResolvedValue(buildGoCardlessEndUserAgreementResponse());
        jest
          .spyOn(GoCardlessDataService.Instance, "createRequisition")
          .mockResolvedValue(buildGoCardlessRequisitionResponse({ link: REDIRECT_URI }));

        const user = await buildUser(
          { residencyCountry: "GR", companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE },
          false
        );

        response = await request(app)
          .post("/api/m2m/bank-accounts/initiate-bank-linking")
          .send({})
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should return 400", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Client has not passed bankId",
              description: "Operation failed"
            }
          })
        );
      });
    });

    describe("when user requests initiation of bank linking", () => {
      const REDIRECT_URI = "https://some-redirect-uri";
      const REQUISITION_ID = faker.string.uuid();
      const REQUISITION_REFERENCE = faker.string.uuid();

      let response: supertest.Response;
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        jest
          .spyOn(GoCardlessDataService.Instance, "createEndUserAgreement")
          .mockResolvedValue(buildGoCardlessEndUserAgreementResponse());
        jest.spyOn(GoCardlessDataService.Instance, "createRequisition").mockResolvedValue(
          buildGoCardlessRequisitionResponse({
            id: REQUISITION_ID,
            link: REDIRECT_URI,
            reference: REQUISITION_REFERENCE
          })
        );

        user = await buildUser(
          { residencyCountry: "GR", companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE },
          false
        );

        response = await request(app)
          .post("/api/m2m/bank-accounts/initiate-bank-linking")
          .send({
            bankId: "piraeus_bank"
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should create a requisition document", async () => {
        const requisition = await Requisition.findOne({ owner: user.id });
        expect(requisition).toEqual(
          expect.objectContaining({
            reference: REQUISITION_REFERENCE,
            providers: {
              gocardlessData: {
                id: REQUISITION_ID
              }
            }
          })
        );
      });

      it("should return 200 with a redirect URI", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject({ redirectUri: REDIRECT_URI });
      });
    });

    describe("when user requests initiation of bank linking with redirect uri state", () => {
      const REDIRECT_URI = "https://some-redirect-uri";
      const REQUISITION_ID = faker.string.uuid();
      const REQUISITION_REFERENCE = faker.string.uuid();
      const AGREEMENT_ID = faker.string.uuid();
      // This is the state that will be passed to the return redirect url after the GC flow finishes
      const REDIRECT_URI_STATE = "__originModal_deposit__redirectPage_accounts";
      const BANK_ID = "piraeus_bank";

      let response: supertest.Response;
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        jest
          .spyOn(GoCardlessDataService.Instance, "createEndUserAgreement")
          .mockResolvedValue(buildGoCardlessEndUserAgreementResponse({ id: AGREEMENT_ID }));
        jest.spyOn(GoCardlessDataService.Instance, "createRequisition").mockResolvedValue(
          buildGoCardlessRequisitionResponse({
            id: REQUISITION_ID,
            link: REDIRECT_URI,
            reference: REQUISITION_REFERENCE
          })
        );

        user = await buildUser(
          { residencyCountry: "GR", companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE },
          false
        );

        response = await request(app)
          .post("/api/m2m/bank-accounts/initiate-bank-linking")
          .send({
            bankId: BANK_ID,
            redirectUriState: REDIRECT_URI_STATE
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should create a gocardless requisition", () => {
        expect(GoCardlessDataService.Instance.createRequisition).toHaveBeenCalledWith(
          user.id,
          "WEB",
          "PIRAEUS_PIRBGRAA",
          AGREEMENT_ID,
          REDIRECT_URI_STATE
        );
      });

      it("should create a requisition document", async () => {
        const requisition = await Requisition.findOne({ owner: user.id });
        expect(requisition).toEqual(
          expect.objectContaining({
            reference: REQUISITION_REFERENCE,
            providers: {
              gocardlessData: {
                id: REQUISITION_ID
              }
            }
          })
        );
      });

      it("should return 200 with a redirect URI", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject({ redirectUri: REDIRECT_URI });
      });
    });
  });
});
