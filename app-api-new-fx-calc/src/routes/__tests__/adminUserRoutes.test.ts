import request from "supertest";
import supertest from "supertest";
import app from "../../app";
import { KycStatusEnum, User, UserDocument, UserTypeEnum } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAccount,
  buildAddress,
  buildAssetTransaction,
  buildDepositCashTransaction,
  buildHoldingDTO,
  buildKycOperation,
  buildParticipant,
  buildPortfolio,
  buildRewardInvitation,
  buildRiskAssessment,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import { CreateUserData } from "../../types/requestBody";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { Participant, ParticipantDocument } from "../../models/Participant";
import { PortfolioModeEnum } from "../../models/Portfolio";
import { auth0ManagementClient } from "../../external-services/auth0ManagementService";
import { ProviderEnum } from "../../configs/providersConfig";
import { Account } from "../../models/Account";
import { Subscription } from "../../models/Subscription";
import { KycOperation } from "../../models/KycOperation";
import { MixpanelAccountStatusEnum } from "../../external-services/segmentAnalyticsService";
import { WealthkernelService } from "../../external-services/wealthkernelService";
import { SumsubService } from "../../external-services/sumsubService";

describe("AdminUserRoutes", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("AdminUserRoutes"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  /**
   * GET
   */
  describe("/users", () => {
    it("should return status 400 when 'page' query param is invalid", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/users?page=esa&@&pageSize=50")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'page' , should be numeric"
          }
        })
      );
    });

    it("should return status 400 when 'pageSize' query param is invalid", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/users?page=1&pageSize=*@D")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'pageSize' , should be numeric"
          }
        })
      );
    });

    it("(should return status 400 when 'pageSize' query param is missing while 'page' query param is valid", async () => {
      const response = await request(app).get("/api/admin/m2m/users?page=1").set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'pageSize' is required"
          }
        })
      );
    });

    it("should return status 400 when 'populatePortfolios' query param is invalid", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/users?populatePortfolios=2asda")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'populatePortfolios' , should be boolean"
          }
        })
      );
    });

    it("should return status 400 when 'populateKycOperation' query param is invalid", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/users?populateKycOperation=2asda")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'populateKycOperation' , should be boolean"
          }
        })
      );
    });

    it("should return 400 when 'role' query param is invalid", async () => {
      const users = await Promise.all([
        buildUser({ role: [UserTypeEnum.ADMIN] }),
        buildUser({ role: [UserTypeEnum.INVESTOR] })
      ]);
      await Promise.all(users.map(({ id }) => buildPortfolio({ owner: id })));

      const response = await request(app)
        .get("/api/admin/m2m/users?role=GARBAGE")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: `Param 'role' has invalid value 'GARBAGE', must be one of [${Object.values(UserTypeEnum)}]`
          }
        })
      );
    });

    it("should return 400 when 'role' query param is invalid", async () => {
      const users = await Promise.all([
        buildUser({ role: [UserTypeEnum.ADMIN] }),
        buildUser({ role: [UserTypeEnum.INVESTOR] })
      ]);
      await Promise.all(users.map(({ id }) => buildPortfolio({ owner: id })));

      const response = await request(app)
        .get("/api/admin/m2m/users?role=GARBAGE")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: `Param 'role' has invalid value 'GARBAGE', must be one of [${Object.values(UserTypeEnum)}]`
          }
        })
      );
    });

    it("should return 400 when 'email' query param is invalid", async () => {
      const users = await Promise.all([
        buildUser({ role: [UserTypeEnum.ADMIN] }),
        buildUser({ role: [UserTypeEnum.INVESTOR] })
      ]);
      await Promise.all(users.map(({ id }) => buildPortfolio({ owner: id })));
      const invalidEmail = "sfsdfs32";
      const response = await request(app)
        .get(`/api/admin/m2m/users?email=${invalidEmail}`)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: `${invalidEmail} is not an email`
          }
        })
      );
    });

    it("should return status 200 with paginated response containing users array when pagination query params are both valid", async () => {
      await Promise.all([buildUser(), buildUser()]);

      const response = await request(app)
        .get("/api/admin/m2m/users?pageSize=1&page=2")
        .set("Accept", "application/json");

      const expectedData = await User.find({})
        .skip((2 - 1) * 1)
        .limit(1);

      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).users).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users (with populated portfolios) array when 'populatePortfolios' query param is true", async () => {
      const users = await Promise.all([buildUser(), buildUser()]);
      await Promise.all(users.map(({ id }) => buildPortfolio({ owner: id })));

      const response = await request(app)
        .get("/api/admin/m2m/users?populatePortfolios=true")
        .set("Accept", "application/json");

      const expectedData = await User.find({}).populate([
        {
          path: "portfolios",
          populate: {
            path: "currentTicker"
          }
        }
      ]);

      expect(expectedData.length).toEqual(2);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users (with populated kycOperation) array when 'populateKycOperation' query param is true", async () => {
      const users = await Promise.all([buildUser(), buildUser()]);
      await Promise.all(users.map(({ id }) => buildKycOperation({ owner: id })));

      const response = await request(app)
        .get("/api/admin/m2m/users?populateKycOperation=true")
        .set("Accept", "application/json");

      const expectedData = await User.find({}).populate([
        {
          path: "kycOperation"
        }
      ]);

      expect(expectedData.length).toEqual(2);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users (with populated accounts) array when 'populateAccounts' query param is true", async () => {
      const users = await Promise.all([buildUser(), buildUser()]);
      await Promise.all(users.map(({ id }) => buildAccount({ owner: id })));

      const response = await request(app)
        .get("/api/admin/m2m/users?populateAccounts=true")
        .set("Accept", "application/json");

      const expectedData = await User.find({}).populate([
        {
          path: "accounts"
        }
      ]);

      expect(expectedData.length).toEqual(2);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users array when 'hasRequestedDeletion' query param is 'undefined'", async () => {
      await Promise.all([
        buildUser({ email: "<EMAIL>" }),
        buildUser({ email: "<EMAIL>" })
      ]);

      const response = await request(app).get("/api/admin/m2m/users").set("Accept", "application/json");

      const expectedData = await User.find();

      expect(expectedData.length).toEqual(2);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users array when 'hasRequestedDeletion' query param is 'false'", async () => {
      await Promise.all([
        buildUser({ email: "<EMAIL>" }),
        buildUser({ email: "<EMAIL>" })
      ]);

      const response = await request(app)
        .get("/api/admin/m2m/users?hasRequestedDeletion=false")
        .set("Accept", "application/json");

      const expectedData = await User.find({ email: "<EMAIL>" });

      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users array when 'hasRequestedDeletion' query param is 'true'", async () => {
      await Promise.all([
        buildUser({ email: "<EMAIL>" }),
        buildUser({ email: "<EMAIL>" })
      ]);

      const response = await request(app)
        .get("/api/admin/m2m/users?hasRequestedDeletion=true")
        .set("Accept", "application/json");

      const expectedData = await User.find({ email: "<EMAIL>" });

      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users array when 'hasAcceptedTerms' query param is 'undefined'", async () => {
      await Promise.all([buildUser({ hasAcceptedTerms: true }), buildUser({ hasAcceptedTerms: false })]);

      const response = await request(app).get("/api/admin/m2m/users").set("Accept", "application/json");

      const expectedData = await User.find();

      expect(expectedData.length).toEqual(2);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users array when 'hasAcceptedTerms' query param is 'false'", async () => {
      await Promise.all([buildUser({ hasAcceptedTerms: true }), buildUser({ hasAcceptedTerms: false })]);

      const response = await request(app)
        .get("/api/admin/m2m/users?hasAcceptedTerms=false")
        .set("Accept", "application/json");

      const expectedData = await User.find({ hasAcceptedTerms: false });

      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users array when 'hasAcceptedTerms' query param is 'true'", async () => {
      await Promise.all([buildUser({ hasAcceptedTerms: false }), buildUser({ hasAcceptedTerms: true })]);

      const response = await request(app)
        .get("/api/admin/m2m/users?hasAcceptedTerms=true")
        .set("Accept", "application/json");

      const expectedData = await User.find({ hasAcceptedTerms: true });

      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users array when 'hasSubmittedRequiredInfo' query param is 'true'", async () => {
      await Promise.all([buildUser(), buildUser({ submittedRequiredInfoAt: undefined })]);

      const response = await request(app)
        .get("/api/admin/m2m/users?hasSubmittedRequiredInfo=true")
        .set("Accept", "application/json");

      const expectedData = await User.find({ submittedRequiredInfoAt: { $exists: true } });

      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users that failed Kyc after the specifed date", async () => {
      const KYC_FAILED_DATE = new Date(2024 - 3 - 12);

      const users = await Promise.all([buildUser(), buildUser({ kycFailedAt: KYC_FAILED_DATE })]);
      await Promise.all(users.map(({ id }) => buildAccount({ owner: id })));

      const response = await request(app)
        .get("/api/admin/m2m/users?kycFailedAfter=" + KYC_FAILED_DATE.toISOString())
        .set("Accept", "application/json");

      const expectedData = await User.find({ kycFailedAt: { $gte: KYC_FAILED_DATE } });

      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users array when 'kycStatus' query param is 'failed'", async () => {
      const users = await Promise.all([buildUser(), buildUser({ kycStatus: KycStatusEnum.FAILED })]);
      await Promise.all(users.map(({ id }) => buildKycOperation({ owner: id })));

      const response = await request(app)
        .get("/api/admin/m2m/users?kycStatus=failed")
        .set("Accept", "application/json");

      const expectedData = await User.find({ kycStatus: KycStatusEnum.FAILED });

      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users (sorted) array when 'sort' query param is '-createdAt'", async () => {
      await Promise.all([buildUser(), buildUser()]);

      const response = await request(app)
        .get("/api/admin/m2m/users?sort=-createdAt")
        .set("Accept", "application/json");

      const expectedData = await User.find({}).sort({ createdAt: -1 });

      expect(expectedData.length).toEqual(2);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users (sorted) array when 'sort' query param is '-lastLogin'", async () => {
      await Promise.all([buildUser(), buildUser()]);

      const response = await request(app)
        .get("/api/admin/m2m/users?sort=-lastLogin")
        .set("Accept", "application/json");

      const expectedData = await User.find({}).sort({ lastLogin: -1 });

      expect(expectedData.length).toEqual(2);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 with response containing users (sorted) array when 'role' query param has value 'INVESTOR'", async () => {
      const users = await Promise.all([
        buildUser({ role: [UserTypeEnum.ADMIN] }),
        buildUser({ role: [UserTypeEnum.INVESTOR] })
      ]);
      await Promise.all(users.map(({ id }) => buildPortfolio({ owner: id })));

      const response = await request(app)
        .get("/api/admin/m2m/users?role=INVESTOR")
        .set("Accept", "application/json");

      const expectedData = await User.find({ role: UserTypeEnum.INVESTOR as UserTypeEnum });
      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });
  });

  describe("/users/:id", () => {
    it("should return 400 when 'id' path param is invalid", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/users/asd!3245asd")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for 'id'"
          }
        })
      );
    });

    it("should return status 200 with object containing user", async () => {
      const user = await buildUser();
      await buildPortfolio({ owner: user.id });
      await buildAccount({ owner: user.id });
      await buildAddress({ owner: user.id });
      await buildRiskAssessment({ owner: user.id });
      await buildKycOperation({ owner: user.id });

      const response = await request(app)
        .get(`/api/admin/m2m/users/${user._id}`)
        .set("Accept", "application/json");
      const expectedData = await User.find({ _id: user._id }).populate(
        "addresses bankAccounts portfolios accounts kycOperation latestRiskAssessment"
      );
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should succeed to update user with status 200 returning object containing updated user", async () => {
      const user = await buildUser({
        isUKTaxResident: true,
        dateOfBirth: new Date("1981-02-12"),
        viewedWelcomePage: false
      });

      const userUpdateData = {
        firstName: "Paris",
        lastName: "Kolovos",
        isUKTaxResident: false,
        dateOfBirth: "1994-05-11T00:00:00.000Z",
        nationalities: ["GR"],
        taxResidency: {
          countryCode: "GB",
          proofType: "NINO",
          value: "test"
        },
        referredByEmail: "<EMAIL>",
        viewedWelcomePage: true
      };

      const response = await request(app)
        .post(`/api/admin/m2m/users/${user._id}`)
        .send(userUpdateData)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      // reparse object to resolve dates fields format problem
      const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
      Object.entries(userUpdateData).forEach(([key, value]) => expect(updatedUser[key]).toEqual(value));
    });

    [
      "dateOfBirth",
      "nationalities",
      "taxResidency",
      "isUKTaxResident",
      "viewedWelcomePage",
      "amlScreening"
    ].forEach((key) =>
      it(`should return status 400 for invalid parameter '${key}'`, async () => {
        const user = await buildUser();
        const response = await request(app)
          .post(`/api/admin/m2m/users/${user._id}`)
          .send({ [key]: faker.string.uuid() })
          .set("Accept", "application/json");
        expect(response.status).toEqual(400);
      })
    );

    it("should return status 400 for invalid parameter 'nationalities'", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post(`/api/admin/m2m/users/${user._id}`)
        .set("external-user-id", user._id)
        .send({ nationalities: [faker.string.sample()] })
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });
  });

  describe("/users/referrals/count", () => {
    let response: supertest.Response;
    let referrer: UserDocument;

    it("should return status 400 for invalid parameter userEmail", async () => {
      const invalidEmail = "invalidEmail";
      const response = await request(app)
        .get(`/api/admin/m2m/users/referrals/count?userEmail=${invalidEmail}`)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: `${invalidEmail} is not an email`
          }
        })
      );
    });
    describe("and request succeeds", () => {
      beforeEach(async () => {
        referrer = await buildUser();
        const referrals = await Promise.all([
          buildUser({ referredByEmail: referrer.email }),
          buildUser({ referredByEmail: referrer.email })
        ]);
        await Promise.all([
          buildPortfolio({
            mode: PortfolioModeEnum.REAL,
            owner: referrals[0].id,
            cash: { GBP: { available: 1000, reserved: 0, settled: 1000 } },
            holdings: [await buildHoldingDTO()]
          })
        ]);

        await buildDepositCashTransaction({
          owner: referrals[0].id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        });

        response = await request(app)
          .get(`/api/admin/m2m/users/referrals/count?userEmail=${referrer.email}`)
          .set("Accept", "application/json");
      });
      it("should return status 200", async () => {
        expect(response.status).toEqual(200);
      });
      it("should return 1 referral only for user with real portfolio (2 users exist)", async () => {
        expect(JSON.parse(response.text).referralsCount).toEqual(1);
      });
    });
  });

  /**
   * POST
   */
  describe("/users/:id", () => {
    describe("and tries to update user's 'referredByEmail' while being undefined", () => {
      let user: UserDocument;
      let response: supertest.Response;
      let userUpdateData: Partial<UserDocument>;

      beforeAll(async () => {
        user = await buildUser({
          referredByEmail: "<EMAIL>"
        });

        userUpdateData = {
          referredByEmail: ""
        };

        response = await request(app)
          .post(`/api/admin/m2m/users/${user._id}`)
          .set("external-user-id", user._id)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("the 'referredByEmail' field should be undefined", async () => {
        expect(response.status).toEqual(200);

        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).not.toBeDefined();
      });
    });
  });

  describe("/users/email/:email", () => {
    let user: UserDocument;
    let response: supertest.Response;

    describe("and a user exists for given email", () => {
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: "<EMAIL>",
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "test-auth0-id"
        }
      };

      beforeEach(async () => {
        user = await buildUser({
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          email: userUpdateData.email,
          auth0: userUpdateData.auth0
        });

        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/admin/m2m/users/email/${user.email}`)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing updated user", async () => {
        expect(response.status).toEqual(200);
        const updatedUser = (await User.findById(user._id)) as UserDocument;

        expect(updatedUser).toEqual(
          expect.objectContaining({
            email: userUpdateData.email,
            emailVerified: userUpdateData.emailVerified === "true",
            role: expect.arrayContaining(userUpdateData.role),
            lastLogin,
            auth0: user.auth0,
            createdAt: user.createdAt,
            lastLoginPlatform: expect.not.stringContaining("") // Last login platform should be empty for Web
          })
        );
      });

      it("should emit a 'login' user event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.logIn.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ justDownloadedApp: false })
        );
      });

      it("should NOT create a new user", async () => {
        const userCount = await User.find({}).countDocuments();
        expect(userCount).toEqual(1);
      });
    });

    describe("and a user exists for given email and they can unlock a free share", () => {
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: "<EMAIL>",
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "test-auth0-id"
        }
      };

      beforeEach(async () => {
        user = await buildUser({
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          email: userUpdateData.email,
          auth0: userUpdateData.auth0,
          referredByEmail: faker.internet.email()
        });
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: [
            {
              percentage: 100,
              assetCommonId: "equities_us"
            }
          ]
        });

        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/admin/m2m/users/email/${user.email}`)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing canUnlockFreeShare set to true", async () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              email: userUpdateData.email,
              canUnlockFreeShare: true
            })
          ]
        });
      });
    });

    describe("and a user exists for given email and they have completed portfolio conversion status & holdings", () => {
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: "<EMAIL>",
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "test-auth0-id"
        }
      };

      beforeEach(async () => {
        user = await buildUser({
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          email: userUpdateData.email,
          auth0: userUpdateData.auth0,
          referredByEmail: faker.internet.email(),
          portfolioConversionStatus: "completed"
        });

        await buildPortfolio({
          owner: user.id,
          holdings: [await buildHoldingDTO(true, "equities_global_clean_energy", 1)]
        });

        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/admin/m2m/users/email/${user.email}`)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing completed portfolioConversionStatus", async () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              email: userUpdateData.email,
              portfolioConversionStatus: "completed"
            })
          ]
        });
      });
    });

    describe("and a user exists for given email and they have inProgress portfolio conversion status", () => {
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: "<EMAIL>",
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "test-auth0-id"
        }
      };

      beforeEach(async () => {
        user = await buildUser({
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          email: userUpdateData.email,
          auth0: userUpdateData.auth0,
          referredByEmail: faker.internet.email(),
          portfolioConversionStatus: "inProgress"
        });

        await buildPortfolio({ owner: user.id });

        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/admin/m2m/users/email/${user.email}`)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing inProgress portfolioConversionStatus", async () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              email: userUpdateData.email,
              portfolioConversionStatus: "inProgress"
            })
          ]
        });
      });
    });

    describe("and a user exists for given email and they have notStarted portfolio conversion status & a pending transaction", () => {
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: "<EMAIL>",
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "test-auth0-id"
        }
      };

      beforeEach(async () => {
        user = await buildUser({
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          email: userUpdateData.email,
          auth0: userUpdateData.auth0,
          referredByEmail: faker.internet.email(),
          portfolioConversionStatus: "notStarted"
        });

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending"
        });

        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/admin/m2m/users/email/${user.email}`)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing inProgress portfolioConversionStatus", async () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              email: userUpdateData.email,
              portfolioConversionStatus: "inProgress"
            })
          ]
        });
      });
    });

    describe("and a user exists for given email and has previously logged in via mobile", () => {
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: "<EMAIL>",
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "test-auth0-id"
        }
      };

      beforeEach(async () => {
        user = await buildUser({
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          email: userUpdateData.email,
          auth0: userUpdateData.auth0,
          lastLoginPlatform: "ios"
        });

        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/admin/m2m/users/email/${user.email}`)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing updated user", async () => {
        expect(response.status).toEqual(200);
        const updatedUser = (await User.findById(user._id)) as UserDocument;

        expect(updatedUser).toEqual(
          expect.objectContaining({
            email: userUpdateData.email,
            emailVerified: userUpdateData.emailVerified === "true",
            role: expect.arrayContaining(userUpdateData.role),
            lastLogin,
            auth0: user.auth0,
            createdAt: user.createdAt,
            lastLoginPlatform: "ios" // Platform should not have changed from iOS
          })
        );
      });

      it("should emit a 'login' user event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.logIn.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ justDownloadedApp: false })
        );
      });

      it("should NOT create a new user", async () => {
        const userCount = await User.find({}).countDocuments();
        expect(userCount).toEqual(1);
      });
    });

    describe("and a user exists for given email but a new auth0 ID is passed", () => {
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: "<EMAIL>",
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "google-oauth2|test-google-auth0-id"
        }
      };

      beforeEach(async () => {
        user = await buildUser({
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          email: userUpdateData.email,
          auth0: {
            id: "email|test-auth0-id",
            email: "email|test-auth0-id"
          }
        });

        jest.spyOn(auth0ManagementClient.users, "link");
        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/admin/m2m/users/email/${user.email}`)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing updated user", async () => {
        expect(response.status).toEqual(200);
        const updatedUser = (await User.findById(user._id)) as UserDocument;

        // auth0.id and createdAt fields should not be updated, but lastLogin should
        expect(updatedUser).toEqual(
          expect.objectContaining({
            email: userUpdateData.email,
            emailVerified: userUpdateData.emailVerified === "true",
            role: expect.arrayContaining(userUpdateData.role),
            lastLogin: lastLogin,
            auth0: { id: user.auth0.id, email: user.auth0.email, google: userUpdateData.auth0.id },
            createdAt: user.createdAt
          })
        );
      });

      it("should emit a 'login' user event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.logIn.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ justDownloadedApp: false })
        );
      });

      it("should NOT create a new user", async () => {
        const userCount = await User.find({}).countDocuments();
        expect(userCount).toEqual(1);
      });

      it("should try to link accounts in Auth0", async () => {
        expect(auth0ManagementClient.users.link).toHaveBeenCalledWith(
          { id: user.auth0.id },
          {
            provider: "google-oauth2",
            user_id: "test-google-auth0-id"
          }
        );
      });
    });

    describe("and a user exists for given email, a new auth0 ID is passed but Auth0 linking fails", () => {
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: "<EMAIL>",
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "email|test-auth0-id"
        }
      };

      beforeEach(async () => {
        user = await buildUser({
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          email: userUpdateData.email,
          auth0: {
            id: "auth0|test-auth0-id",
            apple: "apple|test-apple-auth0-id"
          }
        });

        jest.spyOn(auth0ManagementClient.users, "link").mockImplementation(() => {
          throw new Error("Some error from the Auth0 API");
        });
        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/admin/m2m/users/email/${user.email}`)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing updated user", async () => {
        expect(response.status).toEqual(200);
        const updatedUser = (await User.findById(user._id)) as UserDocument;

        // We should not add the email Auth0 ID as linking failed
        expect(updatedUser).toEqual(
          expect.objectContaining({
            email: userUpdateData.email,
            emailVerified: userUpdateData.emailVerified === "true",
            role: expect.arrayContaining(userUpdateData.role),
            lastLogin: lastLogin,
            auth0: { id: user.auth0.id, apple: user.auth0.apple },
            createdAt: user.createdAt
          })
        );
      });

      it("should emit a 'login' user event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.logIn.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ justDownloadedApp: false })
        );
      });

      it("should NOT create a new user", async () => {
        const userCount = await User.find({}).countDocuments();
        expect(userCount).toEqual(1);
      });

      it("should try to link accounts in Auth0", async () => {
        expect(auth0ManagementClient.users.link).toHaveBeenCalledWith(
          { id: user.auth0.id },
          {
            provider: "email",
            user_id: "test-auth0-id"
          }
        );
      });
    });

    describe("and a user exists for given email but a different (but used before) auth0 ID is passed", () => {
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: "<EMAIL>",
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "email|test-auth0-id"
        }
      };

      beforeEach(async () => {
        user = await buildUser({
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          email: userUpdateData.email,
          auth0: {
            id: "auth0|test-auth0-id",
            apple: "apple|test-auth0-id",
            email: "email|test-auth0-id"
          }
        });

        jest.spyOn(auth0ManagementClient.users, "link");
        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/admin/m2m/users/email/${user.email}`)
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing updated user", async () => {
        expect(response.status).toEqual(200);
        const updatedUser = (await User.findById(user._id)) as UserDocument;

        // auth0.id and createdAt fields should not be updated, but lastLogin should
        expect(updatedUser).toEqual(
          expect.objectContaining({
            email: userUpdateData.email,
            emailVerified: userUpdateData.emailVerified === "true",
            role: expect.arrayContaining(userUpdateData.role),
            lastLogin: lastLogin,
            auth0: user.auth0,
            createdAt: user.createdAt
          })
        );
      });

      it("should emit a 'login' user event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.logIn.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ justDownloadedApp: false })
        );
      });

      it("should NOT create a new user", async () => {
        const userCount = await User.find({}).countDocuments();
        expect(userCount).toEqual(1);
      });

      it("should NOT try to link accounts in Auth0", async () => {
        expect(auth0ManagementClient.users.link).toBeCalledTimes(0);
      });
    });

    describe("and a user does NOT exist for given email", () => {
      const USER_EMAIL = "<EMAIL>";
      const lastLogin = new Date();

      const userUpdateData: CreateUserData = {
        email: USER_EMAIL,
        emailVerified: "true",
        role: [UserTypeEnum.INVESTOR],
        lastLogin: lastLogin.toISOString(),
        auth0: {
          id: "email|new-auth0-id"
        }
      };
      let user: UserDocument;

      beforeEach(async () => {
        jest.spyOn(eventEmitter, "emit");

        const userCount = await User.find({}).countDocuments();
        expect(userCount).toEqual(0);
      });

      describe("and referrer service returns a participant", () => {
        let participant: ParticipantDocument;
        let referrerParticipant: ParticipantDocument;

        beforeEach(async () => {
          referrerParticipant = await buildParticipant();
          participant = await buildParticipant({ email: USER_EMAIL, referrer: referrerParticipant.id });
          await participant.populate("referrer");

          response = await request(app)
            .post(`/api/admin/m2m/users/email/${userUpdateData.email}`)
            .send(userUpdateData)
            .set("Accept", "application/json");

          user = JSON.parse(response.text).data[0];
        });

        it("should create a new user", async () => {
          const userCount = await User.find({}).countDocuments();
          expect(userCount).toEqual(1);

          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser).toEqual(
            expect.objectContaining({
              email: userUpdateData.email,
              emailVerified: userUpdateData.emailVerified === "true",
              role: expect.arrayContaining(userUpdateData.role),
              auth0: { id: userUpdateData.auth0.id, email: userUpdateData.auth0.id },
              lastLogin,
              lastLoginPlatform: expect.not.stringContaining("") // Last login platform should be empty for Web
            })
          );
        });

        it("should return status 200", async () => {
          expect(response.status).toEqual(200);
        });

        it("should emit a 'signUp' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            1,
            events.user.signUp.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: referrerParticipant.email })
              })
            }),
            expect.objectContaining({
              referredStatus: "True"
            })
          );
        });

        it("should emit a 'whAccountStatusUpdate' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            2,
            events.user.whAccountStatusUpdate.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: referrerParticipant.email })
              })
            }),
            expect.objectContaining({
              accountStatus: MixpanelAccountStatusEnum.Pending
            })
          );
        });

        it("should update referredByEmail property of user", async () => {
          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser.referredByEmail).toEqual((participant.referrer as ParticipantDocument).email);
        });

        it("should emit 2 events", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(2);
        });
      });

      describe("and referrer service does not return a participant", () => {
        beforeEach(async () => {
          const participant = await Participant.findOne({ email: USER_EMAIL });
          expect(participant).toBeNull();

          response = await request(app)
            .post(`/api/admin/m2m/users/email/${userUpdateData.email}`)
            .send(userUpdateData)
            .set("Accept", "application/json");

          user = JSON.parse(response.text).data[0];
        });

        it("should create a new participant", async () => {
          const participantCount = await Participant.find({}).countDocuments();
          expect(participantCount).toEqual(1); // There was no participant before so now there is 1

          const participant: any = JSON.parse(JSON.stringify(await Participant.findOne({ email: USER_EMAIL })));
          expect(participant).toEqual(
            expect.objectContaining({
              email: USER_EMAIL,
              isAmbassador: false,
              participantRole: "BASIC",
              wlthdId: expect.anything()
            })
          );
        });

        it("should create a new user", async () => {
          const userCount = await User.find({}).countDocuments();
          expect(userCount).toEqual(1);

          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser).toEqual(
            expect.objectContaining({
              email: userUpdateData.email,
              emailVerified: userUpdateData.emailVerified === "true",
              role: expect.arrayContaining(userUpdateData.role),
              auth0: { id: userUpdateData.auth0.id, email: userUpdateData.auth0.id },
              lastLogin
            })
          );
        });

        it("should return status 200", async () => {
          expect(response.status).toEqual(200);
        });

        it("should emit a 'signUp' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            2,
            events.user.signUp.eventId,
            expect.objectContaining({ id: user.id }),
            expect.objectContaining({ referredStatus: "False" })
          );
        });

        it("should emit a 'whAccountStatusUpdate' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            3,
            events.user.whAccountStatusUpdate.eventId,
            expect.objectContaining({ id: user.id }),
            expect.objectContaining({
              accountStatus: MixpanelAccountStatusEnum.Pending
            })
          );
        });

        it("should NOT update referredByEmail property of user", async () => {
          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser.referredByEmail).toEqual(user.referredByEmail);
        });

        it("should emit a 'emailSubmitted' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            1,
            events.participant.emailSubmitted.eventId,
            expect.objectContaining({ email: user.email })
          );
        });

        it("should emit 3 total events", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(3);
        });
      });

      describe("and referrer service does not return a participant and there is a reward invite for the user", () => {
        let inviterParticipant: ParticipantDocument;

        beforeEach(async () => {
          const participant = await Participant.findOne({ email: USER_EMAIL });
          expect(participant).toBeNull();

          const inviter = await buildUser();
          inviterParticipant = await buildParticipant({ email: inviter.email });
          await buildRewardInvitation({
            targetUserEmail: USER_EMAIL,
            referrer: inviter._id
          });

          response = await request(app)
            .post(`/api/admin/m2m/users/email/${userUpdateData.email}`)
            .send(userUpdateData)
            .set("Accept", "application/json");

          user = JSON.parse(response.text).data[0];
        });

        it("should create a new participant", async () => {
          const participantCount = await Participant.find({}).countDocuments();
          expect(participantCount).toEqual(2); // There was a participant already so now there are 2

          const participant: any = JSON.parse(JSON.stringify(await Participant.findOne({ email: USER_EMAIL })));
          expect(participant).toEqual(
            expect.objectContaining({
              email: USER_EMAIL,
              isAmbassador: false,
              participantRole: "BASIC",
              wlthdId: expect.anything(),
              referrer: inviterParticipant.id
            })
          );
        });

        it("should create a new user", async () => {
          const userCount = await User.find({}).countDocuments();
          expect(userCount).toEqual(2);

          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser).toEqual(
            expect.objectContaining({
              email: userUpdateData.email,
              emailVerified: userUpdateData.emailVerified === "true",
              role: expect.arrayContaining(userUpdateData.role),
              auth0: { id: userUpdateData.auth0.id, email: userUpdateData.auth0.id },
              lastLogin
            })
          );
        });

        it("should return status 200", async () => {
          expect(response.status).toEqual(200);
        });

        it("should emit a 'signUp' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            2,
            events.user.signUp.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: inviterParticipant.email })
              })
            }),
            expect.objectContaining({ referredStatus: "True" })
          );
        });

        it("should emit a 'whAccountStatusUpdate' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            3,
            events.user.whAccountStatusUpdate.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: inviterParticipant.email })
              })
            }),
            expect.objectContaining({
              accountStatus: MixpanelAccountStatusEnum.Pending
            })
          );
        });

        it("should update referredByEmail property of user", async () => {
          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser.referredByEmail).toEqual(inviterParticipant.email);
        });

        it("should emit a 'emailSubmitted' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            1,
            events.participant.emailSubmitted.eventId,
            expect.objectContaining({ email: user.email })
          );
        });

        it("should emit 3 total events", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(3);
        });
      });

      describe("and referrer service does not return a participant and request includes a wlthd ID", () => {
        let referrerParticipant: ParticipantDocument;

        const wlthdId = "some-wlthd-id";

        beforeEach(async () => {
          // Build the referrer participant that owns the referral link
          referrerParticipant = await buildParticipant({
            wlthdId
          });

          // Ensure there is no participant already for the referred user
          const participant = await Participant.findOne({ email: USER_EMAIL });
          expect(participant).toBeNull();

          response = await request(app)
            .post(`/api/admin/m2m/users/email/${userUpdateData.email}`)
            .send({ ...userUpdateData, wlthd: wlthdId })
            .set("Accept", "application/json");

          user = JSON.parse(response.text).data[0];
        });

        it("should create a new participant", async () => {
          const participantCount = await Participant.find({}).countDocuments();
          expect(participantCount).toEqual(2); // There was a participant already so now there are 2

          const participant: any = JSON.parse(JSON.stringify(await Participant.findOne({ email: USER_EMAIL })));
          expect(participant).toEqual(
            expect.objectContaining({
              email: USER_EMAIL,
              referrer: referrerParticipant.id,
              isAmbassador: false,
              participantRole: "BASIC",
              wlthdId: expect.anything()
            })
          );
        });

        it("should create a new user", async () => {
          const userCount = await User.find({}).countDocuments();
          expect(userCount).toEqual(1);

          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser).toEqual(
            expect.objectContaining({
              email: userUpdateData.email,
              emailVerified: userUpdateData.emailVerified === "true",
              role: expect.arrayContaining(userUpdateData.role),
              auth0: { id: userUpdateData.auth0.id, email: userUpdateData.auth0.id },
              lastLogin
            })
          );
        });

        it("should return status 200", async () => {
          expect(response.status).toEqual(200);
        });

        it("should emit a 'signUp' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            2,
            events.user.signUp.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: referrerParticipant.email })
              })
            }),
            expect.objectContaining({ referredStatus: "True" })
          );
        });

        it("should emit a 'whAccountStatusUpdate' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            3,
            events.user.whAccountStatusUpdate.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: referrerParticipant.email })
              })
            }),
            expect.objectContaining({
              accountStatus: MixpanelAccountStatusEnum.Pending
            })
          );
        });

        it("should update referredByEmail property of user", async () => {
          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser.referredByEmail).toEqual(referrerParticipant.email);
        });

        it("should emit a 'emailSubmitted' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            1,
            events.participant.emailSubmitted.eventId,
            expect.objectContaining({ email: user.email })
          );
        });

        it("should emit 3 total events", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(3);
        });
      });

      describe("and referrer service does not return a participant, request includes a wlthd ID, and there is also a reward invite for the user", () => {
        let inviterParticipant: ParticipantDocument;

        const wlthdId = "some-wlthd-id";

        beforeEach(async () => {
          // Build the referrer participant that owns the referral link
          await buildParticipant({
            wlthdId
          });

          const inviter = await buildUser();
          inviterParticipant = await buildParticipant({ email: inviter.email });
          await buildRewardInvitation({
            targetUserEmail: USER_EMAIL,
            referrer: inviter._id
          });

          // Ensure there is no participant already for the referred user
          const participant = await Participant.findOne({ email: USER_EMAIL });
          expect(participant).toBeNull();

          response = await request(app)
            .post(`/api/admin/m2m/users/email/${userUpdateData.email}`)
            .send({ ...userUpdateData, wlthd: wlthdId })
            .set("Accept", "application/json");

          user = JSON.parse(response.text).data[0];
        });

        it("should create a new participant", async () => {
          const participantCount = await Participant.find({}).countDocuments();
          expect(participantCount).toEqual(3); // There was a participant already so now there are 2

          const participant: any = JSON.parse(JSON.stringify(await Participant.findOne({ email: USER_EMAIL })));
          expect(participant).toEqual(
            expect.objectContaining({
              email: USER_EMAIL,
              referrer: inviterParticipant.id,
              isAmbassador: false,
              participantRole: "BASIC",
              wlthdId: expect.anything()
            })
          );
        });

        it("should create a new user", async () => {
          const userCount = await User.find({}).countDocuments();
          expect(userCount).toEqual(2);

          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser).toEqual(
            expect.objectContaining({
              email: userUpdateData.email,
              emailVerified: userUpdateData.emailVerified === "true",
              role: expect.arrayContaining(userUpdateData.role),
              auth0: { id: userUpdateData.auth0.id, email: userUpdateData.auth0.id },
              lastLogin
            })
          );
        });

        it("should return status 200", async () => {
          expect(response.status).toEqual(200);
        });

        it("should emit a 'signUp' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            2,
            events.user.signUp.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: inviterParticipant.email })
              })
            }),
            expect.objectContaining({ referredStatus: "True" })
          );
        });

        it("should emit a 'whAccountStatusUpdate' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            3,
            events.user.whAccountStatusUpdate.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: inviterParticipant.email })
              })
            }),
            expect.objectContaining({
              accountStatus: MixpanelAccountStatusEnum.Pending
            })
          );
        });

        it("should update referredByEmail property of user", async () => {
          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser.referredByEmail).toEqual(inviterParticipant.email);
        });

        it("should emit a 'emailSubmitted' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            1,
            events.participant.emailSubmitted.eventId,
            expect.objectContaining({ email: user.email })
          );
        });

        it("should emit 3 total events", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(3);
        });
      });

      describe("and referrer service does not return a participant and request includes a grsf ID", () => {
        let referrerParticipant: ParticipantDocument;

        const grsfId = "some-grsf-id";

        beforeEach(async () => {
          // Build the referrer participant that owns the referral link
          referrerParticipant = await buildParticipant({
            grsfId
          });

          // Ensure there is no participant already for the referred user
          const participant = await Participant.findOne({ email: USER_EMAIL });
          expect(participant).toBeNull();

          response = await request(app)
            .post(`/api/admin/m2m/users/email/${userUpdateData.email}`)
            .send({ ...userUpdateData, grsf: grsfId })
            .set("Accept", "application/json");

          user = JSON.parse(response.text).data[0];
        });

        it("should create a new participant", async () => {
          const participantCount = await Participant.find({}).countDocuments();
          expect(participantCount).toEqual(2); // There was a participant already so now there are 2

          const participant: any = JSON.parse(JSON.stringify(await Participant.findOne({ email: USER_EMAIL })));
          expect(participant).toEqual(
            expect.objectContaining({
              email: USER_EMAIL,
              referrer: referrerParticipant.id,
              isAmbassador: false,
              participantRole: "BASIC",
              wlthdId: expect.anything()
            })
          );
        });

        it("should create a new user", async () => {
          const userCount = await User.find({}).countDocuments();
          expect(userCount).toEqual(1);

          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser).toEqual(
            expect.objectContaining({
              email: userUpdateData.email,
              emailVerified: userUpdateData.emailVerified === "true",
              role: expect.arrayContaining(userUpdateData.role),
              auth0: { id: userUpdateData.auth0.id, email: userUpdateData.auth0.id },
              lastLogin
            })
          );
        });

        it("should return status 200", async () => {
          expect(response.status).toEqual(200);
        });

        it("should emit a 'signUp' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            2,
            events.user.signUp.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: referrerParticipant.email })
              })
            }),
            expect.objectContaining({ referredStatus: "True" })
          );
        });

        it("should emit a 'whAccountStatusUpdate' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            3,
            events.user.whAccountStatusUpdate.eventId,
            expect.objectContaining({
              id: user.id,
              participant: expect.objectContaining({
                referrer: expect.objectContaining({ email: referrerParticipant.email })
              })
            }),
            expect.objectContaining({
              accountStatus: MixpanelAccountStatusEnum.Pending
            })
          );
        });

        it("should update referredByEmail property of user", async () => {
          const updatedUser = (await User.findById(user.id)) as UserDocument;
          expect(updatedUser.referredByEmail).toEqual(referrerParticipant.email);
        });

        it("should emit a 'emailSubmitted' user event", async () => {
          expect(eventEmitter.emit).toHaveBeenNthCalledWith(
            1,
            events.participant.emailSubmitted.eventId,
            expect.objectContaining({ email: user.email })
          );
        });

        it("should emit 3 total events", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(3);
        });
      });
    });

    describe("and invalid email is passed as a path parameter", () => {
      beforeEach(async () => {
        const userUpdateData: CreateUserData = {
          email: "<EMAIL>",
          emailVerified: "true",
          role: [UserTypeEnum.INVESTOR],
          lastLogin: new Date().toISOString(),
          auth0: {
            id: "email|test-auth0-id"
          }
        };

        response = await request(app)
          .post("/api/admin/m2m/users/email/some-invalid-email")
          .send(userUpdateData)
          .set("Accept", "application/json");
      });

      it("should return status 400", async () => {
        expect(response.status).toEqual(400);
      });
    });

    describe("and invalid body parameter is passed", () => {
      ["email", "emailVerified", "role", "lastLogin"].forEach((key) =>
        it(`should return status 400 for invalid parameter '${key}'`, async () => {
          const user = await buildUser();
          const response = await request(app)
            .post(`/api/admin/m2m/users/email/${user.email}`)
            .send({ [key]: faker.string.uuid() })
            .set("Accept", "application/json");
          expect(response.status).toEqual(400);
        })
      );
    });
  });

  describe("/users/create-wealthkernel-parties", () => {
    beforeEach(() => {
      jest.resetAllMocks();
    });
    afterEach(async () => await clearDb());

    it("should not create party for user that does not have Wealthkernel active provider", async () => {
      const user = await buildUser({
        activeProviders: [],
        isPassportMatchingKycProvider: true
      });
      await buildAddress({ owner: user._id });

      jest.spyOn(WealthkernelService.UKInstance, "retrieveParties").mockResolvedValueOnce([]);
      jest.spyOn(WealthkernelService.UKInstance, "createParty");

      // Run
      const response = await request(app)
        .post("/api/admin/m2m/users/create-wealthkernel-parties")
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      // Assert
      expect(WealthkernelService.UKInstance.createParty).not.toHaveBeenCalled();
    });

    it("should not create party for user with all required info but submitted less than 10 minutes ago", async () => {
      // Prepare the data
      const SUCCESS_PARTY_ID = "SUCCESS_WK_ID";
      const user = await buildUser({
        submittedRequiredInfoAt: new Date()
      });
      await buildAddress({ owner: user._id });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveParties").mockResolvedValueOnce([]);
      jest.spyOn(WealthkernelService.UKInstance, "createParty").mockResolvedValueOnce({ id: SUCCESS_PARTY_ID });

      // Run
      const response = await request(app)
        .post("/api/admin/m2m/users/create-wealthkernel-parties")
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      // Assert
      expect(WealthkernelService.UKInstance.createParty).not.toHaveBeenCalled();
    });

    it("should not create party for user with undefined submittedRequiredInfoAt field", async () => {
      // Prepare the data
      const SUCCESS_PARTY_ID = "SUCCESS_WK_ID";
      const user = await buildUser({
        submittedRequiredInfoAt: undefined
      });
      await buildAddress({ owner: user._id });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveParties").mockResolvedValueOnce([]);
      jest.spyOn(WealthkernelService.UKInstance, "createParty").mockResolvedValueOnce({ id: SUCCESS_PARTY_ID });

      // Run
      const response = await request(app)
        .post("/api/admin/m2m/users/create-wealthkernel-parties")
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      // Assert
      expect(WealthkernelService.UKInstance.createParty).not.toHaveBeenCalled();
    });

    it("should not create party for user without wealthkernel partyId and missing verification details", async () => {
      // Prepare the data
      await buildUser({
        firstName: undefined
      });

      // Run
      const response = await request(app)
        .post("/api/admin/m2m/users/create-wealthkernel-parties")
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      // Assert
      expect(WealthkernelService.UKInstance.createParty).toHaveBeenCalledTimes(0);
    });

    it("should not create party for user that has submitted all required verification details but that already has a wealthkernel partyId", async () => {
      // Prepare the data
      const WK_PARTY_ID = "WK_PARTY_ID";
      await buildUser({ providers: { wealthkernel: { id: WK_PARTY_ID } } });
      jest.spyOn(WealthkernelService.UKInstance, "createParty");
      // Run
      const response = await request(app)
        .post("/api/admin/m2m/users/create-wealthkernel-parties")
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      // Assert
      expect(WealthkernelService.UKInstance.createParty).toHaveBeenCalledTimes(0);
    });

    it("should create a party for all users without Wealthkernel party id that have submitted address, passport & tax residency with passport matching KYC provider", async () => {
      // Prepare the data
      const SUCCESS_PARTY_ID = "SUCCESS_WK_ID";
      const user = await buildUser({
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        isPassportMatchingKycProvider: true
      });
      await buildAddress({ owner: user._id });
      jest.spyOn(WealthkernelService.UKInstance, "retrieveParties").mockResolvedValueOnce([]);
      jest.spyOn(WealthkernelService.UKInstance, "createParty").mockResolvedValueOnce({ id: SUCCESS_PARTY_ID });

      // Run
      const response = await request(app)
        .post("/api/admin/m2m/users/create-wealthkernel-parties")
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      // Assert
      expect(WealthkernelService.UKInstance.createParty).toHaveBeenCalledWith(
        expect.objectContaining({
          emailAddress: user.email
        })
      );
    });
  });

  describe("/users/:id/remove-duplicate-flag", () => {
    it("should return 204 and remove duplicate flag", async () => {
      const user = await buildUser({ isPotentiallyDuplicateAccount: true, kycStatus: KycStatusEnum.FAILED });
      await buildAccount({
        owner: user.id,
        providers: { wealthkernel: { id: "WK_ACCOUNT_ID", status: "Active" } }
      });
      await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: "WK_PORTFOLIO_ID", status: "Active" } }
      });
      await request(app)
        .post(`/api/admin/m2m/users/${user.id}/remove-duplicate-flag`)
        .set("Accept", "application/json")
        .expect(204);
      const updatedUser = await User.findById(user.id);
      const updatedAccount = await Account.findOne({ owner: user.id });
      expect(updatedUser?.isPotentiallyDuplicateAccount).toEqual(false);
      expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PENDING);
      expect(updatedAccount?.providers?.wealthkernel?.status).toEqual("Pending");
    });
  });

  describe("/users/:id/remove-kyc-passport-flag", () => {
    it("should return 204 and remove KYC passport flag and set user to KYC passed", async () => {
      const user = await buildUser({ isPassportMatchingKycProvider: false, kycStatus: KycStatusEnum.FAILED });

      await Promise.all([
        buildKycOperation({
          owner: user.id,
          status: "Passed",
          activeProviders: [ProviderEnum.SUMSUB],
          providers: {
            sumsub: {
              id: faker.string.uuid(),
              submittedAt: new Date(),
              status: "completed",
              decision: "GREEN"
            }
          }
        }),
        buildAccount({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        })
      ]);

      await request(app)
        .post(`/api/admin/m2m/users/${user.id}/remove-kyc-passport-flag`)
        .set("Accept", "application/json")
        .expect(204);

      const updatedUser = await User.findById(user.id);
      expect(updatedUser?.isPassportMatchingKycProvider).toEqual(true);
      expect(updatedUser?.kycPassed).toEqual(true);
    });
  });

  describe("/users/:id/override-kyc-decision", () => {
    it("should return 400 if user hasn't failed his latest kyc operation", async () => {
      const user = await buildUser({ isPassportMatchingKycProvider: false, kycStatus: KycStatusEnum.PENDING });

      await Promise.all([
        buildKycOperation({
          owner: user.id,
          status: "Passed",
          activeProviders: [ProviderEnum.SUMSUB],
          providers: {
            sumsub: {
              id: faker.string.uuid(),
              status: "completed",
              decision: "GREEN"
            }
          }
        }),
        buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Suspended" } }
        }),
        buildSubscription({ owner: user.id, active: false })
      ]);

      await request(app)
        .post(`/api/admin/m2m/users/${user.id}/override-kyc-decision`)
        .set("Accept", "application/json")
        .expect(400);

      const updatedUser = await User.findById(user.id);
      expect(updatedUser?.hasPassedKyc).toEqual(false);

      const subscription = await Subscription.findOne({ owner: user.id });
      expect(subscription?.active).toEqual(false);
    });

    it("should return 204 if user does not have an active WK account but do not pass user", async () => {
      jest.spyOn(SumsubService.Instance, "runAMLCheck").mockResolvedValue();

      const user = await buildUser({ kycStatus: KycStatusEnum.PENDING });
      const [kycOperation, ,] = await Promise.all([
        buildKycOperation({
          owner: user.id,
          status: "Failed",
          activeProviders: [ProviderEnum.SUMSUB],
          providers: {
            sumsub: {
              id: faker.string.uuid(),
              status: "completed",
              decision: "RED"
            }
          }
        }),
        buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Suspended" } }
        }),
        buildSubscription({ owner: user.id, active: false })
      ]);

      await request(app)
        .post(`/api/admin/m2m/users/${user.id}/override-kyc-decision`)
        .set("Accept", "application/json")
        .expect(204);

      const updatedKycOperation = await KycOperation.findById(kycOperation.id);
      expect(updatedKycOperation?.status).toEqual("ManuallyPassed");

      const updatedUser = await User.findById(user.id);
      expect(updatedUser?.hasPassedKyc).toEqual(false);

      const subscription = await Subscription.findOne({ owner: user.id });
      expect(subscription?.active).toEqual(false);
    });

    it("should return 204 and verify user and active his subscription", async () => {
      jest.spyOn(SumsubService.Instance, "runAMLCheck").mockResolvedValue();

      const user = await buildUser({ kycStatus: KycStatusEnum.PENDING });
      const [account, kycOperation] = await Promise.all([
        buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        }),
        buildKycOperation({
          owner: user.id,
          status: "Failed",
          activeProviders: [ProviderEnum.SUMSUB],
          providers: {
            sumsub: {
              id: faker.string.uuid(),
              status: "completed",
              decision: "RED"
            }
          }
        }),
        buildSubscription({ owner: user.id, active: false })
      ]);
      await buildPortfolio({ mode: PortfolioModeEnum.REAL, owner: user.id, account: account.id }, true);

      await request(app)
        .post(`/api/admin/m2m/users/${user.id}/override-kyc-decision`)
        .set("Accept", "application/json")
        .expect(204);

      const updatedKycOperation = await KycOperation.findById(kycOperation.id);
      expect(updatedKycOperation?.status).toEqual("ManuallyPassed");

      const updatedUser = await User.findById(user.id);
      expect(updatedUser?.hasPassedKyc).toEqual(true);

      const subscription = await Subscription.findOne({ owner: user.id });
      expect(subscription?.active).toEqual(true);
    });

    it("should return 204 and create a manual AML workflow", async () => {
      jest.spyOn(SumsubService.Instance, "runAMLCheck").mockResolvedValue();

      const user = await buildUser({
        kycStatus: KycStatusEnum.PENDING,
        providers: { sumsub: { id: faker.string.uuid() } }
      });

      const [account, kycOperation] = await Promise.all([
        buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        }),
        buildKycOperation({
          owner: user.id,
          status: "Failed",
          activeProviders: [ProviderEnum.SUMSUB],
          providers: {
            sumsub: {
              id: faker.string.uuid(),
              status: "completed",
              decision: "RED"
            }
          }
        }),
        buildAddress({ owner: user.id }),
        buildSubscription({ owner: user.id, active: false })
      ]);
      await buildPortfolio({ mode: PortfolioModeEnum.REAL, owner: user.id, account: account.id }, true);

      await request(app)
        .post(`/api/admin/m2m/users/${user.id}/override-kyc-decision`)
        .set("Accept", "application/json")
        .expect(204);

      const updatedKycOperation = await KycOperation.findById(kycOperation.id);
      expect(updatedKycOperation?.status).toEqual("ManuallyPassed");
      expect(updatedKycOperation?.isManualAmlWorkflowSubmitted).toEqual(true);

      const updatedUser = await User.findById(user.id);
      expect(updatedUser?.hasPassedKyc).toEqual(true);

      const subscription = await Subscription.findOne({ owner: user.id });
      expect(subscription?.active).toEqual(true);
    });
  });
});
