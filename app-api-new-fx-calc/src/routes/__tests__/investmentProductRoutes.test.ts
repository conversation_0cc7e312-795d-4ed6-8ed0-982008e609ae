import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { RedisClientService } from "../../loaders/redis";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetNews,
  buildAssetTransaction,
  buildDividendTransaction,
  buildHoldingDTO,
  buildIntraDayAssetTicker,
  buildIntraDayPortfolioTicker,
  buildInvestmentProduct,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildReward,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import axios from "axios";
import StatisticsConfig from "../../configs/statisticsConfig";
import { buildStockFundamentalsResponse } from "../../tests/utils/generateEod";
import { investmentUniverseConfig, entitiesConfig } from "@wealthyhood/shared-configs";
import mongoose from "mongoose";
import { faker } from "@faker-js/faker";
import { PortfolioModeEnum } from "../../models/Portfolio";
import DateUtil from "../../utils/dateUtil";
import eodService from "../../external-services/eodService";
import { DateTime } from "luxon";
import { AssetTransactionInterfaceDTO } from "../../models/Transaction";
import { OrderDTOInterface } from "../../models/Order";

const originalDateNow = Date.now;
const { ASSET_CONFIG } = investmentUniverseConfig;

async function buildAssetTransactionWithOrder(
  assetTransaction: Partial<AssetTransactionInterfaceDTO> = {},
  orderOverrides: Partial<OrderDTOInterface> = {}
): Promise<void> {
  const transaction = await buildAssetTransaction({
    portfolioTransactionCategory: "update",
    status: "Pending",
    ...assetTransaction
  });
  const order = await buildOrder({
    transaction: transaction.id,
    ...orderOverrides
  });

  transaction.orders = [order];
  await transaction.save();
}

describe("InvestmentProductRoutes", () => {
  beforeAll(async () => await connectDb("InvestmentProductRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /investment-products", () => {
    let user: UserDocument;

    beforeEach(async () => {
      jest.resetAllMocks();
      user = await buildUser();
    });
    afterEach(async () => await clearDb());

    it("should include tradedPrice and tradedCurrency if tickers are populated", async () => {
      const PRICE = 100;

      await buildInvestmentProduct(true, {
        assetId: "commodities_gold",
        price: PRICE
      });

      const response = await request(app)
        .get("/api/m2m/investment-products?populateTicker=true")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            tradedPrice: PRICE,
            tradedCurrency: "GBP"
          })
        ])
      );
    });

    it("should include current ticker price if tickers are populated", async () => {
      const EUR_PRICE = 100;
      const GBP_PRICE = 105;
      const USD_PRICE = 95;

      await buildInvestmentProduct(
        true,
        {
          assetId: "commodities_gold"
        },
        {
          pricePerCurrency: { EUR: EUR_PRICE, GBP: GBP_PRICE, USD: USD_PRICE }
        }
      );

      const response = await request(app)
        .get("/api/m2m/investment-products?populateTicker=true")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            currentTicker: expect.objectContaining({
              price: GBP_PRICE
            })
          })
        ])
      );
    });
  });

  describe("GET /investment-products/etf-data", () => {
    let user: UserDocument;

    beforeEach(async () => {
      jest.resetAllMocks();
      user = await buildUser();
    });
    afterEach(async () => await clearDb());

    it("should fail for invalid assetId param", async () => {
      const response = await request(app)
        .get("/api/m2m/investment-products/etf-data?assetId=nothing")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should fail for empty assetId param", async () => {
      const response = await request(app)
        .get("/api/m2m/investment-products/etf-data")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should respond successfully for valid ETF assetId param and user with UK residency country", async () => {
      const PRICE = 200;
      const TODAY = new Date("2022-09-30T11:00:00Z");

      const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", price: PRICE });

      const [newsItem1, newsItem2, newsItem3] = await Promise.all([
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfDaysAgo(TODAY, 3) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfHoursAgo(TODAY, 2) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: TODAY })
      ]);

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
        if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
          return Promise.resolve({
            data: {
              expected_return: 8.8,
              annual_risk: 16.6
            }
          });
        }

        return Promise.resolve({});
      });

      const eodEtfDataResponse = {
        ETF_Data: {
          Valuations_Growth: {
            Valuations_Rates_Portfolio: {
              "Price/Prospective Earnings": "13.88",
              "Dividend-Yield Factor": "2.46"
            }
          },
          Ongoing_Charge: "0.2200",
          Holdings_Count: 100,
          Top_10_Holdings: {
            "AAPL.US": {
              Name: "Apple Inc",
              "Assets_%": 3.73,
              Code: "AAPL",
              Exchange: "US"
            }
          },
          World_Regions: {
            "North America": { "Equity_%": "64.319" },
            "United Kingdom": { "Equity_%": "0.26825" },
            "Europe Developed": { "Equity_%": "6.535" },
            "Europe Emerging": { "Equity_%": "0" },
            "Africa/Middle East": { "Equity_%": "4.253" },
            Japan: { "Equity_%": "5.312" },
            Australasia: { "Equity_%": "0" },
            "Asia Developed": { "Equity_%": "18.636" },
            "Asia Emerging": { "Equity_%": "0.677" },
            "Latin America": { "Equity_%": "0" }
          },
          Sector_Weights: {
            "Basic Materials": { "Equity_%": "0" },
            "Consumer Cyclicals": { "Equity_%": "4.49357" },
            "Financial Services": { "Equity_%": "1.60887" },
            "Real Estate": { "Equity_%": "0" },
            "Communication Services": { "Equity_%": "2.10227" },
            Energy: { "Equity_%": "0" },
            Industrials: { "Equity_%": "1.23068" },
            Technology: { "Equity_%": "89.16771" },
            "Consumer Defensive": { "Equity_%": "0.26825" },
            Healthcare: { "Equity_%": "1.12864" },
            Utilities: { "Equity_%": "0" }
          }
        }
      };

      jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodEtfDataResponse as any);

      const response = await request(app)
        .get("/api/m2m/investment-products/etf-data?assetId=equities_us")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);
      const responseBody = JSON.parse(response.text);
      expect(responseBody).toMatchObject({
        currentPrice: PRICE,
        tradedCurrency: investmentUniverseConfig.ASSET_CONFIG["equities_us"].tradedCurrency,
        topHoldings: [
          {
            name: "Apple",
            weight: "3.73%",
            logoUrl: "https://etf-holdings-logos.wealthyhood.dev/eod/AAPL.png"
          }
        ],
        expenseRatio: "0.22",
        indexStats: { expectedReturn: "8.80%", annualRisk: "16.60%", fpEarnings: "13.88", dividendYield: "2.46%" },
        holdingsCount: 100,
        baseCurrency: "$ USD",
        geographyDistribution: [
          { name: "North America", percentage: 64.5 },
          { name: "Asia", percentage: 24.69 },
          { name: "Europe", percentage: 6.55 },
          { name: "Africa/Middle East", percentage: 4.26 }
        ],
        sectorDistribution: [
          { name: "Technology", id: "technology", percentage: 89.19 },
          { name: "Consumer", id: "consumer", percentage: 4.76 },
          { name: "Communication", id: "communication", percentage: 2.1 },
          { name: "Financials", id: "financials", percentage: 1.6 },
          { name: "Industrials", id: "industrials", percentage: 1.23 },
          { name: "Healthcare", id: "healthcare", percentage: 1.12 }
        ],
        about: {
          provider: "Vanguard",
          income: "Distributing",
          exchange: "LSE",
          isin: "IE00B3XXRP09",
          ticker: "£VUSA",
          assetClass: "Stocks",
          sector: "Global ETFs",
          advancedName: "Vanguard S&P 500 UCITS ETF",
          description:
            "This ETF tracks the performance of the S&P 500 index, including 500 large-sized company stocks in the US representing all major industries."
        },
        news: [
          {
            newsUrl: newsItem3.newsUrl,
            imageUrl: newsItem3.imageUrl,
            title: newsItem3.title,
            text: newsItem3.text,
            source: newsItem3.source,
            date: newsItem3.date.toISOString(),
            sentiment: newsItem3.sentiment,
            type: newsItem3.type,
            topics: newsItem3.topics,
            tickers: newsItem3.tickers,
            displayDate: "0m ago"
          },
          {
            newsUrl: newsItem2.newsUrl,
            imageUrl: newsItem2.imageUrl,
            title: newsItem2.title,
            text: newsItem2.text,
            source: newsItem2.source,
            date: newsItem2.date.toISOString(),
            sentiment: newsItem2.sentiment,
            type: newsItem2.type,
            topics: newsItem2.topics,
            tickers: newsItem2.tickers,
            displayDate: "2h ago"
          },
          {
            newsUrl: newsItem1.newsUrl,
            imageUrl: newsItem1.imageUrl,
            title: newsItem1.title,
            text: newsItem1.text,
            source: newsItem1.source,
            date: newsItem1.date.toISOString(),
            sentiment: newsItem1.sentiment,
            type: newsItem1.type,
            topics: newsItem1.topics,
            tickers: newsItem1.tickers,
            displayDate: "27 Sept 2022, 11:00"
          }
        ]
      });
    });

    it("should respond successfully for valid ETF assetId param and user with EU residency country when EU markets are open", async () => {
      const PRICE = 200;
      const TODAY = new Date("2022-09-30T11:00:00Z");

      const europeanUser = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        residencyCountry: "GR"
      });
      const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", price: PRICE });

      const [newsItem1, newsItem2, newsItem3] = await Promise.all([
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfDaysAgo(TODAY, 3) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfHoursAgo(TODAY, 2) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: TODAY })
      ]);

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
        if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
          return Promise.resolve({
            data: {
              expected_return: 8.8,
              annual_risk: 16.6
            }
          });
        }

        return Promise.resolve({});
      });

      const eodEtfDataResponse = {
        ETF_Data: {
          Valuations_Growth: {
            Valuations_Rates_Portfolio: {
              "Price/Prospective Earnings": "13.88",
              "Dividend-Yield Factor": "2.46"
            }
          },
          Ongoing_Charge: "0.2200",
          Holdings_Count: 100,
          Top_10_Holdings: {
            "AAPL.US": {
              Name: "Apple Inc",
              "Assets_%": 3.73,
              Code: "AAPL",
              Exchange: "US"
            }
          },
          World_Regions: {
            "North America": { "Equity_%": "64.319" },
            "United Kingdom": { "Equity_%": "0.26825" },
            "Europe Developed": { "Equity_%": "6.535" },
            "Europe Emerging": { "Equity_%": "0" },
            "Africa/Middle East": { "Equity_%": "4.253" },
            Japan: { "Equity_%": "5.312" },
            Australasia: { "Equity_%": "0" },
            "Asia Developed": { "Equity_%": "18.636" },
            "Asia Emerging": { "Equity_%": "0.677" },
            "Latin America": { "Equity_%": "0" }
          },
          Sector_Weights: {
            "Basic Materials": { "Equity_%": "0" },
            "Consumer Cyclicals": { "Equity_%": "4.49357" },
            "Financial Services": { "Equity_%": "1.60887" },
            "Real Estate": { "Equity_%": "0" },
            "Communication Services": { "Equity_%": "2.10227" },
            Energy: { "Equity_%": "0" },
            Industrials: { "Equity_%": "1.23068" },
            Technology: { "Equity_%": "89.16771" },
            "Consumer Defensive": { "Equity_%": "0.26825" },
            Healthcare: { "Equity_%": "1.12864" },
            Utilities: { "Equity_%": "0" }
          }
        }
      };

      jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodEtfDataResponse as any);

      const response = await request(app)
        .get("/api/m2m/investment-products/etf-data?assetId=equities_us")
        .set("external-user-id", europeanUser.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);
      const responseBody = JSON.parse(response.text);
      expect(responseBody).toMatchObject({
        currentPrice: PRICE,
        tradedCurrency: investmentUniverseConfig.ASSET_CONFIG["equities_us"].tradedCurrency,
        topHoldings: [
          {
            name: "Apple",
            weight: "3,73%",
            logoUrl: "https://etf-holdings-logos.wealthyhood.dev/eod/AAPL.png"
          }
        ],
        expenseRatio: "0,22",
        indexStats: { expectedReturn: "8,80%", annualRisk: "16,60%", fpEarnings: "13,88", dividendYield: "2,46%" },
        holdingsCount: 100,
        baseCurrency: "$ USD",
        geographyDistribution: [
          { name: "North America", percentage: 64.5 },
          { name: "Asia", percentage: 24.69 },
          { name: "Europe", percentage: 6.55 },
          { name: "Africa/Middle East", percentage: 4.26 }
        ],
        sectorDistribution: [
          { name: "Technology", id: "technology", percentage: 89.19 },
          { name: "Consumer", id: "consumer", percentage: 4.76 },
          { name: "Communication", id: "communication", percentage: 2.1 },
          { name: "Financials", id: "financials", percentage: 1.6 },
          { name: "Industrials", id: "industrials", percentage: 1.23 },
          { name: "Healthcare", id: "healthcare", percentage: 1.12 }
        ],
        about: {
          provider: "Vanguard",
          income: "Distributing",
          exchange: "LSE",
          isin: "IE00B3XXRP09",
          ticker: "£VUSA",
          assetClass: "Stocks",
          sector: "Global ETFs",
          advancedName: "Vanguard S&P 500 UCITS ETF",
          description:
            "This ETF tracks the performance of the S&P 500 index, including 500 large-sized company stocks in the US representing all major industries."
        },
        news: [
          {
            newsUrl: newsItem3.newsUrl,
            imageUrl: newsItem3.imageUrl,
            title: newsItem3.title,
            text: newsItem3.text,
            source: newsItem3.source,
            date: newsItem3.date.toISOString(),
            sentiment: newsItem3.sentiment,
            type: newsItem3.type,
            topics: newsItem3.topics,
            tickers: newsItem3.tickers,
            displayDate: "0m ago"
          },
          {
            newsUrl: newsItem2.newsUrl,
            imageUrl: newsItem2.imageUrl,
            title: newsItem2.title,
            text: newsItem2.text,
            source: newsItem2.source,
            date: newsItem2.date.toISOString(),
            sentiment: newsItem2.sentiment,
            type: newsItem2.type,
            topics: newsItem2.topics,
            tickers: newsItem2.tickers,
            displayDate: "2h ago"
          },
          {
            newsUrl: newsItem1.newsUrl,
            imageUrl: newsItem1.imageUrl,
            title: newsItem1.title,
            text: newsItem1.text,
            source: newsItem1.source,
            date: newsItem1.date.toISOString(),
            sentiment: newsItem1.sentiment,
            type: newsItem1.type,
            topics: newsItem1.topics,
            tickers: newsItem1.tickers,
            displayDate: "27 Sept 2022, 11:00"
          }
        ],
        tags: ["SMART_EXECUTION", "MARKET_OPEN", "FRACTIONAL"],
        marketInfo: {
          isOpen: true,
          nextMarketOpen: DateTime.fromISO("2022-10-03T08:00:00.000", { zone: "Europe/London" }).toMillis()
        }
      });
    });

    it("should respond successfully for valid ETF assetId param and user with EU residency country when EU markets are closed", async () => {
      const PRICE = 200;
      const TODAY = new Date("2022-09-30T20:00:00Z");

      const europeanUser = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        residencyCountry: "GR"
      });
      const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", price: PRICE });

      const [newsItem1, newsItem2, newsItem3] = await Promise.all([
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfDaysAgo(TODAY, 3) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfHoursAgo(TODAY, 2) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: TODAY })
      ]);

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(axios, "get").mockImplementation((url: string): Promise<any> => {
        if (url.includes(StatisticsConfig.URLS.indexStatsV3)) {
          return Promise.resolve({
            data: {
              expected_return: 8.8,
              annual_risk: 16.6
            }
          });
        }

        return Promise.resolve({});
      });

      const eodEtfDataResponse = {
        ETF_Data: {
          Valuations_Growth: {
            Valuations_Rates_Portfolio: {
              "Price/Prospective Earnings": "13.88",
              "Dividend-Yield Factor": "2.46"
            }
          },
          Ongoing_Charge: "0.2200",
          Holdings_Count: 100,
          Top_10_Holdings: {
            "AAPL.US": {
              Name: "Apple Inc",
              "Assets_%": 3.73,
              Code: "AAPL",
              Exchange: "US"
            }
          },
          World_Regions: {
            "North America": { "Equity_%": "64.319" },
            "United Kingdom": { "Equity_%": "0.26825" },
            "Europe Developed": { "Equity_%": "6.535" },
            "Europe Emerging": { "Equity_%": "0" },
            "Africa/Middle East": { "Equity_%": "4.253" },
            Japan: { "Equity_%": "5.312" },
            Australasia: { "Equity_%": "0" },
            "Asia Developed": { "Equity_%": "18.636" },
            "Asia Emerging": { "Equity_%": "0.677" },
            "Latin America": { "Equity_%": "0" }
          },
          Sector_Weights: {
            "Basic Materials": { "Equity_%": "0" },
            "Consumer Cyclicals": { "Equity_%": "4.49357" },
            "Financial Services": { "Equity_%": "1.60887" },
            "Real Estate": { "Equity_%": "0" },
            "Communication Services": { "Equity_%": "2.10227" },
            Energy: { "Equity_%": "0" },
            Industrials: { "Equity_%": "1.23068" },
            Technology: { "Equity_%": "89.16771" },
            "Consumer Defensive": { "Equity_%": "0.26825" },
            Healthcare: { "Equity_%": "1.12864" },
            Utilities: { "Equity_%": "0" }
          }
        }
      };

      jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodEtfDataResponse as any);

      const response = await request(app)
        .get("/api/m2m/investment-products/etf-data?assetId=equities_us")
        .set("external-user-id", europeanUser.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);
      const responseBody = JSON.parse(response.text);
      expect(responseBody).toMatchObject({
        currentPrice: PRICE,
        tradedCurrency: investmentUniverseConfig.ASSET_CONFIG["equities_us"].tradedCurrency,
        topHoldings: [
          {
            name: "Apple",
            weight: "3,73%",
            logoUrl: "https://etf-holdings-logos.wealthyhood.dev/eod/AAPL.png"
          }
        ],
        expenseRatio: "0,22",
        indexStats: { expectedReturn: "8,80%", annualRisk: "16,60%", fpEarnings: "13,88", dividendYield: "2,46%" },
        holdingsCount: 100,
        baseCurrency: "$ USD",
        geographyDistribution: [
          { name: "North America", percentage: 64.5 },
          { name: "Asia", percentage: 24.69 },
          { name: "Europe", percentage: 6.55 },
          { name: "Africa/Middle East", percentage: 4.26 }
        ],
        sectorDistribution: [
          { name: "Technology", id: "technology", percentage: 89.19 },
          { name: "Consumer", id: "consumer", percentage: 4.76 },
          { name: "Communication", id: "communication", percentage: 2.1 },
          { name: "Financials", id: "financials", percentage: 1.6 },
          { name: "Industrials", id: "industrials", percentage: 1.23 },
          { name: "Healthcare", id: "healthcare", percentage: 1.12 }
        ],
        about: {
          provider: "Vanguard",
          income: "Distributing",
          exchange: "LSE",
          isin: "IE00B3XXRP09",
          ticker: "£VUSA",
          assetClass: "Stocks",
          sector: "Global ETFs",
          advancedName: "Vanguard S&P 500 UCITS ETF",
          description:
            "This ETF tracks the performance of the S&P 500 index, including 500 large-sized company stocks in the US representing all major industries."
        },
        news: [
          {
            newsUrl: newsItem3.newsUrl,
            imageUrl: newsItem3.imageUrl,
            title: newsItem3.title,
            text: newsItem3.text,
            source: newsItem3.source,
            date: newsItem3.date.toISOString(),
            sentiment: newsItem3.sentiment,
            type: newsItem3.type,
            topics: newsItem3.topics,
            tickers: newsItem3.tickers,
            displayDate: "0m ago"
          },
          {
            newsUrl: newsItem2.newsUrl,
            imageUrl: newsItem2.imageUrl,
            title: newsItem2.title,
            text: newsItem2.text,
            source: newsItem2.source,
            date: newsItem2.date.toISOString(),
            sentiment: newsItem2.sentiment,
            type: newsItem2.type,
            topics: newsItem2.topics,
            tickers: newsItem2.tickers,
            displayDate: "2h ago"
          },
          {
            newsUrl: newsItem1.newsUrl,
            imageUrl: newsItem1.imageUrl,
            title: newsItem1.title,
            text: newsItem1.text,
            source: newsItem1.source,
            date: newsItem1.date.toISOString(),
            sentiment: newsItem1.sentiment,
            type: newsItem1.type,
            topics: newsItem1.topics,
            tickers: newsItem1.tickers,
            displayDate: "27 Sept 2022, 20:00"
          }
        ],
        tags: ["SMART_EXECUTION", "MARKET_CLOSED", "FRACTIONAL"],
        marketInfo: {
          isOpen: false,
          nextMarketOpen: DateTime.fromISO("2022-10-03T08:00:00.000", { zone: "Europe/London" }).toMillis()
        }
      });
    });

    it("should respond successfully for valid stock assetId param and markets are closed", async () => {
      const PRICE = 200;

      const TODAY = new Date("2022-09-30T11:00:00Z");

      const investmentProduct = await buildInvestmentProduct(true, {
        assetId: "equities_microsoft",
        price: PRICE
      });

      const [newsItem1, newsItem2, newsItem3] = await Promise.all([
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfDaysAgo(TODAY, 3) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfHoursAgo(TODAY, 2) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: TODAY })
      ]);

      Date.now = jest.fn(() => TODAY.valueOf());

      const eodDataResponse = buildStockFundamentalsResponse({
        General: {
          Exchange: "NASDAQ",
          GicIndustry: "Software",
          WebURL: "https://www.microsoft.com",
          FullTimeEmployees: 22000,
          AddressData: {
            City: "Redmond",
            State: "WA",
            Country: "United States"
          },
          Description:
            "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
          Officers: {
            0: {
              Name: "Mr. Satya  Nadella",
              Title: "Chairman & CEO"
            }
          },
          LogoURL: "",
          ISIN: ""
        },
        Highlights: {
          DividendYield: 0.0079,
          EarningsShare: -10.3,
          PERatio: 36.6437,
          MarketCapitalization: 1252000000000, // 1.252 trillion
          WallStreetTargetPrice: 251.12
        },
        Valuation: {
          ForwardPE: 32.432
        },
        Technicals: {
          Beta: 0.885
        },
        AnalystRatings: {
          StrongBuy: 50,
          Buy: 22,
          Hold: 11,
          Sell: 5,
          StrongSell: 3
        }
      });
      jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodDataResponse);

      const response = await request(app)
        .get("/api/m2m/investment-products/etf-data?assetId=equities_microsoft")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject({
        currentPrice: PRICE,
        tradedCurrency: investmentUniverseConfig.ASSET_CONFIG["equities_microsoft"].tradedCurrency,
        about: {
          ticker: "$MSFT",
          exchange: "NASDAQ",
          sector: "Technology",
          industry: "Software",
          description:
            "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
          employees: "22,000",
          website: "microsoft.com",
          ceo: "Satya Nadella",
          headquarters: "Redmond, United States",
          isin: "US5949181045"
        },
        metrics: {
          marketCap: "$1.25T",
          beta: "0.89",
          peRatio: "36.64",
          eps: "-$10.30",
          forwardPE: "32.43",
          dividendYield: "0.79%"
        },
        analystViews: {
          averagePriceTarget: "$251.12",
          priceTargetPercentageDifference: "+25.56%",
          isPriceTargetPercentageDifferencePositive: true,
          totalAnalysts: 91,
          percentageBuy: 80,
          percentageSell: 8,
          percentageHold: 12,
          isMajority: "buy"
        },
        news: [
          {
            newsUrl: newsItem3.newsUrl,
            imageUrl: newsItem3.imageUrl,
            title: newsItem3.title,
            text: newsItem3.text,
            source: newsItem3.source,
            date: newsItem3.date.toISOString(),
            sentiment: newsItem3.sentiment,
            type: newsItem3.type,
            topics: newsItem3.topics,
            tickers: newsItem3.tickers,
            displayDate: "0m ago"
          },
          {
            newsUrl: newsItem2.newsUrl,
            imageUrl: newsItem2.imageUrl,
            title: newsItem2.title,
            text: newsItem2.text,
            source: newsItem2.source,
            date: newsItem2.date.toISOString(),
            sentiment: newsItem2.sentiment,
            type: newsItem2.type,
            topics: newsItem2.topics,
            tickers: newsItem2.tickers,
            displayDate: "2h ago"
          },
          {
            newsUrl: newsItem1.newsUrl,
            imageUrl: newsItem1.imageUrl,
            title: newsItem1.title,
            text: newsItem1.text,
            source: newsItem1.source,
            date: newsItem1.date.toISOString(),
            sentiment: newsItem1.sentiment,
            type: newsItem1.type,
            topics: newsItem1.topics,
            tickers: newsItem1.tickers,
            displayDate: "27 Sept 2022, 11:00"
          }
        ],
        tags: ["COMMISSION_FREE", "MARKET_CLOSED", "FRACTIONAL"],
        marketInfo: {
          isOpen: false,
          nextMarketOpen: DateTime.fromISO("2022-09-30T09:30:00.000", { zone: "America/New_York" }).toMillis()
        }
      });
    });

    it("should respond successfully for valid stock assetId param and markets are open", async () => {
      const PRICE = 200;

      const TODAY = new Date("2022-09-30T18:00:00Z");

      const investmentProduct = await buildInvestmentProduct(true, {
        assetId: "equities_microsoft",
        price: PRICE
      });

      const [newsItem1, newsItem2, newsItem3] = await Promise.all([
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfDaysAgo(TODAY, 3) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: DateUtil.getDateOfHoursAgo(TODAY, 2) }),
        buildAssetNews({ investmentProducts: [investmentProduct.id], date: TODAY })
      ]);

      Date.now = jest.fn(() => TODAY.valueOf());

      const eodDataResponse = buildStockFundamentalsResponse({
        General: {
          Exchange: "NASDAQ",
          GicIndustry: "Software",
          WebURL: "https://www.microsoft.com",
          FullTimeEmployees: 22000,
          AddressData: {
            City: "Redmond",
            State: "WA",
            Country: "United States"
          },
          Description:
            "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
          Officers: {
            0: {
              Name: "Mr. Satya  Nadella",
              Title: "Chairman & CEO"
            }
          },
          LogoURL: "",
          ISIN: ""
        },
        Highlights: {
          DividendYield: 0.0079,
          EarningsShare: -10.3,
          PERatio: 36.6437,
          MarketCapitalization: 1252000000000, // 1.252 trillion
          WallStreetTargetPrice: 251.12
        },
        Valuation: {
          ForwardPE: 32.432
        },
        Technicals: {
          Beta: 0.885
        },
        AnalystRatings: {
          StrongBuy: 50,
          Buy: 22,
          Hold: 11,
          Sell: 5,
          StrongSell: 3
        }
      });
      jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodDataResponse);

      const response = await request(app)
        .get("/api/m2m/investment-products/etf-data?assetId=equities_microsoft")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject({
        currentPrice: PRICE,
        tradedCurrency: investmentUniverseConfig.ASSET_CONFIG["equities_microsoft"].tradedCurrency,
        about: {
          ticker: "$MSFT",
          exchange: "NASDAQ",
          sector: "Technology",
          industry: "Software",
          description:
            "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
          employees: "22,000",
          website: "microsoft.com",
          ceo: "Satya Nadella",
          headquarters: "Redmond, United States",
          isin: "US5949181045"
        },
        metrics: {
          marketCap: "$1.25T",
          beta: "0.89",
          peRatio: "36.64",
          eps: "-$10.30",
          forwardPE: "32.43",
          dividendYield: "0.79%"
        },
        analystViews: {
          averagePriceTarget: "$251.12",
          priceTargetPercentageDifference: "+25.56%",
          isPriceTargetPercentageDifferencePositive: true,
          totalAnalysts: 91,
          percentageBuy: 80,
          percentageSell: 8,
          percentageHold: 12,
          isMajority: "buy"
        },
        news: [
          {
            newsUrl: newsItem3.newsUrl,
            imageUrl: newsItem3.imageUrl,
            title: newsItem3.title,
            text: newsItem3.text,
            source: newsItem3.source,
            date: newsItem3.date.toISOString(),
            sentiment: newsItem3.sentiment,
            type: newsItem3.type,
            topics: newsItem3.topics,
            tickers: newsItem3.tickers,
            displayDate: "0m ago"
          },
          {
            newsUrl: newsItem2.newsUrl,
            imageUrl: newsItem2.imageUrl,
            title: newsItem2.title,
            text: newsItem2.text,
            source: newsItem2.source,
            date: newsItem2.date.toISOString(),
            sentiment: newsItem2.sentiment,
            type: newsItem2.type,
            topics: newsItem2.topics,
            tickers: newsItem2.tickers,
            displayDate: "2h ago"
          },
          {
            newsUrl: newsItem1.newsUrl,
            imageUrl: newsItem1.imageUrl,
            title: newsItem1.title,
            text: newsItem1.text,
            source: newsItem1.source,
            date: newsItem1.date.toISOString(),
            sentiment: newsItem1.sentiment,
            type: newsItem1.type,
            topics: newsItem1.topics,
            tickers: newsItem1.tickers,
            displayDate: "27 Sept 2022, 18:00"
          }
        ],
        tags: ["COMMISSION_FREE", "MARKET_OPEN", "FRACTIONAL"],
        marketInfo: {
          isOpen: true,
          nextMarketOpen: DateTime.fromISO("2022-10-03T09:30:00.000", { zone: "America/New_York" }).toMillis()
        }
      });
    });
  });

  describe("GET /investment-products/prices-by-tenor", () => {
    let user: UserDocument;

    beforeEach(async () => {
      jest.resetAllMocks();
      Date.now = jest.fn(originalDateNow);

      user = await buildUser();
    });
    afterEach(async () => await clearDb());

    it("should fail for invalid assetId param", async () => {
      const response = await request(app)
        .get("/api/m2m/investment-products/prices-by-tenor?assetId=nothing")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should fail for empty assetId param", async () => {
      const response = await request(app)
        .get("/api/m2m/investment-products/prices-by-tenor")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should respond successfully for valid assetId param with data organised by tenors", async () => {
      const YESTERDAY = DateUtil.getDateOfDaysAgo(new Date(), 1);
      const WEEK_AGO = DateUtil.getDateOfDaysAgo(new Date(), 7);
      const MONTH_AGO = DateUtil.getDateOfDaysAgo(new Date(), 30);
      const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });

      // today's ticker
      const todaysIntraDayTicker = await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: new Date(),
        pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
        investmentProduct: investmentProduct.id
      });
      // weekly tenor intraday data
      const eodWeeklyIntraDayData = [{ timestamp: new Date(YESTERDAY).getTime(), close: 1 }];
      await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);
      // monthly tenor intraday data
      const eodMonthlyIntraDayData = [{ timestamp: new Date(WEEK_AGO).getTime(), close: 2 }];
      await RedisClientService.Instance.set("eod:historical:m:equities_apple", eodMonthlyIntraDayData);
      // historical data (3m, 6m, 1y, max)
      const eodHistoricalDailyData = [{ date: DateTime.fromJSDate(new Date(MONTH_AGO)).toISODate(), close: 5 }];
      await RedisClientService.Instance.set("eod:historical:equities_apple", eodHistoricalDailyData);

      const response = await request(app)
        .get("/api/m2m/investment-products/prices-by-tenor?assetId=equities_apple")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          "1w": {
            // [1, 10]
            data: [
              {
                timestamp: DateUtil.mapDateToClosestTimeMark(
                  new Date(eodWeeklyIntraDayData[0].timestamp),
                  10,
                  "minutes"
                ).getTime(),
                close: eodWeeklyIntraDayData[0].close
              },
              {
                timestamp: DateUtil.mapDateToClosestTimeMark(
                  new Date(todaysIntraDayTicker.timestamp),
                  10,
                  "minutes"
                ).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 1) / 1 * 100
            returns: 900,
            displayIntraday: true
          },
          "1m": {
            // [2, 10]
            data: [
              {
                timestamp: DateUtil.mapDateToClosestTimeMark(
                  new Date(eodMonthlyIntraDayData[0].timestamp),
                  30,
                  "minutes"
                ).getTime(),
                close: eodMonthlyIntraDayData[0].close
              },
              {
                timestamp: DateUtil.mapDateToClosestTimeMark(
                  new Date(todaysIntraDayTicker.timestamp),
                  30,
                  "minutes"
                ).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 2) / 2 * 100
            returns: 400,
            displayIntraday: true
          },
          "3m": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          "6m": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          "1y": {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          },
          max: {
            // [5, 10]
            data: [
              {
                timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                  .toJSDate()
                  .getTime(),
                close: eodHistoricalDailyData[0].close
              },
              {
                timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
                close: todaysIntraDayTicker.pricePerCurrency["USD"]
              }
            ],
            // (10 - 5) / 5 * 100
            returns: 100,
            displayIntraday: false
          }
        })
      );
    });

    it("should respond successfully with the all aggregated prices when users has order activity for asset", async () => {
      // set conditions
      const ASSET_ID = "equities_apple";
      const YESTERDAY = DateUtil.getDateOfDaysAgo(new Date(Date.now()), 1);
      const WEEK_AGO = DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7);
      const MONTH_AGO = DateUtil.getDateOfDaysAgo(new Date(Date.now()), 30);
      const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });

      const ISIN = investmentUniverseConfig.ASSET_CONFIG[ASSET_ID].isin;
      const rebalanceTransaction = await buildRebalanceTransaction({
        owner: user,
        rebalanceStatus: "PendingSell"
      });
      await Promise.all([
        buildAssetTransactionWithOrder(
          { owner: user.id, portfolioTransactionCategory: "update", status: "Settled" },
          {
            side: "Buy",
            quantity: 12,
            status: "Matched",
            filledAt: YESTERDAY,
            isin: ISIN
          }
        ),
        buildAssetTransactionWithOrder(
          { owner: user.id, portfolioTransactionCategory: "update", status: "Pending" },
          {
            side: "Sell",
            quantity: 10,
            status: "Matched",
            filledAt: YESTERDAY,
            isin: ISIN
          }
        ),
        buildAssetTransactionWithOrder(
          { owner: user.id, portfolioTransactionCategory: "update", status: "Pending" },
          {
            side: "Buy",
            quantity: 25,
            status: "Matched",
            filledAt: WEEK_AGO,
            isin: ISIN
          }
        ),
        buildOrder({
          transaction: rebalanceTransaction._id,
          side: "Sell",
          quantity: 33,
          status: "Matched",
          filledAt: MONTH_AGO,
          isin: ISIN
        })
      ]);

      // today's ticker
      const todaysIntraDayTicker = await buildIntraDayAssetTicker({
        currency: "USD",
        timestamp: new Date(),
        pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
        investmentProduct: investmentProduct.id
      });
      // weekly tenor intraday data
      const eodWeeklyIntraDayData = [{ timestamp: new Date(YESTERDAY).getTime(), close: 1 }];
      await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);
      // monthly tenor intraday data
      const eodMonthlyIntraDayData = [
        { timestamp: new Date(YESTERDAY).getTime(), close: 1 },
        { timestamp: new Date(WEEK_AGO).getTime(), close: 2 }
      ];
      await RedisClientService.Instance.set("eod:historical:m:equities_apple", eodMonthlyIntraDayData);
      // historical data (3m, 6m, 1y, max)
      const eodHistoricalDailyData = [
        { date: DateTime.fromJSDate(new Date(YESTERDAY)).toISODate(), close: 1 },
        { date: DateTime.fromJSDate(new Date(WEEK_AGO)).toISODate(), close: 2 },
        { date: DateTime.fromJSDate(new Date(MONTH_AGO)).toISODate(), close: 5 }
      ];
      await RedisClientService.Instance.set("eod:historical:equities_apple", eodHistoricalDailyData);

      // expected response by tenor
      const ONE_WEEK_RESPONSE = {
        // [1, 10]
        data: [
          {
            timestamp: DateUtil.mapDateToClosestTimeMark(
              new Date(eodWeeklyIntraDayData[0].timestamp),
              10,
              "minutes"
            ).getTime(),
            close: eodWeeklyIntraDayData[0].close,
            data: {
              quantity: 27,
              type: "net"
            }
          },
          {
            timestamp: DateUtil.mapDateToClosestTimeMark(
              new Date(todaysIntraDayTicker.timestamp),
              10,
              "minutes"
            ).getTime(),
            close: todaysIntraDayTicker.pricePerCurrency["USD"]
          }
        ],
        // (10 - 1) / 1 * 100
        returns: 900,
        displayIntraday: true
      };
      const ONE_MONTH_RESPONSE = {
        // [1, 2, 10]
        data: [
          {
            timestamp: DateUtil.mapDateToClosestTimeMark(
              new Date(eodMonthlyIntraDayData[1].timestamp),
              30,
              "minutes"
            ).getTime(),
            close: eodMonthlyIntraDayData[1].close,
            data: {
              quantity: -8,
              type: "net"
            }
          },
          {
            timestamp: DateUtil.mapDateToClosestTimeMark(
              new Date(eodMonthlyIntraDayData[0].timestamp),
              30,
              "minutes"
            ).getTime(),
            close: eodMonthlyIntraDayData[0].close,
            data: {
              quantity: 2,
              type: "net"
            }
          },
          {
            timestamp: DateUtil.mapDateToClosestTimeMark(
              new Date(todaysIntraDayTicker.timestamp),
              30,
              "minutes"
            ).getTime(),
            close: todaysIntraDayTicker.pricePerCurrency["USD"]
          }
        ],
        // (10 - 2) / 2 * 100
        returns: 400,
        displayIntraday: true
      };
      const THREE_MONTHS_RESPONSE = {
        // [1, 2, 5, 10]
        data: [
          {
            timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
              .toJSDate()
              .getTime(),
            close: eodHistoricalDailyData[0].close,
            data: {
              quantity: 2,
              type: "net"
            }
          },
          {
            timestamp: DateTime.fromISO(eodHistoricalDailyData[1].date as string)
              .toJSDate()
              .getTime(),
            close: eodHistoricalDailyData[1].close,
            data: {
              quantity: 25,
              type: "buy"
            }
          },
          {
            timestamp: DateTime.fromISO(eodHistoricalDailyData[2].date as string)
              .toJSDate()
              .getTime(),
            close: eodHistoricalDailyData[2].close,
            data: {
              quantity: -33,
              type: "sell"
            }
          },
          {
            timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
            close: todaysIntraDayTicker.pricePerCurrency["USD"]
          }
        ],
        // (10 - 1) / 1 * 100
        returns: 900,
        displayIntraday: false
      };

      // assertions
      const response = await request(app)
        .get("/api/m2m/investment-products/prices-by-tenor?assetId=equities_apple")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          "1w": ONE_WEEK_RESPONSE,
          "1m": ONE_MONTH_RESPONSE,
          "3m": THREE_MONTHS_RESPONSE,
          // No new changes past 3 months - reuse response
          "6m": THREE_MONTHS_RESPONSE,
          "1y": THREE_MONTHS_RESPONSE,
          max: THREE_MONTHS_RESPONSE
        })
      );
    });

    describe("when today's intraday prices and historical data timestamps coincide and user has order activity today", () => {
      it("should respond successfully with the all aggregated prices and only keep unique timestamps prioritising today's intraday prices", async () => {
        // set conditions
        const ASSET_ID = "equities_apple";

        const YESTERDAY = DateUtil.getDateOfDaysAgo(new Date(), 1);
        const WEEK_AGO = DateUtil.getDateOfDaysAgo(new Date(), 7);
        const MONTH_AGO = DateUtil.getDateOfDaysAgo(new Date(), 30);
        const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });

        const ISIN = investmentUniverseConfig.ASSET_CONFIG[ASSET_ID].isin;

        await Promise.all([
          buildAssetTransactionWithOrder(
            { owner: user.id, portfolioTransactionCategory: "update", status: "Settled" },
            {
              side: "Buy",
              quantity: 12,
              status: "Matched",
              filledAt: new Date(),
              isin: ISIN
            }
          ),
          buildAssetTransactionWithOrder(
            { owner: user.id, portfolioTransactionCategory: "update", status: "Pending" },
            {
              side: "Sell",
              quantity: 10,
              status: "Matched",
              filledAt: new Date(),
              isin: ISIN
            }
          )
        ]);

        // today's ticker
        const todaysIntraDayTicker = await buildIntraDayAssetTicker({
          currency: "USD",
          timestamp: new Date(),
          pricePerCurrency: { GBP: 9, EUR: 9, USD: 10 },
          investmentProduct: investmentProduct.id
        });

        // historical intraday ticker that should be overriden by today's intradayticker with the same timestamp
        const sameTimestampAsIntraDayTickerThatShouldNotBeUsed = { timestamp: new Date().getTime(), close: 11 };

        // weekly tenor intraday data
        const eodWeeklyIntraDayData = [
          { timestamp: new Date(YESTERDAY).getTime(), close: 1 },
          sameTimestampAsIntraDayTickerThatShouldNotBeUsed
        ];
        await RedisClientService.Instance.set("eod:historical:w:equities_apple", eodWeeklyIntraDayData);
        // monthly tenor intraday data
        const eodMonthlyIntraDayData = [
          { timestamp: new Date(YESTERDAY).getTime(), close: 1 },
          { timestamp: new Date(WEEK_AGO).getTime(), close: 2 }
        ];
        await RedisClientService.Instance.set("eod:historical:m:equities_apple", eodMonthlyIntraDayData);
        // historical data (3m, 6m, 1y, max)
        const eodHistoricalDailyData = [
          { date: DateTime.fromJSDate(new Date(YESTERDAY)).toISODate(), close: 1 },
          { date: DateTime.fromJSDate(new Date(WEEK_AGO)).toISODate(), close: 2 },
          { date: DateTime.fromJSDate(new Date(MONTH_AGO)).toISODate(), close: 5 }
        ];
        await RedisClientService.Instance.set("eod:historical:equities_apple", eodHistoricalDailyData);

        // expected response by tenor
        const ONE_WEEK_RESPONSE = {
          // [1, 10]
          data: [
            {
              timestamp: DateUtil.mapDateToClosestTimeMark(
                new Date(eodWeeklyIntraDayData[0].timestamp),
                10,
                "minutes"
              ).getTime(),
              close: eodWeeklyIntraDayData[0].close
            },
            {
              timestamp: DateUtil.mapDateToClosestTimeMark(
                new Date(todaysIntraDayTicker.timestamp),
                10,
                "minutes"
              ).getTime(),
              close: todaysIntraDayTicker.pricePerCurrency["USD"],
              data: {
                quantity: 2,
                type: "net"
              }
            }
          ],
          // (10 - 1) / 1 * 100
          returns: 900,
          displayIntraday: true
        };
        const ONE_MONTH_RESPONSE = {
          // [1, 2, 10]
          data: [
            {
              timestamp: DateUtil.mapDateToClosestTimeMark(
                new Date(eodMonthlyIntraDayData[1].timestamp),
                30,
                "minutes"
              ).getTime(),
              close: eodMonthlyIntraDayData[1].close
            },
            {
              timestamp: DateUtil.mapDateToClosestTimeMark(
                new Date(eodMonthlyIntraDayData[0].timestamp),
                30,
                "minutes"
              ).getTime(),
              close: eodMonthlyIntraDayData[0].close
            },
            {
              timestamp: DateUtil.mapDateToClosestTimeMark(
                new Date(todaysIntraDayTicker.timestamp),
                30,
                "minutes"
              ).getTime(),
              close: todaysIntraDayTicker.pricePerCurrency["USD"],
              data: {
                quantity: 2,
                type: "net"
              }
            }
          ],
          // (10 - 2) / 2 * 100
          returns: 400,
          displayIntraday: true
        };
        const THREE_MONTHS_RESPONSE = {
          // [1, 2, 5, 10]
          data: [
            {
              timestamp: DateTime.fromISO(eodHistoricalDailyData[0].date as string)
                .toJSDate()
                .getTime(),
              close: eodHistoricalDailyData[0].close
            },
            {
              timestamp: DateTime.fromISO(eodHistoricalDailyData[1].date as string)
                .toJSDate()
                .getTime(),
              close: eodHistoricalDailyData[1].close
            },
            {
              timestamp: DateTime.fromISO(eodHistoricalDailyData[2].date as string)
                .toJSDate()
                .getTime(),
              close: eodHistoricalDailyData[2].close
            },
            {
              timestamp: new Date(todaysIntraDayTicker.timestamp).getTime(),
              close: todaysIntraDayTicker.pricePerCurrency["USD"],
              data: {
                quantity: 2,
                type: "net"
              }
            }
          ],
          // (10 - 1) / 1 * 100
          returns: 900,
          displayIntraday: false
        };

        // assertions
        const response = await request(app)
          .get("/api/m2m/investment-products/prices-by-tenor?assetId=equities_apple")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            "1w": ONE_WEEK_RESPONSE,
            "1m": ONE_MONTH_RESPONSE,
            "3m": THREE_MONTHS_RESPONSE,
            // No new changes past 3 months - reuse response
            "6m": THREE_MONTHS_RESPONSE,
            "1y": THREE_MONTHS_RESPONSE,
            max: THREE_MONTHS_RESPONSE
          })
        );
      });
    });
  });

  describe("GET /investment-products/recent-activity", () => {
    const TODAY = new Date("2023-12-07T09:00:00Z");

    beforeEach(() => {
      jest.clearAllMocks();
      Date.now = jest.fn(() => TODAY.valueOf());
    });
    afterEach(async () => await clearDb());

    it("should return status 400 with proper message for invalid assetId param", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/investment-products/recent-activity?assetId=garbageId")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "garbageId is not a valid asset"
          }
        })
      );
    });

    it("should return status 200 with a list of all transactions that match the asset in descending order and has a limit of 5 transactions", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });
      await buildHoldingDTO(true, "corporate_bonds_eu", 1);

      await Promise.all([
        buildDividendTransaction({
          owner: user.id,
          asset: "corporate_bonds_eu",
          createdAt: new Date("2023-09-11T11:00:00Z")
        }),
        buildReward({
          targetUser: user.id,
          asset: "corporate_bonds_eu",
          status: "Settled",
          createdAt: new Date("2023-08-11T11:00:00Z"),
          updatedAt: new Date("2023-08-12T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "corporate_bonds_eu",
          createdAt: new Date("2023-07-11T11:00:00Z"),
          updatedAt: new Date("2023-07-11T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "corporate_bonds_eu",
          createdAt: new Date("2023-06-11T11:00:00Z"),
          updatedAt: new Date("2023-06-11T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "corporate_bonds_eu",
          createdAt: new Date("2023-05-11T11:00:00Z"),
          updatedAt: new Date("2023-05-11T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "corporate_bonds_eu",
          createdAt: new Date("2023-04-11T11:00:00Z"),
          updatedAt: new Date("2023-04-11T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "corporate_bonds_eu",
          createdAt: new Date("2023-03-11T11:00:00Z"),
          updatedAt: new Date("2023-03-11T11:00:00Z"),
          accepted: true
        })
      ]);

      const assetTransaction = await buildAssetTransaction({
        owner: user.id
      });
      const secondAssetTransaction = await buildAssetTransaction({
        owner: user.id
      });

      const firstOrder = await buildOrder({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
        side: "Sell",
        status: "Matched",
        transaction: new mongoose.Types.ObjectId(assetTransaction.id),
        isin: ASSET_CONFIG["corporate_bonds_eu"].isin,
        createdAt: new Date("2023-12-05T11:00:00Z"),
        filledAt: new Date("2023-12-05T12:00:00Z")
      });
      assetTransaction.orders = [firstOrder];
      await assetTransaction.save();

      const secondOrder = await buildOrder({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
        side: "Sell",
        status: "Matched",
        transaction: new mongoose.Types.ObjectId(secondAssetTransaction.id),
        isin: ASSET_CONFIG["corporate_bonds_eu"].isin,
        createdAt: new Date("2023-12-05T11:00:00Z"),
        filledAt: new Date("2023-12-05T14:00:00Z")
      });
      secondAssetTransaction.orders = [secondOrder];
      await secondAssetTransaction.save();

      const response = await request(app)
        .get("/api/m2m/investment-products/recent-activity?assetId=corporate_bonds_eu&limit=5")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const data = JSON.parse(response.text);
      expect(data.length).toBe(5);

      expect(data).toEqual([
        expect.objectContaining({
          type: "order",
          item: expect.objectContaining({
            isin: secondOrder.isin,
            _id: secondOrder.id,
            transaction: expect.objectContaining({
              _id: secondAssetTransaction.id,
              owner: user.id
            }),
            createdAt: "2023-12-05T11:00:00.000Z",
            displayDate: "2023-12-05T14:00:00.000Z",
            isCancellable: false
          })
        }),
        expect.objectContaining({
          type: "order",
          item: expect.objectContaining({
            isin: firstOrder.isin,
            _id: firstOrder.id,
            transaction: expect.objectContaining({
              _id: assetTransaction.id,
              owner: user.id
            }),
            createdAt: "2023-12-05T11:00:00.000Z",
            displayDate: "2023-12-05T12:00:00.000Z",
            isCancellable: false
          })
        }),
        expect.objectContaining({
          type: "dividend",
          item: expect.objectContaining({
            asset: "corporate_bonds_eu",
            owner: user.id,
            createdAt: "2023-09-11T11:00:00.000Z",
            displayDate: "2023-09-11T11:00:00.000Z"
          })
        }),
        expect.objectContaining({
          type: "reward",
          item: expect.objectContaining({
            asset: "corporate_bonds_eu",
            targetUser: expect.objectContaining({ id: user.id }),
            createdAt: "2023-08-11T11:00:00.000Z",
            displayDate: "2023-08-12T11:00:00.000Z",
            isCancellable: false,
            accepted: true
          })
        }),
        expect.objectContaining({
          type: "reward",
          item: expect.objectContaining({
            asset: "corporate_bonds_eu",
            targetUser: expect.objectContaining({ id: user.id }),
            createdAt: "2023-07-11T11:00:00.000Z",
            displayDate: "2023-07-11T11:00:00.000Z",
            isCancellable: false,
            accepted: true
          })
        })
      ]);
    });

    it("should return status 200 with a list of all transactions that match the asset in descending order and with no limit", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });
      await buildHoldingDTO(true, "equities_block", 1);

      await Promise.all([
        buildDividendTransaction({
          owner: user.id,
          asset: "equities_block",
          createdAt: new Date("2023-09-11T11:00:00Z")
        }),
        buildReward({ asset: "equities_block", accepted: true }),
        buildReward({
          targetUser: user.id,
          asset: "equities_block",
          createdAt: new Date("2023-08-11T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "equities_block",
          createdAt: new Date("2023-07-11T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "equities_block",
          createdAt: new Date("2023-06-11T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "equities_block",
          createdAt: new Date("2023-05-11T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "equities_block",
          createdAt: new Date("2023-04-11T11:00:00Z"),
          accepted: true
        }),
        buildReward({
          targetUser: user.id,
          asset: "equities_block",
          createdAt: new Date("2023-03-11T11:00:00Z"),
          accepted: true
        })
      ]);

      const assetTransaction = await buildAssetTransaction({
        owner: user.id
      });
      const secondAssetTransaction = await buildAssetTransaction({
        owner: user.id
      });

      const firstOrder = await buildOrder({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
        side: "Sell",
        quantity: 1,
        transaction: new mongoose.Types.ObjectId(assetTransaction.id),
        isin: ASSET_CONFIG["equities_block"].isin,
        createdAt: new Date("2023-12-05T11:00:00Z")
      });
      assetTransaction.orders = [firstOrder];
      await assetTransaction.save();

      const secondOrder = await buildOrder({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
        side: "Sell",
        quantity: 1,
        transaction: new mongoose.Types.ObjectId(secondAssetTransaction.id),
        isin: ASSET_CONFIG["equities_block"].isin,
        createdAt: new Date("2023-12-05T11:00:00Z")
      });
      secondAssetTransaction.orders = [secondOrder];
      await secondAssetTransaction.save();

      const response = await request(app)
        .get("/api/m2m/investment-products/recent-activity?assetId=equities_block")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const data = JSON.parse(response.text);
      expect(data.length).toBe(9);

      expect(data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: firstOrder.isin,
              _id: firstOrder.id,
              transaction: expect.objectContaining({
                _id: assetTransaction.id,
                owner: user.id
              }),
              createdAt: "2023-12-05T11:00:00.000Z",
              isCancellable: false
            })
          }),
          expect.objectContaining({
            type: "order",
            item: expect.objectContaining({
              isin: secondOrder.isin,
              _id: secondOrder.id,
              transaction: expect.objectContaining({
                _id: secondAssetTransaction.id,
                owner: user.id
              }),
              createdAt: "2023-12-05T11:00:00.000Z",
              isCancellable: false
            })
          }),
          expect.objectContaining({
            type: "dividend",
            item: expect.objectContaining({
              asset: "equities_block",
              owner: user.id,
              createdAt: "2023-09-11T11:00:00.000Z"
            })
          }),
          expect.objectContaining({
            type: "reward",
            item: expect.objectContaining({
              asset: "equities_block",
              targetUser: expect.objectContaining({ id: user.id }),
              createdAt: "2023-08-11T11:00:00.000Z",
              isCancellable: false,
              accepted: true
            })
          }),
          expect.objectContaining({
            type: "reward",
            item: expect.objectContaining({
              asset: "equities_block",
              targetUser: expect.objectContaining({ id: user.id }),
              createdAt: "2023-07-11T11:00:00.000Z",
              isCancellable: false,
              accepted: true
            })
          }),
          expect.objectContaining({
            type: "reward",
            item: expect.objectContaining({
              asset: "equities_block",
              targetUser: expect.objectContaining({ id: user.id }),
              createdAt: "2023-06-11T11:00:00.000Z",
              isCancellable: false,
              accepted: true
            })
          }),
          expect.objectContaining({
            type: "reward",
            item: expect.objectContaining({
              asset: "equities_block",
              targetUser: expect.objectContaining({ id: user.id }),
              createdAt: "2023-05-11T11:00:00.000Z",
              isCancellable: false,
              accepted: true
            })
          }),
          expect.objectContaining({
            type: "reward",
            item: expect.objectContaining({
              asset: "equities_block",
              targetUser: expect.objectContaining({ id: user.id }),
              createdAt: "2023-04-11T11:00:00.000Z",
              isCancellable: false,
              accepted: true
            })
          }),
          expect.objectContaining({
            type: "reward",
            item: expect.objectContaining({
              asset: "equities_block",
              targetUser: expect.objectContaining({ id: user.id }),
              createdAt: "2023-03-11T11:00:00.000Z",
              isCancellable: false,
              accepted: true
            })
          })
        ])
      );
    });
  });

  describe("GET /investment-products/investment-details", function () {
    beforeEach(async () => {
      jest.clearAllMocks();
      await clearDb();
    });
    afterEach(async () => await clearDb());

    it("(with invalid id) should return status 400 with proper message for invalid assetId param", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/investment-products/investment-details?assetId=garbageId")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "garbageId is not a valid asset"
          }
        })
      );
    });

    it("should return status 400 with proper message when user does not hold that asset", async () => {
      const user = await buildUser();
      await buildPortfolio({ owner: user.id });
      const response = await request(app)
        .get("/api/m2m/investment-products/investment-details?assetId=equities_us")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "User does not hold that asset"
          }
        })
      );
    });

    it("should return status 200 for a valid portfolio with positive performance", async () => {
      const user = await buildUser();

      // User has a portfolio worth £1000; £500 of US Stocks and £500 of UK Stocks
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [
          await buildHoldingDTO(
            true,
            "equities_us",
            1,
            { price: 500 },
            { date: new Date("2022-09-30T11:00:00Z") }
          ),
          await buildHoldingDTO(true, "equities_uk", 1, { price: 500 }, { date: new Date("2022-09-30T11:00:00Z") })
        ]
      });
      await buildIntraDayPortfolioTicker({
        portfolio: portfolio._id,
        timestamp: new Date(),
        pricePerCurrency: { GBP: 1000 }
      });

      // User has one portfolio buy transaction
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      assetTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amountSubmitted: 40000,
            amount: 40000,
            currency: "GBP"
          },
          filledAt: new Date("2022-08-31T11:00:00Z"),
          quantity: 1,
          updatedAt: new Date("2022-08-31T11:00:00Z")
        }),
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
          consideration: {
            amountSubmitted: 40000,
            amount: 40000,
            currency: "GBP"
          },
          quantity: 1,
          filledAt: new Date("2022-08-31T11:00:00Z"),
          updatedAt: new Date("2022-08-31T11:00:00Z")
        })
      ];

      await assetTransaction.save();

      const response = await request(app)
        .get("/api/m2m/investment-products/investment-details?assetId=equities_us")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const data = JSON.parse(response.text);
      expect(data).toEqual(
        expect.objectContaining({
          currentValue: "£500.00",
          numberOfShares: "1",
          performancePercentage: "Up by 25.00%",
          performanceValue: "+£100.00",
          portfolioAllocation: "50.00%",
          isPerformancePositive: true,
          averagePricePerShare: "£400.00", // (400 / 1 + 400 / 1) / (1+1)
          totalDividends: "£0.00"
        })
      );
    });

    it("should return status 200 for a valid portfolio with positive performance that was bought for the first time on the same day", async () => {
      const user = await buildUser();

      const TODAY = new Date("2022-09-30T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      // User has a portfolio worth £500; £500 of US Stocks
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, "equities_us", 1, { price: 520 }, { date: TODAY })]
      });
      await buildIntraDayPortfolioTicker({
        portfolio: portfolio._id,
        timestamp: TODAY,
        pricePerCurrency: { GBP: 520 }
      });

      // User has one portfolio buy transaction
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      assetTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amountSubmitted: 50000,
            amount: 50000,
            currency: "GBP"
          },
          quantity: 1,
          updatedAt: DateUtil.getDateOfHoursAgo(TODAY, 1)
        })
      ];

      await assetTransaction.save();

      const response = await request(app)
        .get("/api/m2m/investment-products/investment-details?assetId=equities_us")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const data = JSON.parse(response.text);
      expect(data).toEqual(
        expect.objectContaining({
          currentValue: "£520.00",
          numberOfShares: "1",
          performancePercentage: "Up by 4.00%",
          performanceValue: "+£20.00",
          portfolioAllocation: "100.00%",
          isPerformancePositive: true,
          averagePricePerShare: "£500.00",
          totalDividends: "£0.00"
        })
      );
    });

    it("should return status 200 for a valid portfolio with negative performance", async () => {
      const user = await buildUser();

      // User has a portfolio worth £1000; £500 of US Stocks and £500 of UK Stocks
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [
          await buildHoldingDTO(
            true,
            "equities_us",
            1,
            { price: 400 },
            { date: new Date("2022-09-30T11:00:00Z") }
          ),
          await buildHoldingDTO(true, "equities_uk", 1, { price: 400 }, { date: new Date("2022-09-30T11:00:00Z") })
        ]
      });
      await buildIntraDayPortfolioTicker({
        portfolio: portfolio._id,
        timestamp: new Date(),
        pricePerCurrency: { GBP: 800 }
      });

      // User has one portfolio buy transaction
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      assetTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amountSubmitted: 50000,
            amount: 50000,
            currency: "GBP"
          },
          quantity: 1,
          filledAt: new Date("2022-08-31T11:00:00Z"),
          updatedAt: new Date("2022-08-31T11:00:00Z")
        }),
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
          consideration: {
            amountSubmitted: 50000,
            amount: 50000,
            currency: "GBP"
          },
          quantity: 1,
          filledAt: new Date("2022-08-31T11:00:00Z"),
          updatedAt: new Date("2022-08-31T11:00:00Z")
        })
      ];

      await assetTransaction.save();

      const response = await request(app)
        .get("/api/m2m/investment-products/investment-details?assetId=equities_us")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const data = JSON.parse(response.text);
      expect(data).toEqual(
        expect.objectContaining({
          currentValue: "£400.00",
          numberOfShares: "1",
          performancePercentage: "Down by 20.00%",
          performanceValue: "-£100.00",
          portfolioAllocation: "50.00%",
          isPerformancePositive: false,
          averagePricePerShare: "£500.00", // (500 / 1 + 500 / 1) / (1+1)
          totalDividends: "£0.00"
        })
      );
    });

    it("should return status 200 for an investment that has dividends", async () => {
      const user = await buildUser();

      // User has a portfolio worth £1000; £500 of US Stocks and £500 of UK Stocks
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [
          await buildHoldingDTO(
            true,
            "equities_us",
            1,
            { price: 400 },
            { date: new Date("2022-09-30T11:00:00Z") }
          ),
          await buildHoldingDTO(true, "equities_uk", 1, { price: 400 }, { date: new Date("2022-09-30T11:00:00Z") })
        ]
      });
      await buildIntraDayPortfolioTicker({
        portfolio: portfolio._id,
        timestamp: new Date(),
        pricePerCurrency: { GBP: 800 }
      });

      // User has one portfolio buy transaction
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        portfolioTransactionCategory: "buy"
      });

      assetTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amountSubmitted: 50000,
            amount: 50000,
            currency: "GBP"
          },
          quantity: 1,
          updatedAt: new Date("2022-08-31T11:00:00Z")
        })
      ];

      await assetTransaction.save();

      await Promise.all([
        buildDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          asset: "equities_us",
          consideration: {
            amount: 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        }),
        buildDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          asset: "equities_us",
          consideration: {
            amount: 200,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        })
      ]);

      const response = await request(app)
        .get("/api/m2m/investment-products/investment-details?assetId=equities_us")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const data = JSON.parse(response.text);
      expect(data).toEqual(
        expect.objectContaining({
          averagePricePerShare: "£500.00",
          totalDividends: "£3.00"
        })
      );
    });
  });
});
