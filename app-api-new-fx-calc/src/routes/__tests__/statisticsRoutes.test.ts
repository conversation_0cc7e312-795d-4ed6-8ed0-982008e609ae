import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { closeDb, connectDb } from "../../tests/utils/db";
import { buildUser } from "../../tests/utils/generateModels";
import axios from "axios";
import { StatisticsService } from "../../services/statisticsService";
import { allocationsConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";

const { ASSET_CONFIG } = investmentUniverseConfig;

const PAST_PERFORMANCE = {
  "10y": {
    "1663200000000": 2696.7714768459,
    "1663286400000": 2675.5469611692,
    "1663545600000": 2689.7329609475,
    "1663632000000": 2665.9997482949,
    "1663718400000": 2634.1958902211
  },
  "5y": {
    "1663545600000": 1503.0635774256,
    "1663632000000": 1489.8888181047,
    "1663718400000": 1473.1111956241
  }
};
const PAST_PERFORMANCE_MODIFIED = {
  "10y": [
    {
      date: new Date(1663200000000),
      value: 2697
    },
    {
      date: new Date(1663286400000),
      value: 2676
    },
    {
      date: new Date(1663545600000),
      value: 2690
    },
    {
      date: new Date(1663632000000),
      value: 2666
    },
    {
      date: new Date(1663718400000),
      value: 2634
    }
  ],
  "5y": [
    {
      date: new Date(1663545600000),
      value: 1503
    },
    {
      date: new Date(1663632000000),
      value: 1490
    },
    {
      date: new Date(1663718400000),
      value: 1473
    }
  ]
};

const METRICS = {
  "10y": {
    maximum_drawdown: 28.5,
    volatility: 15.1,
    annualised_return: 10.2
  },
  "5y": {
    maximum_drawdown: 27.2,
    volatility: 18.3,
    annualised_return: 8.1
  }
};

jest.mock("axios", () => {
  // Require the original module to not be mocked...
  const originalModule = jest.requireActual("axios");

  return {
    ...originalModule,
    post: jest.fn().mockReturnValue({ data: { access_token: 123, expires_in: 600000 } }),
    get: jest.fn().mockReturnValue({ data: { allocation: {} } })
  };
});

describe("StatisticsRoutes", () => {
  beforeAll(async () => await connectDb("StatisticsRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /statistics/optimal-allocation", () => {
    let user: UserDocument;

    beforeAll(async () => {
      user = await buildUser();
    });

    it("should fail for invalid risk param", async () => {
      const response = await request(app)
        .get("/api/m2m/statistics/optimal-allocation?risk=nonumber&asset=Gold")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should fail for invalid universe params", async () => {
      const response = await request(app)
        .get("/api/m2m/statistics/optimal-allocation?risk=nonumber&asset=commodities_gold")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should fail for invalid robo-advisor risk template", async () => {
      const response = await request(app)
        .get("/api/m2m/statistics/optimal-allocation?roboAdvisorRiskLevel=VERYCAUTIOUS")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toBe(400);
    });

    it("should succeed for valid robo-advisor risk template", async () => {
      jest.spyOn(StatisticsService, "fetchOptimalAllocation");

      const response = await request(app)
        .get(
          `/api/m2m/statistics/optimal-allocation?roboAdvisorRiskLevel=${allocationsConfig.RoboAdvisorRiskLevelEnum.CAUTIOUS}`
        )
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(StatisticsService.fetchOptimalAllocation).toHaveBeenCalledWith({
        risk: 0.15,
        asset: expect.arrayContaining([
          "US-Equities",
          "Global-Equities",
          "UK-Equities",
          "EU-Equities",
          "EU-Government",
          "UK-Government",
          "US-Government",
          "Gold",
          "Global-Real-Estate"
        ])
      });
    });

    it("should succeed for valid risk & investment universe of one asset", async () => {
      jest.spyOn(StatisticsService, "fetchOptimalAllocation");

      const response = await request(app)
        .get("/api/m2m/statistics/optimal-allocation?risk=0.5&asset=Gold")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(StatisticsService.fetchOptimalAllocation).toHaveBeenCalledWith({
        risk: 0.5,
        asset: ["Gold"]
      });
    });

    it("should succeed for valid risk & investment universe of multiple assets", async () => {
      jest.spyOn(StatisticsService, "fetchOptimalAllocation");

      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/statistics/optimal-allocation?risk=0.5&asset=Global-Equities&asset=Gold")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(StatisticsService.fetchOptimalAllocation).toHaveBeenCalledWith({
        risk: 0.5,
        asset: expect.arrayContaining(["Gold", "Global-Equities"])
      });
    });
  });

  describe("GET /statistics/past-performance", () => {
    let user: UserDocument;

    beforeAll(async () => {
      user = await buildUser();
    });
    afterEach(async () => {
      jest.clearAllMocks();
    });

    it("should fail with status 400 if parameter initial is invalid", async () => {
      const invalidInitial = "23asd";
      const response = await request(app)
        .get(`/api/m2m/statistics/past-performance?initial=${invalidInitial}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: "Invalid value for param 'initial' , should be numeric",
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should fail with status 400 if asset parameter key is invalid", async () => {
      const invalidAssetKey = "SADssdS";
      const response = await request(app)
        .get(`/api/m2m/statistics/past-performance?initial=1000&${invalidAssetKey}=34`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: `${invalidAssetKey} is not a valid asset`,
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should fail with status 400 if asset parameter value is invalid", async () => {
      const invalidAssetValue = "345sD";
      const response = await request(app)
        .get(`/api/m2m/statistics/past-performance?initial=1000&equities_uk=${invalidAssetValue}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: "Invalid value for param 'equities_uk' , should be numeric",
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should fail with status 400 if allocation does not sum to 100", async () => {
      const response = await request(app)
        .get("/api/m2m/statistics/past-performance?initial=1000&equities_uk=50")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: "Allocation does not sum to 100",
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should succeed with status 200", async () => {
      jest.mock("axios");
      jest.spyOn(axios, "get").mockResolvedValueOnce({
        data: {
          total_past_performance_all: PAST_PERFORMANCE,
          metrics: METRICS
        }
      });

      const INITIAL = 1000;

      const response = await request(app)
        .get(`/api/m2m/statistics/past-performance?initial=${INITIAL}&equities_uk=50&equities_eu=50`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(axios.get).toHaveBeenCalledWith(
        StatisticsService.URLS.portfolioPastPerformanceV3,
        expect.objectContaining({
          params: expect.objectContaining({
            initial: INITIAL,
            tenor: "0",
            [(ASSET_CONFIG["equities_uk"] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping]: 50,
            [(ASSET_CONFIG["equities_eu"] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping]: 50
          })
        })
      );
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject(
        JSON.parse(
          JSON.stringify({
            pastPerformance: PAST_PERFORMANCE_MODIFIED,
            metrics: METRICS
          })
        )
      );
    });
  });

  describe("GET /statistics/future-performance", () => {
    let user: UserDocument;

    beforeAll(async () => {
      user = await buildUser();
    });
    afterEach(async () => {
      jest.clearAllMocks();
    });

    it("should fail with status 400 if parameter initial is invalid", async () => {
      const invalidInitial = "23asd";
      const response = await request(app)
        .get(`/api/m2m/statistics/future-performance?initial=${invalidInitial}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: "Invalid value for param 'initial' , should be numeric",
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should fail with status 400 if asset parameter key is invalid", async () => {
      const invalidAssetKey = "SADssdS";
      const response = await request(app)
        .get(`/api/m2m/statistics/future-performance?initial=1000&${invalidAssetKey}=34`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: `${invalidAssetKey} is not a valid asset`,
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should fail with status 400 if asset parameter value is invalid", async () => {
      const invalidAssetValue = "345sD";
      const response = await request(app)
        .get(`/api/m2m/statistics/future-performance?initial=1000&equities_uk=${invalidAssetValue}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: "Invalid value for param 'equities_uk' , should be numeric",
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should fail with status 400 if allocation does not sum to 100", async () => {
      const response = await request(app)
        .get("/api/m2m/statistics/future-performance?initial=1000&equities_uk=50")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: "Allocation does not sum to 100",
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should succeed with status 200", async () => {
      const INITIAL = 1000;
      jest.mock("axios");
      jest.spyOn(axios, "get").mockResolvedValueOnce({
        data: {
          total_future_performance_all: {},
          total_future_performance_all_best: {},
          total_future_performance_all_worse: {}
        }
      });

      const response = await request(app)
        .get(`/api/m2m/statistics/future-performance?initial=${INITIAL}&equities_uk=50&equities_eu=50`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(axios.get).toHaveBeenCalledWith(
        StatisticsService.URLS.portfolioFuturePerformanceV3,
        expect.anything()
      );
      expect(axios.get).toHaveBeenCalledWith(
        StatisticsService.URLS.portfolioFuturePerformanceV3,
        expect.objectContaining({
          params: expect.objectContaining({
            initial: INITIAL,
            [(ASSET_CONFIG["equities_uk"] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping]: 50,
            [(ASSET_CONFIG["equities_eu"] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping]: 50
          })
        })
      );
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        futurePerformance: {},
        futurePerformanceWorse: {},
        futurePerformanceBest: {}
      });
    });
  });

  describe("GET /statistics/future-performance-monte-carlo", () => {
    let user: UserDocument;

    beforeAll(async () => {
      user = await buildUser();
    });
    afterEach(async () => {
      jest.clearAllMocks();
    });

    it("should fail with status 400 if parameter initial is invalid", async () => {
      const invalidInitial = "23asd";
      const response = await request(app)
        .get(`/api/m2m/statistics/future-performance-monte-carlo?initial=${invalidInitial}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: "Invalid value for param 'initial' , should be numeric",
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should fail with status 400 if asset parameter key is invalid", async () => {
      const invalidAssetKey = "SADssdS";
      const response = await request(app)
        .get(`/api/m2m/statistics/future-performance-monte-carlo?initial=1000&${invalidAssetKey}=34`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: `${invalidAssetKey} is not a valid asset`,
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should fail with status 400 if asset parameter value is invalid", async () => {
      const invalidAssetValue = "345sD";
      const response = await request(app)
        .get(`/api/m2m/statistics/future-performance-monte-carlo?initial=1000&equities_uk=${invalidAssetValue}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: "Invalid value for param 'equities_uk' , should be numeric",
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should fail with status 400 if allocation does not sum to 100", async () => {
      const response = await request(app)
        .get("/api/m2m/statistics/future-performance-monte-carlo?initial=1000&equities_uk=50")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            message: "Allocation does not sum to 100",
            description: "Invalid parameter"
          }
        })
      );
    });

    it("should succeed with status 200", async () => {
      const INITIAL = 150;
      jest.mock("axios");
      jest.spyOn(axios, "get").mockResolvedValueOnce({
        data: {
          total_future_performance_all_best: {},
          total_future_performance_all_worse: {}
        }
      });

      const response = await request(app)
        .get(`/api/m2m/statistics/future-performance-monte-carlo?initial=${INITIAL}&equities_uk=50&equities_eu=50`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(axios.get).toHaveBeenCalledWith(
        StatisticsService.URLS.portfolioFuturePerformanceMonteCarloV3,
        expect.objectContaining({
          params: expect.objectContaining({
            initial: INITIAL,
            [(ASSET_CONFIG["equities_uk"] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping]: 50,
            [(ASSET_CONFIG["equities_eu"] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping]: 50
          })
        })
      );
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        futurePerformanceWorse: {},
        futurePerformanceBest: {}
      });
    });
  });
});
