import request from "supertest";
import app from "../../app";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildNotificationSettings,
  buildPortfolio,
  buildUser,
  buildUserDataRequest
} from "../../tests/utils/generateModels";
import { UserDataRequest } from "../../models/UserDataRequest";
import { PortfolioModeEnum } from "../../models/Portfolio";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";

describe("AdminUserDataRequestRoutes", () => {
  beforeAll(async () => await connectDb("AdminUserDataRequestRoutes"));
  afterAll(async () => await closeDb());

  describe("POST /user-data-requests", () => {
    describe("when request body is empty", () => {
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/user-data-requests")
          .send({})
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Missing field 'owner'"
            }
          })
        );
      });
    });

    describe("when user already has a user data request", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildUserDataRequest({
          requestType: "disassociation",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/user-data-requests")
          .send({ owner: owner.id, requestType: "disassociation" })
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User already has a user data request"
            }
          })
        );
      });
    });

    describe("when request body is valid", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        await Promise.all([
          buildPortfolio({
            owner: owner.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL
          }),
          buildNotificationSettings({ owner: owner.id })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return 204 and successfully create a disassociation request for the user", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/user-data-requests")
          .send({ owner: owner.id, requestType: "disassociation" })
          .set("Accept", "application/json");
        expect(response.status).toBe(204);

        const userDataRequest = await UserDataRequest.findOne({ owner: owner.id });
        expect(userDataRequest).toEqual(
          expect.objectContaining({
            owner: owner._id,
            requestType: "disassociation",
            reason: "admin-request"
          })
        );
      });

      it("should emit event for disassociation", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.disassociation.eventId,
          expect.objectContaining({ email: owner.email }),
          { reason: "admin-request" }
        );
      });
    });
  });
});
