import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildBankAccount, buildMandate, buildUser } from "../../tests/utils/generateModels";
import { BankAccountDocument } from "../../models/BankAccount";
import { MandateDocument } from "../../models/Mandate";
import { ProviderEnum } from "../../configs/providersConfig";
import { TruelayerPaymentsClient } from "../../external-services/truelayerService";
import { buildProviderType } from "../../tests/utils/generateTruelayer";
import { faker } from "@faker-js/faker";
import { entitiesConfig } from "@wealthyhood/shared-configs";

describe("MandateRoutes", () => {
  beforeAll(async () => await connectDb("MandateRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /mandates", () => {
    describe("when user does not have a mandate", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([buildProviderType()]);

        owner = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 200 with an empty data array", async () => {
        const response = await request(app)
          .get("/api/m2m/mandates")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({ data: [] });
      });
    });

    describe("when there is an inactive mandate for the user but includeInactive=false", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const BANK_ID = "mock";
        const truelayerProvider = buildProviderType({ provider_id: BANK_ID });
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([truelayerProvider]);

        owner = await buildUser({}, false);
        const bankAccount = await buildBankAccount({
          owner: owner.id,
          providers: { truelayer: { bankId: BANK_ID } }
        });
        await buildMandate({
          owner: owner.id,
          bankAccount: bankAccount.id,
          providers: {
            gocardless: {
              id: "MND-123",
              status: "cancelled"
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 with an empty data array", async () => {
        const response = await request(app)
          .get("/api/m2m/mandates?includeInactive=false")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({ data: [] });
      });
    });

    describe("when user has both an inactive and an active mandate and includeInactive=true", () => {
      let owner: UserDocument;
      let activeMandate: MandateDocument;
      let inactiveMandate: MandateDocument;

      beforeAll(async () => {
        owner = await buildUser();
        inactiveMandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          providers: {
            gocardless: {
              id: "MND-123",
              status: "cancelled"
            }
          }
        });
        activeMandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the existing mandate", async () => {
        const response = await request(app)
          .get("/api/m2m/mandates?includeInactive=true")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              _id: activeMandate.id,
              owner: owner.id
            }),
            expect.objectContaining({
              _id: inactiveMandate.id,
              owner: owner.id
            })
          ]
        });
      });
    });

    describe("when user has both Top-Up and Subscription mandates but category=Subscription", () => {
      let owner: UserDocument;
      let subscriptionMandate: MandateDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const BANK_ID = "mock";
        const truelayerProvider = buildProviderType({ provider_id: BANK_ID });
        jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([truelayerProvider]);

        owner = await buildUser({}, false);
        const bankAccount = await buildBankAccount({
          owner: owner.id,
          providers: { truelayer: { bankId: BANK_ID } }
        });
        subscriptionMandate = await buildMandate({
          owner: owner.id,
          category: "Subscription",
          bankAccount: bankAccount.id
        });
        await buildMandate({
          owner: owner.id,
          category: "Top-Up",
          bankAccount: bankAccount.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the existing mandate", async () => {
        const response = await request(app)
          .get("/api/m2m/mandates?category=Subscription")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              _id: subscriptionMandate.id,
              owner: owner.id
            })
          ]
        });
      });
    });

    describe("when user has both Top-Up and Subscription mandates but category=TopUp", () => {
      const BANK_ID = "mock-payments-gb-redirect";
      let owner: UserDocument;
      let topUpMandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        owner = await buildUser({}, false);
        bankAccount = await buildBankAccount({
          owner: owner.id,
          bankId: BANK_ID,
          providers: { truelayer: { bankId: BANK_ID } }
        });
        await buildMandate({
          owner: owner.id,
          category: "Subscription",
          bankAccount: bankAccount.id
        });
        topUpMandate = await buildMandate({
          owner: owner.id,
          category: "Top-Up",
          bankAccount: bankAccount.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the existing mandate", async () => {
        const response = await request(app)
          .get("/api/m2m/mandates?category=Top-Up")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              id: topUpMandate.id,
              owner: owner.id,
              bankAccount: expect.objectContaining({
                bankIconURL: "https://img.wealthyhood.dev/bank-icons/Unknown.png",
                displayBankName: "Mock UK Bank",
                displayAccountIdentifier: `${bankAccount.sortCode} ${bankAccount.number}`
              })
            })
          ]
        });
      });
    });

    describe("when user has both Top-Up and Subscription mandates but category=TopUp with EU company entity", () => {
      let owner: UserDocument;
      let topUpMandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        owner = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE }, false);
        bankAccount = await buildBankAccount({
          owner: owner.id,
          iban: faker.finance.iban(),
          bic: "CRBAGRAA",
          bankId: "alphabank",
          name: owner.fullName,
          activeProviders: [ProviderEnum.GOCARDLESS_DATA],
          providers: {
            gocardlessData: {
              id: faker.string.uuid(),
              bankId: "ALPHABANK_CRBAGRAA"
            }
          }
        });
        await buildMandate({
          owner: owner.id,
          category: "Subscription",
          bankAccount: bankAccount.id
        });
        topUpMandate = await buildMandate({
          owner: owner.id,
          category: "Top-Up",
          bankAccount: bankAccount.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the existing mandate", async () => {
        const response = await request(app)
          .get("/api/m2m/mandates?category=Top-Up")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              id: topUpMandate.id,
              owner: owner.id,
              bankAccount: expect.objectContaining({
                displayBankName: "Alpha Bank",
                bankIconURL: "https://img.wealthyhood.dev/bank-icons/AlphaBank.png",
                displayAccountIdentifier: bankAccount.iban
              })
            })
          ]
        });
      });
    });
  });

  describe("POST /mandates", () => {
    describe("when the request body is empty", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/mandates")
          .send({})
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
      });
    });

    describe("when there is an existing pending/active mandate for that bank account", () => {
      let owner: UserDocument;
      let bankAccount: BankAccountDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        owner = await buildUser();
        bankAccount = owner.bankAccounts[0];
        mandate = await buildMandate({ owner: owner.id, bankAccount: bankAccount.id });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the existing mandate", async () => {
        const response = await request(app)
          .post("/api/m2m/mandates")
          .send({
            category: "Subscription",
            bankAccount: bankAccount.id
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: mandate.id,
            owner: owner.id,
            bankAccount: bankAccount.id
          })
        );
      });
    });

    describe("when there is an existing pending/active mandate for that bank account but of a different category", () => {
      let owner: UserDocument;
      let bankAccount: BankAccountDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        owner = await buildUser();
        bankAccount = owner.bankAccounts[0];
        mandate = await buildMandate({ owner: owner.id, bankAccount: bankAccount.id, category: "Top-Up" });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the existing mandate", async () => {
        const response = await request(app)
          .post("/api/m2m/mandates")
          .send({
            category: "Subscription",
            bankAccount: bankAccount.id
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: expect.not.stringMatching(mandate.id),
            owner: owner.id,
            bankAccount: bankAccount.id
          })
        );
      });
    });

    describe("when there is an existing cancelled/failed mandate for that bank account", () => {
      let owner: UserDocument;
      let bankAccount: BankAccountDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        owner = await buildUser();
        bankAccount = owner.bankAccounts[0];
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: bankAccount.id,
          providers: {
            gocardless: {
              id: "MAD123",
              status: "cancelled"
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the created mandate", async () => {
        const response = await request(app)
          .post("/api/m2m/mandates")
          .send({
            category: "Subscription",
            bankAccount: bankAccount.id
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: expect.not.stringMatching(mandate.id),
            owner: owner.id,
            bankAccount: bankAccount.id
          })
        );
      });
    });

    describe("when there is no mandate for that bank account and category is top-up", () => {
      let owner: UserDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        owner = await buildUser();
        bankAccount = owner.bankAccounts[0];
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the created mandate", async () => {
        const response = await request(app)
          .post("/api/m2m/mandates")
          .send({
            category: "Top-Up",
            bankAccount: bankAccount.id
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            bankAccount: bankAccount.id,
            category: "Top-Up",
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          })
        );
      });
    });

    describe("when there is an existing pending/active mandate for that bank account in EU company entity", () => {
      let owner: UserDocument;
      let bankAccount: BankAccountDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        owner = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        });
        bankAccount = owner.bankAccounts[0];
        mandate = await buildMandate({
          category: "Top-Up",
          owner: owner.id,
          bankAccount: bankAccount.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the existing mandate", async () => {
        const response = await request(app)
          .post("/api/m2m/mandates")
          .send({
            category: "Top-Up",
            bankAccount: bankAccount.id
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: mandate.id,
            owner: owner.id,
            bankAccount: bankAccount.id,
            category: "Top-Up",
            activeProviders: [ProviderEnum.GOCARDLESS]
          })
        );
      });
    });

    describe("when there is an existing cancelled/failed mandate for that bank account in EU company entity", () => {
      let owner: UserDocument;
      let bankAccount: BankAccountDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        owner = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        });
        bankAccount = owner.bankAccounts[0];
        mandate = await buildMandate({
          category: "Top-Up",
          owner: owner.id,
          bankAccount: bankAccount.id,
          providers: {
            gocardless: {
              id: "MAD123",
              status: "cancelled"
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the created mandate", async () => {
        const response = await request(app)
          .post("/api/m2m/mandates")
          .send({
            category: "Top-Up",
            bankAccount: bankAccount.id
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: expect.not.stringMatching(mandate.id),
            owner: owner.id,
            bankAccount: bankAccount.id,
            category: "Top-Up",
            activeProviders: [ProviderEnum.GOCARDLESS]
          })
        );
      });
    });
  });
});
