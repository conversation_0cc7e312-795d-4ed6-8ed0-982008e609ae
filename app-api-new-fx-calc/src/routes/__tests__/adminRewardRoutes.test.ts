import request from "supertest";
import supertest from "supertest";
import { fees, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import app from "../../app";
import MailerService from "../../external-services/mailerService";
import { Portfolio, PortfolioDocument } from "../../models/Portfolio";
import { Reward, RewardDocument } from "../../models/Reward";
import { KycStatusEnum, User, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildHoldingDTO,
  buildInvestmentProduct,
  buildNotificationSettings,
  buildPortfolio,
  buildReward,
  buildUser
} from "../../tests/utils/generateModels";
import {
  buildWealthkernelBonusResponse,
  buildWealthkernelOrderResponse
} from "../../tests/utils/generateWealthkernel";
import { CurrencyEnum, WealthkernelService } from "../../external-services/wealthkernelService";
import { faker } from "@faker-js/faker";
import Decimal from "decimal.js";
import { DepositCashTransactionDocument } from "../../models/Transaction";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { ProviderEnum } from "../../configs/providersConfig";

const { MINIMUM_FX_FEE, MINIMUM_COMMISSION_FEE } = fees;
const { AssetArrayConst, ASSET_CONFIG } = investmentUniverseConfig;
const isArraySortedDesc = (arr: any[]) => arr.slice(1).every((item, i) => arr[i] >= item); //desc order

describe("AdminRewardRoutes", () => {
  beforeAll(async () => await connectDb("AdminRewardRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /rewards", () => {
    let firstReward: RewardDocument;
    let secondReward: RewardDocument;
    let response: request.Response;

    afterAll(async () => await clearDb());

    it("should return status 200 with a list of all rewards sorted by date in descending order and with the targetUser field populated", async () => {
      firstReward = await buildReward();
      secondReward = await buildReward();

      response = await request(app).get("/api/admin/m2m/rewards").set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const data = JSON.parse(response.text).data as RewardDocument[];

      expect(data.length).toBe(2);
      expect(isArraySortedDesc(data.map((reward) => new Date(reward.createdAt)))).toEqual(true);
      data.forEach((reward: RewardDocument) => {
        expect((reward.targetUser as UserDocument).email).toBeDefined();
      });
      expect(data[0]._id.toString()).toEqual(secondReward._id.toString());
      expect(data[1]._id.toString()).toEqual(firstReward._id.toString());
    });

    it("should return status 200 with a list of all rewards of the target user", async () => {
      const targetUser = await buildUser();
      await Promise.all([
        buildReward(),
        buildReward({ targetUser: targetUser.id }),
        buildReward({ targetUser: targetUser.id })
      ]);

      response = await request(app)
        .get(`/api/admin/m2m/rewards?targetUser=${targetUser.id}`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const data = JSON.parse(response.text).data as RewardDocument[];

      expect(data.length).toBe(2);
      data.forEach((reward) => expect((reward.targetUser as UserDocument).id).toEqual(targetUser.id));
    });

    it("should return status 200 with a list of all rewards matching asset and orderSubmissionDay params", async () => {
      await Promise.all([
        buildReward({
          asset: "equities_eu"
        }),
        await buildReward({
          asset: "equities_us",
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                submittedAt: new Date("2022-10-07")
              }
            }
          }
        })
      ]);

      const validReward = await buildReward({
        asset: "equities_eu",
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              submittedAt: new Date("2022-10-07")
            }
          }
        }
      });

      response = await request(app)
        .get("/api/admin/m2m/rewards?assetId=equities_eu&orderSubmissionDay=2022-10-07")
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const data = JSON.parse(response.text).data as RewardDocument[];
      expect(data.length).toBe(1);
      expect(data[0].id).toBe(validReward.id);
    });

    it("should return status 400 when 'page' query param is invalid", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/rewards?page=esa&@&pageSize=50")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'page' , should be numeric"
          }
        })
      );
    });

    it("should return status 400 when 'targetUser' query param is invalid", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/rewards?targetUser=garbage_id")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for 'targetUser'"
          }
        })
      );
    });

    it("should return status 400 when 'pageSize' query param is invalid", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/rewards?page=1&pageSize=*@D")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'pageSize' , should be numeric"
          }
        })
      );
    });

    it("(should return status 400 when 'pageSize' query param is missing while 'page' query param is valid", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/rewards?page=1")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'pageSize' is required"
          }
        })
      );
    });

    it("should return status 400 with proper message when 'assetId' param is invalid", async () => {
      const invalidAssetId = "garbage";
      const response = await request(app)
        .get(
          `/api/admin/m2m/rewards?pageSize=50&page=1&populatePortfolio=true&sort=-createdAt&assetId=${invalidAssetId}`
        )
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: { description: "Invalid parameter", message: `${invalidAssetId} is not a valid asset` }
        })
      );
    });

    it("should return status 400 with proper message when 'orderSubmissionDay' param is invalid", async () => {
      const response = await request(app)
        .get(
          "/api/admin/m2m/rewards?pageSize=1&page=2&populatePortfolio=true&sort=-createdAt&orderSubmissionDay=garbage"
        )
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'orderSubmissionDay' , should be date with format YYYY-MM-DD"
          }
        })
      );
    });

    it("should return status 200 with paginated response containing reward array when pagination query params are both valid", async () => {
      await Promise.all([buildReward(), buildReward()]);

      const response = await request(app)
        .get("/api/admin/m2m/rewards?pageSize=1&page=2&populatePortfolio=true&sort=-createdAt")
        .set("Accept", "application/json");

      const expectedData = await Reward.find({})
        .sort({ createdAt: -1 })
        .skip((2 - 1) * 1)
        .limit(1)
        .populate({
          path: "targetUser",
          populate: {
            path: "portfolios"
          }
        });

      expect(expectedData.length).toEqual(1);
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).rewards).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });
  });

  describe("GET /rewards/:id", () => {
    let reward: RewardDocument;
    let response: request.Response;

    beforeAll(async () => {
      reward = await buildReward();
      await buildReward();

      response = await request(app).get(`/api/admin/m2m/rewards/${reward.id}`).set("Accept", "application/json");
    });
    afterAll(async () => await clearDb());

    it("should return the reward document for the corresponding id with the referrer, referral & targetUser fields populated", () => {
      const data = JSON.parse(response.text);
      expect(data.id.toString()).toEqual(reward.id.toString());
      expect(data.referrer.email).toBeDefined();
      expect(data.referral.email).toBeDefined();
      expect(data.targetUser.email).toBeDefined();
    });
  });

  describe("POST /rewards", () => {
    afterEach(async () => await clearDb());

    describe("when we want to send a double reward", () => {
      beforeAll(async () => {
        const referrer = await buildUser();
        const referral = await buildUser();

        const rewardData = {
          referrerEmail: referrer.email,
          referralEmail: referral.email,
          targetUserEmail: referral.email,
          asset: faker.helpers.arrayElement(AssetArrayConst),
          considerationAmount: 10
        };
        await request(app).post("/api/admin/m2m/rewards").send(rewardData).set("Accept", "application/json");
      });

      it("should create a new reward document", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(1);

        expect(rewards[0]).toEqual(
          expect.objectContaining({
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            fees: expect.objectContaining({
              fx: { amount: MINIMUM_FX_FEE, currency: "GBP" },
              commission: { amount: MINIMUM_COMMISSION_FEE, currency: "GBP" },
              executionSpread: { amount: 0, currency: "GBP" } // 0% of £10
            })
          })
        );
      });
    });

    describe("when we want to send a single reward", () => {
      beforeAll(async () => {
        const targetUser = await buildUser();

        await buildPortfolio({
          cash: { GBP: { available: 1000, reserved: 0, settled: 0 } },
          owner: targetUser.id,
          providers: { wealthkernel: { id: "portfolio-id", status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_us", 1)]
        });

        const rewardData = {
          targetUserEmail: targetUser.email,
          asset: faker.helpers.arrayElement(AssetArrayConst),
          considerationAmount: 10
        };
        await request(app).post("/api/admin/m2m/rewards").send(rewardData).set("Accept", "application/json");
      });

      it("should create a new reward document", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(1);

        expect(rewards[0]).toEqual(
          expect.objectContaining({
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            order: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            },
            fees: expect.objectContaining({
              fx: { amount: MINIMUM_FX_FEE, currency: "GBP" },
              commission: { amount: MINIMUM_COMMISSION_FEE, currency: "GBP" },
              executionSpread: { amount: 0, currency: "GBP" } // 0% of £10
            })
          })
        );
      });
    });
  });

  describe("POST /rewards/create-deposits", () => {
    beforeEach(() => {
      jest.resetAllMocks();
    });
    afterEach(async () => await clearDb());

    it("should not call Wealthkernel API for rewards that already have WK deposit ids", async () => {
      jest.spyOn(WealthkernelService.UKInstance, "createBonus");

      await buildReward({
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Created" } }
        }
      });
      await request(app).post("/api/admin/m2m/rewards/create-deposits").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.createBonus).not.toHaveBeenCalled();
    });

    it("should not call Wealthkernel API for rewards that have a consideration amount < £1", async () => {
      jest.spyOn(WealthkernelService.UKInstance, "createBonus");

      const WK_TARGET_PORTFOLIO_ID = faker.string.uuid();

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: WK_TARGET_PORTFOLIO_ID, status: "Active" } }
      });
      await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referral.id,
        consideration: {
          amount: 1,
          currency: "GBP"
        }
      });
      await request(app).post("/api/admin/m2m/rewards/create-deposits").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.createBonus).not.toHaveBeenCalled();
    });

    it("should not call Wealthkernel API for rewards whose target portfolio is not submitted to WK", async () => {
      jest.spyOn(WealthkernelService.UKInstance, "createBonus");

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildPortfolio({
        owner: referral.id,
        providers: undefined
      });
      await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referral.id,
        consideration: {
          amount: 1000,
          currency: "GBP"
        }
      });
      await request(app).post("/api/admin/m2m/rewards/create-deposits").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.createBonus).not.toHaveBeenCalled();
    });

    it("should not call Wealthkernel API for rewards that have not been accepted", async () => {
      const WK_BONUS_ID = faker.string.uuid();
      const WK_TARGET_PORTFOLIO_ID = faker.string.uuid();
      jest.spyOn(WealthkernelService.UKInstance, "createBonus").mockResolvedValue({
        id: WK_BONUS_ID
      });

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: WK_TARGET_PORTFOLIO_ID, status: "Active" } }
      });
      await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referral.id
      });

      const TODAY = new Date("2022-08-31T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      await request(app).post("/api/admin/m2m/rewards/create-deposits").send().set("Accept", "application/json");

      expect(WealthkernelService.UKInstance.createBonus).not.toHaveBeenCalled();
    });

    it("should not call Wealthkernel API for rewards that have been declined", async () => {
      const WK_BONUS_ID = faker.string.uuid();
      const WK_TARGET_PORTFOLIO_ID = faker.string.uuid();
      jest.spyOn(WealthkernelService.UKInstance, "createBonus").mockResolvedValue({
        id: WK_BONUS_ID
      });

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: WK_TARGET_PORTFOLIO_ID, status: "Active" } }
      });
      await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referral.id,
        accepted: false
      });

      const TODAY = new Date("2022-08-31T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      await request(app).post("/api/admin/m2m/rewards/create-deposits").send().set("Accept", "application/json");

      expect(WealthkernelService.UKInstance.createBonus).not.toHaveBeenCalled();
    });

    it("should create bonus payment requests for rewards missing WK deposit ids", async () => {
      const WK_BONUS_ID = faker.string.uuid();
      const WK_GIA_TARGET_PORTFOLIO_ID = faker.string.uuid();
      const WK_ISA_TARGET_PORTFOLIO_ID = faker.string.uuid();
      jest.spyOn(WealthkernelService.UKInstance, "createBonus").mockResolvedValue({
        id: WK_BONUS_ID
      });

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });

      // The referred user has both a GIA and an ISA portfolio
      await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: WK_GIA_TARGET_PORTFOLIO_ID, status: "Active" } }
      });
      await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: WK_ISA_TARGET_PORTFOLIO_ID, status: "Active" } }
      });

      const reward = await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referral.id,
        accepted: true,
        fees: {
          fx: {
            amount: MINIMUM_FX_FEE,
            currency: "GBP"
          },
          commission: {
            amount: MINIMUM_COMMISSION_FEE,
            currency: "GBP"
          },
          executionSpread: {
            amount: faker.number.float({ min: 0.01, max: 0.03 }),
            currency: "GBP"
          }
        }
      });

      const TODAY = new Date("2022-08-31T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      await request(app).post("/api/admin/m2m/rewards/create-deposits").send().set("Accept", "application/json");

      const updatedReward = (await Reward.findOne({ _id: reward.id })) as RewardDocument;
      expect(updatedReward.deposit.providers.wealthkernel).toMatchObject({
        id: WK_BONUS_ID,
        status: "Created",
        submittedAt: TODAY
      });
      expect(updatedReward.depositStatus).toBe("Pending");

      expect(WealthkernelService.UKInstance.createBonus).toHaveBeenCalledWith(
        expect.objectContaining({
          clientReference: reward.id,
          consideration: {
            currency: "GBP",
            amount: Decimal.div(reward.consideration.amount, 100)
              .add(reward.fees.fx.amount)
              .add(reward.fees.commission.amount)
              .add(reward.fees.executionSpread.amount)
              .toNumber()
          },
          destinationPortfolio: WK_GIA_TARGET_PORTFOLIO_ID
        })
      );
    });
  });

  describe("POST /rewards/create-orders", () => {
    beforeEach(() => {
      jest.resetAllMocks();
    });
    afterEach(async () => await clearDb());

    it("should not call Wealthkernel API for rewards that already have WK order ids", async () => {
      jest.spyOn(WealthkernelService.UKInstance, "createOrder");
      jest.spyOn(eventEmitter, "emit");

      const reward = await buildReward({
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid() } }
        }
      });

      await buildInvestmentProduct(true, { assetId: reward.asset });

      await request(app).post("/api/admin/m2m/rewards/create-orders").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      expect(eventEmitter.emit).not.toHaveBeenCalled();
    });

    it("should not call Wealthkernel API for rewards whose ETF has paused buys", async () => {
      jest.spyOn(WealthkernelService.UKInstance, "createOrder");
      jest.spyOn(eventEmitter, "emit");

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: "portfolio-id-2", status: "Active" } }
      });
      const reward = await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referrer.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } }
      });
      await buildInvestmentProduct(true, { assetId: reward.asset, buyLine: { active: false } });

      await request(app).post("/api/admin/m2m/rewards/create-orders").send().set("Accept", "application/json");

      expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      expect(eventEmitter.emit).not.toHaveBeenCalled();
    });

    it("should not call Wealthkernel API for rewards don't have settled associated deposit transaction", async () => {
      jest.spyOn(WealthkernelService.UKInstance, "createOrder");
      jest.spyOn(eventEmitter, "emit");

      const reward = await buildReward({
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Created" } }
        },
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } }
      });

      await buildInvestmentProduct(true, { assetId: reward.asset });

      await request(app).post("/api/admin/m2m/rewards/create-orders").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      expect(eventEmitter.emit).not.toHaveBeenCalled();
    });

    it("should create order requests for for rewards with settled WK deposits that are missing WK order ids", async () => {
      const WK_ORDER_ID = faker.string.uuid();
      const SUBMISSION_DATE = new Date("2022-07-16T11:00:00Z");

      Date.now = jest.fn(() => SUBMISSION_DATE.valueOf());
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({
        id: WK_ORDER_ID
      });
      jest.spyOn(eventEmitter, "emit");

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: "portfolio-id-2", status: "Active" } }
      });
      const reward = await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referrer.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } }
      });

      await buildInvestmentProduct(true, { assetId: reward.asset });

      await request(app).post("/api/admin/m2m/rewards/create-orders").send().set("Accept", "application/json");

      const updatedReward = (await Reward.findOne({ _id: reward.id })) as RewardDocument;
      expect(updatedReward.order.providers.wealthkernel).toMatchObject({
        id: WK_ORDER_ID,
        status: "Pending",
        submittedAt: SUBMISSION_DATE
      });
      expect(updatedReward.depositStatus).toBe("Settled");
      expect(updatedReward.orderStatus).toBe("Pending");
      expect(updatedReward.status).toBe("Pending");

      expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
        expect.objectContaining({ isin: investmentUniverseConfig.ASSET_CONFIG[reward.asset].isin }),
        reward.id
      );

      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        1,
        events.transaction.firstInvestmentCreation.eventId,
        expect.objectContaining({
          email: referrer.email
        })
      );
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        2,
        events.transaction.investmentCreation.eventId,
        expect.objectContaining({ email: referrer.email }),
        expect.objectContaining({
          isFirst: true,
          assetName: investmentUniverseConfig.ASSET_CONFIG[reward.asset].simpleName
        })
      );
    });

    it("should not emit firstInvestmentCreated event if user has another reward", async () => {
      const WK_ORDER_ID = faker.string.uuid();
      const SUBMISSION_DATE = new Date("2022-07-16T11:00:00Z");

      Date.now = jest.fn(() => SUBMISSION_DATE.valueOf());
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({
        id: WK_ORDER_ID
      });
      jest.spyOn(eventEmitter, "emit");

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: "portfolio-id-2", status: "Active" } }
      });
      const reward = await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referrer.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } }
      });

      // Past reward
      await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referrer.id,
        status: "Settled",
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
        },
        quantity: 1
      });

      await buildInvestmentProduct(true, { assetId: reward.asset });

      await request(app).post("/api/admin/m2m/rewards/create-orders").send().set("Accept", "application/json");

      expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.transaction.investmentCreation.eventId,
        expect.objectContaining({ email: referrer.email }),
        expect.objectContaining({ isFirst: false })
      );
    });

    it("should not emit firstInvestmentCreated event if user has an asset transaction", async () => {
      const WK_ORDER_ID = faker.string.uuid();
      const SUBMISSION_DATE = new Date("2022-07-16T11:00:00Z");

      Date.now = jest.fn(() => SUBMISSION_DATE.valueOf());
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({
        id: WK_ORDER_ID
      });
      jest.spyOn(eventEmitter, "emit");

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      const targetUserPortfolio = await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: "portfolio-id-2", status: "Active" } }
      });
      const reward = await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referrer.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } }
      });

      // User has a pending asset transaction that was created before the reward
      await buildAssetTransaction({
        portfolio: targetUserPortfolio.id,
        owner: referrer.id,
        portfolioTransactionCategory: "buy",
        status: "Pending"
      });

      await buildInvestmentProduct(true, { assetId: reward.asset });

      await request(app).post("/api/admin/m2m/rewards/create-orders").send().set("Accept", "application/json");

      expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.transaction.investmentCreation.eventId,
        expect.objectContaining({ email: referrer.email }),
        expect.objectContaining({ isFirst: false })
      );
    });
  });

  describe("POST /rewards/sync-deposits", () => {
    const DATE = new Date("2022-08-31T11:00:00Z");
    let minimumCreationTime: Date;

    beforeEach(() => {
      jest.resetAllMocks();
      Date.now = jest.fn(() => DATE.valueOf());
      minimumCreationTime = new Date(Date.now() - 15 * 60 * 1000);
    });
    afterEach(async () => await clearDb());

    it("should not call Wealthkernel API for rewards that don't have WK deposit ids", async () => {
      jest.spyOn(WealthkernelService.UKInstance, "retrieveTransaction");

      await buildReward({
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } },
        createdAt: minimumCreationTime
      });
      await request(app).post("/api/admin/m2m/rewards/sync-deposits").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.retrieveTransaction).not.toHaveBeenCalled();
    });

    it("should not call Wealthkernel API for rewards that have settled deposits", async () => {
      jest.spyOn(WealthkernelService.UKInstance, "retrieveTransaction");

      await buildReward({
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } },
        createdAt: minimumCreationTime
      });
      await request(app).post("/api/admin/m2m/rewards/sync-deposits").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.retrieveTransaction).not.toHaveBeenCalled();
    });

    it("should update WK deposit status for rewards that have unsettled WK deposits with existing ids", async () => {
      const WK_TRANSACTION_ID = `bns-${faker.string.uuid()}`;
      jest.spyOn(WealthkernelService.UKInstance, "retrieveTransaction");
      jest.spyOn(WealthkernelService.UKInstance, "retrieveBonus").mockResolvedValue(
        buildWealthkernelBonusResponse({
          id: WK_TRANSACTION_ID
        })
      );

      const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: referrer.id,
        providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
      });
      await buildPortfolio({
        owner: referral.id,
        providers: { wealthkernel: { id: "portfolio-id-2", status: "Active" } }
      });
      const reward = await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referrer.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: WK_TRANSACTION_ID, status: "Created" } }
        },
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } },
        createdAt: minimumCreationTime
      });
      expect(reward.depositStatus).toBe("Pending");

      await request(app).post("/api/admin/m2m/rewards/sync-deposits").send().set("Accept", "application/json");

      const updatedReward = (await Reward.findOne({ _id: reward.id })) as RewardDocument;
      expect((updatedReward.deposit as DepositCashTransactionDocument).providers.wealthkernel).toMatchObject({
        id: WK_TRANSACTION_ID,
        status: "Settled"
      });
      expect(updatedReward.depositStatus).toBe("Settled");
      expect(updatedReward.orderStatus).toBe("Empty");
      expect(updatedReward.status).toBe("Pending");

      expect(WealthkernelService.UKInstance.retrieveTransaction).not.toHaveBeenCalled();
    });

    it("should not sync for rewards created in the last 15 minutes", async () => {
      jest.spyOn(WealthkernelService.UKInstance, "retrieveTransaction");

      await buildReward({
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } },
        createdAt: DATE
      });
      await request(app).post("/api/admin/m2m/rewards/sync-deposits").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.retrieveTransaction).not.toHaveBeenCalled();
    });
  });

  describe("POST /rewards/sync-orders", () => {
    it("should not call Wealthkernel API for rewards that don't have WK order ids", async () => {
      jest.resetAllMocks();
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder");
      jest.spyOn(eventEmitter, "emit");

      await buildReward({
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL], providers: { wealthkernel: {} } }
      });

      await request(app).post("/api/admin/m2m/rewards/sync-orders").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.retrieveOrder).not.toHaveBeenCalled();
      expect(eventEmitter.emit).not.toHaveBeenCalled();

      await clearDb();
    });

    it("should not call Wealthkernel API for rewards that have pending orders", async () => {
      jest.clearAllMocks();
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder");
      jest.spyOn(eventEmitter, "emit");

      await buildReward({
        status: "Pending",
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
        }
      });
      await request(app).post("/api/admin/m2m/rewards/sync-orders").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.retrieveOrder).not.toHaveBeenCalled();
      expect(eventEmitter.emit).not.toHaveBeenCalled();

      await clearDb();
    });

    it("should not call Wealthkernel API for rewards that have Settled status", async () => {
      jest.clearAllMocks();
      jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder");
      jest.spyOn(eventEmitter, "emit");

      await buildReward({
        status: "Settled",
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
        }
      });
      await request(app).post("/api/admin/m2m/rewards/sync-orders").send().set("Accept", "application/json");
      expect(WealthkernelService.UKInstance.retrieveOrder).not.toHaveBeenCalled();
      expect(eventEmitter.emit).not.toHaveBeenCalled();

      await clearDb();
    });

    describe("when rewards have matched WK orders and pending status", () => {
      let referrerReward: RewardDocument;
      let referralReward: RewardDocument;
      let referrer: UserDocument;
      let referral: UserDocument;
      const CONSIDERATION_AMOUNT = 10;
      const UNINVESTED_CASH = 0.01;
      const WK_ORDER_ID = faker.string.uuid();
      const ASSET_ID = faker.helpers.arrayElement(AssetArrayConst);
      const ISIN = ASSET_CONFIG[ASSET_ID].isin;
      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse(
        {
          id: WK_ORDER_ID,
          isin: ISIN,
          status: "Matched"
        },
        {
          // WK matches the order for one cent less.
          // We should give the user that uninvested cent.
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: CONSIDERATION_AMOUNT - UNINVESTED_CASH
          },
          quantity: 1,
          price: {
            currency: "USD",
            amount: 200
          }
        }
      );

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);
        jest.spyOn(eventEmitter, "emit");

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        await Promise.all([
          buildNotificationSettings({ owner: referrer.id }),
          buildNotificationSettings({ owner: referral.id })
        ]);

        await buildPortfolio({
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          owner: referrer.id,
          holdings: [],
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
        });
        await buildPortfolio({
          owner: referral.id,
          holdings: [],
          providers: { wealthkernel: { id: "portfolio-id-2", status: "Active" } }
        });
        referrerReward = await buildReward({
          status: "Pending",
          asset: ASSET_ID,
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          consideration: {
            currency: "GBP",
            amount: CONSIDERATION_AMOUNT * 100
          },
          quantity: 1,
          unitPrice: {
            amount: 200,
            currency: "USD"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: WK_ORDER_ID, status: "Matched" } }
          }
        });
        expect(referrerReward.depositStatus).toBe("Settled");
        expect(referrerReward.orderStatus).toBe("Settled");
        referralReward = await buildReward({
          status: "Pending",
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referral.id,
          consideration: {
            currency: "GBP",
            amount: CONSIDERATION_AMOUNT * 100
          },
          quantity: 1,
          unitPrice: {
            amount: 200,
            currency: "USD"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: WK_ORDER_ID, status: "Matched" } }
          }
        });
        await request(app).post("/api/admin/m2m/rewards/sync-orders").send().set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should update the reward status", async () => {
        const updatedReferrerReward = (await Reward.findOne({ _id: referrerReward.id })) as RewardDocument;
        expect(updatedReferrerReward.order.providers.wealthkernel).toMatchObject({
          id: WK_ORDER_ID,
          status: "Matched"
        });
        expect(updatedReferrerReward.depositStatus).toBe("Settled");
        expect(updatedReferrerReward.orderStatus).toBe("Settled");
        expect(updatedReferrerReward.status).toBe("Settled");
        expect(updatedReferrerReward.unitPrice).toMatchObject({
          amount: 200,
          currency: "USD"
        });

        const updatedReferralReward = (await Reward.findOne({ _id: referralReward.id })) as RewardDocument;
        expect(updatedReferralReward.order.providers.wealthkernel).toMatchObject({
          id: WK_ORDER_ID,
          status: "Matched"
        });
        expect(updatedReferralReward.depositStatus).toBe("Settled");
        expect(updatedReferralReward.orderStatus).toBe("Settled");
        expect(updatedReferralReward.status).toBe("Settled");
      });

      it("should update the users' portfolio holdings and cash (with any uninvested amount)", async () => {
        // referrer portfolio
        const updatedReferrerPortfolio = (await Portfolio.findOne({
          owner: referrer.id
        })) as PortfolioDocument;
        const referrerHoldings = updatedReferrerPortfolio.holdings;
        const referrerCash = updatedReferrerPortfolio.cash.GBP.available;
        expect(referrerHoldings.length).toBe(1);

        const quantity = WK_ORDER_RESPONSE.fills
          .filter((fill) => fill.status === "Matched")
          .map((fill) => fill.quantity)
          .reduce((sum, quantity) => sum + quantity, 0);
        expect(referrerHoldings[0]).toMatchObject(
          expect.objectContaining({
            assetCommonId: ASSET_ID,
            quantity
          })
        );
        expect(referrerCash).toEqual(UNINVESTED_CASH);
      });

      it("should update the user's conversion status to completed", async () => {
        const updatedUser = (await User.findById(referrer.id)) as UserDocument;
        expect(updatedUser.hasConvertedPortfolio).toBe(true);
      });

      it("should emit an investmentSuccess event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ email: referrer.email }),
          expect.objectContaining({
            isFirst: true,
            category: "reward",
            assetName: ASSET_CONFIG[ASSET_ID].simpleName,
            amount: CONSIDERATION_AMOUNT,
            currency: "GBP",
            fxFees: referrerReward.fees.fx.amount,
            commissionFees: referrerReward.fees.commission.amount,
            side: "buy"
          }),
          expect.objectContaining({ type: "reward" })
        );
      });

      it("should send referral or referrer emails for each settled reward", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(2);
        expect(MailerService.sendEmail).toHaveBeenCalledWith(
          expect.objectContaining({ email: referrer.email }),
          "rewardSuccess",
          { action_url: `https://wealthyhood.com/referral-dashboard/?wlthd-email=${referrer.email}` }
        );
        expect(MailerService.sendEmail).toHaveBeenCalledWith(
          expect.objectContaining({ email: referral.email }),
          "rewardSuccess",
          { action_url: `https://wealthyhood.com/referral-dashboard/?wlthd-email=${referral.email}` }
        );
      });

      it("should emit 'onRewardSettled' event to target user for each settled reward", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.rewardSettled.eventId,
          expect.objectContaining({ email: referrer.email })
        );
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.rewardSettled.eventId,
          expect.objectContaining({ email: referral.email })
        );
      });

      it("should store WK quantity to reward quantity", async () => {
        const updatedReferrerReward = (await Reward.findOne({ _id: referrerReward.id })) as RewardDocument;

        const quantity = WK_ORDER_RESPONSE.fills
          .filter((fill) => fill.status === "Matched")
          .map((fill) => fill.quantity)
          .reduce((sum, quantity) => sum + quantity, 0);
        expect(updatedReferrerReward.quantity).toBe(quantity);
      });
    });

    describe("when a single pending reward (from a referral campaign) has a matched WK order", () => {
      let referral: UserDocument;
      const CONSIDERATION_AMOUNT = 10;
      const WK_ORDER_ID = faker.string.uuid();
      const ASSET_ID = faker.helpers.arrayElement(AssetArrayConst);
      const ISIN = ASSET_CONFIG[ASSET_ID].isin;
      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse(
        {
          id: WK_ORDER_ID,
          isin: ISIN,
          status: "Matched"
        },
        {
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: CONSIDERATION_AMOUNT
          }
        }
      );

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        await buildNotificationSettings({ owner: referral.id });
        await buildPortfolio({
          owner: referral.id,
          holdings: [],
          providers: { wealthkernel: { id: "portfolio-id", status: "Active" } }
        });
        await buildReward({
          status: "Pending",
          referralCampaign: "CRM Campaign",
          referral: referral.id,
          targetUser: referral.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: WK_ORDER_ID, status: "Matched" } }
          }
        });
        await request(app).post("/api/admin/m2m/rewards/sync-orders").send().set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should send a single e-mail to the referred user", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
        expect(MailerService.sendEmail).toHaveBeenCalledWith(
          expect.objectContaining({ email: referral.email }),
          "rewardSuccess",
          { action_url: `https://wealthyhood.com/referral-dashboard/?wlthd-email=${referral.email}` }
        );
      });

      it("should emit 'onRewardSettled' event for referred user", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.referral.rewardSettled.eventId,
          expect.objectContaining({ email: referral.email })
        );
      });
    });

    describe("when a pending reward has a matched WK order and user already has a reward", () => {
      let referrer: UserDocument;
      let referral: UserDocument;
      let reward: RewardDocument;
      const CONSIDERATION_AMOUNT = 10;
      const UNINVESTED_CASH = 0.01;
      const WK_ORDER_ID = faker.string.uuid();
      const ASSET_ID = faker.helpers.arrayElement(AssetArrayConst);
      const ISIN = ASSET_CONFIG[ASSET_ID].isin;
      const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse(
        {
          id: WK_ORDER_ID,
          isin: ISIN,
          status: "Matched"
        },
        {
          // WK matches the order for one cent less.
          // We should give the user that uninvested cent.
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: CONSIDERATION_AMOUNT - UNINVESTED_CASH
          }
        }
      );

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);
        jest.spyOn(eventEmitter, "emit");

        referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildPortfolio({
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          owner: referrer.id,
          holdings: [],
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
        });
        await buildPortfolio({
          owner: referral.id,
          holdings: [],
          providers: { wealthkernel: { id: "portfolio-id-2", status: "Active" } }
        });
        reward = await buildReward({
          status: "Pending",
          asset: ASSET_ID,
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          consideration: {
            currency: "GBP",
            amount: CONSIDERATION_AMOUNT * 100
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: WK_ORDER_ID, status: "Matched" } }
          }
        });

        // Another past reward for the same user
        await buildReward({
          status: "Settled",
          asset: ASSET_ID,
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          consideration: {
            currency: "GBP",
            amount: CONSIDERATION_AMOUNT * 100
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: WK_ORDER_ID, status: "Matched" } }
          },
          quantity: 1
        });

        await request(app).post("/api/admin/m2m/rewards/sync-orders").send().set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should emit an investmentSuccess event with isFirst set to false", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ email: referrer.email }),
          expect.objectContaining({
            isFirst: false,
            category: "reward",
            amount: CONSIDERATION_AMOUNT,
            currency: "GBP",
            fxFees: reward.fees.fx.amount,
            commissionFees: reward.fees.commission.amount,
            side: "buy"
          }),
          expect.objectContaining({ type: "reward" })
        );
      });
    });
  });

  describe("POST /rewards/:id", () => {
    let reward: RewardDocument;
    const WK_DEPOSIT_ID = "wk-deposit-id";
    let response: supertest.Response;

    beforeAll(async () => {
      reward = await buildReward();
      expect(reward?.deposit?.providers?.wealthkernel?.id).toBeUndefined();

      const rewardData = { depositId: WK_DEPOSIT_ID };
      response = await request(app)
        .post(`/api/admin/m2m/rewards/${reward.id}`)
        .send(rewardData)
        .set("Accept", "application/json");
    });
    afterAll(async () => await clearDb());

    it("should return status 204", async () => {
      expect(response.status).toEqual(204);
    });

    it("should update the wealthkernel deposit id of the reward document", async () => {
      const updatedReward = (await Reward.findOne({ _id: reward.id })) as RewardDocument;
      expect(updatedReward.deposit.providers?.wealthkernel?.id).toBe(WK_DEPOSIT_ID);
    });
  });
});
