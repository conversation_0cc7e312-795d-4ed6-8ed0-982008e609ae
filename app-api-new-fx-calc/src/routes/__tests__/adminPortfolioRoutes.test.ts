import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import {
  buildAccount,
  buildDailyPortfolioTicker,
  buildHoldingDTO,
  buildPortfolio,
  buildUser
} from "../../tests/utils/generateModels";
import request from "supertest";
import app from "../../app";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { faker } from "@faker-js/faker";
import mongoose from "mongoose";
import { WealthkernelService, PortfolioWrapperTypeEnum } from "../../external-services/wealthkernelService";
import { ADMINS_ALLOWED_TO_SEND_DEPOSITS } from "../../controllers/adminPortfolioController";
import Decimal from "decimal.js";
import { TransactionService } from "../../services/transactionService";
import { DepositCashTransaction } from "../../models/Transaction";

describe("AdminPortfolioRoutes", () => {
  beforeAll(async () => await connectDb("AdminPortfolioRoutes"));
  afterAll(async () => await closeDb());

  describe("/portfolios", function () {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());
    describe("GET /portfolios", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
        }[] = [
          { assetId: "equities_china", quantity: 2, price: 10 },
          { assetId: "equities_eu", quantity: 3, price: 8 }
        ];
        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );

        // create another portfolio that will not be retrieved
        await Promise.all([
          buildPortfolio({ mode: "VIRTUAL" }),
          buildPortfolio({ owner: user.id, mode: "VIRTUAL" })
        ]);

        const realPortfolio = await buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL, holdings });
        await buildDailyPortfolioTicker({ portfolio: realPortfolio._id });
      });

      it("GET /portfolios?mode=REAL should return status 200 and array of portfolios", async () => {
        const response = await request(app)
          .get("/api/admin/m2m/portfolios?mode=REAL&populateTicker=true")
          .set("Accept", "application/json");

        const expectedPortfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL }).populate([
          { path: "currentTicker" },
          { path: "owner" },
          { path: "initialHoldingsAllocation.asset" },
          {
            path: "holdings.asset",
            populate: {
              path: "currentTicker"
            }
          }
        ]);

        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedPortfolios)));
      });

      it("GET /portfolios?mode=VIRTUAL should return status 400", async () => {
        const response = await request(app)
          .get("/api/admin/m2m/portfolios?mode=VIRTUAL&populateTicker=false")
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
      });

      it("GET /admin/portfolios?owner=<owner> should return status 200 and array of portfolios", async () => {
        const response = await request(app)
          .get(`/api/admin/m2m/portfolios?owner=${user._id}&populateTicker=false`)
          .set("Accept", "application/json");

        const expectedPortfolios = await Portfolio.find({ owner: user.id });
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedPortfolios)));
      });

      it("GET /portfolios?sort=createdAt&populateTickers=false should return status 200 with portfolios array sorted by createdAt asc and not 'ticker' populated", async () => {
        await Promise.all([buildPortfolio({ owner: user.id }), buildPortfolio({ owner: user.id })]);
        const response = await request(app)
          .get("/api/admin/m2m/portfolios?sort=createdAt&populateTicker=false")
          .set("Accept", "application/json");

        expect(response.status).toEqual(200);
        const expectedData = await Portfolio.find({}).sort({ createdAt: 1 });
        const portfoliosReceived: PortfolioDocument[] = JSON.parse(response.text);

        expect(portfoliosReceived).toMatchObject(JSON.parse(JSON.stringify(expectedData)));

        const isArraySorted = (arr: any[]) => arr.slice(1).every((item, i) => arr[i] <= item);
        expect(isArraySorted(portfoliosReceived.map((portfolio) => new Date(portfolio.createdAt)))).toEqual(true);
      });

      it("GET /admin/portfolios?owner=<owner> (with owner id that does not exist) should return status 400 with proper cause", async () => {
        const response = await request(app)
          .get(`/api/admin/m2m/portfolios?owner=${new mongoose.Types.ObjectId()}&populateTicker=false`)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Param 'owner' does not refer to a user",
              description: "Invalid parameter"
            }
          })
        );
      });
    });
  });

  describe("/portfolios/add-bonus-deposit", () => {
    let admin: UserDocument;
    let targetUser: UserDocument;
    let generalInvestmentPortfolio: PortfolioDocument;
    let response: request.Response;

    const WK_BONUS_ID = faker.string.uuid();
    const bonusDepositAmount = 50;

    describe("and request is invalid", () => {
      beforeEach(async () => {
        admin = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED
        });
        jest.spyOn(TransactionService, "addBonusDepositToUser");
        jest.spyOn(WealthkernelService.UKInstance, "createBonus").mockResolvedValue({
          id: WK_BONUS_ID
        });
      });
      afterEach(async () => await clearDb());

      it("should return 400 if the request body is empty", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/portfolios/add-bonus-deposit")
          .send({})
          .set("external-user-id", admin.id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
      });

      it("should return 400 if the body attributes names are not valid", async () => {
        targetUser = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          email: "<EMAIL>"
        });

        const response = await request(app)
          .post("/api/admin/m2m/portfolios/add-bonus-deposit")
          .send({
            targetUserIdss: targetUser._id,
            bonusDepositAmounat: 50
          })
          .set("external-user-id", admin._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
      });

      it("should return 400 if the target email is not valid", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/portfolios/add-bonus-deposit")
          .send({
            targetUserId: "some-invalid-userId",
            bonusDepositAmount: 50
          })
          .set("external-user-id", admin.id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
      });

      it("should return 400 if the bonusDepositAmount is not valid", async () => {
        targetUser = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          email: "<EMAIL>"
        });

        const response = await request(app)
          .post("/api/admin/m2m/portfolios/add-bonus-deposit")
          .send({
            targetUserId: targetUser._id,
            bonusDepositAmount: "a5b0a"
          })
          .set("external-user-id", admin.id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
      });

      it("should return 400 if the bonusDepositAmount exceeds limit", async () => {
        admin = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          email: ADMINS_ALLOWED_TO_SEND_DEPOSITS[0]
        });

        targetUser = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          email: "<EMAIL>"
        });

        const response = await request(app)
          .post("/api/admin/m2m/portfolios/add-bonus-deposit")
          .send({
            targetUserId: targetUser._id,
            bonusDepositAmount: 5000
          })
          .set("external-user-id", admin.id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
      });

      it("should throw a 400 if the user has not a linked bank account", async () => {
        targetUser = await buildUser(
          {
            providers: { wealthkernel: { id: faker.string.uuid() } },
            kycStatus: KycStatusEnum.PASSED,
            email: "<EMAIL>"
          },
          false
        );

        const adminAllowedToSendDeposit = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          email: ADMINS_ALLOWED_TO_SEND_DEPOSITS[0]
        });

        const response = await request(app)
          .post("/api/admin/m2m/portfolios/add-bonus-deposit")
          .send({ targetUserId: targetUser._id, bonusDepositAmount: 50 })
          .set("external-user-id", adminAllowedToSendDeposit.id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(WealthkernelService.UKInstance.createBonus).not.toHaveBeenCalled();
      });

      it("should throw a forbidden error if the admin is not authorized", async () => {
        targetUser = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          email: "<EMAIL>"
        });

        const response = await request(app)
          .post("/api/admin/m2m/portfolios/add-bonus-deposit")
          .send({ targetUserId: targetUser._id, bonusDepositAmount: 50 })
          .set("external-user-id", admin.id)
          .set("Accept", "application/json");
        expect(response.status).toEqual(403);
      });
    });

    describe("and request is valid", () => {
      beforeAll(async () => {
        jest.restoreAllMocks();

        targetUser = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED
        });

        const generalInvestmentAccount = await buildAccount({
          owner: targetUser.id,
          wrapperType: PortfolioWrapperTypeEnum.GIA
        });

        generalInvestmentPortfolio = await buildPortfolio({
          owner: targetUser.id,
          mode: PortfolioModeEnum.REAL,
          account: generalInvestmentAccount,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        admin = await buildUser({
          providers: { wealthkernel: { id: faker.string.uuid() } },
          kycStatus: KycStatusEnum.PASSED,
          email: ADMINS_ALLOWED_TO_SEND_DEPOSITS[0]
        });
        jest.spyOn(WealthkernelService.UKInstance, "createBonus").mockResolvedValue({
          id: WK_BONUS_ID
        });
        jest.spyOn(TransactionService, "addBonusDepositToUser");

        response = await request(app)
          .post("/api/admin/m2m/portfolios/add-bonus-deposit")
          .send({ targetUserId: targetUser._id, bonusDepositAmount })
          .set("external-user-id", admin.id)
          .set("Accept", "application/json");
      });

      afterAll(async () => {
        jest.restoreAllMocks();
      });

      it("should create the bonus payment", async () => {
        expect(WealthkernelService.UKInstance.createBonus).toHaveBeenCalledWith(
          expect.objectContaining({
            consideration: {
              currency: "GBP",
              amount: bonusDepositAmount
            },
            destinationPortfolio: generalInvestmentPortfolio.providers.wealthkernel.id
          })
        );
      });

      it("should create the fake top-up in the db", async () => {
        const fakeTopup = await DepositCashTransaction.findOne({ owner: targetUser._id });

        expect(fakeTopup?.consideration).toEqual({
          currency: "GBP",
          amount: Decimal.mul(bonusDepositAmount, 100).toNumber()
        });
        expect(fakeTopup?.providers?.truelayer?.status).toEqual("executed");
        expect(fakeTopup?.portfolio).toEqual(generalInvestmentPortfolio._id);
        expect(fakeTopup?.bankAccount).toEqual(targetUser.bankAccounts[0]._id);
        expect(fakeTopup?.settledAt).toBeDefined();
        expect(fakeTopup?.status).toEqual("Settled");
      });

      it("should update user`s cash ", async () => {
        const updatedPortfolio = await Portfolio.findById(generalInvestmentPortfolio.id);
        expect(
          Decimal.sub(
            updatedPortfolio?.cash.GBP.available ?? 0,
            generalInvestmentPortfolio?.cash.GBP.available ?? 0
          ).toNumber()
        ).toEqual(bonusDepositAmount);
      });

      it("should return a success status code", async () => {
        expect(response.status).toBe(201);
      });
    });
  });
});
