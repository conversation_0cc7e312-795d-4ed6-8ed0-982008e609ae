import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildNotificationSettings, buildUser } from "../../tests/utils/generateModels";
import {
  AppNotificationSettingEnum,
  AppNotificationSettings,
  EmailNotificationSettingEnum,
  EmailNotificationSettings,
  NotificationSettings
} from "../../models/NotificationSettings";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";

describe("NotificationSettingsRoutes", () => {
  beforeAll(async () => await connectDb("NotificationSettingsRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /notification-settings/me", () => {
    describe("when the user has no notification settings", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        response = await request(app)
          .get("/api/m2m/notification-settings/me")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and empty object", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({});
      });
    });

    describe("when the user has notification settings", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: true,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: true,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: true,
                [AppNotificationSettingEnum.QUICK_TAKE]: true,
                [AppNotificationSettingEnum.DAILY_RECAP]: true,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: true,
                [AppNotificationSettingEnum.PROMOTIONAL]: true
              })
            ) as AppNotificationSettings
          },
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                [EmailNotificationSettingEnum.WEALTHYBITES]: true
              })
            ) as EmailNotificationSettings
          }
        });

        response = await request(app)
          .get("/api/m2m/notification-settings/me")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the user's notification settings", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({
          app: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional notifications",
                  description: "Buys, sells, deposits, dividends, etc",
                  id: "app_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Market insights",
              notifications: [
                {
                  name: "Learning guides",
                  description: "Updates for new educational guides",
                  id: "app_learning_guide",
                  active: true
                },
                {
                  name: "Analysis",
                  description: "Investment ideas, research, insights",
                  id: "app_analyst_insight",
                  active: true
                },
                {
                  name: "Quick takes",
                  description: "Bit-sized insights to start your day",
                  id: "app_quick_take",
                  active: true
                },
                {
                  name: "Daily market recaps",
                  description: "Portfolio summary & news digest",
                  id: "app_daily_recap",
                  active: true
                },
                {
                  name: "Weekly reviews",
                  description: "A weekly summary of market news",
                  id: "app_weekly_review",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "app_promotional",
                  active: true
                }
              ]
            }
          ],
          email: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional emails",
                  description: "Updates on your account & activity",
                  id: "email_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Newsletters",
              notifications: [
                {
                  name: "Wealthybites",
                  description: "Every day's news & insights",
                  id: "email_wealthybites",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "email_promotional",
                  active: true
                }
              ]
            }
          ]
        });
      });
    });
  });

  describe("POST /notification-settings/me", () => {
    describe("when the user has notification settings turned ON and they are disabling a specific app notification", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: true,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: true,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: true,
                [AppNotificationSettingEnum.QUICK_TAKE]: true,
                [AppNotificationSettingEnum.DAILY_RECAP]: true,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: true,
                [AppNotificationSettingEnum.PROMOTIONAL]: true
              })
            ) as AppNotificationSettings
          },
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                [EmailNotificationSettingEnum.WEALTHYBITES]: true
              })
            ) as EmailNotificationSettings
          }
        });

        response = await request(app)
          .post("/api/m2m/notification-settings/me")
          .send({
            id: AppNotificationSettingEnum.PROMOTIONAL,
            active: false
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the user's notification settings", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({
          app: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional notifications",
                  description: "Buys, sells, deposits, dividends, etc",
                  id: "app_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Market insights",
              notifications: [
                {
                  name: "Learning guides",
                  description: "Updates for new educational guides",
                  id: "app_learning_guide",
                  active: true
                },
                {
                  name: "Analysis",
                  description: "Investment ideas, research, insights",
                  id: "app_analyst_insight",
                  active: true
                },
                {
                  name: "Quick takes",
                  description: "Bit-sized insights to start your day",
                  id: "app_quick_take",
                  active: true
                },
                {
                  name: "Daily market recaps",
                  description: "Portfolio summary & news digest",
                  id: "app_daily_recap",
                  active: true
                },
                {
                  name: "Weekly reviews",
                  description: "A weekly summary of market news",
                  id: "app_weekly_review",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "app_promotional",
                  active: false
                }
              ]
            }
          ],
          email: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional emails",
                  description: "Updates on your account & activity",
                  id: "email_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Newsletters",
              notifications: [
                {
                  name: "Wealthybites",
                  description: "Every day's news & insights",
                  id: "email_wealthybites",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "email_promotional",
                  active: true
                }
              ]
            }
          ]
        });
      });
    });

    describe("when the user has notification settings turned OFF and they are enabling a specific app notification", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: false,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: false,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: false,
                [AppNotificationSettingEnum.QUICK_TAKE]: false,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: false,
                [AppNotificationSettingEnum.PROMOTIONAL]: false
              })
            ) as AppNotificationSettings
          },
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: false,
                [EmailNotificationSettingEnum.PROMOTIONAL]: false,
                [EmailNotificationSettingEnum.WEALTHYBITES]: false
              })
            ) as EmailNotificationSettings
          }
        });

        response = await request(app)
          .post("/api/m2m/notification-settings/me")
          .send({
            id: AppNotificationSettingEnum.PROMOTIONAL,
            active: true
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the user's notification settings", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({
          app: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional notifications",
                  description: "Buys, sells, deposits, dividends, etc",
                  id: "app_transactional",
                  active: false
                }
              ]
            },
            {
              category: "Market insights",
              notifications: [
                {
                  name: "Learning guides",
                  description: "Updates for new educational guides",
                  id: "app_learning_guide",
                  active: false
                },
                {
                  name: "Analysis",
                  description: "Investment ideas, research, insights",
                  id: "app_analyst_insight",
                  active: false
                },
                {
                  name: "Quick takes",
                  description: "Bit-sized insights to start your day",
                  id: "app_quick_take",
                  active: false
                },
                {
                  name: "Daily market recaps",
                  description: "Portfolio summary & news digest",
                  id: "app_daily_recap",
                  active: false
                },
                {
                  name: "Weekly reviews",
                  description: "A weekly summary of market news",
                  id: "app_weekly_review",
                  active: false
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "app_promotional",
                  active: true
                }
              ]
            }
          ],
          email: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional emails",
                  description: "Updates on your account & activity",
                  id: "email_transactional",
                  active: false
                }
              ]
            },
            {
              category: "Newsletters",
              notifications: [
                {
                  name: "Wealthybites",
                  description: "Every day's news & insights",
                  id: "email_wealthybites",
                  active: false
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "email_promotional",
                  active: false
                }
              ]
            }
          ]
        });
      });
    });

    describe("when the user has notification settings turned OFF and they are enabling Wealthybites e-mail notifications", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: false,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: false,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: false,
                [AppNotificationSettingEnum.QUICK_TAKE]: false,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: false,
                [AppNotificationSettingEnum.PROMOTIONAL]: false
              })
            ) as AppNotificationSettings
          },
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: false,
                [EmailNotificationSettingEnum.PROMOTIONAL]: false,
                [EmailNotificationSettingEnum.WEALTHYBITES]: false
              })
            ) as EmailNotificationSettings
          }
        });

        response = await request(app)
          .post("/api/m2m/notification-settings/me")
          .send({
            id: EmailNotificationSettingEnum.WEALTHYBITES,
            active: true
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the user's notification settings", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({
          app: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional notifications",
                  description: "Buys, sells, deposits, dividends, etc",
                  id: "app_transactional",
                  active: false
                }
              ]
            },
            {
              category: "Market insights",
              notifications: [
                {
                  name: "Learning guides",
                  description: "Updates for new educational guides",
                  id: "app_learning_guide",
                  active: false
                },
                {
                  name: "Analysis",
                  description: "Investment ideas, research, insights",
                  id: "app_analyst_insight",
                  active: false
                },
                {
                  name: "Quick takes",
                  description: "Bit-sized insights to start your day",
                  id: "app_quick_take",
                  active: false
                },
                {
                  name: "Daily market recaps",
                  description: "Portfolio summary & news digest",
                  id: "app_daily_recap",
                  active: false
                },
                {
                  name: "Weekly reviews",
                  description: "A weekly summary of market news",
                  id: "app_weekly_review",
                  active: false
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "app_promotional",
                  active: false
                }
              ]
            }
          ],
          email: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional emails",
                  description: "Updates on your account & activity",
                  id: "email_transactional",
                  active: false
                }
              ]
            },
            {
              category: "Newsletters",
              notifications: [
                {
                  name: "Wealthybites",
                  description: "Every day's news & insights",
                  id: "email_wealthybites",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "email_promotional",
                  active: false
                }
              ]
            }
          ]
        });
      });

      it("should emit event that user subscribed to wealthybites", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.wealthybitesSubscription.eventId,
          expect.objectContaining({
            id: owner.id
          }),
          { enabled: true }
        );
      });
    });

    describe("when the user has notification settings turned ON and they are disabling Wealthybites e-mail notifications", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        await buildNotificationSettings({
          owner: owner.id
        });

        response = await request(app)
          .post("/api/m2m/notification-settings/me")
          .send({
            id: EmailNotificationSettingEnum.WEALTHYBITES,
            active: false
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the user's notification settings", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({
          app: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional notifications",
                  description: "Buys, sells, deposits, dividends, etc",
                  id: "app_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Market insights",
              notifications: [
                {
                  name: "Learning guides",
                  description: "Updates for new educational guides",
                  id: "app_learning_guide",
                  active: true
                },
                {
                  name: "Analysis",
                  description: "Investment ideas, research, insights",
                  id: "app_analyst_insight",
                  active: true
                },
                {
                  name: "Quick takes",
                  description: "Bit-sized insights to start your day",
                  id: "app_quick_take",
                  active: true
                },
                {
                  name: "Daily market recaps",
                  description: "Portfolio summary & news digest",
                  id: "app_daily_recap",
                  active: true
                },
                {
                  name: "Weekly reviews",
                  description: "A weekly summary of market news",
                  id: "app_weekly_review",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "app_promotional",
                  active: true
                }
              ]
            }
          ],
          email: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional emails",
                  description: "Updates on your account & activity",
                  id: "email_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Newsletters",
              notifications: [
                {
                  name: "Wealthybites",
                  description: "Every day's news & insights",
                  id: "email_wealthybites",
                  active: false
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "email_promotional",
                  active: true
                }
              ]
            }
          ]
        });
      });

      it("should emit event that user unsubscribed from wealthybites", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.wealthybitesSubscription.eventId,
          expect.objectContaining({
            id: owner.id
          }),
          { enabled: false }
        );
      });
    });

    describe("when the user has notification settings turned OFF and they are enabling promotional e-mail notifications", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: false,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: false,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: false,
                [AppNotificationSettingEnum.QUICK_TAKE]: false,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: false,
                [AppNotificationSettingEnum.PROMOTIONAL]: false
              })
            ) as AppNotificationSettings
          },
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: false,
                [EmailNotificationSettingEnum.PROMOTIONAL]: false,
                [EmailNotificationSettingEnum.WEALTHYBITES]: false
              })
            ) as EmailNotificationSettings
          }
        });

        response = await request(app)
          .post("/api/m2m/notification-settings/me")
          .send({
            id: EmailNotificationSettingEnum.PROMOTIONAL,
            active: true
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the user's notification settings", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({
          app: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional notifications",
                  description: "Buys, sells, deposits, dividends, etc",
                  id: "app_transactional",
                  active: false
                }
              ]
            },
            {
              category: "Market insights",
              notifications: [
                {
                  name: "Learning guides",
                  description: "Updates for new educational guides",
                  id: "app_learning_guide",
                  active: false
                },
                {
                  name: "Analysis",
                  description: "Investment ideas, research, insights",
                  id: "app_analyst_insight",
                  active: false
                },
                {
                  name: "Quick takes",
                  description: "Bit-sized insights to start your day",
                  id: "app_quick_take",
                  active: false
                },
                {
                  name: "Daily market recaps",
                  description: "Portfolio summary & news digest",
                  id: "app_daily_recap",
                  active: false
                },
                {
                  name: "Weekly reviews",
                  description: "A weekly summary of market news",
                  id: "app_weekly_review",
                  active: false
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "app_promotional",
                  active: false
                }
              ]
            }
          ],
          email: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional emails",
                  description: "Updates on your account & activity",
                  id: "email_transactional",
                  active: false
                }
              ]
            },
            {
              category: "Newsletters",
              notifications: [
                {
                  name: "Wealthybites",
                  description: "Every day's news & insights",
                  id: "email_wealthybites",
                  active: false
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "email_promotional",
                  active: true
                }
              ]
            }
          ]
        });
      });

      it("should emit event that user subscribed to promotional emails", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.promotionalEmailSubscription.eventId,
          expect.objectContaining({
            id: owner.id
          }),
          { enabled: true }
        );
      });
    });

    describe("when the user has notification settings turned ON and they are disabling promotional e-mail notifications", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        await buildNotificationSettings({
          owner: owner.id
        });

        response = await request(app)
          .post("/api/m2m/notification-settings/me")
          .send({
            id: EmailNotificationSettingEnum.PROMOTIONAL,
            active: false
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the user's notification settings", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({
          app: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional notifications",
                  description: "Buys, sells, deposits, dividends, etc",
                  id: "app_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Market insights",
              notifications: [
                {
                  name: "Learning guides",
                  description: "Updates for new educational guides",
                  id: "app_learning_guide",
                  active: true
                },
                {
                  name: "Analysis",
                  description: "Investment ideas, research, insights",
                  id: "app_analyst_insight",
                  active: true
                },
                {
                  name: "Quick takes",
                  description: "Bit-sized insights to start your day",
                  id: "app_quick_take",
                  active: true
                },
                {
                  name: "Daily market recaps",
                  description: "Portfolio summary & news digest",
                  id: "app_daily_recap",
                  active: true
                },
                {
                  name: "Weekly reviews",
                  description: "A weekly summary of market news",
                  id: "app_weekly_review",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "app_promotional",
                  active: true
                }
              ]
            }
          ],
          email: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional emails",
                  description: "Updates on your account & activity",
                  id: "email_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Newsletters",
              notifications: [
                {
                  name: "Wealthybites",
                  description: "Every day's news & insights",
                  id: "email_wealthybites",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "email_promotional",
                  active: false
                }
              ]
            }
          ]
        });
      });

      it("should emit event that user unsubscribed from promotional e-mails", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.promotionalEmailSubscription.eventId,
          expect.objectContaining({
            id: owner.id
          }),
          { enabled: false }
        );
      });
    });

    describe("when the user has device notification settings turned OFF and they are enabling a specific app notification", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: false,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: true,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: true,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: true,
                [AppNotificationSettingEnum.QUICK_TAKE]: true,
                [AppNotificationSettingEnum.DAILY_RECAP]: true,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: true,
                [AppNotificationSettingEnum.PROMOTIONAL]: true
              })
            ) as AppNotificationSettings
          },
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                [EmailNotificationSettingEnum.WEALTHYBITES]: true
              })
            ) as EmailNotificationSettings
          }
        });

        response = await request(app)
          .post("/api/m2m/notification-settings/me")
          .send({
            id: AppNotificationSettingEnum.PROMOTIONAL,
            active: true
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the user's notification settings", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual({
          app: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional notifications",
                  description: "Buys, sells, deposits, dividends, etc",
                  id: "app_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Market insights",
              notifications: [
                {
                  name: "Learning guides",
                  description: "Updates for new educational guides",
                  id: "app_learning_guide",
                  active: true
                },
                {
                  name: "Analysis",
                  description: "Investment ideas, research, insights",
                  id: "app_analyst_insight",
                  active: true
                },
                {
                  name: "Quick takes",
                  description: "Bit-sized insights to start your day",
                  id: "app_quick_take",
                  active: true
                },
                {
                  name: "Daily market recaps",
                  description: "Portfolio summary & news digest",
                  id: "app_daily_recap",
                  active: true
                },
                {
                  name: "Weekly reviews",
                  description: "A weekly summary of market news",
                  id: "app_weekly_review",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "app_promotional",
                  active: true
                }
              ]
            }
          ],
          email: [
            {
              category: "Activity",
              notifications: [
                {
                  name: "Transactional emails",
                  description: "Updates on your account & activity",
                  id: "email_transactional",
                  active: true
                }
              ]
            },
            {
              category: "Newsletters",
              notifications: [
                {
                  name: "Wealthybites",
                  description: "Every day's news & insights",
                  id: "email_wealthybites",
                  active: true
                }
              ]
            },
            {
              category: "Offers",
              notifications: [
                {
                  name: "Promos & special offers",
                  description: "Free shares, rewards & gifts",
                  id: "email_promotional",
                  active: true
                }
              ]
            }
          ]
        });
      });

      it("should update the device settings flag to true", async () => {
        const updatedNotificationSettings = await NotificationSettings.findOne({ owner: owner.id });
        expect(updatedNotificationSettings.app).toEqual(
          expect.objectContaining({ deviceNotificationsEnabled: true })
        );
      });
    });
  });

  describe("POST /notification-settings/me/device-settings", () => {
    describe("when the user enables device notifications", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: false,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: true,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: true
              })
            ) as AppNotificationSettings
          },
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: true
              })
            ) as EmailNotificationSettings
          }
        });

        response = await request(app)
          .post("/api/m2m/notification-settings/me/device-settings")
          .send({ deviceNotificationsEnabled: true })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });

      afterAll(async () => await clearDb());

      it("should return 204 status code", () => {
        expect(response.status).toBe(204);
      });

      it("should update deviceNotificationsEnabled to true", async () => {
        const updatedNotificationSettings = await NotificationSettings.findOne({ owner: owner.id });
        expect(updatedNotificationSettings?.app.deviceNotificationsEnabled).toBe(true);
      });
    });

    describe("when the user disables device notifications", () => {
      let response: request.Response;
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        // Set up initial notification settings all notifications (including device) to enabled.
        await buildNotificationSettings({
          owner: owner.id
        });

        response = await request(app)
          .post("/api/m2m/notification-settings/me/device-settings")
          .send({ deviceNotificationsEnabled: false })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });

      afterAll(async () => await clearDb());

      it("should return 204 status code", () => {
        expect(response.status).toBe(204);
      });

      it("should update deviceNotificationsEnabled to false", async () => {
        const updatedNotificationSettings = await NotificationSettings.findOne({ owner: owner.id });
        expect(updatedNotificationSettings?.app.deviceNotificationsEnabled).toBe(false);
      });

      it("should update all app notification settings to false", async () => {
        const updatedNotificationSettings = await NotificationSettings.findOne({ owner: owner.id });
        expect(Object.fromEntries(updatedNotificationSettings?.toObject()?.app?.settings)).toEqual({
          [AppNotificationSettingEnum.TRANSACTIONAL]: false,
          [AppNotificationSettingEnum.LEARNING_GUIDE]: false,
          [AppNotificationSettingEnum.ANALYST_INSIGHT]: false,
          [AppNotificationSettingEnum.DAILY_RECAP]: false,
          [AppNotificationSettingEnum.QUICK_TAKE]: false,
          [AppNotificationSettingEnum.WEEKLY_REVIEW]: false,
          [AppNotificationSettingEnum.PROMOTIONAL]: false
        });
      });
    });
  });
});
