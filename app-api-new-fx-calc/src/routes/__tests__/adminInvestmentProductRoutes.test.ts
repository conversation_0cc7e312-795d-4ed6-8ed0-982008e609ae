import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildInvestmentProduct } from "../../tests/utils/generateModels";
import request from "supertest";
import app from "../../app";
import { InvestmentProduct, InvestmentProductDocument } from "../../models/InvestmentProduct";

describe("AdminInvestmentProductRoutes", () => {
  beforeAll(async () => await connectDb("AdminInvestmentProductRoutes"));
  afterAll(async () => await closeDb());

  describe("POST /investment-products/:id/pause", () => {
    describe("when a side query parameter is not passed", () => {
      let investmentProduct: InvestmentProductDocument;

      beforeAll(async () => {
        investmentProduct = await buildInvestmentProduct(true, {
          assetId: "equities_uk"
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/investment-products/${investmentProduct.id}/pause`)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Param 'side' is required, must be one of [Buy,Sell]",
              description: "Invalid parameter"
            }
          })
        );
      });
    });

    describe("when investment product is already paused", () => {
      let pausedInvestmentProduct: InvestmentProductDocument;

      beforeAll(async () => {
        pausedInvestmentProduct = await buildInvestmentProduct(true, {
          assetId: "equities_uk",
          buyLine: { active: false }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/investment-products/${pausedInvestmentProduct.id}/pause?side=Buy`)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: `Investment product ${pausedInvestmentProduct.id} is already paused for buys`,
              description: "Operation failed"
            }
          })
        );
      });
    });

    describe("when side query parameter is Buy", () => {
      let investmentProduct: InvestmentProductDocument;

      beforeAll(async () => {
        investmentProduct = await buildInvestmentProduct(true, {
          assetId: "equities_uk"
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and pause buy side", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/investment-products/${investmentProduct.id}/pause?side=Buy`)
          .set("Accept", "application/json");

        const updatedInvestmentProduct: InvestmentProductDocument = await InvestmentProduct.findById(
          investmentProduct.id
        );

        expect(response.status).toBe(200);
        expect(updatedInvestmentProduct.buyLine.active).toEqual(false);
        expect(updatedInvestmentProduct.sellLine.active).toEqual(true);
      });
    });

    describe("when side query parameter is Sell", () => {
      let investmentProduct: InvestmentProductDocument;

      beforeAll(async () => {
        investmentProduct = await buildInvestmentProduct(true, {
          assetId: "equities_uk"
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and pause sell side", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/investment-products/${investmentProduct.id}/pause?side=Sell`)
          .set("Accept", "application/json");

        const updatedInvestmentProduct: InvestmentProductDocument = await InvestmentProduct.findById(
          investmentProduct.id
        );

        expect(response.status).toBe(200);
        expect(updatedInvestmentProduct.buyLine.active).toEqual(true);
        expect(updatedInvestmentProduct.sellLine.active).toEqual(false);
      });
    });
  });

  describe("POST /investment-products/:id/resume", () => {
    describe("when a side query parameter is not passed", () => {
      let investmentProduct: InvestmentProductDocument;

      beforeAll(async () => {
        investmentProduct = await buildInvestmentProduct(true, {
          assetId: "equities_uk"
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/investment-products/${investmentProduct.id}/resume`)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Param 'side' is required, must be one of [Buy,Sell]",
              description: "Invalid parameter"
            }
          })
        );
      });
    });

    describe("when investment product is already active", () => {
      let resumedInvestmentProduct: InvestmentProductDocument;

      beforeAll(async () => {
        resumedInvestmentProduct = await buildInvestmentProduct(true, {
          assetId: "equities_uk",
          buyLine: { active: true }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/investment-products/${resumedInvestmentProduct.id}/resume?side=Buy`)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: `Investment product ${resumedInvestmentProduct.id} is already active for buys`,
              description: "Operation failed"
            }
          })
        );
      });
    });

    describe("when side query parameter is Buy", () => {
      let investmentProduct: InvestmentProductDocument;

      beforeAll(async () => {
        investmentProduct = await buildInvestmentProduct(true, {
          assetId: "equities_uk",
          buyLine: { active: false }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and resume buy side", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/investment-products/${investmentProduct.id}/resume?side=Buy`)
          .set("Accept", "application/json");

        const updatedInvestmentProduct: InvestmentProductDocument = await InvestmentProduct.findById(
          investmentProduct.id
        );

        expect(response.status).toBe(200);
        expect(updatedInvestmentProduct.buyLine.active).toEqual(true);
        expect(updatedInvestmentProduct.sellLine.active).toEqual(true);
      });
    });

    describe("when side query parameter is Sell", () => {
      let investmentProduct: InvestmentProductDocument;

      beforeAll(async () => {
        investmentProduct = await buildInvestmentProduct(true, {
          assetId: "equities_uk",
          sellLine: { active: false }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and resume sell side", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/investment-products/${investmentProduct.id}/resume?side=Sell`)
          .set("Accept", "application/json");

        const updatedInvestmentProduct: InvestmentProductDocument = await InvestmentProduct.findById(
          investmentProduct.id
        );

        expect(response.status).toBe(200);
        expect(updatedInvestmentProduct.buyLine.active).toEqual(true);
        expect(updatedInvestmentProduct.sellLine.active).toEqual(true);
      });
    });
  });
});
