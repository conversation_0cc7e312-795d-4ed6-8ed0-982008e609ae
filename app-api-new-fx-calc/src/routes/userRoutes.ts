import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import UserController from "../controllers/userController";
import UserMiddleware from "../middlewares/userMiddleware";
import ValidationMiddleware from "../middlewares/validationMiddleware";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/me/linked-bank-accounts", ErrorMiddleware.catchAsyncErrors(UserController.getLinkedBankAccounts));
// to be deprecated
router.get(
  "/:id/linked-bank-accounts",
  ValidationMiddleware.pathUserIdHeaderMatch,
  ErrorMiddleware.catchAsyncErrors(UserController.getLinkedBankAccounts)
);
router.get("/me", ErrorMiddleware.catchAsyncErrors(UserController.getSelfUser));
router.get("/prompts", ErrorMiddleware.catchAsyncErrors(UserController.getPrompts));
router.get("/me/transaction-activity", ErrorMiddleware.catchAsyncErrors(UserController.getTransactionActivity));
router.get("/me/investment-activity", ErrorMiddleware.catchAsyncErrors(UserController.getInvestmentActivity));
router.get("/employment-config", ErrorMiddleware.catchAsyncErrors(UserController.getEmploymentConfiguration));
router.get(
  "/me/daily-summaries",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(UserController.getDailySummaries)
);

/**
 * POST REQUESTS
 */
router.post(
  "/me/account-statements/generate",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(UserController.generateAccountStatement)
);
router.post("/me/deletion-feedback", ErrorMiddleware.catchAsyncErrors(UserController.submitDeletionFeedback));
router.post("/me/device-token", ErrorMiddleware.catchAsyncErrors(UserController.updateDeviceToken));
router.post("/me/residency-country", ErrorMiddleware.catchAsyncErrors(UserController.submitResidencyCountry));
router.post("/me/employment-info", ErrorMiddleware.catchAsyncErrors(UserController.submitEmploymentInfo));
router.post(
  "/me/viewed-referral-code-screen",
  ErrorMiddleware.catchAsyncErrors(UserController.viewedReferralCodeScreen)
);
router.post(
  "/verify",
  ErrorMiddleware.catchAsyncErrors(UserMiddleware.userHasSubmittedRequiredInfo),
  ErrorMiddleware.catchAsyncErrors(UserController.verifyUser)
);
router.post("/me/join-waiting-list", ErrorMiddleware.catchAsyncErrors(UserController.joinWaitingList));
router.post("/me/set-referrer", ErrorMiddleware.catchAsyncErrors(UserController.setReferrer));
router.post(
  "/me/subscribe-wealthybites",
  ErrorMiddleware.catchAsyncErrors(UserController.viewedWealthybitesScreen)
);
router.post("/me", ErrorMiddleware.catchAsyncErrors(UserController.updateUser));
// to be deprecated
router.post("/:id", ErrorMiddleware.catchAsyncErrors(UserController.updateUser));
router.post("/prompts/seen", ErrorMiddleware.catchAsyncErrors(UserController.markPromptsAsSeen));

export default router;
