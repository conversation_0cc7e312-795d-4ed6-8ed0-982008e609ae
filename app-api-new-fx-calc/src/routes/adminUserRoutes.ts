import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AdminUserController from "../controllers/adminUserController";

const router = express.Router();

/**
 * GET
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(AdminUserController.getUsers));
router.get("/referrals/count", ErrorMiddleware.catchAsyncErrors(AdminUserController.getUserReferralsCount));
router.get("/:id", ErrorMiddleware.catchAsyncErrors(AdminUserController.getUser));

/**
 * POST
 */
router.post(
  "/:id/account-statements/generate",
  ErrorMiddleware.catchAsyncErrors(AdminUserController.generateAccountStatement)
);
router.post("/email/:email", ErrorMiddleware.catchAsyncErrors(AdminUserController.createOrUpdateUserByEmail));
router.post("/create-wealthkernel-parties", ErrorMiddleware.catchAsyncErrors(AdminUserController.createWkParties));
router.post("/:id", ErrorMiddleware.catchAsyncErrors(AdminUserController.updateUser));
router.post(
  "/:id/remove-duplicate-flag",
  ErrorMiddleware.catchAsyncErrors(AdminUserController.removeDuplicateFlag)
);
router.post(
  "/:id/remove-kyc-passport-flag",
  ErrorMiddleware.catchAsyncErrors(AdminUserController.removeKycPassportFlag)
);
router.post(
  "/:id/override-kyc-decision",
  ErrorMiddleware.catchAsyncErrors(AdminUserController.overrideKycDecision)
);

export default router;
