import { Request, Response } from "express";
import { verifyRequest } from "@contentful/node-apps-toolkit";
import logger from "../external-services/loggerService";
import { ContentfulContentTypeEnum } from "../configs/contentfulConfig";
import { ForbiddenError } from "../models/ApiErrors";
import ContentEntryService from "../services/contentEntryService";

class ContentfulWebhookController {
  public static async processPublishWebhook(req: Request, res: Response): Promise<Response> {
    logger.info("Received Contentful publish webhook", {
      module: "ContentfulWebhookController",
      method: "processPublishWebhook",
      data: {
        body: req.body,
        headers: req.headers
      }
    });

    if (!ContentfulWebhookController._validateRequestSignature(req)) {
      throw new ForbiddenError("Invalid Contentful request signature");
    }

    if (req.headers["x-contentful-topic"] !== "ContentManagement.Entry.publish") {
      throw new ForbiddenError("Invalid Contentful topic");
    }

    const contentType = req.body.sys.contentType.sys.id;
    if (contentType !== ContentfulContentTypeEnum.LEARNING_GUIDE) {
      logger.info("Skipping non-learning guide content type", {
        module: "ContentfulWebhookController",
        method: "processPublishWebhook",
        data: { contentType }
      });
      return res.sendStatus(204);
    }

    await ContentEntryService.createLearningGuideContentEntry({
      title: req.body.fields.title["en-US"],
      contentfulConfig: {
        id: req.body.sys.id,
        spaceId: req.body.sys.space.sys.id,
        environmentId: req.body.sys.environment.sys.id
      }
    });

    return res.sendStatus(200);
  }

  private static _validateRequestSignature(req: Request): boolean {
    const canonicalRequest = {
      path: req.path as string,
      headers: req.headers as Record<string, string>,
      method: req.method as "GET" | "PATCH" | "HEAD" | "POST" | "DELETE" | "OPTIONS" | "PUT",
      body: JSON.stringify(req.body)
    };

    const secret = process.env.CONTENTFUL_WEBHOOK_SECRET;
    return verifyRequest(secret, canonicalRequest);
  }
}

export default ContentfulWebhookController;
