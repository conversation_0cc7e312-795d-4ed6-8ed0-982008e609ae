import { CustomRequest } from "custom";
import { Response } from "express";
import KycOperationService from "../services/kycOperationService";

export default class KycOperationController {
  public static async initiateKycOperation(req: CustomRequest, res: Response) {
    const kycInitiationResponse = await KycOperationService.initiateKycOperation(req.user);

    return res.status(200).json(kycInitiationResponse);
  }

  public static async retrieveKycOperation(req: CustomRequest, res: Response) {
    const kycOperation = await KycOperationService.retrieveKycOperation(req.user);

    return res.status(200).json(kycOperation);
  }
}
