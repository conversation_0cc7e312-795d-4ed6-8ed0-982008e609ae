import { CustomRequest } from "custom";
import { Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { BadRequestError } from "../models/ApiErrors";
import RewardInvitationService from "../services/rewardInvitationService";

export default class RewardInvitationController {
  public static async createRewardInvitation(req: CustomRequest, res: Response): Promise<Response> {
    const referrerId = req.user.id;

    if (Object.keys(req.body).length === 0) {
      throw new BadRequestError("Request body cannot be empty");
    }

    const targetUserEmail = req.body?.targetUserEmail?.trim()?.toLowerCase();

    ParamsValidationUtil.isEmailParamValid("targetUserEmail", targetUserEmail);

    await RewardInvitationService.createRewardInvitation(targetUserEmail, referrerId);

    return res.sendStatus(200);
  }
}
