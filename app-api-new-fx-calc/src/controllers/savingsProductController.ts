import { CustomRequest } from "custom";
import { Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import { TransactionService } from "../services/transactionService";
import {
  SavingsDividendTransactionDocument,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument,
  TransactionDocument
} from "../models/Transaction";
import logger from "../external-services/loggerService";
import SavingsProductService from "../services/savingsProductService";

type SavingsToCashTranferType = {
  type: "savingsToCash";
  item: SavingsWithdrawalTransactionDocument;
};

type CashToSavingsTranferType = {
  type: "cashToSavings";
  item: SavingsTopupTransactionDocument;
};

type BankDepositToSavingsTranferType = {
  type: "bankDepositToSavings";
  item: SavingsTopupTransactionDocument;
};

type SavingsInterestType = {
  type: "savingsInterest";
  item: SavingsDividendTransactionDocument;
};

export type SavingsProductActivityItemType =
  | BankDepositToSavingsTranferType
  | CashToSavingsTranferType
  | SavingsToCashTranferType
  | SavingsInterestType;

export default class SavingsProductController {
  public static async getUserSavings(req: CustomRequest, res: Response): Promise<Response> {
    const userId = req.user.id as string;
    const response = await SavingsProductService.getUserSavings(userId);

    return res.status(200).json(response);
  }

  public static async getSavingsProductActivity(req: CustomRequest, res: Response): Promise<Response> {
    const userId = req.user.id as string;
    const savingsProductId = req.query.savingsProductId as savingsUniverseConfig.SavingsProductType;
    ParamsValidationUtil.isSavingProductValidValid("savingsProductId", savingsProductId, { isRequired: true });
    let limit: number;
    if (req.query.limit) {
      limit = ParamsValidationUtil.isNumericParamValid("limit", req.query.limit, { isRequired: false });
    }

    const savingsProductActivityTransactions = await TransactionService.getSavingsProductActivityTransactions(
      userId,
      savingsProductId,
      limit
    );

    const response = SavingsProductController._transformSavingsTransactionsToActivityResponse(
      savingsProductActivityTransactions
    );

    return res.status(200).json(response);
  }

  public static async getUserSavingsProductFeeDetails(req: CustomRequest, res: Response): Promise<Response> {
    const savingsProductId = req.query.savingsProductId as savingsUniverseConfig.SavingsProductType;
    ParamsValidationUtil.isSavingProductValidValid("savingsProductId", savingsProductId, { isRequired: true });

    const response = await SavingsProductService.getUserSavingsProductFeeDetails(req.user, savingsProductId);

    return res.status(200).json(response);
  }

  public static async getSavingsProductData(req: CustomRequest, res: Response): Promise<Response> {
    const userId = req.user.id as string;
    const savingsProductId = req.query.savingsProductId as savingsUniverseConfig.SavingsProductType;
    ParamsValidationUtil.isSavingProductValidValid("savingsProductId", savingsProductId, { isRequired: true });

    const response = await SavingsProductService.getSavingsProductData(userId, savingsProductId);

    return res.status(200).json(response);
  }

  private static _transformSavingsTransactionsToActivityResponse(
    transactions: TransactionDocument[]
  ): SavingsProductActivityItemType[] {
    return transactions.map((transaction) => {
      if (transaction.status === "Cancelled" || transaction.status === "Rejected") {
        logger.warn(`Ovewriting status to 'Pending' for ${transaction.id} transaction`, {
          module: "SavingsProductController",
          method: "_transformSavingsTransactionsToActivityResponse"
        });
        transaction.status = "Pending";
      }

      if (transaction.category === "SavingsTopupTransaction") {
        const savingsTopup = transaction as SavingsTopupTransactionDocument;

        return {
          type: savingsTopup.pendingDeposit ? "bankDepositToSavings" : "cashToSavings",
          item: savingsTopup
        };
      } else if (transaction.category === "SavingsWithdrawalTransaction") {
        const savingsWithdrawal = transaction as SavingsWithdrawalTransactionDocument;

        return {
          type: "savingsToCash",
          item: savingsWithdrawal
        };
      } else if (transaction.category === "SavingsDividendTransaction") {
        const savingsDividend = transaction as SavingsDividendTransactionDocument;

        return {
          type: "savingsInterest",
          item: savingsDividend
        };
      }
    });
  }
}
