import { CustomRequest } from "custom";
import { Response } from "express";
import { countriesConfig } from "@wealthyhood/shared-configs";
import UserService, {
  ModalTypeArray,
  ModalTypeType,
  PromptTypeArray,
  PromptTypeType,
  VerificationStatusType
} from "../services/userService";
import AppsflyerService from "../external-services/appsflyerService";
import logger from "../external-services/loggerService";
import UserValidationUtil from "../utils/userValidationUtil";
import { CreateParticipantData, CreateUserPartial, UserData } from "requestBody";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import { BadRequestError } from "../models/ApiErrors";
import { PlatformType, UserPopulationFieldsEnum, UserTypeEnum } from "../models/User";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import BankAccountService from "../services/bankAccountService";
import ProviderService, { ProviderScopeEnum } from "../services/providerService";
import { EmploymentInfoConfigurationType, INCOME_RANGE_CONFIG } from "../configs/employmentInfoConfig";
import ConfigUtil from "../utils/configUtil";
import {
  EMPLOYMENT_STATUSES_REQUIRE_INDUSTRY,
  EmploymentStatusArray,
  IndustryArray,
  SourceOfWealthArray
} from "../external-services/wealthkernelService";
import { spaceOnCapitalLetter } from "../utils/stringUtil";
import CurrencyUtil from "../utils/currencyUtil";
import DateUtil from "../utils/dateUtil";
import StatementUtil from "../utils/statementUtil";
import { AddressDocument } from "../models/Address";
import CloudflareService, { BucketsEnum, ContentTypeEnum } from "../external-services/cloudflareService";
import { Readable } from "stream";
import DbUtil from "../utils/dbUtil";
import { TransactionDocument, TransactionInvestmentActivityFilterEnum } from "../models/Transaction";
import { RewardDocument, RewardActivityFilterEnum } from "../models/Reward";

export default class UserController {
  public static async createOrUpdateUser(req: CustomRequest, res: Response): Promise<Response> {
    const idToken = req.identityTokenPayload;
    const email = idToken.email;
    const auth0Sub = idToken.sub;
    const emailVerified = idToken.email_verified;
    const lastLogin = new Date(idToken.updated_at);

    const userData: CreateUserPartial = {
      auth0: { id: auth0Sub },
      email,
      emailVerified,
      lastLogin,
      role: [UserTypeEnum.INVESTOR],
      lastLoginPlatform: req.platform
    };

    // referral info
    const { anonymousId, appsflyerId, gaClientId, pageUserLanded, grsf, wlthd, sid } =
      AppsflyerService.parseAttributionParams(req.headers);
    const trackingSource = AppsflyerService.parseSource(req.headers);
    const googleAdsMetadata = AppsflyerService.parseGoogleAdMetadata(req.headers);
    const referral: CreateParticipantData = {
      appInstallInfo: {
        createdAt: new Date(),
        platform: req.platform
      },
      financeAdsSessionId: sid,
      anonymousId,
      appsflyerId,
      googleAdsMetadata,
      gaClientId,
      pageUserLanded,
      trackingSource
    };
    if (wlthd) {
      referral.wlthd = wlthd;
    } else if (grsf) {
      referral.grsf = grsf;
    }

    const user = await UserService.createOrUpdateUser(email, userData, referral);
    return res.status(200).json({ data: [user] });
  }

  public static async getSelfUser(req: CustomRequest, res: Response): Promise<Response> {
    const populate: string[] = req.query.populate
      ? ParamsValidationUtil.isArrayQueryParamValid("populate", req.query.populate as string, {
          isRequired: false
        })
      : [];

    if (populate.length > 0) {
      await req.user.populate({ path: populate.join(" "), strictPopulate: false });
    }

    // Req.user should always be set by the authMiddleware at that point
    let user = req.user.toObject();

    user = {
      ...user,
      portfolioConversionStatus: await UserService.getPortfolioConversionStatus(req.user)
    };

    if (populate.includes("canUnlockFreeShare")) {
      user = {
        ...user,
        canUnlockFreeShare: await UserService.canUnlockFreeShare(req.user)
      };
    }

    if (populate.includes("canReceiveCashback")) {
      user = {
        ...user,
        canReceiveCashback: await UserService.canReceiveCashback(req.user)
      };
    }

    return res.status(200).json(user);
  }

  public static async submitDeletionFeedback(req: CustomRequest, res: Response): Promise<Response> {
    const feedback = req.body.feedback as string;
    if (!feedback) {
      throw new BadRequestError(`Account deletion feedback was submitted by user ${req.user.email} but is empty.`);
    }

    await UserService.submitDeletionFeedback(req.user, feedback);

    return res.sendStatus(204);
  }

  public static async submitResidencyCountry(req: CustomRequest, res: Response): Promise<Response> {
    const residencyCountry = req.body.residencyCountry as countriesConfig.CountryCodesType;
    if (
      !ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "residencyCountry",
        residencyCountry,
        countriesConfig.countryCodesArray,
        { isRequired: true }
      )
    ) {
      throw new BadRequestError(
        `Residency country request was submitted by user ${req.user.email} with forbidden value "${residencyCountry}".`
      );
    }

    await UserService.submitResidencyCountry(req.user.id, residencyCountry);

    return res.sendStatus(204);
  }

  public static readonly updateUser = async (req: CustomRequest, res: Response) => {
    const userId = req.user.id;
    const userUpdateData = UserValidationUtil.validateUserData(req.body as UserData);
    const updatedUser = await UserService.updateUser(userId, userUpdateData);

    if (!req.user.viewedWelcomePage && updatedUser.viewedWelcomePage) {
      eventEmitter.emit(events.user.welcome.eventId, updatedUser);
    }

    return res.status(200).json({ data: [updatedUser] });
  };

  public static readonly setReferrer = async (req: CustomRequest, res: Response) => {
    const { referralCode } = req.body;
    if (!referralCode) {
      throw new BadRequestError("Referral code cannot be empty", "Invalid Request");
    }

    await UserService.setReferrerByCode(req.user.id, referralCode);
    return res.sendStatus(204);
  };

  public static readonly verifyUser = async (req: CustomRequest, res: Response): Promise<Response> => {
    // Populate portfolios in order to use `isVerified` virtual
    const user = await UserService.getUser(req.user.id, { portfolios: true });

    if (user.isVerified) {
      logger.info(`User ${user._id} is already verified, returning verified...`, {
        module: "UserService",
        method: "verifyUser",
        userEmail: user.email
      });
      return res.status(200).json({ status: "verified" });
    }

    const verificationStatus: VerificationStatusType = await UserService.verifyUserStepByStep(user);

    return res.status(200).json({ status: verificationStatus });
  };

  public static readonly getLinkedBankAccounts = async (req: CustomRequest, res: Response) => {
    const linkedBankAccounts = await BankAccountService.getLinkedBankAccounts(req.user);
    return res.status(200).json({ data: linkedBankAccounts });
  };

  public static async getPrompts(req: CustomRequest, res: Response) {
    const type = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "type",
      req.query.type,
      PromptTypeArray,
      {
        isRequired: false
      }
    ) as PromptTypeType;

    const prompts = await UserService.getPrompts(req.user, type);

    return res.status(200).json(prompts);
  }

  public static async markPromptsAsSeen(req: CustomRequest, res: Response) {
    const promptType = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "promptType",
      req.body.promptType,
      PromptTypeArray
    );

    if (promptType === "modal") {
      const modalType = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "modalType",
        req.body.modalType,
        ModalTypeArray
      ) as ModalTypeType;

      const ids = req.body.ids as string[];
      if (!ids || ids.length < 1) {
        //TODO remove fix
        if (ids && ids.length === 0) {
          await UserService.markPromptsAsSeen(
            {
              ids,
              modalType
            },
            req.user.id
          );
          return res.sendStatus(200);
        }
        // END of fix
        throw new BadRequestError("Did not found ids array with at least 1 id in request body");
      }

      await UserService.markPromptsAsSeen({
        ids,
        modalType
      });

      return res.sendStatus(200);
    } else {
      throw new BadRequestError(`Prompt type ${promptType} is not handled`);
    }
  }

  public static readonly getTransactionActivity = async (req: CustomRequest, res: Response): Promise<Response> => {
    let limit;
    if (req.query.limit) {
      limit = ParamsValidationUtil.isNumericParamValid("limit", req.query.limit, {
        isRequired: false
      });
    }

    if (limit && limit < 0) {
      throw new BadRequestError("Limit cannot be negative");
    }

    return res.status(200).json(await UserService.getTransactionActivity(req.user, limit));
  };

  public static readonly getInvestmentActivity = async (req: CustomRequest, res: Response): Promise<Response> => {
    const items = await UserService.getInvestmentActivity(req.user);

    const userActivityItems = items.map((item) => {
      if ((item as any).isReward) {
        const reward = item as RewardDocument;
        return {
          type: "reward",
          item: reward,
          activityFilter: RewardActivityFilterEnum[reward.activityFilter]
        };
      } else {
        const transaction = item as TransactionDocument;
        return {
          type: "transaction",
          item: transaction,
          activityFilter: TransactionInvestmentActivityFilterEnum[transaction.investmentActivityFilter]
        };
      }
    });

    return res.status(200).json(userActivityItems);
  };

  public static readonly generateAccountStatement = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const start = req.query.start ? DateUtil.getStartOfDay(new Date(req.query.start as string)) : undefined;
    const end = req.query.end ? DateUtil.getEndOfDay(new Date(req.query.end as string)) : undefined;
    const user = req.user;

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.ADDRESSES);
    const address = user.addresses[0] as AddressDocument;

    const activity = await UserService.getAccountStatementActivity(user.id, start, end);
    const data = StatementUtil.generateAccountStatementPDF(user, address, activity, {
      start,
      end
    });

    const { fileUri } = await CloudflareService.Instance.uploadObject(
      BucketsEnum.ACCOUNT_STATEMENTS,
      StatementUtil.generateAccountStatementFilePath(user.id),
      Readable.from(data),
      {
        contentType: ContentTypeEnum.APPLICATION_PDF
      }
    );

    return res.status(200).json({ fileUri });
  };

  public static async submitEmploymentInfo(req: CustomRequest, res: Response) {
    const userId = req.user.id as string;

    const incomeRange = INCOME_RANGE_CONFIG.find(
      (incomeRange) => incomeRange.id.toString() == req.body.incomeRangeId
    );
    if (!incomeRange) {
      throw new BadRequestError("Invalid income range id");
    }

    const employmentInfo = {
      incomeRangeId: req.body.incomeRangeId,
      annualIncome: {
        amount: incomeRange.annualIncomeToSubmit,
        currency: req.user.currency
      },
      // prevent industry from being empty string
      industry: req.body?.industry || undefined,
      employmentStatus: req.body.employmentStatus,
      sourcesOfWealth: req.body.sourcesOfWealth
    };

    const isEmploymentInfoValid = UserValidationUtil.validateEmploymentInfo(employmentInfo);
    if (!isEmploymentInfoValid) {
      throw new BadRequestError("Employment info is not valid for user");
    }

    await UserService.updateUser(userId, { employmentInfo });

    return res.sendStatus(204);
  }

  public static async viewedWealthybitesScreen(req: CustomRequest, res: Response) {
    const didSubscribe = ParamsValidationUtil.isBooleanParamValid(
      "didSubscribe",
      req.query.didSubscribe as string,
      { isRequired: true }
    );
    await UserService.viewedWealthybitesScreen(req.user, didSubscribe);

    return res.sendStatus(204);
  }

  public static async updateDeviceToken(req: CustomRequest, res: Response): Promise<Response> {
    const { deviceToken } = req.body;

    await UserService.updateDeviceToken(req.user.id, req.headers.platform as PlatformType, deviceToken);

    return res.sendStatus(204);
  }

  public static async getEmploymentConfiguration(req: CustomRequest, res: Response) {
    const locale = ConfigUtil.getDefaultUserLocale(req.user.residencyCountry);

    const employmentInfo: EmploymentInfoConfigurationType = {
      employmentStatuses: EmploymentStatusArray.map((employmentStatus) => ({
        id: employmentStatus,
        label: spaceOnCapitalLetter(employmentStatus)
      })),
      employmentStatusThatRequireIndustry: EMPLOYMENT_STATUSES_REQUIRE_INDUSTRY,
      industries: IndustryArray.map((industry) => ({
        id: industry,
        label: spaceOnCapitalLetter(industry)
      })),
      incomeRanges: INCOME_RANGE_CONFIG.map(({ id, minIncome, maxIncome }) => {
        const label = maxIncome
          ? `${CurrencyUtil.formatCurrency(minIncome, req.user.currency, locale, false)} - ${CurrencyUtil.formatCurrency(
              maxIncome,
              req.user.currency,
              locale,
              false
            )}`
          : `${CurrencyUtil.formatCurrency(minIncome, req.user.currency, locale, false)} and above`;

        return {
          id: id.toString(),
          label
        };
      }),
      sourcesOfWealth: SourceOfWealthArray.map((sourceOfWealth) => ({
        id: sourceOfWealth,
        label: spaceOnCapitalLetter(sourceOfWealth)
      }))
    };

    return res.status(200).json(employmentInfo);
  }

  public static async getDailySummaries(req: CustomRequest, res: Response) {
    const dailySummaries = await UserService.getDailySummaries(req.user);

    return res.status(200).json(dailySummaries);
  }

  public static async viewedReferralCodeScreen(req: CustomRequest, res: Response) {
    const userId = req.user.id as string;

    await UserService.updateUser(userId, { viewedReferralCodeScreen: true });
    return res.sendStatus(204);
  }

  /**
   * @description Adds the user to a waiting list by setting the joinedWaitingListAt timestamp & setting the user's
   * residency country.
   *
   * @param req CustomRequest
   * @param res Response
   * @returns Response with status 200 on success
   */
  public static async joinWaitingList(req: CustomRequest, res: Response): Promise<Response> {
    const userId = req.user.id;
    const residencyCountry = req.body.residencyCountry as countriesConfig.CountryCodesType;
    if (
      !ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "residencyCountry",
        residencyCountry,
        countriesConfig.countryCodesArray,
        { isRequired: true }
      )
    ) {
      throw new BadRequestError(
        `User ${req.user.email} tried to join waiting list with forbidden value "${residencyCountry}".`
      );
    }

    const [updatedUser] = await Promise.all([
      UserService.updateUser(userId, { joinedWaitingListAt: new Date() }),
      UserService.submitResidencyCountry(userId, residencyCountry)
    ]);

    logger.info(`User ${updatedUser.email} joined the waiting list`, {
      module: "UserController",
      method: "joinWaitingList",
      userEmail: updatedUser.email
    });

    return res.sendStatus(200);
  }
}
