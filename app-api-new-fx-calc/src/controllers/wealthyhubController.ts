import { CustomRequest } from "custom";
import { Response } from "express";
import WealthyhubService from "../services/wealthyhubService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import RestUtil from "../utils/restUtil";

export default class WealthyhubController {
  public static async getAnalystInsights(req: CustomRequest, res: Response): Promise<Response> {
    let page =
      ParamsValidationUtil.isNumericParamValid("page", req.query.page as string, { isRequired: false }) ?? 1;
    // FIXME: page index should always start from 1
    if (page === 0) {
      page = 1;
    }

    const subscription = await RestUtil.getSubscriptionFromResponse(req, res);

    const analystInsights = await WealthyhubService.getAnalystInsights(
      { page },
      { isPaidPlan: subscription?.isPaidPlan }
    );
    return res.status(200).json(analystInsights);
  }

  public static async getAnalystInsight(req: CustomRequest, res: Response): Promise<Response> {
    const analystInsightId = req.params.id;
    const subscription = await RestUtil.getSubscriptionFromResponse(req, res);

    const analystInsights = {
      data: await WealthyhubService.getAnalystInsight(analystInsightId, { isPaidPlan: subscription?.isPaidPlan })
    };
    return res.status(200).json(analystInsights);
  }

  public static async getLearningGuides(req: CustomRequest, res: Response): Promise<Response> {
    return res.status(200).json({ data: await WealthyhubService.getLearningGuides() });
  }

  public static async getLearningGuideBySlug(req: CustomRequest, res: Response): Promise<Response> {
    const learningGuideSlug = req.params.slug;
    const subscription = await RestUtil.getSubscriptionFromResponse(req, res);

    return res.status(200).json({
      data: await WealthyhubService.getLearningGuideBySlug(learningGuideSlug, {
        isPaidPlan: subscription?.isPaidPlan
      })
    });
  }

  public static async getLearningGuideById(req: CustomRequest, res: Response): Promise<Response> {
    const learningGuideId = req.params.id;
    const subscription = await RestUtil.getSubscriptionFromResponse(req, res);

    return res.status(200).json({
      data: await WealthyhubService.getLearningGuideById(learningGuideId, {
        isPaidPlan: subscription?.isPaidPlan
      })
    });
  }

  public static async getNews(req: CustomRequest, res: Response): Promise<Response> {
    const news = { data: await WealthyhubService.getNews() };
    return res.status(200).json(news);
  }

  public static async getGlossary(req: CustomRequest, res: Response): Promise<Response> {
    const glossary = { data: await WealthyhubService.getGlossaryItems() };
    return res.status(200).json(glossary);
  }

  public static async getHelpCentre(req: CustomRequest, res: Response): Promise<Response> {
    const helpCentre = { data: await WealthyhubService.getHelpCentre(req.user) };
    return res.status(200).json(helpCentre);
  }
}
