import { CustomRequest } from "custom";
import { Response } from "express";
import InvestmentProductService from "../services/investmentProductService";
import { OrderSideArray, OrderSideType } from "../external-services/wealthkernelService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";

export class AdminInvestmentProductController {
  public static readonly pauseOrders = async (req: CustomRequest, res: Response): Promise<Response> => {
    const side = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "side",
      req.query.side as string,
      OrderSideArray
    );
    const assetDatabaseId = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    await InvestmentProductService.pauseOrders(assetDatabaseId, side as OrderSideType);
    return res.sendStatus(200);
  };

  public static readonly resumeOrders = async (req: CustomRequest, res: Response): Promise<Response> => {
    const side = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "side",
      req.query.side as string,
      OrderSideArray
    );
    const assetDatabaseId = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    await InvestmentProductService.resumeOrders(assetDatabaseId, side as OrderSideType);
    return res.sendStatus(200);
  };
}
