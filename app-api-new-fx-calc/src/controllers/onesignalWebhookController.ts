import { Request, Response } from "express";
import logger from "../external-services/loggerService";
import { ForbiddenError } from "../models/ApiErrors";
import { PlatformType } from "../models/User";
import UserService from "../services/userService";

type OneSignalWebhookBodyType = {
  user_id: string;
  event_id: string;
  event: string;
  deviceType: string;
  originalTimestamp: string;
};

class OneSignalWebhookController {
  /**
   * Processes webhook events when a user is unsubscribed due to a push, SMS, or email message.
   * This can happen when a user opts out of notifications through the notification itself.
   *
   * @param req Express request object containing the webhook payload
   * @param res Express response object
   * @returns Promise resolving to the Express response
   */
  public static async processUnsubscribeWebhook(req: Request, res: Response): Promise<Response> {
    const body = req.body as OneSignalWebhookBodyType;

    logger.info("Received OneSignal unsubscribe webhook", {
      module: "OneSignalWebhookController",
      method: "processUnsubscribeWebhook",
      data: {
        body
      }
    });

    if (body.event !== "message.push.unsubscribe") {
      throw new ForbiddenError("Invalid OneSignal event type");
    }
    const platform = OneSignalWebhookController._mapDeviceTypeToPlatform(body.deviceType);
    if (!platform) {
      throw new ForbiddenError(`Device type '${body.deviceType}' could not be mapped to android or ios`);
    }
    await UserService.removeDeviceToken(body.user_id, platform);
    return res.sendStatus(204);
  }

  /**
   * Maps OneSignal device type to our platform type using case-insensitive string matching
   * @param deviceType The device type string from OneSignal
   * @returns The mapped platform type or undefined if no match
   */
  private static _mapDeviceTypeToPlatform(deviceType: string): PlatformType | undefined {
    const normalizedType = deviceType.toLowerCase();

    if (normalizedType.includes("android")) {
      return "android";
    }

    if (normalizedType.includes("ios")) {
      return "ios";
    }

    return undefined;
  }
}

export default OneSignalWebhookController;
