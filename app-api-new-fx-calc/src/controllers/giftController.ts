import { CustomRequest } from "custom";
import { Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { BadRequestError } from "../models/ApiErrors";
import GiftService from "../services/giftService";
import { GiftDTOInterface, GiftStatusArray, GiftStatusType } from "../models/Gift";
import Decimal from "decimal.js";
import { replaceLineBreaksWithSpaces } from "../utils/stringUtil";

const GIFT_AMOUNT = 20;

export default class GiftController {
  public static async getGifts(req: CustomRequest, res: Response): Promise<Response> {
    const status = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "status",
      req.query.status,
      GiftStatusArray,
      {
        isRequired: false
      }
    ) as GiftStatusType;
    const hasViewedAppModal = ParamsValidationUtil.isBooleanParamValid(
      "hasViewedAppModal",
      req.query.hasViewedAppModal as string,
      { isRequired: false }
    );
    const used = ParamsValidationUtil.isBooleanParamValid("used", req.query.used as string, {
      isRequired: false
    });

    const targetUserEmail = req.user.email;

    return res.status(200).json(
      await GiftService.getGifts({
        targetUserEmail,
        hasViewedAppModal,
        status,
        used
      })
    );
  }

  public static async createGift(req: CustomRequest, res: Response): Promise<Response> {
    if (Object.keys(req.body).length === 0) {
      throw new BadRequestError("Request body cannot be empty");
    }

    const { targetUserEmail, message } = req.body;

    const finalTargetUserEmail = targetUserEmail.trim().toLowerCase();

    if (finalTargetUserEmail === req.user.email) {
      throw new BadRequestError("Gift receiver must be different from sender");
    }

    ParamsValidationUtil.isEmailParamValid("targetUserEmail", finalTargetUserEmail);

    const giftData: GiftDTOInterface = {
      gifter: req.user._id,
      targetUserEmail: finalTargetUserEmail,
      consideration: {
        currency: req.user.currency,
        amount: Decimal.mul(GIFT_AMOUNT, 100).toNumber()
      },
      message: replaceLineBreaksWithSpaces(message).trim()
    };

    const gift = await GiftService.createGift(giftData);

    return res.status(200).json(gift);
  }

  public static async updateGift(req: CustomRequest, res: Response): Promise<Response> {
    const giftId = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    if (Object.keys(req.body).length === 0) {
      throw new BadRequestError("Request body cannot be empty");
    }

    const hasViewedAppModal = ParamsValidationUtil.isBooleanParamValid(
      "hasViewedAppModal",
      req.body.hasViewedAppModal as string,
      {
        isRequired: false
      }
    );

    await GiftService.updateGift(giftId, { hasViewedAppModal });
    return res.sendStatus(204);
  }
}
