import { CustomRequest } from "custom";
import { Response } from "express";
import { RewardStatusArray, RewardStatusType } from "../models/Reward";
import RewardService from "../services/rewardService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { BadRequestError } from "../models/ApiErrors";

export default class RewardController {
  public static async getRewards(req: CustomRequest, res: Response): Promise<Response> {
    const status = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "status",
      req.query.status,
      RewardStatusArray,
      {
        isRequired: false
      }
    ) as RewardStatusType;
    const hasViewedAppModal = ParamsValidationUtil.isBooleanParamValid(
      "hasViewedAppModal",
      req.query.hasViewedAppModal as string,
      { isRequired: false }
    );
    const restrictedOnly = ParamsValidationUtil.isBooleanParamValid(
      "restrictedOnly",
      req.query.restrictedOnly as string,
      {
        isRequired: false
      }
    );

    const targetUser = req.user.id as string;
    return res.status(200).json(
      await RewardService.getRewards({
        targetUser,
        hasViewedAppModal,
        status,
        restrictedOnly
      })
    );
  }

  public static async updateReward(req: CustomRequest, res: Response): Promise<Response> {
    const rewardId = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    if (Object.keys(req.body).length === 0) {
      throw new BadRequestError("Request body cannot be empty");
    }

    const [hasViewedAppModal, accepted] = [
      ParamsValidationUtil.isBooleanParamValid("hasViewedAppModal", req.body.hasViewedAppModal as string, {
        isRequired: false
      }),
      ParamsValidationUtil.isBooleanParamValid("accepted", req.body.accepted as string, {
        isRequired: false
      })
    ];

    await RewardService.updateReward(rewardId, { hasViewedAppModal, accepted });
    return res.sendStatus(204);
  }
}
