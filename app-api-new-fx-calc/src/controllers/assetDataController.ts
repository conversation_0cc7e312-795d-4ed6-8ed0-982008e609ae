import { CustomRequest } from "custom";
import { Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";
import * as CacheUtil from "../utils/cacheUtil";
import eodService from "../external-services/eodService";
import DateUtil from "../utils/dateUtil";
import CorporateEventService from "../services/corporateEventService";
import {
  DISPLAY_IN_INVESTMENTS_CHART_CONFIG,
  DURATIONS_MAP,
  INTRADAY_DISPLAY_CONFIG,
  TenorEnum
} from "../configs/durationConfig";
import * as TickerUtil from "../utils/tickerUtil";
import PortfolioUtil from "../utils/portfolioUtil";
import type { PartialRecord } from "types/utils";
import { AssetPricesWithReturnsType } from "../services/investmentProductService";
import { DateTime } from "luxon";

const ASSET_INTRADAY_INTERVAL_MINUTES_WEEKLY = 10;
const ASSET_INTRADAY_INTERVAL_MINUTES_MONTHLY = 30;
const ASSET_ALL_TIME_SAMPLING_THRESHOLD = 502; // if more then 502 data points, then we sample
const ASSET_INTERVAL_WEEKS_ALL_TIME = 1; // 1 week
const ASSET_CACHING_INTRADAY_TTL_SECONDS = 60 * 15; // 15 minutes;
const ASSET_CACHING_HISTORICAL_TTL_SECONDS = 60 * 60 * 4; // 4 hours;

export class AssetDataController {
  /**
   * This method is used by our public asset pages to retrieve prices per tenor for the given asset ID.
   *
   * It should work regardless of whether we have an investment product for that particular asset ID, as public asset
   * pages could include assets not within our universe, given they are configured in shared config.
   *
   * @param req
   * @param res
   */
  public static async getAssetPricesByTenor(req: CustomRequest, res: Response): Promise<Response> {
    const assetId = req.query.assetId as publicInvestmentUniverseConfig.PublicAssetType;
    ParamsValidationUtil.isPublicAssetValid("asset", assetId, { isRequired: true });

    const [historicalPriceWeek, historicalPriceMonth, historicalPriceAll, stockSplit] = await Promise.all([
      CacheUtil.getCachedDataWithFallback<{ timestamp: number; close: number }[]>(
        `eod:historical:w:${assetId}`,
        async () => {
          const historicalPriceWeek = await eodService.getIntradayPrices(assetId, {
            from: DateUtil.getStartAndEndOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7)).start
          });
          return historicalPriceWeek;
        },
        (_) => ASSET_CACHING_INTRADAY_TTL_SECONDS
      ),
      CacheUtil.getCachedDataWithFallback<{ timestamp: number; close: number }[]>(
        `eod:historical:m:${assetId}`,
        async () => {
          const historicalPriceMonth = await eodService.getIntradayPrices(assetId, {
            from: DateUtil.getStartAndEndOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 30)).start
          });
          return historicalPriceMonth;
        },
        (_) => ASSET_CACHING_INTRADAY_TTL_SECONDS
      ),
      CacheUtil.getCachedDataWithFallback<{ date: string; close: number }[]>(
        `eod:historical:${assetId}`,
        async () => {
          const today = new Date(Date.now());
          const historicalPriceAll = await eodService.getHistoricalPrices(assetId, {
            from: DateUtil.getYearAndMonthAndDay(DateUtil.getDateOfYearsAgo(today, 10)),
            period: "d"
          });
          return historicalPriceAll;
        },
        (_) => ASSET_CACHING_HISTORICAL_TTL_SECONDS
      ),
      CorporateEventService.getMostRecentStockSplit(assetId)
    ]);

    const historicalPriceAllWithTimestamp = historicalPriceAll.map(
      ({ date, close }: { date: string; close: number }) => ({
        timestamp: DateTime.fromISO(date, { zone: "GMT" }).toJSDate().getTime(),
        close
      })
    );

    // Organise data by tenor
    const assetPricesByTenor = Object.fromEntries(
      Object.values(TenorEnum)
        .filter((tenor) => DISPLAY_IN_INVESTMENTS_CHART_CONFIG[tenor])
        .map((tenor) => {
          let data: {
            timestamp: number;
            close: number;
          }[];
          if (tenor === TenorEnum.ONE_WEEK) {
            const filteredHistoricalPriceWeek = TickerUtil.filterOlderThanNDaysOfPriceData(
              historicalPriceWeek,
              DURATIONS_MAP[tenor]
            );

            data = TickerUtil.sampleTickers(filteredHistoricalPriceWeek, ASSET_INTRADAY_INTERVAL_MINUTES_WEEKLY);
          } else if (tenor === TenorEnum.ONE_MONTH) {
            data = TickerUtil.sampleTickers(historicalPriceMonth, ASSET_INTRADAY_INTERVAL_MINUTES_MONTHLY);
          } else if (tenor === TenorEnum.ALL_TIME) {
            data = [...historicalPriceAllWithTimestamp];

            if (data.length >= ASSET_ALL_TIME_SAMPLING_THRESHOLD) {
              data = TickerUtil.sampleTickers(data, ASSET_INTERVAL_WEEKS_ALL_TIME, "weeks");
            }
          } else {
            // We set the date to N days ago and to the *start of the day*
            const { start: dateAtTenorStart } = DateUtil.getStartAndEndOfDay(
              DateUtil.getDateOfDaysAgo(new Date(Date.now()), DURATIONS_MAP[tenor])
            );
            data = historicalPriceAllWithTimestamp.filter(
              ({ timestamp }: { timestamp: number; close: number }) =>
                timestamp >= new Date(dateAtTenorStart).getTime()
            );
          }

          if ([TenorEnum.ONE_WEEK, TenorEnum.ONE_MONTH].includes(tenor) && stockSplit) {
            data = TickerUtil.adjustPricesForSplit(data, stockSplit);
          }

          return [
            tenor,
            {
              data,
              returns: PortfolioUtil.getReturnsOnArray(data.map(({ close }) => close)),
              displayIntraday: INTRADAY_DISPLAY_CONFIG[tenor]
            }
          ];
        })
    ) as PartialRecord<TenorEnum, AssetPricesWithReturnsType>;

    return res.status(200).json(assetPricesByTenor);
  }
}
