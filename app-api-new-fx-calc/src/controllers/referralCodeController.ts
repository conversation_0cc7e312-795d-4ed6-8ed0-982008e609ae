import { CustomRequest } from "custom";
import { Response } from "express";
import ReferralCodeService from "../services/referralCodeService";

export default class ReferralCodeController {
  public static async generateNewReferralCode(req: CustomRequest, res: Response): Promise<Response> {
    const referralCode = await ReferralCodeService.generateExpiringCode(req.user);
    return res.status(200).json(referralCode);
  }
}
