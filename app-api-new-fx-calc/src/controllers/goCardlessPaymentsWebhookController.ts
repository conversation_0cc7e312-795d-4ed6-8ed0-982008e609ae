import { CustomRequest } from "custom";
import { Response } from "express";
import logger from "../external-services/loggerService";
import { EventsType, GoCardlessPaymentsService, EventType } from "../external-services/goCardlessPaymentsService";
import MandateService from "../services/mandateService";
import { TransactionService } from "../services/transactionService";
import PayoutService from "../services/payoutService";

/**
 * This controller handles webhooks received from GoCardless Core API (includes payments, madnates etc.)
 *
 * **Note:** This controller is named GoCardlessPayments Webhook Controller, to avoid confusion with GoCardlessData API.
 *
 * Currently listening to the following events:
 * - mandates
 * - payments
 * - payouts
 */
export default class GoCardlessPaymentsWebhookController {
  public static async processWebhook(req: CustomRequest, res: Response): Promise<Response> {
    const requestSignature = req.headers["webhook-signature"] as string;
    try {
      GoCardlessPaymentsService.checkSignature(requestSignature, req.body);
    } catch (err) {
      return res.sendStatus(498);
    }

    const response = req.body as EventsType;

    await Promise.all([...response.events.map(GoCardlessPaymentsWebhookController._processEvent)]);

    return res.sendStatus(204);
  }

  private static _processEvent(event: EventType): Promise<void> {
    logger.info(`Received event ${event.id} of type ${event.resource_type}-${event.action}`, {
      module: "GoCardlessPaymentsWebhookController",
      method: "_processEvent",
      data: event
    });

    switch (event.resource_type) {
      case "mandates":
        return MandateService.processGoCardlessMandateEvent(event);
      case "payments":
        return TransactionService.processGoCardlessPaymentEvent(event);
      case "payouts":
        return PayoutService.processGoCardlessPayoutEvent(event);
      default:
        logger.info(`Not processing events of type ${event.resource_type}`, {
          module: "GoCardlessPaymentsWebhookController",
          method: "_processEvent"
        });
        break;
    }
  }
}
