import { CustomRequest } from "custom";
import { Response } from "express";
import NotificationSettingsService from "../services/notificationSettingsService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import {
  AppNotificationSettingEnum,
  EmailNotificationSettingEnum,
  NotificationSettingEnum
} from "../models/NotificationSettings";

export default class NotificationSettingsController {
  public static async getNotificationSettings(req: CustomRequest, res: Response) {
    const notificationSettings = await NotificationSettingsService.getNotificationsSettings(req.user);

    return res.status(200).json(notificationSettings);
  }

  public static async updateNotificationSettings(req: CustomRequest, res: Response) {
    const notificationId = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "id",
      req.body.id as string,
      [...Object.values(AppNotificationSettingEnum), ...Object.values(EmailNotificationSettingEnum)],
      { isRequired: true }
    ) as NotificationSettingEnum;
    const active = ParamsValidationUtil.isBooleanParamValid("active", req.body.active as string, {
      isRequired: true
    });

    const notificationSettings = await NotificationSettingsService.updateNotificationSetting(
      req.user,
      notificationId,
      active
    );

    return res.status(200).json(notificationSettings);
  }

  public static async updateDeviceNotificationSettings(req: CustomRequest, res: Response) {
    const deviceNotificationsEnabled = ParamsValidationUtil.isBooleanParamValid(
      "deviceNotificationsEnabled",
      req.body.deviceNotificationsEnabled as string,
      { isRequired: true }
    );

    await NotificationSettingsService.updateDeviceNotificationSettings(req.user, deviceNotificationsEnabled);

    return res.sendStatus(204);
  }
}
