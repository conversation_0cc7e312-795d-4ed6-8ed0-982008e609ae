import { Request, Response } from "express";
import { BadRequestError } from "../models/ApiErrors";
import { User } from "../models/User";
import BankAccountService from "../services/bankAccountService";
import { TruelayerDataClient } from "../external-services/truelayerService";
import BanksUtil from "../utils/banksUtil";

export default class TruelayerController {
  public static async linkAccount(req: Request, res: Response): Promise<Response> {
    const { code, userId } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      throw new BadRequestError(`No user found for id '${userId}'`, "Operation failed");
    }

    const truelayerDataService = new TruelayerDataClient(code);
    const ukAccount = await truelayerDataService.getUkAccount();
    const accountNumberInfo = ukAccount.account_number;

    const bankAccount = await BankAccountService.createOrUpdateBankAccount(
      user.id,
      {
        sortCode: accountNumberInfo.sort_code,
        number: accountNumberInfo.number,
        name: ukAccount.display_name,
        bankId: BanksUtil.getBankFromTruelayerProviderId(ukAccount.provider.provider_id),
        truelayerProviderId: ukAccount.provider.provider_id,
        active: true,
        owner: user.id
      },
      "truelayer_data"
    );

    if (!bankAccount) {
      throw new BadRequestError(`Could not create bank account for user '${user.email}'`, "Operation failed");
    }

    return res.status(200).json({
      provider_id: bankAccount.truelayerProviderId,
      account_number: accountNumberInfo.number,
      bankAccountId: bankAccount.id
    });
  }
}
