import { TransactionService } from "../services/transactionService";
import {
  truelayerPaymentWebhookClient,
  TruelayerPaymentWebhookClient
} from "../external-services/truelayerService";
import { Request, Response } from "express";
import { BadRequestError } from "../models/ApiErrors";

export default class TruelayerWebhookController {
  /**
   * @description
   * Process Truelayer Payment webhook, currently capable of handling payments to external account.
   * If we decide to handle payments to merchant account, payment_settled event should be added as well as further handling
   */
  public static async processPaymentsWebhook(req: Request, res: Response): Promise<Response> {
    await truelayerPaymentWebhookClient.verifyRequest(req);

    const truelayerId: string = req.body.payment_id;

    const payload = TruelayerPaymentWebhookClient.getPayloadFromPaymentEvent(req.body);

    // A truelayerId will only match with only one deposit or lifetime charge
    const [deposit, lifetimeCharge] = await Promise.all([
      TransactionService.getDepositByTruelayerId(truelayerId),
      TransactionService.getChargeByTruelayerId(truelayerId)
    ]);

    if (deposit) {
      await TransactionService.updateDepositTruelayerData(truelayerId, payload);
    } else if (lifetimeCharge) {
      await TransactionService.updateLifetimeChargeTruelayerData(truelayerId, payload);
    } else {
      throw new BadRequestError(
        `Received Truelayer webhook but payment id ${truelayerId} does not match any deposit or charge`
      );
    }

    return res.sendStatus(200);
  }
}
