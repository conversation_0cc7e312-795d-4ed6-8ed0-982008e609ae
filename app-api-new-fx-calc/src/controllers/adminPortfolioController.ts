import { CustomRequest } from "custom";
import { Response } from "express";
import { PortfolioModeEnum } from "../models/Portfolio";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import PortfolioService from "../services/portfolioService";
import mongoose from "mongoose";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import { BadRequestError, ForbiddenError } from "../models/ApiErrors";
import UserService from "../services/userService";
import { TransactionService } from "../services/transactionService";

export const ADMINS_ALLOWED_TO_SEND_DEPOSITS = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>"
];
export const MAXIMUM_ALLOWED_BONUS = 150;

export class AdminPortfolioController {
  public static readonly getPortfolios = async (req: CustomRequest, res: Response): Promise<Response> => {
    const params: {
      owner?: string;
      mode?: PortfolioModeEnum;
      wealthkernelExists?: boolean;
    } = {};

    const mode = req.query.mode;
    if (mode) {
      ParamsValidationUtil.isStringParamFromAllowedValuesValid("mode", mode, PortfolioModeEnum);
      params.mode = mode as PortfolioModeEnum;
    }

    const wealthkernelExists = req.query.wealthkernelExists as string;
    if (wealthkernelExists) {
      params.wealthkernelExists = ParamsValidationUtil.isBooleanParamValid(
        "wealthkernelExists",
        wealthkernelExists
      );
    }

    const owner = req.query.owner as string;
    if (owner) {
      ParamsValidationUtil.isObjectIdParamValid("owner", owner);
      const user = await User.findById(new mongoose.Types.ObjectId(owner));
      if (!user) {
        throw new BadRequestError("Param 'owner' does not refer to a user", "Invalid parameter");
      }
      params.owner = owner;
    }

    const sort = req.query.sort as string;
    const populateTickers = req.query.populateTicker
      ? ParamsValidationUtil.isBooleanParamValid("populateTicker", req.query.populateTicker as string)
      : true;

    return res.status(200).json(await PortfolioService.getPortfolios(params, sort, populateTickers));
  };

  public static async addBonusDepositToUser(req: CustomRequest, res: Response): Promise<Response> {
    if (Object.keys(req.body).length === 0) {
      throw new BadRequestError("Request body cannot be empty");
    }

    const targetUserId = ParamsValidationUtil.isObjectIdParamValid("targetUserId", req.body.targetUserId);
    const bonusAmount = ParamsValidationUtil.isNumericParamValid(
      "bonusDepositAmount",
      req.body.bonusDepositAmount
    );

    if (bonusAmount > MAXIMUM_ALLOWED_BONUS) {
      throw new BadRequestError("Requested amount exceeds limit");
    }
    if (!ADMINS_ALLOWED_TO_SEND_DEPOSITS.includes(req.user.email)) {
      throw new ForbiddenError("Current admin is not authorized to send deposits");
    }

    const user = await UserService.getUser(targetUserId);

    if (!user) {
      throw new BadRequestError("User does not exist");
    }

    await TransactionService.addBonusDepositToUser(user, bonusAmount);

    logger.info(`Admin ${req.user.email} created bonus deposit`, {
      module: "adminPortfolioController",
      method: "addBonusDepositToUser",
      data: {
        targetUserId,
        bonusAmount
      }
    });

    return res.sendStatus(201);
  }
}
