import { CustomRequest } from "custom";
import { Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { investmentUniverseConfig, allocationsConfig } from "@wealthyhood/shared-configs";
import StatisticsConfig from "../configs/statisticsConfig";
import { BadRequestError } from "../models/ApiErrors";
import {
  FuturePerformanceParamsType,
  PerformanceParamsType,
  StatisticsService
} from "../services/statisticsService";

const { ASSET_CONFIG, ASSET_CLASS_CONFIG } = investmentUniverseConfig;

export default class StatisticsController {
  public static async getOptimalAllocation(req: CustomRequest, res: Response): Promise<void> {
    const { risk, assets } = StatisticsController._getOptimalAllocationParamsFromRequest(req);

    const allocation = await StatisticsService.fetchOptimalAllocation({ risk, asset: assets });
    res.json({ allocation });
  }

  public static async getPortfolioPastPerformance(req: CustomRequest, res: Response): Promise<void> {
    const { initial, ...allocation } = req.query as Record<string, string>;

    const params = {
      tenor: "0",
      initial: ParamsValidationUtil.isNumericParamValid("initial", initial as string),
      allocation: { ...StatisticsController._validateAllocation(allocation) }
    } as PerformanceParamsType;

    const pastPerformanceData = await StatisticsService.fetchPastPerformance(params);

    res.status(200).json(pastPerformanceData);
  }

  public static async getPortfolioFuturePerformance(req: CustomRequest, res: Response): Promise<void> {
    const { initial, monthly, ...allocation } = req.query as Record<string, string>;

    const params = {
      initial: initial
        ? ParamsValidationUtil.isNumericParamValid("initial", initial as string, { isRequired: false })
        : StatisticsConfig.FUTURE_DEFAULT_INITIAL_INVESTMENT,
      monthly: monthly
        ? ParamsValidationUtil.isNumericParamValid("monthly", monthly as string, { isRequired: false })
        : StatisticsConfig.FUTURE_DEFAULT_MONTHLY_INVESTMENT,
      allocation: { ...StatisticsController._validateAllocation(allocation) }
    } as FuturePerformanceParamsType;

    const futurePerformanceData = await StatisticsService.fetchFuturePerformance(params);

    res.status(200).json(futurePerformanceData);
  }

  public static async getPortfolioFuturePerformanceMonteCarlo(req: CustomRequest, res: Response): Promise<void> {
    const { initial, monthly, ...allocation } = req.query as Record<string, string>;

    const params = {
      initial: initial
        ? ParamsValidationUtil.isNumericParamValid("initial", initial as string, { isRequired: false })
        : StatisticsConfig.FUTURE_DEFAULT_INITIAL_INVESTMENT,
      monthly: monthly
        ? ParamsValidationUtil.isNumericParamValid("monthly", monthly as string, { isRequired: false })
        : StatisticsConfig.FUTURE_DEFAULT_MONTHLY_INVESTMENT,
      allocation: { ...StatisticsController._validateAllocation(allocation) }
    } as FuturePerformanceParamsType;

    const futurePerformanceData = await StatisticsService.fetchFuturePerformanceMonteCarlo(params);

    res.status(200).json(futurePerformanceData);
  }

  private static _validateAllocation(allocation: Record<string, string>) {
    // validate that all remaining query params are asset keys with numeric values
    const totalAllocation = Object.entries(allocation)
      .map(([key, value]: [string, string]) => {
        ParamsValidationUtil.isAssetValid(key, key);
        ParamsValidationUtil.isNumericParamValid(key, value);
        return Number(value);
      })
      .reduce((sum, percentage) => sum + percentage, 0);

    if (totalAllocation != 100) {
      throw new BadRequestError("Allocation does not sum to 100", "Invalid parameter");
    }

    return Object.fromEntries(Object.entries(allocation).map(([key, value]) => [key, Number(value)]));
  }

  private static _getOptimalAllocationParamsFromRequest(req: CustomRequest): {
    risk: number;
    assets: string[];
  } {
    const roboAdvisorRiskLevel = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "roboAdvisorRiskLevel",
      req.query.roboAdvisorRiskLevel as string,
      allocationsConfig.RoboAdvisorRiskLevelEnum,
      { isRequired: false }
    ) as allocationsConfig.RoboAdvisorRiskLevelEnum;

    // If the client has passed a robo-advisor risk level, then we retrieve risk & assets from configuration.
    if (roboAdvisorRiskLevel) {
      const risk = allocationsConfig.ROBO_ADVISOR_CONFIG[roboAdvisorRiskLevel].risk;
      const assets = allocationsConfig.ROBO_ADVISOR_CONFIG[roboAdvisorRiskLevel].assetClasses
        .flatMap(
          (assetClass) =>
            ASSET_CLASS_CONFIG[assetClass].coreAssets[
              allocationsConfig.ROBO_ADVISOR_CONFIG[roboAdvisorRiskLevel].geography
            ]
        )
        .map(
          (asset) => (ASSET_CONFIG[asset] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping ?? asset
        );

      return { risk, assets };
    }

    // If not, we retrieve those parameters from query parameters provided.
    const risk = ParamsValidationUtil.isNumericParamValid("risk", req.query.risk as string, { isRequired: true });
    const assets = Array.isArray(req.query.asset)
      ? (req.query.asset as [investmentUniverseConfig.AssetType])
      : ([req.query.asset] as [investmentUniverseConfig.AssetType]);

    // FIXME: When clients are released, we should make sure they stop using statsUrlMapping and this check is updated
    assets.forEach((assetUrlMapping) => {
      const assetId = Object.entries(ASSET_CONFIG).find(
        ([, assetConfig]) =>
          (assetConfig as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping ??
          assetId === assetUrlMapping
      )[0] as investmentUniverseConfig.AssetType;
      ParamsValidationUtil.isAssetValid("asset", assetId, { isRequired: true });
    });

    return { risk, assets };
  }
}
