import { CustomRequest } from "custom";
import { Response } from "express";
import mongoose from "mongoose";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { PaymentStatusArrayV1, PaymentStatusArrayV3 } from "../external-services/truelayerService";
import {
  PortfolioTransactionCategoryType,
  Transaction,
  TransactionCashActivityFilterEnum,
  TransactionCategoryArray,
  TransactionCategoryType,
  TransactionStatusArray
} from "../models/Transaction";
import { TransactionService } from "../services/transactionService";
import { DepositTransactionsFilter, TransactionsFilter } from "filters";
import BankAccountValidationUtil from "../utils/bankAccountValidationUtil";
import { BadRequestError } from "../models/ApiErrors";
import PortfolioService, { PendingOrdersType, PortfolioAllocationMethodEnum } from "../services/portfolioService";
import logger from "../external-services/loggerService";
import { AssetType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { PlatformCategoryEnum } from "../configs/platformConfig";
import { DepositActionEnum } from "../configs/depositsConfig";
import { banksConfig, entitiesConfig } from "@wealthyhood/shared-configs";

export default class TransactionController {
  public static readonly getTransaction = async (req: CustomRequest, res: Response): Promise<Response> => {
    // req url param id is being validated in transaction middleware
    return res.status(200).json(await Transaction.findById(req.params.id));
  };

  public static readonly getAssetTransaction = async (req: CustomRequest, res: Response): Promise<Response> => {
    const populateOrders = req.query.populateOrders
      ? ParamsValidationUtil.isBooleanParamValid("populateOrders", req.query.populateOrders as string)
      : false;
    const populateForeignCurrencyRates = req.query.populateOrders
      ? ParamsValidationUtil.isBooleanParamValid(
          "populateForeignCurrencyRates",
          req.query.populateForeignCurrencyRates as string,
          { isRequired: false }
        )
      : false;
    // req url param id is being validated in transaction middleware
    return res
      .status(200)
      .json(
        await TransactionService.getAssetTransaction(req.params.id, populateOrders, populateForeignCurrencyRates)
      );
  };

  public static readonly getAssetTransactionLinkedToDeposit = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    return res.status(200).json(await TransactionService.getAssetTransactionLinkedToDeposit(req.params.id));
  };

  public static readonly getCashActivity = async (req: CustomRequest, res: Response): Promise<Response> => {
    let limit;
    if (req.query.limit) {
      limit = ParamsValidationUtil.isNumericParamValid("limit", req.query.limit, {
        isRequired: false
      });
    }

    if (limit && limit < 0) {
      throw new BadRequestError("Limit cannot be negative");
    }

    const cashActivityTransactions = await TransactionService.getUserCashActivity(req.user, limit);
    const transactionsWithTypeField = cashActivityTransactions.map((transaction) => {
      return {
        type: "transaction",
        item: transaction,
        activityFilter: TransactionCashActivityFilterEnum[transaction.cashActivityFilter],
        cashFlowSign: transaction.isCashFlowPositive ? 1 : -1
      };
    });

    let limitedTransactionsWithTypeField = transactionsWithTypeField;
    if (limit) {
      limitedTransactionsWithTypeField = transactionsWithTypeField.slice(0, limit);
    }

    return res.status(200).json(limitedTransactionsWithTypeField);
  };

  public static readonly getDepositCashTransaction = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    // req url param id is being validated in transaction middleware
    return res.status(200).json(await TransactionService.getDepositCashTransaction(req.params.id));
  };

  public static readonly getTransactions = async (req: CustomRequest, res: Response): Promise<Response> => {
    const filter = TransactionController._createValidTransactionsFilter(req);

    // If the client has not specified categories, we do not return revert reward transactions which are irrelevant for clients.
    if (!filter.categories) {
      filter.categories = TransactionCategoryArray.filter(
        (transaction) => !["RevertRewardTransaction"].includes(transaction)
      );
    }

    const sort = req.query.sort as string;

    // set up population options
    const populateOwner =
      ParamsValidationUtil.isBooleanParamValid("populateOwner", req.query.populateOwner as string, {
        isRequired: false
      }) ?? false;
    const populatePendingDeposit =
      ParamsValidationUtil.isBooleanParamValid(
        "populatePendingDeposit",
        req.query.populatePendingDeposit as string,
        { isRequired: false }
      ) ?? true;
    const populateLinkedAutomation =
      ParamsValidationUtil.isBooleanParamValid(
        "populateLinkedAutomation",
        req.query.populateLinkedAutomation as string,
        { isRequired: false }
      ) ?? true;
    const populate: string[] = req.query.populate
      ? ParamsValidationUtil.isArrayQueryParamValid("populate", req.query.populate as string, {
          isRequired: false
        })
      : [];
    const populateOptions = Object.fromEntries(
      populate
        .map((key) => [key, true])
        .concat([
          ["owner", populateOwner],
          ["pendingDeposit", populatePendingDeposit],
          ["linkedAutomation", populateLinkedAutomation]
        ])
    );
    const transactions = await TransactionService.getTransactions(filter, null, populateOptions, sort);

    return res.status(200).json(transactions);
  };

  public static readonly getPendingTransactions = async (req: CustomRequest, res: Response): Promise<Response> => {
    return res.status(200).json(await TransactionService.getPendingTransactions(req.user));
  };

  public static readonly getPendingRebalances = async (req: CustomRequest, res: Response): Promise<Response> => {
    return res.status(200).json(await TransactionService.getPendingRebalances(req.user.id));
  };

  public static readonly getDepositCashTransactions = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const filter: DepositTransactionsFilter = TransactionController._createValidTransactionsFilter(req);

    if (req.query.truelayerPaymentId) {
      filter.truelayerPaymentId = req.query.truelayerPaymentId as string;
    } else if (req.query.saltedgeCustomPaymentId) {
      filter.saltedgeCustomPaymentId = req.query.saltedgeCustomPaymentId as string;
    }

    const sort = req.query.sort as string;
    const populateOwner = req.query.populateOwner
      ? ParamsValidationUtil.isBooleanParamValid("populateOwner", req.query.populateOwner as string)
      : false;
    const populate: string[] = req.query.populate
      ? ParamsValidationUtil.isArrayQueryParamValid("populate", req.query.populate as string, {
          isRequired: false
        })
      : [];
    const populateOptions = Object.fromEntries(
      populate.map((key) => [key, true]).concat([["owner", populateOwner]])
    );

    return res.status(200).json({
      data: await TransactionService.getDepositCashTransactions(filter, null, populateOptions, sort)
    });
  };

  public static readonly getAssetTransactions = async (req: CustomRequest, res: Response): Promise<Response> => {
    const filter = TransactionController._createValidTransactionsFilter(req);

    const sort = req.query.sort as string;
    const populateOwner = req.query.populateOwner
      ? ParamsValidationUtil.isBooleanParamValid("populateOwner", req.query.populateOwner as string)
      : false;
    const populateOrders = req.query.populateOrders
      ? ParamsValidationUtil.isBooleanParamValid("populateOrders", req.query.populateOrders as string)
      : false;
    const populatePendingDeposit = req.query.populatePendingDeposit
      ? ParamsValidationUtil.isBooleanParamValid(
          "populatePendingDeposit",
          req.query.populatePendingDeposit as string
        )
      : true;

    return res.status(200).json({
      data: await TransactionService.getAssetTransactions(
        filter,
        null,
        { owner: populateOwner, orders: populateOrders, pendingDeposit: populatePendingDeposit },
        sort
      )
    });
  };

  public static readonly createPayment = async (req: CustomRequest, res: Response): Promise<Response> => {
    const paymentAmount = ParamsValidationUtil.isNumericParamValid("paymentAmount", req.body.paymentAmount);
    const depositAndInvest = req.body.depositAndInvest
      ? ParamsValidationUtil.isBooleanParamValid("depositAndInvest", req.body.depositAndInvest)
      : false;
    const depositAction = depositAndInvest ? DepositActionEnum.DEPOSIT_AND_INVEST : DepositActionEnum.JUST_PAY;
    const bankId = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "bankId",
      req.body.bankId,
      banksConfig.BankArrayConst,
      {
        isRequired: false
      }
    ) as banksConfig.BankType;

    const bankAccountId = await TransactionController._extractAndValidateBankAccountIdFromRequest(req);
    const portfolioId = ParamsValidationUtil.isObjectIdParamValid("portfolioId", req.body.portfolioId, {
      isRequired: false
    });
    const platform = ["ios", "android"].includes(req.headers.platform as string)
      ? PlatformCategoryEnum.MOBILE
      : PlatformCategoryEnum.WEB;
    const version = req.headers.version as string;

    return res.status(200).json({
      data: await TransactionService.createOpenBankingDepositTransaction(
        req.user,
        paymentAmount,
        bankAccountId,
        portfolioId,
        depositAction,
        bankId,
        { platform, version }
      )
    });
  };

  public static readonly createLifetimeSubscriptionCharge = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const bankAccountId = ParamsValidationUtil.isObjectIdParamValid("bankAccountId", req.body.bankAccountId);
    const price = req.body["price"];

    if (!price) {
      throw new BadRequestError("Missing field 'price'", "Operation failed");
    } else if (!bankAccountId) {
      throw new BadRequestError("Missing field 'bankAccountId'", "Operation failed");
    }

    await BankAccountValidationUtil.validateBankAccountIsActive(bankAccountId);

    const platform = ["ios", "android"].includes(req.headers.platform as string)
      ? PlatformCategoryEnum.MOBILE
      : PlatformCategoryEnum.WEB;
    const version = req.headers.version as string;

    const { paymentUri } = await TransactionService.createLifetimeSubscriptionChargeTransaction(
      req.user,
      price,
      bankAccountId,
      { platform, version }
    );

    return res.status(200).json({
      data: { paymentUri }
    });
  };

  public static readonly createSavingsDepositPayment = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const paymentAmount = ParamsValidationUtil.isNumericParamValid("paymentAmount", req.body.paymentAmount);
    const bankAccountId = await TransactionController._extractAndValidateBankAccountIdFromRequest(req);
    const portfolioId = ParamsValidationUtil.isObjectIdParamValid("portfolioId", req.body.portfolioId, {
      isRequired: false
    });
    const platform = ["ios", "android"].includes(req.headers.platform as string)
      ? PlatformCategoryEnum.MOBILE
      : PlatformCategoryEnum.WEB;
    const version = req.headers.version as string;

    return res.status(200).json({
      data: await TransactionService.createOpenBankingDepositTransaction(
        req.user,
        paymentAmount,
        bankAccountId,
        portfolioId,
        DepositActionEnum.DEPOSIT_AND_SAVE,
        null, // It's currently TBD whether we'll implement one-step investments (including savings) for EU.
        { platform, version }
      )
    });
  };

  public static readonly getTransactionPreview = async (req: CustomRequest, res: Response): Promise<Response> => {
    const portfolioId = ParamsValidationUtil.isObjectIdParamValid("portfolioId", req.query.portfolioId);

    const transactionCategory = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "category",
      req.query.category,
      TransactionCategoryArray
    ) as TransactionCategoryType;
    if (transactionCategory !== "AssetTransaction") {
      throw new BadRequestError(`Preview cannot be given for asset transaction category ${transactionCategory}`);
    }

    const portfolioTransactionType = req.query.portfolioTransactionType as PortfolioTransactionCategoryType;
    if (!["buy", "update"].includes(portfolioTransactionType)) {
      throw new BadRequestError(`Preview cannot be given for asset transaction type ${portfolioTransactionType}`);
    }

    const portfolio = await PortfolioService.getPortfolio(portfolioId, true);

    if (portfolioTransactionType === "update") {
      const pendingOrders: PendingOrdersType = req.body.pendingOrders;
      if (!pendingOrders) {
        throw new BadRequestError("Update portfolio operation preview required pendingOrders in body");
      } else if (Object.keys(pendingOrders).length !== 1) {
        throw new BadRequestError("Portfolio update only allows a single order in the request body");
      }
      const [asset, pendingOrder] = Object.entries(pendingOrders)[0];

      const transactionPreview = await TransactionService.getPortfolioUpdatePreview(
        portfolio,
        asset as AssetType,
        pendingOrder
      );

      return res.status(200).json(transactionPreview);
    }

    if (portfolioTransactionType === "buy") {
      const allocationMethod = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "allocationMethod",
        req.query.allocationMethod,
        PortfolioAllocationMethodEnum,
        {
          isRequired: true
        }
      ) as PortfolioAllocationMethodEnum;

      if (
        allocationMethod === PortfolioAllocationMethodEnum.TARGET_ALLOCATION &&
        !portfolio.isTargetAllocationSetup
      ) {
        throw new BadRequestError("Target allocation is not set up for this portfolio");
      } else if (allocationMethod === PortfolioAllocationMethodEnum.HOLDINGS && !portfolio.holdings.length) {
        throw new BadRequestError("Portfolio has no holdings");
      }

      const orderAmount = ParamsValidationUtil.isNumericParamValid("orderAmount", req.query.orderAmount);

      const transactionPreview = await TransactionService.getPortfolioBuyPreview(portfolio, orderAmount, {
        allocationMethod
      });

      return res.status(200).json(transactionPreview);
    }
  };

  public static readonly cancelTransaction = async (req: CustomRequest, res: Response): Promise<Response> => {
    const updatedTransaction = await TransactionService.cancelTransaction(req.params.id);

    return res.status(200).json(updatedTransaction);
  };

  /**
   * @deprecated endpoint that used to sync the status from truelayer to the local deposit document.
   *
   * Currently, as we do this in webhooks, this endpoint just returns the latest version of the deposit document.
   * Clients should move to using GET /deposits
   */
  public static readonly syncDepositTruelayerStatus = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const truelayerId = req.query.truelayerId as string;

    const deposit = await TransactionService.getDepositByTruelayerId(truelayerId);

    logger.info(`Syncing deposit truelayer status for ${truelayerId}`, {
      module: "TransactionController",
      method: "syncDepositTruelayerStatus",
      userEmail: req.user.email,
      data: { deposit }
    });

    if (!deposit) throw new BadRequestError(`TruelayerId ${truelayerId} did not match any deposit transaction`);

    return res.status(200).json(deposit);
  };

  /**
   * @description Syncs deposit cash transaction with truelayer by paymentId.
   */
  public static readonly syncLifetimeChargeTruelayerStatus = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    logger.info("Handling truelayer pay callback for lifetime charge.", {
      module: "TransactionController",
      method: "syncLifetimeChargeTruelayerStatus",
      userEmail: req.user.email
    });

    const truelayerId = req.query.truelayerId as string;

    const result = await TransactionService.syncLifetimeChargeTruelayerStatus(truelayerId);
    const syncedChargeTransaction = result.data[0];

    return res.status(200).json(syncedChargeTransaction);
  };

  public static readonly updateWealthyhoodDividendTransaction = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const transactionId = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    if (Object.keys(req.body).length === 0) {
      throw new BadRequestError("Request body cannot be empty");
    }

    const hasViewedAppModal = ParamsValidationUtil.isBooleanParamValid(
      "hasViewedAppModal",
      req.body.hasViewedAppModal as string,
      {
        isRequired: true
      }
    );

    await TransactionService.updateWealthyhoodDividendTransaction(transactionId, { hasViewedAppModal });

    return res.sendStatus(204);
  };

  public static readonly getBilling = async (req: CustomRequest, res: Response): Promise<Response> => {
    const userId = req.user.id as string;
    let limit;
    if (req.query.limit) {
      limit = ParamsValidationUtil.isNumericParamValid("limit", req.query.limit, {
        isRequired: false
      });
    }

    if (limit && limit < 0) {
      throw new BadRequestError("Limit cannot be negative");
    }

    return res.status(200).json(await TransactionService.getUserBilling(userId, limit));
  };

  private static readonly _createValidTransactionsFilter = (req: CustomRequest): TransactionsFilter => {
    const filter: TransactionsFilter = {};

    const portfolio = req.query.portfolio as string;
    if (portfolio) {
      ParamsValidationUtil.isObjectIdParamValid("portfolio", portfolio);
      filter.portfolio = new mongoose.Types.ObjectId(portfolio);
    }
    const category = req.query.category as string;
    if (category) {
      filter.categories = [category] as TransactionCategoryType[];
    }
    const owner = req.user.id;
    filter.owner = owner as string;
    if (req.query.truelayerStatus) {
      const truelayerStatuses = (
        Array.isArray(req.query.truelayerStatus) ? req.query.truelayerStatus : [req.query.truelayerStatus]
      ) as string[];

      truelayerStatuses.forEach((status) =>
        ParamsValidationUtil.isStringParamFromAllowedValuesValid("truelayerStatus", status, [
          ...PaymentStatusArrayV1,
          ...PaymentStatusArrayV3
        ])
      );
      filter.truelayerStatuses = truelayerStatuses;
    }
    if (req.query.status) {
      const statuses = (Array.isArray(req.query.status) ? req.query.status : [req.query.status]) as string[];
      statuses.forEach((status) =>
        ParamsValidationUtil.isStringParamFromAllowedValuesValid("status", status, TransactionStatusArray)
      );
      filter.statuses = statuses;
    }
    const hasViewedAppModal = req.query.hasViewedAppModal as string;
    if (hasViewedAppModal === "true") {
      filter.hasViewedAppModal = true;
    } else if (hasViewedAppModal === "false") {
      filter.hasViewedAppModal = false;
    }

    return filter;
  };

  private static readonly _extractAndValidateBankAccountIdFromRequest = async (
    req: CustomRequest
  ): Promise<string> => {
    if (req.body.bankAccountId) {
      ParamsValidationUtil.isObjectIdParamValid("bankAccountId", req.body.bankAccountId);
      await BankAccountValidationUtil.validateBankAccountIsActive(req.body.bankAccountId);
      return req.body.bankAccountId;
    } else if (req.user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      throw new BadRequestError("Operation failed", "A bank account ID is required for the UK payment flow");
    }
  };
}
