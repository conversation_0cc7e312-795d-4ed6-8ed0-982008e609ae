import { Response } from "express";
import { CustomRequest } from "custom";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import InvestmentProductService from "../services/investmentProductService";
import DateUtil from "../utils/dateUtil";
import AssetNewsService from "../services/assetNewsService";
import { AssetNewsDocument } from "../models/AssetNews";
const DAYS_TO_BE_IN_LATEST = 7;

export default class AssetNewsController {
  public static readonly getAssetNews = async (req: CustomRequest, res: Response): Promise<Response> => {
    const assetId = req.query.assetId as investmentUniverseConfig.AssetType;
    ParamsValidationUtil.isAssetValid("assetId", assetId, { isRequired: true });

    let limit;
    if (req.query.limit) {
      limit = ParamsValidationUtil.isNumericParamValid("limit", req.query.limit, {
        isRequired: false
      });
    }
    const investmentProduct = await InvestmentProductService.getInvestmentProduct(assetId, false);
    const assetNews = await AssetNewsService.getAssetNews(investmentProduct.id, limit);

    return res.status(200).json(this._groupNewsBySections(assetNews));
  };

  private static readonly _groupNewsBySections = (
    assetNews: AssetNewsDocument[]
  ): {
    sectionTitle: string;
    data: AssetNewsDocument[];
  }[] => {
    const now = new Date(Date.now());

    const groupedNews = assetNews.reduce(
      (
        acc: {
          [key: string]: AssetNewsDocument[];
        },
        item
      ) => {
        const diffDays = DateUtil.dateDiffInWholeDays(item.date, now);
        let sectionTitle: string;

        if (diffDays < DAYS_TO_BE_IN_LATEST) {
          sectionTitle = "Latest";
        } else {
          sectionTitle = DateUtil.formatDateToMONYYYY(item.date);
        }

        if (!acc[sectionTitle]) {
          acc[sectionTitle] = [];
        }
        acc[sectionTitle].push(item);
        return acc;
      },
      {}
    );

    return Object.keys(groupedNews).map((sectionTitle) => ({
      sectionTitle,
      data: groupedNews[sectionTitle]
    }));
  };
}
