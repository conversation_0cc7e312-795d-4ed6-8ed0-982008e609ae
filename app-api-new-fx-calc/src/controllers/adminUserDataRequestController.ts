import { CustomRequest } from "custom";
import { Response } from "express";
import UserDataRequestService from "../services/userDataRequestService";
import { BadRequestError } from "../models/ApiErrors";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import UserService from "../services/userService";
import { UserDataRequestReasonEnum } from "../models/UserDataRequest";

export default class AdminUserDataRequestController {
  public static readonly getUserDataRequests = async (req: CustomRequest, res: Response): Promise<Response> => {
    const userId = ParamsValidationUtil.isObjectIdParamValid("owner", req.query.owner);

    return res.status(200).json(await UserDataRequestService.getUserDataRequests({ owner: userId }));
  };

  public static readonly createUserDataRequest = async (req: CustomRequest, res: Response): Promise<Response> => {
    const { owner, requestType } = req.body;
    ["owner", "requestType"].forEach((prop) => {
      if (!req.body[prop]) {
        throw new BadRequestError(`Missing field '${prop}'`, "Operation failed");
      }
    });

    const user = await UserService.getUser(owner);
    if (!user) {
      throw new BadRequestError(`User ${owner} does not exist!`);
    }

    await UserDataRequestService.createUserDataRequest(user, requestType, UserDataRequestReasonEnum.ADMIN_REQUEST);

    return res.sendStatus(204);
  };
}
