import { CustomRequest } from "custom";
import { Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import PaginationUtil from "../utils/paginationUtil";
import GiftService from "../services/giftService";

export default class AdminGiftController {
  public static async getGift(req: CustomRequest, res: Response): Promise<Response> {
    const giftId = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id);

    const gift = await GiftService.getGift(giftId, { gifter: true, linkedAssetTransaction: true });
    return res.status(200).json(gift);
  }

  public static async getGifts(req: CustomRequest, res: Response): Promise<Response> {
    const targetUserEmail = ParamsValidationUtil.isEmailParamValid(
      "targetUserEmail",
      req.query.targetUserEmail as string,
      {
        isRequired: false
      }
    );

    const pageConfig = PaginationUtil.getValidatedPageConfigFromPaginationProvided(
      req.query.pageSize as string,
      req.query.page as string
    );

    return res.status(200).json(await GiftService.getGifts({ targetUserEmail }, pageConfig));
  }
}
