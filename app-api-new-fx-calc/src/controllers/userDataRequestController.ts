import { CustomRequest } from "custom";
import { Response } from "express";
import UserDataRequestService from "../services/userDataRequestService";
import { BadRequestError } from "../models/ApiErrors";
import { UserDataRequestReasonEnum } from "../models/UserDataRequest";

export default class UserDataRequestController {
  public static readonly getUserDataRequests = async (req: CustomRequest, res: Response): Promise<Response> => {
    const user = req.user.id as string;

    return res.status(200).json({ data: await UserDataRequestService.getUserDataRequests({ owner: user }) });
  };

  public static readonly createUserDataRequest = async (req: CustomRequest, res: Response): Promise<Response> => {
    const { requestType } = req.body;
    ["requestType"].forEach((prop) => {
      if (!req.body[prop]) {
        throw new BadRequestError(`Missing field '${prop}'`, "Operation failed");
      }
    });

    const userDataRequest = await UserDataRequestService.createUserDataRequest(
      req.user,
      requestType,
      UserDataRequestReasonEnum.USER_REQUEST
    );

    return res.status(200).json(userDataRequest);
  };
}
