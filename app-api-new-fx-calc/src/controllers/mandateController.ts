import { CustomRequest } from "custom";
import { Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import MandateService from "../services/mandateService";
import mongoose from "mongoose";
import { MandateCategoryArray, MandateCategoryType, MandateDocument, MandateInterface } from "../models/Mandate";
import BankAccountService from "../services/bankAccountService";
import { BankAccountDocument } from "../models/BankAccount";
import { UserDocument } from "../models/User";

export default class MandateController {
  public static async getMandates(req: CustomRequest, res: Response): Promise<Response> {
    const owner = req.user.id as string;
    const category = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "category",
      req.query.category,
      MandateCategoryArray,
      { isRequired: false }
    ) as MandateCategoryType;
    const includeInactive =
      ParamsValidationUtil.isBooleanParamValid("includeInactive", req.query.includeInactive as string, {
        isRequired: false
      }) ?? true;

    const mandates = await MandateService.getMandates({
      owner,
      category,
      includeInactive
    });

    return res.status(200).json({ data: await MandateController._fillClientDisplayFields(req.user, mandates) });
  }

  public static async setupMandate(req: CustomRequest, res: Response): Promise<Response> {
    const owner = req.user.id as string;
    const bankAccount = ParamsValidationUtil.isObjectIdParamValid("bankAccount", req.body.bankAccount as string, {
      isRequired: true
    });
    const category = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "category",
      req.body.category as string,
      MandateCategoryArray,
      {
        isRequired: true
      }
    ) as MandateCategoryType;

    const mandate = await MandateService.createMandate({
      owner: new mongoose.Types.ObjectId(owner),
      bankAccount: new mongoose.Types.ObjectId(bankAccount),
      category
    });

    return res.status(200).json(mandate);
  }

  private static async _fillClientDisplayFields(
    user: UserDocument,
    mandates: MandateDocument[]
  ): Promise<MandateInterface[]> {
    return mandates.map((mandate) => {
      return {
        ...mandate.toObject(),
        bankAccount: mandate.populated("bankAccount")
          ? (BankAccountService.fillClientDisplayFields(
              mandate.bankAccount as BankAccountDocument,
              user
            ) as BankAccountDocument)
          : mandate.bankAccount
      } as MandateInterface;
    });
  }
}
