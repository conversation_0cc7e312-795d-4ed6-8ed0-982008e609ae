import { CustomRequest } from "custom";
import { Response } from "express";
import BankAccountService from "../services/bankAccountService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import BankAccountRepository from "../repositories/bankAccountRepository";
import { WealthyhoodBankAccountStatusArray, WealthyhoodBankAccountStatusType } from "../models/BankAccount";

export class AdminBankAccountController {
  public static readonly createAllWkBankAccounts = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    await BankAccountService.createAllWkBankAccounts();
    return res.sendStatus(204);
  };

  public static readonly syncAllWkBankAccounts = async (req: CustomRequest, res: Response): Promise<Response> => {
    await BankAccountService.syncAllWkBankAccounts();
    return res.sendStatus(204);
  };

  public static readonly getSuspendedBankAccounts = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const suspendedBankAccounts = await BankAccountService.getSuspendedBankAccounts();
    return res.status(200).json({ data: suspendedBankAccounts });
  };

  public static readonly getPendingBankAccounts = async (req: CustomRequest, res: Response): Promise<Response> => {
    const pendingBankAccounts = await BankAccountService.getPendingBankAccounts();
    return res.status(200).json({ data: pendingBankAccounts });
  };

  public static readonly updateBankAccountWealthyhoodStatus = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const bankAccountId = ParamsValidationUtil.isObjectIdParamValid("bankAccountId", req.params.id);
    const status = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "status",
      req.body.status,
      WealthyhoodBankAccountStatusArray
    ) as WealthyhoodBankAccountStatusType;

    const updatedBankAccount = await BankAccountRepository.updateWealthyhoodStatus(bankAccountId, status);

    return res.status(200).json({ data: updatedBankAccount });
  };
}
