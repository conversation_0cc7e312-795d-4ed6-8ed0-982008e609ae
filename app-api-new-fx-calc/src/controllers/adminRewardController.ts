import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { CustomRequest } from "custom";
import { Response } from "express";
import { BadRequestError } from "../models/ApiErrors";
import RewardService from "../services/rewardService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import PaginationUtil from "../utils/paginationUtil";

export default class AdminRewardController {
  public static async createReward(req: CustomRequest, res: Response): Promise<Response> {
    const { referrerEmail, referralEmail, targetUserEmail, asset, considerationAmount } = req.body;

    const isDoubleReward = referrerEmail && referralEmail;
    if (isDoubleReward) {
      ParamsValidationUtil.isEmailParamValid("referrerEmail", referrerEmail);
      ParamsValidationUtil.isEmailParamValid("referralEmail", referralEmail);
    }

    ParamsValidationUtil.isAssetValid("asset", asset);
    ParamsValidationUtil.isEmailParamValid("targetUserEmail", targetUserEmail);
    ParamsValidationUtil.isNumericParamValid("considerationAmount", considerationAmount);

    await RewardService.createAdminReward(
      referrerEmail,
      referralEmail,
      targetUserEmail,
      asset,
      considerationAmount
    );

    return res.sendStatus(204);
  }

  public static async createRewardDeposits(req: CustomRequest, res: Response): Promise<Response> {
    await RewardService.createRewardDeposits();
    return res.sendStatus(204);
  }

  public static async createRewardOrders(req: CustomRequest, res: Response): Promise<Response> {
    await RewardService.createRewardOrders();
    return res.sendStatus(204);
  }

  public static async getReward(req: CustomRequest, res: Response): Promise<Response> {
    const rewardId = req.params.id;
    ParamsValidationUtil.isObjectIdParamValid("id", rewardId);

    const reward = await RewardService.getReward(rewardId);
    return res.status(200).json(reward);
  }

  public static async getRewards(req: CustomRequest, res: Response): Promise<Response> {
    const assetId = req.query.assetId as investmentUniverseConfig.AssetType;
    ParamsValidationUtil.isAssetValid("assetId", assetId, { isRequired: false });
    const orderSubmissionDay = ParamsValidationUtil.isDateParamValid(
      "orderSubmissionDay",
      req.query.orderSubmissionDay as string,
      {
        isRequired: false,
        isOnlyDate: true
      }
    );

    const pageConfig = PaginationUtil.getValidatedPageConfigFromPaginationProvided(
      req.query.pageSize as string,
      req.query.page as string
    );
    const populatePortfolio =
      ParamsValidationUtil.isBooleanParamValid("populatePortfolio", req.query.populatePortfolio as string, {
        isRequired: false
      }) ?? true;
    const targetUser = ParamsValidationUtil.isObjectIdParamValid("targetUser", req.query.targetUser, {
      isRequired: false
    });

    const sort = (req.query.sort as string) ?? "-createdAt";
    return res
      .status(200)
      .json(
        await RewardService.getRewards(
          { targetUser, assetId, orderSubmissionDay },
          pageConfig,
          { portfolio: populatePortfolio },
          sort
        )
      );
  }

  public static async syncPendingRewardDeposits(req: CustomRequest, res: Response): Promise<Response> {
    await RewardService.syncPendingRewardDeposits();
    return res.sendStatus(204);
  }

  public static async syncPendingRewardOrders(req: CustomRequest, res: Response): Promise<Response> {
    await RewardService.syncPendingRewards();
    return res.sendStatus(204);
  }

  public static async updateReward(req: CustomRequest, res: Response): Promise<Response> {
    const rewardId = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    const { depositId } = req.body;
    if (!depositId) {
      throw new BadRequestError("Deposit ID cannot be empty", "Invalid Request");
    }

    await RewardService.updateReward(rewardId, { depositId });
    return res.sendStatus(204);
  }
}
