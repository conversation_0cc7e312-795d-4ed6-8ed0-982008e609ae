import { CustomRequest } from "custom";
import { Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import UserService, { UpdateUserOptions } from "../services/userService";
import { KycStatusArray, KycStatusEnum, KycStatusType, UserTypeEnum } from "../models/User";
import { UsersFilter } from "filters";
import PaginationUtil from "../utils/paginationUtil";
import UserValidationUtil from "../utils/userValidationUtil";
import { CreateParticipantData, CreateUserData, CreateUserPartial, UserData } from "requestBody";
import logger from "../external-services/loggerService";
import { Account } from "../models/Account";
import KycOperationService from "../services/kycOperationService";
import { AddressDocument } from "../models/Address";
import StatementUtil from "../utils/statementUtil";
import CloudflareService, { BucketsEnum, ContentTypeEnum } from "../external-services/cloudflareService";
import { Readable } from "stream";

export default class AdminUserController {
  public static readonly getUsers = async (req: CustomRequest, res: Response) => {
    const filter: UsersFilter = {};

    const pageConfig = PaginationUtil.getValidatedPageConfigFromPaginationProvided(
      req.query.pageSize as string,
      req.query.page as string
    );
    const populatePortfolios =
      ParamsValidationUtil.isBooleanParamValid("populatePortfolios", req.query.populatePortfolios as string, {
        isRequired: false
      }) ?? false;

    const populateKycOperation =
      ParamsValidationUtil.isBooleanParamValid("populateKycOperation", req.query.populateKycOperation as string, {
        isRequired: false
      }) ?? false;

    const populateAccounts =
      ParamsValidationUtil.isBooleanParamValid("populateAccounts", req.query.populateAccounts as string, {
        isRequired: false
      }) ?? false;

    const potentiallyDuplicateOnly =
      ParamsValidationUtil.isBooleanParamValid(
        "potentiallyDuplicateOnly",
        req.query.potentiallyDuplicateOnly as string,
        {
          isRequired: false
        }
      ) ?? false;
    if (potentiallyDuplicateOnly) {
      filter.potentiallyDuplicateOnly = true;
    }

    if (req.query.role) {
      const roles = (Array.isArray(req.query.role) ? req.query.role : [req.query.role]) as string[];

      roles.forEach((status) =>
        ParamsValidationUtil.isStringParamFromAllowedValuesValid("role", status, UserTypeEnum)
      );
      filter.roles = roles as UserTypeEnum[];
    }
    filter.email = ParamsValidationUtil.isEmailParamValid("email", req.query.email as string, {
      isRequired: false
    });

    if (req.query.kycStatus) {
      filter.kycStatus = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "kycStatus",
        req.query.kycStatus,
        KycStatusArray,
        {
          isRequired: false
        }
      ) as KycStatusType;
    }

    filter.createdAfter = ParamsValidationUtil.isDateParamValid("createdAfter", req.query.createdAfter as string, {
      isRequired: false
    });
    filter.kycFailedAfter = ParamsValidationUtil.isDateParamValid(
      "kycFailedAfter",
      req.query.kycFailedAfter as string,
      {
        isRequired: false
      }
    );
    if (req.query.hasRequestedDeletion == "true") {
      filter.hasRequestedDeletion = true;
    } else if (req.query.hasRequestedDeletion == "false") {
      filter.hasRequestedDeletion = false;
    }
    if (req.query.hasAcceptedTerms == "true") {
      filter.hasAcceptedTerms = true;
    } else if (req.query.hasAcceptedTerms == "false") {
      filter.hasAcceptedTerms = false;
    }
    if (req.query.hasSubmittedRequiredInfo == "true") {
      filter.hasSubmittedRequiredInfo = true;
    } else if (req.query.hasSubmittedRequiredInfo == "false") {
      filter.hasSubmittedRequiredInfo = false;
    }
    const sort = req.query.sort as string;

    return res.status(200).json(
      await UserService.getUsers(
        filter,
        pageConfig,
        {
          portfolios: populatePortfolios,
          kycOperation: populateKycOperation,
          accounts: populateAccounts
        },
        sort
      )
    );
  };

  public static readonly getUser = async (req: CustomRequest, res: Response) => {
    const userId = req.params.id;
    ParamsValidationUtil.isObjectIdParamValid("id", userId);
    return res.status(200).json({ data: [await UserService.getUserDetailed(userId)] });
  };

  public static readonly getUserReferralsCount = async (req: CustomRequest, res: Response) => {
    const userEmail = req.query.userEmail as string;
    ParamsValidationUtil.isEmailParamValid("userEmail", userEmail);

    const referralsCount = await UserService.getValidUserReferralsCount(userEmail);
    return res.status(200).json({ referralsCount });
  };

  public static readonly updateUser = async (req: CustomRequest, res: Response) => {
    const userId = req.params.id;
    ParamsValidationUtil.isObjectIdParamValid("id", userId);
    const userUpdateData = UserValidationUtil.validateUserData(req.body as UserData);

    // Delete referredByEmail field when updating with falsy value from admin dashboard
    const options: UpdateUserOptions = { fieldsToDelete: { referredByEmail: !userUpdateData.referredByEmail } };

    return res.status(200).json(await UserService.updateUser(userId, userUpdateData, options));
  };

  public static readonly createOrUpdateUserByEmail = async (req: CustomRequest, res: Response) => {
    const email = ParamsValidationUtil.isEmailParamValid("email", req.params.email);
    const requestBody = req.body as CreateUserData & CreateParticipantData;
    const userData = UserValidationUtil.validateUserData(requestBody) as CreateUserPartial;

    const referral: Partial<CreateParticipantData> = { financeAdsSessionId: requestBody.financeAdsSessionId };
    if (requestBody.wlthd) {
      referral.wlthd = requestBody.wlthd;
    } else if (requestBody.grsf) {
      referral.grsf = requestBody.grsf;
    }

    return res.status(200).json({ data: [await UserService.createOrUpdateUser(email, userData, referral)] });
  };

  public static readonly createWkParties = async (req: CustomRequest, res: Response) => {
    await UserService.createAllWkParties();
    res.sendStatus(204);
  };

  public static readonly generateAccountStatement = async (req: CustomRequest, res: Response) => {
    const start = req.query.start ? new Date(req.query.start as string) : undefined;
    const end = req.query.end ? new Date(req.query.end as string) : undefined;
    const userId = req.params.id as string;

    const user = await UserService.getUser(userId, { addresses: true });

    const activity = await UserService.getAccountStatementActivity(user.id, start, end);
    const data = StatementUtil.generateAccountStatementPDF(user, user.addresses[0] as AddressDocument, activity, {
      start,
      end
    });

    const { fileUri } = await CloudflareService.Instance.uploadObject(
      BucketsEnum.ACCOUNT_STATEMENTS,
      StatementUtil.generateAccountStatementFilePath(user.id),
      Readable.from(data),
      {
        contentType: ContentTypeEnum.APPLICATION_PDF
      }
    );

    return res.status(200).json({ fileUri });
  };

  public static readonly removeDuplicateFlag = async (req: CustomRequest, res: Response) => {
    const userId = req.params.id as string;

    logger.info(`Removing duplicate flag for user ${userId}`, {
      module: "AdminUserController",
      method: "removeDuplicateFlag"
    });

    await UserService.updateUser(userId, {
      isPotentiallyDuplicateAccount: false,
      kycStatus: KycStatusEnum.PENDING
    });

    await Account.updateOne({ owner: userId }, { "providers.wealthkernel.status": "Pending" });

    logger.info(`Duplicate flag successfully removed for user ${userId}`, {
      module: "AdminUserController",
      method: "removeDuplicateFlag"
    });

    res.sendStatus(204);
  };

  public static readonly removeKycPassportFlag = async (req: CustomRequest, res: Response) => {
    const userId = req.params.id as string;

    logger.info(`Removing KYC provider passport flag for user ${userId}`, {
      module: "AdminUserController",
      method: "removeKycPassportFlag"
    });

    await UserService.updateUser(userId, {
      isPassportMatchingKycProvider: true
    });
    await UserService.setPassedKycStatusIfEligible(userId);

    logger.info(`KYC provider passport flag successfully removed for user ${userId}`, {
      module: "AdminUserController",
      method: "removeKycPassportFlag"
    });

    res.sendStatus(204);
  };

  public static readonly overrideKycDecision = async (req: CustomRequest, res: Response) => {
    const userId = req.params.id as string;

    await KycOperationService.overrideFailedKycDecision(userId);

    res.sendStatus(204);
  };
}
