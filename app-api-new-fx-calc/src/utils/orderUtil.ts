import { OrderActivityItemType } from "../services/orderService";
import { AggregatedAssetPriceDataPointType, AssetPriceDataPointType } from "tickers";
import Decimal from "decimal.js";
import { OrderInterface } from "../models/Order";
import { getAssetIdFromIsin } from "./investmentUniverseUtil";
import { OrdersPreviewType } from "previews";
import { InvestmentProductsDictType } from "investmentProducts";
import { currenciesConfig } from "@wealthyhood/shared-configs";

export class OrderUtil {
  public static formatOrdersPreview(
    orders: Partial<OrderInterface>[],
    options: {
      investmentProductsDict: InvestmentProductsDictType;
      userCurrency: currenciesConfig.MainCurrencyType;
      fees: number;
    }
  ): OrdersPreviewType {
    return Object.fromEntries(
      orders.map((order: OrderInterface) => {
        const assetPrice = options.investmentProductsDict[order.isin].currentTicker.getPrice(options.userCurrency);

        if (order.side === "Sell") {
          return [
            getAssetIdFromIsin(order.isin),
            {
              side: order.side.toLowerCase(),
              quantity: order.quantity,
              // For sells, we also estimate the money amount post-fees.
              money: Decimal.mul(order.quantity, assetPrice).sub(options?.fees).toNumber()
            }
          ];
        } else {
          // In pounds/euros
          const orderAmount = Decimal.div(order.consideration?.amount ?? 0, 100).toNumber();
          return [
            getAssetIdFromIsin(order.isin),
            {
              side: order.side.toLowerCase(),
              money: orderAmount,
              // estimated quantity
              // NOTE: for portfolioBuyPreview it will be wrong but we can ignore it
              quantity: Decimal.sub(orderAmount, options.fees).div(assetPrice).toDecimalPlaces(4).toNumber()
            }
          ];
        }
      })
    );
  }

  public static addOrderActivityToPriceDataPoints(
    data: AssetPriceDataPointType[],
    orderActivityItems: OrderActivityItemType[]
  ): AggregatedAssetPriceDataPointType[] {
    // 1. Quick exit if there are no order activity items
    if (!orderActivityItems.length) {
      return data;
    }

    // 2. Create a dictionary of order activity items based on the closest timestamp
    const orderActivityClosestTimestampDict = OrderUtil._mapOrderActivitiesToClosestDataPoints(
      data,
      orderActivityItems
    );

    // 3. Add the order activity data to the price data points
    return data.map((dp) =>
      OrderUtil._createAggregateDatapoint(dp, orderActivityClosestTimestampDict[dp.timestamp])
    );
  }

  private static _mapOrderActivitiesToClosestDataPoints(
    data: { timestamp: number; close: number }[],
    orderActivityItems: OrderActivityItemType[]
  ): Record<number, OrderActivityItemType[]> {
    return orderActivityItems.reduce(
      (orderActivityClosestTimestampDict, item) => {
        const closestDataPoint = data.reduce(
          (prev, curr) =>
            Math.abs(curr.timestamp - item.timestamp) < Math.abs(prev.timestamp - item.timestamp) ? curr : prev,
          data[0]
        );

        const key = closestDataPoint.timestamp;
        if (!orderActivityClosestTimestampDict[key]) {
          orderActivityClosestTimestampDict[key] = [item];
        } else {
          orderActivityClosestTimestampDict[key].push(item);
        }

        return orderActivityClosestTimestampDict;
      },
      {} as Record<number, OrderActivityItemType[]>
    );
  }

  private static _createAggregateDatapoint(
    dataPoint: { timestamp: number; close: number },
    orderActivity: OrderActivityItemType[]
  ): AggregatedAssetPriceDataPointType {
    if (orderActivity) {
      const { buyQuantity, sellQuantity } = orderActivity.reduce(
        (acc, { side, quantity }) => {
          if (side === "Buy") {
            acc.buyQuantity = acc.buyQuantity.plus(quantity);
          } else if (side === "Sell") {
            acc.sellQuantity = acc.sellQuantity.plus(quantity);
          }
          return acc;
        },
        { buyQuantity: new Decimal(0), sellQuantity: new Decimal(0) }
      );

      const quantity = buyQuantity.sub(sellQuantity);
      let type: "buy" | "sell" | "net";
      if (buyQuantity.gt(0) && sellQuantity.gt(0)) {
        type = "net";
      } else if (buyQuantity.gt(0)) {
        type = "buy";
      } else {
        type = "sell";
      }

      return {
        ...dataPoint,
        data: {
          quantity: quantity.toNumber(),
          type: type
        }
      };
    }
    return dataPoint;
  }
}
