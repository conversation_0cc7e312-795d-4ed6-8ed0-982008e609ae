import {
  currenciesConfig,
  indexesConfig,
  investmentUniverseConfig,
  publicInvestmentUniverseConfig
} from "@wealthyhood/shared-configs";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { PUBLIC_ASSET_CONFIG } = publicInvestmentUniverseConfig;
const { CURRENCY_SYMBOLS } = currenciesConfig;
const { INDEX_CONFIG_GLOBAL } = indexesConfig;

/**
 * @description The method returns the active assetId for a given ISIN (active or deprecated).
 */
export const getAssetIdFromIsin = (assetIsin: string): investmentUniverseConfig.AssetType => {
  const assetId = Object.entries(ASSET_CONFIG).find(
    ([, { isin }]) => isin === assetIsin
  )?.[0] as investmentUniverseConfig.AssetType;

  if (ASSET_CONFIG[assetId].deprecated) {
    return ASSET_CONFIG[assetId].deprecatedBy;
  } else {
    return assetId;
  }
};

/**
 * @description The method returns an array of ISINs (active & deprecated), given an asset id.
 * NOTE: the input assetId should always be the active assetId.
 */
export const getIsinActiveAndDeprecated = (assetId: investmentUniverseConfig.AssetType): string[] => {
  let activeAssetId = assetId;
  if (ASSET_CONFIG[assetId].deprecated) {
    activeAssetId = ASSET_CONFIG[assetId].deprecatedBy;
  }

  const activeIsin = ASSET_CONFIG[activeAssetId].isin;
  const deprecatedIsins = Object.values(ASSET_CONFIG)
    .filter(({ deprecatedBy }) => deprecatedBy === activeAssetId)
    .map(({ isin }) => isin);
  return [activeIsin, ...deprecatedIsins];
};

/**
 * @description The method returns an array of asset IDs (both from the public and app investment universe) for a given ticker symbol.
 * One such example is the ticker "GM".
 * @param ticker
 */
export const getAllAssetIdsFromTicker = (ticker: string): publicInvestmentUniverseConfig.PublicAssetType[] => {
  return Object.entries(PUBLIC_ASSET_CONFIG)
    .filter(([, config]) => config.formalTicker === ticker)
    .map(([assetId]) => assetId as publicInvestmentUniverseConfig.PublicAssetType);
};

export const getPublicAssetIdFromTickerAndExchange = (
  ticker: string,
  exchange: string
): publicInvestmentUniverseConfig.PublicAssetType => {
  return Object.entries(PUBLIC_ASSET_CONFIG).find(
    ([, { formalTicker, formalExchange }]) => ticker === formalTicker && exchange === formalExchange
  )?.[0] as publicInvestmentUniverseConfig.PublicAssetType;
};

export const getIndexIdFromTicker = (indexSymbol: string): indexesConfig.IndexType => {
  return Object.entries(INDEX_CONFIG_GLOBAL).find(
    ([, { symbol }]) => indexSymbol === symbol
  )?.[0] as indexesConfig.IndexType;
};

export const getAssetConfigFromIsin = (assetIsin: string): investmentUniverseConfig.AssetConfigType => {
  return Object.entries(ASSET_CONFIG).find(([, { isin }]) => isin === assetIsin)?.[1];
};

export const isAssetActive = (assetId: investmentUniverseConfig.AssetType): boolean => {
  return ASSET_CONFIG[assetId] && !ASSET_CONFIG[assetId].deprecated;
};

export const isPublicAssetActive = (assetId: publicInvestmentUniverseConfig.PublicAssetType): boolean => {
  return !!PUBLIC_ASSET_CONFIG[assetId];
};

/**
 * @returns base currency string formatted as '£GBP'
 * @param assetCommonId
 * @private
 */
export const getBaseCurrency = (assetCommonId: publicInvestmentUniverseConfig.PublicAssetType): string => {
  const baseCurrency = (PUBLIC_ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType)
    .baseCurrency;
  return `${CURRENCY_SYMBOLS[baseCurrency]} ${baseCurrency}`;
};
