import DateUtil from "../dateUtil";

describe("DateUtil", () => {
  describe("calculateNextUKWorkDay", () => {
    beforeAll(() => {
      DateUtil.UK_BANK_HOLIDAYS = [
        { title: "Bank Holiday Test 1", date: new Date("2022-07-06") },
        { title: "Bank Holiday Test 2", date: new Date("2022-07-11") },
        { title: "Bank Holiday Test 3", date: new Date("2022-07-12") }
      ];
    });

    it("should return 2022-07-05 00:00 for date 2022-07-04 (Monday) ", () => {
      const executionDate = DateUtil.calculateNextUKWorkDay(new Date("2022-07-04"));
      expect(executionDate).toEqual(new Date("2022-07-05T00:00:00")); // No weekend, No bank holidays
    });

    it("should return 2022-07-07 00:00 for date 2022-07-05 (Tuesday) ", () => {
      const executionDate = DateUtil.calculateNextUKWorkDay(new Date("2022-07-05"));
      expect(executionDate).toEqual(new Date("2022-07-07T00:00:00")); // One bank holiday
    });

    it("should return 2022-07-13 00:00 for date 2022-07-08 (Friday) ", () => {
      const executionDate = DateUtil.calculateNextUKWorkDay(new Date("2022-07-08"));
      expect(executionDate).toEqual(new Date("2022-07-13T00:00:00")); // Weekend + 2 consecutive bank holidays
    });

    it("should return 2022-07-13 00:00 for date 2022-07-08 (Friday) ", () => {
      const executionDate = DateUtil.calculateNextUKWorkDay(new Date("2022-07-08"));
      expect(executionDate).toEqual(new Date("2022-07-13T00:00:00")); // Weekend + 2 consecutive bank holidays
    });

    it("should return 2022-07-13 00:00 for 2022-07-07 (Thursday) for two consecutive dates (rebalancing scenario)", () => {
      const executionDate = DateUtil.calculateNextUKWorkDay(
        DateUtil.calculateNextUKWorkDay(new Date("2022-07-07"))
      );
      expect(executionDate).toEqual(new Date("2022-07-13T00:00:00")); // Next day + weekend + 2 consecutive bank holidays
    });
  });

  describe("calculateNextUSWorkDay", () => {
    beforeAll(() => {
      DateUtil.US_BANK_HOLIDAYS = [
        { title: "Bank Holiday Test 1", date: new Date("2022-07-06") },
        { title: "Bank Holiday Test 2", date: new Date("2022-07-11") },
        { title: "Bank Holiday Test 3", date: new Date("2022-07-12") }
      ];
    });

    it("should return 2022-07-05 00:00 for date 2022-07-04 (Monday) ", () => {
      const executionDate = DateUtil.calculateNextUSWorkDay(new Date("2022-07-04"));
      expect(executionDate).toEqual(new Date("2022-07-05T00:00:00")); // No weekend, No bank holidays
    });

    it("should return 2022-07-07 00:00 for date 2022-07-05 (Tuesday) ", () => {
      const executionDate = DateUtil.calculateNextUSWorkDay(new Date("2022-07-05"));
      expect(executionDate).toEqual(new Date("2022-07-07T00:00:00")); // One bank holiday
    });

    it("should return 2022-07-13 00:00 for date 2022-07-08 (Friday) ", () => {
      const executionDate = DateUtil.calculateNextUSWorkDay(new Date("2022-07-08"));
      expect(executionDate).toEqual(new Date("2022-07-13T00:00:00")); // Weekend + 2 consecutive bank holidays
    });

    it("should return 2022-07-13 00:00 for date 2022-07-08 (Friday) ", () => {
      const executionDate = DateUtil.calculateNextUSWorkDay(new Date("2022-07-08"));
      expect(executionDate).toEqual(new Date("2022-07-13T00:00:00")); // Weekend + 2 consecutive bank holidays
    });
  });

  describe("calculatePreviousWorkDay", () => {
    beforeAll(() => {
      DateUtil.UK_BANK_HOLIDAYS = [
        { title: "Bank Holiday Test 1", date: new Date("2022-07-06") },
        { title: "Bank Holiday Test 2", date: new Date("2022-07-11") },
        { title: "Bank Holiday Test 3", date: new Date("2022-07-12") }
      ];
    });

    it("should return 2022-07-05 00:00 for date 2022-07-07 (Thirsday) ", () => {
      const executionDate = DateUtil.calculatePreviousWorkDay(new Date("2022-07-07"));
      expect(executionDate).toEqual(new Date("2022-07-05T00:00:00")); // No weekend, No bank holidays
    });

    it("should return 2022-07-01 00:00 for date 2022-07-04 (Monday) ", () => {
      const executionDate = DateUtil.calculatePreviousWorkDay(new Date("2022-07-04"));
      expect(executionDate).toEqual(new Date("2022-07-01T00:00:00")); // One bank holiday
    });

    it("should return 2022-07-08 00:00 for date 2022-07-13 (Wednesday) ", () => {
      const executionDate = DateUtil.calculatePreviousWorkDay(new Date("2022-07-13"));
      expect(executionDate).toEqual(new Date("2022-07-08T00:00:00")); // Weekend + 2 consecutive bank holidays
    });

    it("should return 2022-07-07 00:00 for 2022-07-13 (Wednesday) for two consecutive dates", () => {
      const executionDate = DateUtil.calculatePreviousWorkDay(
        DateUtil.calculatePreviousWorkDay(new Date("2022-07-13"))
      );
      expect(executionDate).toEqual(new Date("2022-07-07T00:00:00")); // Next day + weekend + 2 consecutive bank holidays
    });
  });

  describe("getDateNWorkDaysAgo", () => {
    it("should return 2022-06-29 for date 2022-07-07 (Thursday) and N = 5", () => {
      const executionDate = DateUtil.getDateNWorkDaysAgo(new Date("2022-07-07"), 5);
      expect(executionDate).toEqual(new Date("2022-06-29T00:00:00"));
    });
  });

  describe("getDateOfHoursAgo", () => {
    it("should return 2022-08-05T09:00:00Z for 2022-08-05T10:00:00Z", () => {
      const FRIDAY = new Date("2022-08-05T10:00:00Z");
      Date.now = jest.fn(() => FRIDAY.valueOf());

      const executionDate = DateUtil.getDateOfHoursAgo(FRIDAY, 1);
      expect(executionDate).toEqual(new Date("2022-08-05T09:00:00Z"));
    });

    it("should return 2022-08-04T23:00:00 for 2022-08-05T01:00:00Z", () => {
      const FRIDAY = new Date("2022-08-05T01:00:00Z");
      Date.now = jest.fn(() => FRIDAY.valueOf());

      const executionDate = DateUtil.getDateOfHoursAgo(FRIDAY, 2);
      expect(executionDate).toEqual(new Date("2022-08-04T23:00:00Z"));
    });

    it("should return 2022-12-31T23:00:00Z for 2023-01-01T01:00:00Z", () => {
      const FRIDAY = new Date("2023-01-01T01:00:00Z");
      Date.now = jest.fn(() => FRIDAY.valueOf());

      const executionDate = DateUtil.getDateOfHoursAgo(FRIDAY, 2);
      expect(executionDate).toEqual(new Date("2022-12-31T23:00:00Z"));
    });
  });

  describe("getYearAndMonthAndDay", () => {
    it("should return 2022-08-05 for 2022-08-05T10:00:00Z", () => {
      const FRIDAY = new Date("2022-08-05T10:00:00Z");
      Date.now = jest.fn(() => FRIDAY.valueOf());
      const newDateFormat = DateUtil.getYearAndMonthAndDay(new Date(Date.now()));
      expect(newDateFormat).toEqual("2022-08-05");
    });
  });

  describe("getDateOfYearsAgo", () => {
    it("should return 2012-08-05T10:00:00Z for 2022-08-05T10:00:00Z", () => {
      const FRIDAY = new Date("2022-08-05T10:00:00Z");
      Date.now = jest.fn(() => FRIDAY.valueOf());
      const executionDate = DateUtil.getDateOfYearsAgo(FRIDAY, 10);
      expect(executionDate).toEqual(new Date("2012-08-05T10:00:00Z"));
    });

    it("should return 2020-01-01T01:00:00Z for 2023-01-01T01:00:00Z", () => {
      const FRIDAY = new Date("2023-01-01T01:00:00Z");
      Date.now = jest.fn(() => FRIDAY.valueOf());

      const executionDate = DateUtil.getDateOfYearsAgo(FRIDAY, 3);
      expect(executionDate).toEqual(new Date("2020-01-01T01:00:00.00Z"));
    });
  });

  describe("dateDiffInYears", () => {
    it("should return proper years diff between known dates", () => {
      expect(DateUtil.dateDiffInYears(new Date("1994-11-05T12:21:00"), new Date("2012-11-05T18:45:00"))).toBe(18);
    });
  });

  describe("getAllDatesBetweenTwoDates", () => {
    it("should return correct dates at the end of the month", () => {
      expect(
        DateUtil.getAllDatesBetweenTwoDates(new Date("2023-04-28T12:00:00"), new Date("2023-05-02T12:00:00"))
      ).toEqual([
        new Date("2023-04-28T12:00:00.000Z"),
        new Date("2023-04-29T12:00:00.000Z"),
        new Date("2023-04-30T12:00:00.000Z"),
        new Date("2023-05-01T12:00:00.000Z"),
        new Date("2023-05-02T12:00:00.000Z")
      ]);
    });
  });

  describe("isFirstMondayOfTheMonthOrLater", () => {
    it("should return true for 2023-04-07 (Friday)", () => {
      expect(DateUtil.isFirstMondayOfTheMonthOrLater(new Date("2023-04-07T12:00:00"))).toEqual(true);
    });

    it("should return true for 2023-04-03 (Monday)", () => {
      expect(DateUtil.isFirstMondayOfTheMonthOrLater(new Date("2023-04-03T12:00:00"))).toEqual(true);
    });

    it("should return false for 2023-04-02 (Sunday)", () => {
      expect(DateUtil.isFirstMondayOfTheMonthOrLater(new Date("2023-04-02T12:00:00"))).toEqual(false);
    });
  });

  describe("formatDateToYYYYMONDD", () => {
    it("should return with correct yyyy-MM-dd format", () => {
      expect(DateUtil.formatDateToYYYYMMDD(new Date("2024-01-31T12:00:00"))).toEqual("2024-01-31");
    });
  });

  describe("mapDateToClosestTimeMark", () => {
    describe("when unit is 'minutes'", () => {
      it("should map the minutes of the dates to the 10 minute interval", () => {
        const dates = [
          new Date("2024-09-01T03:57:00"),
          new Date("2024-09-01T04:02:00"),
          new Date("2024-09-01T04:08:00"),
          new Date("2024-09-01T04:09:00"),
          new Date("2024-09-01T05:18:00"),
          new Date("2024-09-01T05:35:00"),
          new Date("2024-09-01T05:47:00")
        ];

        const mappedDates = dates
          .map((date) => DateUtil.mapDateToClosestTimeMark(date, 10, "minutes"))
          .map((date) => date.getTime());

        expect(mappedDates).toEqual(
          expect.arrayContaining(
            [
              new Date("2024-09-01T04:00:00"),
              new Date("2024-09-01T04:00:00"),
              new Date("2024-09-01T04:10:00"),
              new Date("2024-09-01T04:10:00"),
              new Date("2024-09-01T05:20:00"),
              new Date("2024-09-01T05:40:00"),
              new Date("2024-09-01T05:50:00")
            ].map((date) => date.getTime())
          )
        );
      });
    });

    describe("when unit is 'weeks'", () => {
      it("should map each day of the week to the closest Friday", () => {
        const testCases = [
          // Sunday => previous Friday (-2 days)
          { input: new Date("2024-03-17T15:30:00"), expected: new Date("2024-03-15T00:00:00") },
          // Monday => previous Friday (-3 days)
          { input: new Date("2024-03-18T10:20:00"), expected: new Date("2024-03-15T00:00:00") },
          // Tuesday => next Friday (+3 days)
          { input: new Date("2024-03-19T09:15:00"), expected: new Date("2024-03-22T00:00:00") },
          // Wednesday => next Friday (+2 days)
          { input: new Date("2024-03-20T14:45:00"), expected: new Date("2024-03-22T00:00:00") },
          // Thursday => next Friday (+1 day)
          { input: new Date("2024-03-21T16:30:00"), expected: new Date("2024-03-22T00:00:00") },
          // Friday => same Friday (0 days)
          { input: new Date("2024-03-22T11:11:00"), expected: new Date("2024-03-22T00:00:00") },
          // Saturday => previous Friday (-1 day)
          { input: new Date("2024-03-23T23:59:59"), expected: new Date("2024-03-22T00:00:00") }
        ];

        testCases.forEach(({ input, expected }) => {
          const result = DateUtil.mapDateToClosestTimeMark(input, 1, "weeks");
          expect(result.getTime()).toEqual(expected.getTime());
        });
      });

      it("should reset time to start of day (00:00:00) for weekly intervals", () => {
        const input = new Date("2024-03-22T23:59:59"); // Friday with late time
        const result = DateUtil.mapDateToClosestTimeMark(input, 1, "weeks");

        expect(result.getHours()).toBe(0);
        expect(result.getMinutes()).toBe(0);
        expect(result.getSeconds()).toBe(0);
        expect(result.getMilliseconds()).toBe(0);
      });
    });
  });
});
