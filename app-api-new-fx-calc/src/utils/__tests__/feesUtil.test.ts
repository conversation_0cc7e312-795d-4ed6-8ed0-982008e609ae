import { calculateBrokerFxFee } from "../feesUtil";

describe("feesUtil", () => {
  describe("calculateBrokerFxFee", () => {
    it("should calculate correct fee amount for different plans", () => {
      // 10000 cents = £100
      expect(calculateBrokerFxFee({ considerationAmount: 10000, plan: "paid_mid", brokerFxRate: 1.2 })).toBe(25); // 0.25% of £100 = £0.25 (25 cents)
      expect(calculateBrokerFxFee({ considerationAmount: 10000, plan: "paid_low", brokerFxRate: 1.2 })).toBe(30); // 0.30% of £100 = £0.30 (30 cents)
      expect(calculateBrokerFxFee({ considerationAmount: 10000, plan: "free", brokerFxRate: 1.2 })).toBe(40); // 0.40% of £100 = £0.40 (40 cents)
    });

    it("should handle zero amount", () => {
      expect(calculateBrokerFxFee({ considerationAmount: 0, plan: "paid_mid", brokerFxRate: 1.2 })).toBe(0);
    });

    it("should handle fractional amounts", () => {
      expect(calculateBrokerFxFee({ considerationAmount: 1234, plan: "paid_mid", brokerFxRate: 1.2 })).toBe(3.085);
    });

    it("should return 0 when broker FX rate is 1", () => {
      expect(calculateBrokerFxFee({ considerationAmount: 10000, plan: "paid_mid", brokerFxRate: 1 })).toBe(0);
      expect(calculateBrokerFxFee({ considerationAmount: 10000, plan: "paid_low", brokerFxRate: 1 })).toBe(0);
      expect(calculateBrokerFxFee({ considerationAmount: 10000, plan: "free", brokerFxRate: 1 })).toBe(0);
    });

    it("should calculate fee when broker FX rate is not 1", () => {
      expect(calculateBrokerFxFee({ considerationAmount: 10000, plan: "paid_mid", brokerFxRate: 1.5 })).toBe(25);
      expect(calculateBrokerFxFee({ considerationAmount: 10000, plan: "paid_mid", brokerFxRate: 0.8 })).toBe(25);
    });
  });
});
