import PortfolioUtil from "../portfolioUtil";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildHoldingDTO, buildInvestmentProduct } from "../../tests/utils/generateModels";

describe("PortfolioUtil", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("PortfolioUtil"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("filterSmallPercentageHoldingsDistributingAllocationForBuying", () => {
    describe("when given a portfolio with a single asset", () => {
      beforeAll(async () => {
        await buildInvestmentProduct(true, {
          assetId: "equities_uk",
          price: 1
        });
      });

      it("should return an adjusted distribution with that asset at 100%", async () => {
        const result = await PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForBuying(
          "GBP",
          {
            equities_uk: 100
          },
          100
        );

        expect(result).toEqual({
          equities_uk: 100
        });
      });
    });

    describe("when given a portfolio with a two assets, both with valid percentages", () => {
      beforeAll(async () => {
        await buildInvestmentProduct(true, {
          assetId: "equities_uk",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 1
        });
      });

      it("should return an adjusted distribution with that asset at 100%", async () => {
        const result = await PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForBuying(
          "GBP",
          {
            equities_uk: 50,
            equities_us: 50
          },
          100
        );

        expect(result).toEqual({
          equities_uk: 50,
          equities_us: 50
        });
      });
    });

    describe("when given a portfolio with a two assets, and one is below the amount limit", () => {
      beforeAll(async () => {
        await buildInvestmentProduct(true, {
          assetId: "equities_uk",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 1
        });
      });

      it("should return an adjusted distribution with that asset at 100%", async () => {
        const result = await PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForBuying(
          "GBP",
          {
            equities_uk: 99,
            equities_us: 1
          },
          10
        );

        expect(result).toEqual({
          equities_uk: 100
        });
      });
    });

    describe("when given a portfolio with multiple assets, and multiple are below the amount limit", () => {
      beforeAll(async () => {
        await buildInvestmentProduct(true, {
          assetId: "equities_uk",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "equities_china",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "equities_jp",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "corporate_bonds_uk",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "corporate_bonds_us",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "government_bonds_us",
          price: 1
        });
      });

      it("should return an adjusted distribution with that asset at 100%", async () => {
        const result = await PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForBuying(
          "GBP",
          {
            equities_us: 70,
            equities_china: 14,
            equities_uk: 5,
            corporate_bonds_us: 5,
            equities_jp: 4,
            corporate_bonds_uk: 1,
            government_bonds_us: 1
          },
          10
        );

        expect(result).toEqual({
          equities_us: 70,
          equities_china: 14,
          equities_uk: 5,
          corporate_bonds_us: 5,
          equities_jp: 4,
          corporate_bonds_uk: 2
        });
      });
    });

    describe("when given a portfolio with multiple assets, multiple are below the amount limit and correction is also needed", () => {
      beforeAll(async () => {
        await buildInvestmentProduct(true, {
          assetId: "equities_uk",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "equities_china",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "equities_jp",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 1
        });
        await buildInvestmentProduct(true, {
          assetId: "corporate_bonds_uk",
          price: 1
        });
      });

      it("should return an adjusted distribution with that asset at 100%", async () => {
        const result = await PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForBuying(
          "GBP",
          {
            equities_us: 79,
            equities_china: 14,
            equities_uk: 4,
            equities_jp: 1.5,
            corporate_bonds_uk: 1.5
          },
          10
        );

        expect(result).toEqual({
          equities_us: 79.81,
          equities_china: 14.14,
          equities_uk: 4.04,
          equities_jp: 2
        });
      });
    });
  });

  describe("filterSmallPercentageHoldingsDistributingAllocationForSelling", () => {
    describe("when given a portfolio with a single asset", () => {
      it("should return a single order for the full amount", async () => {
        const holdings = await buildHoldingDTO(true, "equities_uk", 1, {
          price: 1
        });

        const result = PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForSelling(
          "GBP",
          [holdings],
          0.02
        );

        expect(result).toEqual({
          equities_uk: 0.02
        });
      });
    });

    describe("when given a portfolio with a two assets with equal percentages that both result to orders below limit", () => {
      it("should return no orders", async () => {
        const holdings = await Promise.all([
          buildHoldingDTO(true, "equities_uk", 1, {
            price: 1
          }),
          buildHoldingDTO(true, "equities_us", 1, {
            price: 1
          })
        ]);

        const result = PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForSelling(
          "GBP",
          holdings,
          0.02
        );

        expect(result).toEqual({
          equities_uk: 0.02
        });
      });
    });

    describe("when given a portfolio with a two assets and one is below the amount limit", () => {
      it("should return a single order", async () => {
        const holdings = await Promise.all([
          buildHoldingDTO(true, "equities_uk", 1, {
            price: 4 // 80% of the portfolio's holdings -> £0.04 should be sold
          }),
          buildHoldingDTO(true, "equities_us", 1, {
            price: 1 // 20% of the portfolio's holdings -> £0.01 which is under the limit
          })
        ]);

        const result = PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForSelling(
          "GBP",
          holdings,
          0.05
        );

        expect(result).toEqual({
          equities_uk: 0.01 // when multiplied with asset's price -> £0.04
        });
      });
    });

    describe("when given a portfolio with multiple assets, and all are below the amount limit", () => {
      it("should return a single order taken from the biggest holding of the user", async () => {
        const holdings = await Promise.all([
          buildHoldingDTO(true, "equities_uk", 2, {
            price: 500 // Initial order: £0.10
          }),
          buildHoldingDTO(true, "equities_us", 1, {
            price: 500 // Initial order: £0.05
          }),
          buildHoldingDTO(true, "equities_china", 0.0001, {
            price: 100 // Initial order: £0.01, below limit!
          }),
          buildHoldingDTO(true, "equities_eu", 0.0001, {
            price: 100 // Initial order: £0.01, below limit!
          }),
          buildHoldingDTO(true, "equities_jp", 0.0001, {
            price: 100 // Initial order: £0.01, below limit!
          })
        ]);

        const result = PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForSelling(
          "GBP",
          holdings,
          0.02
        );

        expect(result).toEqual({
          equities_uk: 0.0001
        });
      });
    });

    describe("when given a portfolio with multiple assets, and none are below the amount limit", () => {
      it("should return a single order taken from the biggest holding of the user", async () => {
        const holdings = await Promise.all([
          buildHoldingDTO(true, "equities_uk", 1, {
            price: 100 // Initial order: £100
          }),
          buildHoldingDTO(true, "equities_us", 1, {
            price: 100 // Initial order: £100
          }),
          buildHoldingDTO(true, "equities_china", 1, {
            price: 100 // Initial order: £100
          })
        ]);

        const result = PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForSelling(
          "GBP",
          holdings,
          300
        );

        expect(result).toEqual({
          equities_uk: 1,
          equities_us: 1,
          equities_china: 1
        });
      });
    });

    describe("when given a portfolio with multiple assets, and one is below the amount limit", () => {
      it("should return a single order taken from the biggest holding of the user", async () => {
        const holdings = await Promise.all([
          buildHoldingDTO(true, "equities_uk", 1, {
            price: 100 // Initial order: £100
          }),
          buildHoldingDTO(true, "equities_us", 1, {
            price: 100 // Initial order: £100
          }),
          buildHoldingDTO(true, "equities_china", 0.0001, {
            price: 100 // Initial order: £0.01 -> below the limit!
          })
        ]);

        const result = PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForSelling(
          "GBP",
          holdings,
          300
        );

        expect(result).toEqual({
          equities_uk: 1,
          equities_us: 1
        });
      });
    });

    describe("when given a portfolio with multiple assets, and multiple (but not all) are below the amount limit", () => {
      it("should return a single order taken from the biggest holding of the user", async () => {
        const holdings = await Promise.all([
          buildHoldingDTO(true, "equities_uk", 1, {
            price: 100 // Initial order: £200
          }),
          buildHoldingDTO(true, "equities_us", 1, {
            price: 100 // Initial order: £100
          }),
          buildHoldingDTO(true, "equities_china", 0.0001, {
            price: 100 // Initial order: £0.01 -> below the limit!
          }),
          buildHoldingDTO(true, "equities_jp", 0.0001, {
            price: 100 // Initial order: £0.01 -> below the limit!
          }),
          buildHoldingDTO(true, "equities_eu", 0.0001, {
            price: 100 // Initial order: £0.01 -> below the limit!
          })
        ]);

        const result = PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForSelling(
          "GBP",
          holdings,
          300
        );

        expect(result).toEqual({
          equities_uk: 1,
          equities_us: 1
        });
      });
    });
  });
});
