import * as TickerUtil from "../tickerUtil";

describe("TickerUtil", () => {
  describe("sampleTickers", () => {
    it("should return empty array for empty array input", () => {
      const tickers = [];
      const sampledTickers = TickerUtil.sampleTickers(tickers, 10, "minutes");
      expect(sampledTickers).toEqual([]);
    });

    describe("when interval is 'minutes'", () => {
      it("should sample tickers to have 10 min freq and map to the closest 10 min mark", () => {
        const tickers = [
          { timestamp: new Date("2024-08-01T04:02:00").getTime(), close: 9 },
          { timestamp: new Date("2024-09-01T03:57:00").getTime(), close: 8 },
          { timestamp: new Date("2024-09-01T04:02:00").getTime(), close: 10 },
          { timestamp: new Date("2024-09-01T04:08:00").getTime(), close: 20 },
          { timestamp: new Date("2024-09-01T04:09:00").getTime(), close: 30 },
          { timestamp: new Date("2024-09-01T05:18:00").getTime(), close: 40 },
          { timestamp: new Date("2024-09-01T05:35:00").getTime(), close: 5 },
          { timestamp: new Date("2024-09-01T05:47:00").getTime(), close: 1 }
        ];

        const sampledTickers = TickerUtil.sampleTickers(tickers, 10, "minutes");
        expect(sampledTickers).toEqual([
          { timestamp: new Date("2024-08-01T04:00:00").getTime(), close: 9 }, // 04:02 => 04:00
          { timestamp: new Date("2024-09-01T04:00:00").getTime(), close: 10 }, // 04:02 => 04:00
          { timestamp: new Date("2024-09-01T04:10:00").getTime(), close: 30 }, // 04:09 => 04:10
          { timestamp: new Date("2024-09-01T05:20:00").getTime(), close: 40 }, // 05:18 => 05:20
          { timestamp: new Date("2024-09-01T05:40:00").getTime(), close: 5 }, // 05:35 => 05:40
          { timestamp: new Date("2024-09-01T05:50:00").getTime(), close: 1 } // 05:47 => 05:50
        ]);
      });

      it("should sample tickers to have 30 min freq and map to the closest 30 min mark", () => {
        const tickers = [
          { timestamp: new Date("2024-09-01T03:42:00").getTime(), close: 100 }, // 12 min past 3:30
          { timestamp: new Date("2024-09-01T03:57:00").getTime(), close: 110 }, // 3 min to 4:00
          { timestamp: new Date("2024-09-01T04:13:00").getTime(), close: 120 }, // 13 min past 4:00
          { timestamp: new Date("2024-09-01T04:28:00").getTime(), close: 130 }, // 2 min to 4:30
          { timestamp: new Date("2024-09-01T04:44:00").getTime(), close: 140 }, // 14 min past 4:30
          { timestamp: new Date("2024-09-01T05:18:00").getTime(), close: 150 }, // 12 min to 5:30
          { timestamp: new Date("2024-09-01T05:35:00").getTime(), close: 160 } // 5 min past 5:30
        ];

        const sampledTickers = TickerUtil.sampleTickers(tickers, 30, "minutes");
        expect(sampledTickers).toEqual([
          { timestamp: new Date("2024-09-01T03:30:00").getTime(), close: 100 }, // 03:42 closest to 03:30
          { timestamp: new Date("2024-09-01T04:00:00").getTime(), close: 110 }, // 03:57 closest to 04:00
          { timestamp: new Date("2024-09-01T04:30:00").getTime(), close: 130 }, // 04:28 closest to 04:30
          { timestamp: new Date("2024-09-01T05:30:00").getTime(), close: 160 } // 05:35 closest to 05:30
        ]);
      });
    });

    describe("when interval is 'weeks'", () => {
      it("should sample tickers to map to the closest Friday", () => {
        const tickers = [
          // Previous week
          { timestamp: new Date("2024-03-10T14:30:00").getTime(), close: 50 }, // Sunday -> Fri Mar 8 (38.5h from target)
          { timestamp: new Date("2024-03-11T10:20:00").getTime(), close: 51 }, // Monday -> Fri Mar 8 (58.3h from target)
          // Current week
          { timestamp: new Date("2024-03-12T15:45:00").getTime(), close: 52 }, // Tuesday -> Fri Mar 15
          { timestamp: new Date("2024-03-13T09:30:00").getTime(), close: 53 }, // Wednesday -> Fri Mar 15
          { timestamp: new Date("2024-03-14T16:20:00").getTime(), close: 54 }, // Thursday -> Fri Mar 15
          { timestamp: new Date("2024-03-15T02:00:00").getTime(), close: 55 }, // Friday early morning -> same day
          { timestamp: new Date("2024-03-16T12:30:00").getTime(), close: 56 } // Saturday -> Fri Mar 15
        ];

        const sampledTickers = TickerUtil.sampleTickers(tickers, 1, "weeks");
        expect(sampledTickers).toEqual([
          { timestamp: new Date("2024-03-08T00:00:00").getTime(), close: 50 }, // Sunday is closer to Fri Mar 8 midnight
          { timestamp: new Date("2024-03-15T00:00:00").getTime(), close: 55 } // Early Friday morning is closest to target
        ]);
      });

      it("should handle multiple tickers on same week mapping to same Friday", () => {
        const tickers = [
          // All these map to Friday March 15
          { timestamp: new Date("2024-03-12T10:00:00").getTime(), close: 100 }, // Tuesday -> next Friday
          { timestamp: new Date("2024-03-13T11:00:00").getTime(), close: 110 }, // Wednesday -> next Friday
          { timestamp: new Date("2024-03-14T12:00:00").getTime(), close: 120 }, // Thursday -> next Friday
          { timestamp: new Date("2024-03-15T01:00:00").getTime(), close: 130 }, // Friday very early morning
          { timestamp: new Date("2024-03-16T14:00:00").getTime(), close: 140 } // Saturday -> previous Friday
        ];

        const sampledTickers = TickerUtil.sampleTickers(tickers, 1, "weeks");
        expect(sampledTickers).toEqual([
          { timestamp: new Date("2024-03-15T00:00:00").getTime(), close: 130 } // Early Friday morning is closest to target midnight
        ]);
      });
    });
  });
});
