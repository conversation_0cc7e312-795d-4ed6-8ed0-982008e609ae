import mongoose from "mongoose";
import validator from "validator";
import Decimal from "decimal.js";
import {
  investmentUniverseConfig,
  publicInvestmentUniverseConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import { BadRequestError } from "../models/ApiErrors";
import * as InvestmentUniverseUtil from "../utils/investmentUniverseUtil";

export type ParamsValidationOptionsType = {
  isRequired?: boolean;
  responseMessage?: string;
};
export default class ParamsValidationUtil {
  /**
   * @description Checks if parameter's value exists and validates against emum's values. If validation fails it sets the response (status, data) properly.
   * @param param parameter's name
   * @param value parameter's value
   * @param targetEnum Enum or array to be used for validation
   * @param options (optional) to override default behavior
   * @returns boolean
   */
  public static isStringParamFromAllowedValuesValid(
    param: string,
    value: any,
    targetEnum: any,
    options: ParamsValidationOptionsType = {}
  ): string {
    const optionsToUse: ParamsValidationOptionsType = {
      isRequired: true,
      ...options
    };
    const validVals = Object.values(targetEnum).map((val) => val.toString());

    if (optionsToUse.isRequired && !value) {
      throw new BadRequestError(
        `Param '${param}' is required, must be one of [${validVals}]`,
        "Invalid parameter"
      );
    }
    if (value != null && !validVals.includes(value)) {
      throw new BadRequestError(
        optionsToUse.responseMessage ??
          `Param '${param}' has invalid value '${value}', must be one of [${validVals}]`,
        "Invalid parameter"
      );
    }

    return value;
  }

  /**
   * @description Checks if parameter's value exists and is a valid mongoose object id. If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns boolean
   */
  public static isObjectIdParamValid(
    param: string,
    value: any,
    options: ParamsValidationOptionsType = {}
  ): string {
    const optionsToUse = {
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && !value) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }
    if (value != null && !mongoose.isValidObjectId(value)) {
      throw new BadRequestError(
        optionsToUse.responseMessage ?? `Invalid value for '${param}'`,
        "Invalid parameter"
      );
    }
    return value;
  }

  /**
   * @description Checks if parameter's value exists and is numeric. If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns boolean
   */
  public static isNumericParamValid(param: string, value: any, options: ParamsValidationOptionsType = {}): number {
    const optionsToUse = {
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && !value && value !== 0) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }
    if (value != null && !validator.isNumeric(value?.toString())) {
      throw new BadRequestError(
        optionsToUse.responseMessage ?? `Invalid value for param '${param}' , should be numeric`,
        "Invalid parameter"
      );
    }

    if (value) {
      return new Decimal(value).toNumber();
    }
  }

  /**
   * @description Checks if parameter's value exists and is array. If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns array
   */
  public static isArrayParamValid(param: string, value: any[], options: ParamsValidationOptionsType = {}): any[] {
    const optionsToUse = {
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && !value) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }
    if (value != null && !Array.isArray(value)) {
      throw new BadRequestError(
        optionsToUse.responseMessage ?? `Invalid value for param '${param}' , should be array`,
        "Invalid parameter"
      );
    }

    return value;
  }

  /**
   * @description Checks if a query parameter's value exists and is array (comma-separated list of strings). If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns array
   */
  public static isArrayQueryParamValid(
    param: string,
    value: string,
    options: ParamsValidationOptionsType = {}
  ): any[] {
    const optionsToUse = {
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && !value) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }

    return value.split(",");
  }

  /**
   * @description Checks if parameter's value exists and is object. If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns array
   */
  public static isObjectParamValid(param: string, value: any, options: ParamsValidationOptionsType = {}): any {
    const optionsToUse = {
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && !value) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }
    if (value != null && !(value === Object(value))) {
      throw new BadRequestError(
        optionsToUse.responseMessage ?? `Invalid value for param '${param}' , should be object`,
        "Invalid parameter"
      );
    }

    return value;
  }

  /**
   * @description Checks if parameter's value exists and is boolean. If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns boolean
   */
  public static isBooleanParamValid(
    param: string,
    value: boolean | string,
    options: ParamsValidationOptionsType = {}
  ): boolean {
    const optionsToUse = {
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && value == null) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }
    if (value != null && !validator.isBoolean(value?.toString())) {
      throw new BadRequestError(
        optionsToUse.responseMessage ?? `Invalid value for param '${param}' , should be boolean`,
        "Invalid parameter"
      );
    }
    return value != null ? value?.toString() == "true" : null;
  }

  /**
   * @description Checks if parameter's value exists and is date with format YYYY-MM-DDTHH:MM:SS.
   * If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns boolean
   */
  public static isDateParamValid(
    param: string,
    value: string,
    options: ParamsValidationOptionsType & { isOnlyDate?: boolean } = {}
  ): Date {
    const optionsToUse = {
      isOnlyDate: false,
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && value == null) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }
    if (value != null) {
      let errorMessage = `Invalid value for param '${param}' , should be date with format YYYY-MM-DD`;
      if (!options.isOnlyDate) {
        errorMessage += " or YYYY-MM-DDTHH:MM:SS";
      }

      if (options.isOnlyDate && value.length != 10) {
        throw new BadRequestError(optionsToUse.responseMessage ?? errorMessage, "Invalid parameter");
      }

      const date = new Date(value);
      if (isNaN(date.getDay())) {
        throw new BadRequestError(optionsToUse.responseMessage ?? errorMessage, "Invalid parameter");
      }
      return date;
    }
    return null;
  }

  /**
   * @description Checks if parameter's value exists and is email. If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns string
   */
  public static isEmailParamValid(
    param: string,
    value: string,
    options: ParamsValidationOptionsType = {}
  ): string {
    const optionsToUse = {
      isRequired: true,
      ...options
    };

    if (optionsToUse.isRequired && !value) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }
    if (value != null && !validator.isEmail(value)) {
      throw new BadRequestError(optionsToUse.responseMessage ?? `${value} is not an email`, "Invalid parameter");
    }

    return value;
  }

  /**
   * @description Checks if parameter's value exists and is a valid asset. If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns boolean
   */
  public static isAssetValid(param: string, value: string, options: ParamsValidationOptionsType = {}): boolean {
    const optionsToUse = {
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && !value) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }

    // If value is null (and not required), we can return true.
    if (!value) {
      return true;
    }

    if (!InvestmentUniverseUtil.isAssetActive(value as investmentUniverseConfig.AssetType)) {
      throw new BadRequestError(
        optionsToUse.responseMessage ?? `${value} is not a valid asset`,
        "Invalid parameter"
      );
    }

    return true;
  }

  /**
   * @description Checks if parameter's value exists and is a valid public asset. If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns boolean
   */
  public static isPublicAssetValid(
    param: string,
    value: string,
    options: ParamsValidationOptionsType = {}
  ): boolean {
    const optionsToUse = {
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && !value) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }

    // If value is null (and not required), we can return true.
    if (!value) {
      return true;
    }

    if (!InvestmentUniverseUtil.isPublicAssetActive(value as publicInvestmentUniverseConfig.PublicAssetType)) {
      throw new BadRequestError(
        optionsToUse.responseMessage ?? `${value} is not a valid asset`,
        "Invalid parameter"
      );
    }

    return true;
  }

  /**
   * @description Checks if parameter's value exists and is a valid Saving Product. If validation fails it throws BadRequestError.
   * @param param parameter's name
   * @param value parameter's value
   * @param options (optional) to override default behavior
   * @returns boolean
   */
  public static isSavingProductValidValid(
    param: string,
    value: string,
    options: ParamsValidationOptionsType = {}
  ): boolean {
    const optionsToUse = {
      isRequired: true,
      ...options
    };
    if (optionsToUse.isRequired && !value) {
      throw new BadRequestError(`Param '${param}' is required`, "Invalid parameter");
    }
    if (
      value != null &&
      !savingsUniverseConfig.SavingsProductArray.includes(value as savingsUniverseConfig.SavingsProductType)
    ) {
      throw new BadRequestError(
        optionsToUse.responseMessage ?? `${value} is not a valid Saving Product`,
        "Invalid parameter"
      );
    }

    return true;
  }
}
