import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { PartialRecord } from "utils";

export const FMP_EXCHANGE_MAP: PartialRecord<investmentUniverseConfig.ExchangeType, string> = {
  LSE: "L",
  XETRA: "DE"
};

/**
 * @returns the correct FMP symbol for a given asset.
 * - For stocks: just the formalTicker
 * - For ETFs: formalTicker.fmpExchange
 */
export function getFMPSymbolFromAssetId(assetCommonId: investmentUniverseConfig.AssetType): string {
  const { formalTicker, formalExchange, category } = investmentUniverseConfig.ASSET_CONFIG[assetCommonId];
  if (category === "stock") {
    return formalTicker;
  } else if (category === "etf") {
    const fmpExchange = FMP_EXCHANGE_MAP[formalExchange] || formalExchange;
    return `${formalTicker}.${fmpExchange}`;
  } else {
    throw new Error(`Unsupported asset category for FMP symbol mapping: ${category}`);
  }
}
