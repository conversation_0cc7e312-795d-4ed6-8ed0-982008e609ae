import Decimal from "decimal.js";

export default class MathUtil {
  /**
   * Adjusts a score based on the number of observations.
   * @param observations
   * @param score
   * @param k is a constant that controls how strongly we want to 'push' scores with few observations toward 0.5
   */
  public static adjustBayesian(observations: number, score: number, k: number): number {
    return Decimal.div(
      Decimal.mul(observations, score).add(Decimal.mul(k, 0.5)),
      Decimal.add(observations, k)
    ).toNumber();
  }

  /**
   * Performs a log transformation of the given value.
   * @param value
   * @param base
   */
  public static log(value: number, base: number = 10): number {
    if (value === 0) {
      return 0;
    }

    return Decimal.log(value, base).toNumber();
  }
}
