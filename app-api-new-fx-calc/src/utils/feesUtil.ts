import { FeesType } from "../models/Transaction";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";
import { plansConfig } from "@wealthyhood/shared-configs";

/**
 * Returns the broker FX spread percentage based on user's subscription plan
 * gold => 25 bps
 * plus => 30 bps
 * free => 40 bps
 *
 * NOTE: numbers tbd offline
 *
 * @param plan User's subscription plan
 * @returns Spread percentage as a decimal (e.g., 0.0025 for 0.25%)
 */
const _getBrokerFxSpreadByPlan = (plan: plansConfig.PlanType): number => {
  switch (plan) {
    case "paid_mid":
      return 0.0025;
    case "paid_low":
      return 0.003;
    case "free":
      return 0.004;
    default:
      return 0.004;
  }
};

export const getZeroFees = (currency: currenciesConfig.MainCurrencyType): FeesType => {
  return {
    fx: {
      currency,
      amount: 0
    },
    commission: {
      currency,
      amount: 0
    },
    executionSpread: {
      currency,
      amount: 0
    },
    realtimeExecution: {
      currency,
      amount: 0
    }
  };
};

export const getTotalFeeAmount = (fees: FeesType): number => {
  if (!fees) return 0;

  return Object.entries(fees)
    .map(([, fee]) => fee.amount)
    .filter((amount) => amount > 0)
    .reduce((sum, fee) => Decimal.add(sum, fee), new Decimal(0))
    .toNumber();
};

/**
 * Calculates the broker FX fee amount based on consideration amount, plan, and broker FX rate
 * @param params Object containing considerationAmount, plan, and brokerFxRate
 * @param params.considerationAmount Amount in cents
 * @param params.plan User's subscription plan
 * @param params.brokerFxRate Broker FX rate (if 1, no FX fee is applied)
 * @returns FX fee amount in cents
 */
export const calculateBrokerFxFee = ({
  considerationAmount,
  plan,
  brokerFxRate
}: {
  considerationAmount: number;
  plan: plansConfig.PlanType;
  brokerFxRate: number;
}): number => {
  // If broker FX rate is 1, no FX fee is applied
  if (brokerFxRate === 1) {
    return 0;
  }

  const spread = _getBrokerFxSpreadByPlan(plan);
  return Decimal.mul(considerationAmount, spread).toNumber();
};

export const aggregateFees = (fees: FeesType[], currency: currenciesConfig.MainCurrencyType): FeesType => {
  return fees.reduce((totalFees, currentFees) => {
    return {
      executionSpread: {
        amount: Decimal.add(
          totalFees.executionSpread.amount,
          currentFees?.executionSpread?.amount ?? 0
        ).toNumber(),
        currency: totalFees.executionSpread.currency
      },
      fx: {
        amount: Decimal.add(totalFees.fx.amount, currentFees?.fx?.amount ?? 0).toNumber(),
        currency: totalFees.fx.currency
      },
      commission: {
        amount: Decimal.add(totalFees.commission.amount, currentFees?.commission?.amount ?? 0).toNumber(),
        currency: totalFees.commission.currency
      },
      realtimeExecution: {
        amount: Decimal.add(
          totalFees.realtimeExecution.amount,
          currentFees?.realtimeExecution?.amount ?? 0
        ).toNumber(),
        currency: totalFees.realtimeExecution.currency
      }
    } as FeesType;
  }, getZeroFees(currency));
};
