import { AssetTransactionDocument } from "../models/Transaction";
import { InternalServerError } from "../models/ApiErrors";

export enum ExtendedAssetTransactionCategoryEnum {
  PORTFOLIO_BUY = "PORTFOLIO_BUY",
  PORTFOL<PERSON>_SELL = "PORTFOLIO_SELL",
  PORTFOLIO_UPDATE = "PORTFOLIO_UPDATE", // Deprecated portfolio update that contains both buy/sell orders.
  ASSET_BUY = "ASSET_BUY",
  ASSET_SELL = "ASSET_SELL"
}

export const getExtendedPortfolioTransactionCategory = (
  transaction: AssetTransactionDocument
): ExtendedAssetTransactionCategoryEnum => {
  if (transaction.portfolioTransactionCategory === "buy") {
    return ExtendedAssetTransactionCategoryEnum.PORTFOLIO_BUY;
  } else if (transaction.portfolioTransactionCategory === "sell") {
    return ExtendedAssetTransactionCategoryEnum.PORTFOLIO_SELL;
  }

  // Portfolio transaction category is 'update'
  if (
    transaction.portfolioTransactionCategory === "update" &&
    transaction.orders.length === 1 &&
    transaction.orders[0].side === "Buy"
  ) {
    return ExtendedAssetTransactionCategoryEnum.ASSET_BUY;
  } else if (
    transaction.portfolioTransactionCategory === "update" &&
    transaction.orders.length === 1 &&
    transaction.orders[0].side === "Sell"
  ) {
    return ExtendedAssetTransactionCategoryEnum.ASSET_SELL;
  } else if (transaction.portfolioTransactionCategory === "update" && transaction.orders.length > 1) {
    return ExtendedAssetTransactionCategoryEnum.PORTFOLIO_UPDATE;
  }

  // If no portfolio transaction category was found, then something is wrong!
  throw new InternalServerError(
    `Extended portfolio transaction category type could not be found for ${transaction.id}`
  );
};

export const getBuyOrderAmount = (transaction: AssetTransactionDocument) => {
  if (transaction.category !== "AssetTransaction") {
    return 0;
  }

  const isPortfolioBuy = transaction.portfolioTransactionCategory === "buy";
  const isAssetBuy =
    transaction.portfolioTransactionCategory === "update" &&
    transaction.orders.length === 1 &&
    transaction.orders[0].side === "Buy";

  if (!isAssetBuy && !isPortfolioBuy) {
    return 0;
  }

  if (isPortfolioBuy) {
    return transaction.originalInvestmentAmount ?? transaction.consideration.amount;
  } else if (isAssetBuy) {
    return (
      transaction.orders[0].consideration.originalAmount ??
      transaction.orders[0].consideration.amountSubmitted ??
      transaction.orders[0].consideration.amount
    );
  }
};
