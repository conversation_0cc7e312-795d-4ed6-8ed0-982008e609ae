export type EnvironmentType = "development" | "staging" | "production";

export const getEnvironment = (): EnvironmentType => process.env.NODE_ENV as EnvironmentType;

export const envIsDev = (): boolean => process.env.NODE_ENV === "development";
export const envIsDemo = (): boolean =>
  process.env.NODE_ENV === "development" || process.env.NODE_ENV === "staging";
export const envIsProd = (): boolean => process.env.NODE_ENV === "production";
