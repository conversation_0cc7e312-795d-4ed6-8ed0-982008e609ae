import { BadRequestError } from "../models/ApiErrors";
import ParamsValidationUtil from "./paramsValidationUtil";

export default class PaginationUtil {
  public static getValidatedPageConfigFromPaginationProvided(pageSizeParam: string, pageParam: string) {
    let pageConfig: { pageSize: number; page: number };
    if (pageSizeParam || pageParam) {
      pageConfig = {
        pageSize: ParamsValidationUtil.isNumericParamValid("pageSize", pageSizeParam),
        page: ParamsValidationUtil.isNumericParamValid("page", pageParam)
      };
    }

    return pageConfig;
  }
  public static getPaginationParametersFor(
    totalItems: number,
    paginationInputParams: { page: number; pageSize: number }
  ): { page: number; pageSize: number; pages: number; total: number } {
    if (paginationInputParams.pageSize > totalItems) {
      paginationInputParams.pageSize = totalItems;
    }

    const pages =
      totalItems / paginationInputParams.pageSize > 0 ? Math.ceil(totalItems / paginationInputParams.pageSize) : 1; // if modulo exists round up to nearest integer
    if (paginationInputParams.page < 1 || paginationInputParams.page > pages) {
      throw new BadRequestError("Invalid page number requested");
    }

    const { page, pageSize } = paginationInputParams;
    return { page, pageSize, pages, total: totalItems };
  }
}
