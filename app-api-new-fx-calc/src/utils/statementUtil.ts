import { UserDocument } from "../models/User";
import { AddressDocument } from "../models/Address";
import DateUtil from "./dateUtil";
import { capitalizeFirstLetter } from "./stringUtil";
import { AccountStatementActivity } from "../services/userService";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import path from "path";
import * as fs from "fs";
import { countriesConfig, entitiesConfig } from "@wealthyhood/shared-configs";
import { customAlphabet } from "nanoid";

const alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
export const nanoid = customAlphabet(alphabet, 6);

export default class StatementUtil {
  /**
   * Generates a PDF account statement in the format of an array buffer.
   * @param user
   * @param address
   * @param data
   * @param options
   * @private
   */
  public static generateAccountStatementPDF(
    user: UserDocument,
    address: AddressDocument,
    data: AccountStatementActivity,
    options?: {
      start?: Date;
      end?: Date;
    }
  ): Buffer {
    // Retrieve static file paths needed for PDF (logo & fonts).
    const logoPath = path.join(__dirname + "/../../static/whlogobanner.png");
    const lightFontPath = path.join(__dirname + "/../../static/poppins-light.txt");
    const regularFontPath = path.join(__dirname + "/../../static/poppins-regular.txt");
    const semiBoldFontPath = path.join(__dirname + "/../../static/poppins-semibold.txt");
    const boldFontPath = path.join(__dirname + "/../../static/poppins-bold.txt");

    const document = new jsPDF();

    // Add files for fonts
    document.addFileToVFS("Poppins-Light.ttf", fs.readFileSync(lightFontPath, "utf-8"));
    document.addFileToVFS("Poppins-Regular.ttf", fs.readFileSync(regularFontPath, "utf-8"));
    document.addFileToVFS("Poppins-SemiBold.ttf", fs.readFileSync(semiBoldFontPath, "utf-8"));
    document.addFileToVFS("Poppins-Bold.ttf", fs.readFileSync(boldFontPath, "utf-8"));
    document.addFont("Poppins-Light.ttf", "Poppins Light", "normal");
    document.addFont("Poppins-Regular.ttf", "Poppins", "normal");
    document.addFont("Poppins-SemiBold.ttf", "Poppins", "semibold");
    document.addFont("Poppins-Bold.ttf", "Poppins", "bold");

    // Add Wealthyhood logo (top-left)
    document.addImage(fs.readFileSync(logoPath, "base64"), "PNG", 15, 24, 61, 10);

    // Add Account statement title and subtitle (top-right)
    document.setFont("Poppins", "semibold");
    document.setFontSize(20);
    document.text("Account Statement", 190, 30, { align: "right" });

    document.setFont("Poppins Light", "normal");
    document.setFontSize(9);
    document.setTextColor(128, 128, 128);
    document.text(`Generated on ${DateUtil.formatDateToDDMONTHYYYY(new Date(Date.now()))}`, 190, 37, {
      align: "right"
    });

    // Reset text styling
    document.setFont("Poppins", "normal");
    document.setTextColor("black");

    // Add user details
    const endingHeight = this._addAccountStatementUserDetailsSection(document, user, address, {
      startingHeight: 55
    });

    // Add table title
    document.setFontSize(12);
    document.setFont("Poppins", "bold");

    const formattedData = data.map((row) => {
      if (row.type === "order") {
        return [
          DateUtil.formatDateToDDMONYYYY(row.date),
          capitalizeFirstLetter(row.type),
          row.side,
          row.asset,
          row.isin,
          row.currency,
          row.amount.toFixed(2)
        ];
      } else if (row.type === "dividend") {
        return [
          DateUtil.formatDateToDDMONYYYY(row.date),
          capitalizeFirstLetter(row.type),
          "-",
          row.asset,
          row.isin,
          row.currency,
          row.amount.toFixed(2)
        ];
      } else if (row.type === "reward") {
        return [
          DateUtil.formatDateToDDMONYYYY(row.date),
          "Bonus",
          "-",
          row.asset,
          row.isin,
          row.currency,
          row.amount.toFixed(2)
        ];
      } else {
        return [
          DateUtil.formatDateToDDMONYYYY(row.date),
          capitalizeFirstLetter(row.type),
          "-",
          "-",
          "-",
          row.currency,
          row.amount.toFixed(2)
        ];
      }
    });

    const startDate = DateUtil.formatDateToDDMONYYYY(options?.start ?? data?.at(-1)?.date);
    const endDate = DateUtil.formatDateToDDMONYYYY(options?.end ?? new Date(Date.now()));

    document.text(`Transactions from ${startDate} to ${endDate}`, 15, endingHeight + 25);

    // Add table
    this._addAccountStatementTransactionsSection(document, formattedData, endingHeight);

    // Add footer
    this._addAccountStatementFooterSection(document, user);

    return Buffer.from(document.output("arraybuffer"));
  }

  public static generateAccountStatementFilePath(userId: string): string {
    return `${userId}/${nanoid()}/wealthyhood-account-statement_${DateUtil.formatDateToDDMONYYYY(new Date(), { separatorCharacter: "-" })}.pdf`.toLowerCase();
  }

  private static _addAccountStatementUserDetailsSection(
    document: jsPDF,
    user: UserDocument,
    address: AddressDocument,
    options: { startingHeight: number }
  ): number {
    // Default height padding between every user detail row
    const padding = 7;

    // Extra padding under the user full name
    const extraNamePadding = 3;

    document.setFontSize(16);
    document.setFont("Poppins", "bold");
    document.text(user.fullName, 15, options?.startingHeight);

    document.setFontSize(10);
    document.setFont("Poppins", "semibold");
    document.text("Account reference:", 15, options?.startingHeight + extraNamePadding + padding);
    document.text("Email:", 15, options?.startingHeight + extraNamePadding + padding * 2);
    document.text("Address:", 15, options?.startingHeight + extraNamePadding + padding * 3);

    document.setFont("Poppins Light", "normal");
    document.text(user.id, 50, options?.startingHeight + extraNamePadding + padding);
    document.text(user.email, 27, options?.startingHeight + extraNamePadding + padding * 2);
    document.text(
      `${address.line1}, ${address.city}, ${address.postalCode}, ${countriesConfig.countries.find(({ code }) => code === address.countryCode).name}`,
      32,
      options?.startingHeight + extraNamePadding + padding * 3
    );

    return options?.startingHeight + extraNamePadding + padding * 3;
  }

  /**
   * Adds a footer (specific to the user's company entity) in **every** page of the PDF statement.
   * @param document
   * @param user
   * @private
   */
  private static _addAccountStatementFooterSection(document: jsPDF, user: UserDocument) {
    const pageCount = document.getNumberOfPages();

    document.setFontSize(6);
    document.setFont("Poppins Light", "normal");
    for (let i = 1; i <= pageCount; i++) {
      document.setPage(i);
      document.setTextColor(164, 164, 164);

      const companyName =
        user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
          ? "2024 Wealthyhood Europe AEPEY"
          : "2024 Wealthyhood Ltd";
      const disclaimer =
        user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
          ? "Wealthyhood Europe AEPEY is authorised and regulated by the Hellenic Capital Markets Commission (3/1014). Wealthyhood Europe AEPEY is registered in Greece\n(************) at Solonos 60, Athens, 10672, Greece."
          : "Wealthyhood Ltd (FCA Register: 933675) is an appointed representative of RiskSave Technologies Ltd, which is authorised and regulated by the Financial Conduct Authority\n(FRN 775330). Wealthyhood Ltd is registered in England and Wales (********) at 9 Kingsland Road, London, E2 8DD, United Kingdom.";

      document.text(disclaimer, 15, 270);
      document.text(companyName, 15, 280);
      document.text(`Page ${i} of ${pageCount}`, 190, 280, { align: "right" });
    }
  }

  private static _addAccountStatementTransactionsSection(
    document: jsPDF,
    data: string[][],
    previousElementEndingHeight: number
  ) {
    autoTable(document, {
      theme: "plain",
      startY: previousElementEndingHeight + 33, // Previous height
      margin: { bottom: 40 },
      rowPageBreak: "avoid",
      styles: {
        font: "Poppins Light",
        fontSize: 7,
        lineWidth: {
          bottom: 0.5
        },
        minCellHeight: 14,
        lineColor: [228, 228, 228],
        valign: "middle",
        minCellWidth: 20
      },
      headStyles: {
        font: "Poppins",
        fontSize: 9,
        lineWidth: {
          bottom: 0.5
        },
        lineColor: "black"
      },
      head: [["Date", "Category", "Side", "Description", "ISIN", "Currency", "Amount"]],
      body: data
    });
  }
}
