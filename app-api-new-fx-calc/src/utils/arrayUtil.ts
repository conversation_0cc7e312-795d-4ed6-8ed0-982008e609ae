/**
 * @description Concate 2 arrays, while keeping the same order and removing any duplicates
 */
export function concatenateUnique<T>(arr1: T[], arr2: T[]): T[] {
  // Create a new array to hold the concatenated result
  const result: T[] = [];

  // Add all elements from the first array to the result
  arr1.forEach((item) => {
    if (!result.includes(item)) {
      result.push(item);
    }
  });

  // Add elements from the second array to the result, only if they are not already included
  arr2.forEach((item) => {
    if (!result.includes(item)) {
      result.push(item);
    }
  });

  return result;
}

/**
 * @description Concatenate two arrays while keeping the same order and removing duplicates based on a key,
 * keeping the latest occurrence of each unique key.
 */
export function concatenateUniqueByKey<T, K extends keyof T>(arr1: T[], arr2: T[], key: K): T[] {
  const map = new Map<T[K], T>();

  [...arr1, ...arr2].forEach((item) => {
    // Always set, so the latest occurrence is kept
    map.set(item[key], item);
  });

  return Array.from(map.values());
}
