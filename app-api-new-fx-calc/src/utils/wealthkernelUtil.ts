import { WealthkernelService } from "../external-services/wealthkernelService";

export default class WealthkernelUtil {
  public static async onEachInstance(
    processorFn: (instance: WealthkernelService) => Promise<void>
  ): Promise<void> {
    await Promise.all(
      WealthkernelService.getAllRegionInstances().map((wealthkernelService) => processorFn(wealthkernelService))
    );
  }

  /**
   * Checks that latest valuation exists based on the given WK instance (either EU or UK).
   */
  public static async checkLatestWealthkernelValuationExists(wealthkernelService: WealthkernelService) {
    const startDate = WealthkernelService.getLatestCalculationDate();
    const endDate = startDate;
    const limit = 10;

    const valuations = await wealthkernelService.retrieveValuations({
      startDate,
      endDate,
      limit
    });

    if (!(valuations.length > 0)) {
      throw new Error("💔 Wealthkernel valuation hasn't happened yet, exiting!");
    }
  }
}
