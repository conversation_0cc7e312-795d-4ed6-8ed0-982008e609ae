import { plansConfig } from "@wealthyhood/shared-configs";
import { SubscriptionDocument } from "../models/Subscription";

export const isDowngrade = (
  from: plansConfig.PlanType,
  to: plansConfig.PlanType,
  planConfig: Record<plansConfig.PlanType, plansConfig.PlanConfigType>
): boolean => {
  return planConfig[from].weight > planConfig[to].weight;
};

export const isUpgrade = (
  from: plansConfig.PlanType,
  to: plansConfig.PlanType,
  planConfig: Record<plansConfig.PlanType, plansConfig.PlanConfigType>
): boolean => {
  return planConfig[from].weight < planConfig[to].weight;
};

export const isUpgradeFromPaidLow = (
  from: plansConfig.PlanType,
  to: plansConfig.PlanType,
  planConfig: Record<plansConfig.PlanType, plansConfig.PlanConfigType>
): boolean => {
  return isUpgrade(from, to, planConfig) && from === "paid_low";
};

export const isUpdateOfRecurrence = (
  from: plansConfig.PriceType,
  to: plansConfig.PriceType,
  priceConfig: Record<plansConfig.PriceType, plansConfig.PriceConfigType>
): boolean => {
  const oldPrice = priceConfig[from];
  const newPrice = priceConfig[to];

  return oldPrice.plan === newPrice.plan && oldPrice.recurrence !== newPrice.recurrence;
};

export const isOnCardSubscription = (subscription: SubscriptionDocument): boolean => {
  return subscription.category === "CardPaymentSubscription";
};

export const isOnLifetimeSubscription = (
  subscription: SubscriptionDocument,
  priceConfig: Record<plansConfig.PriceType, plansConfig.PriceConfigType>
): boolean => {
  return priceConfig[subscription.price].recurrence === "lifetime";
};

export const isDowngradeToFree = (
  from: plansConfig.PlanType,
  to: plansConfig.PlanType,
  planConfig: Record<plansConfig.PlanType, plansConfig.PlanConfigType>
): boolean => {
  return isDowngrade(from, to, planConfig) && to === "free";
};

export const isDowngradeToPaidLow = (
  from: plansConfig.PlanType,
  to: plansConfig.PlanType,
  planConfig: Record<plansConfig.PlanType, plansConfig.PlanConfigType>
): boolean => {
  return isDowngrade(from, to, planConfig) && to === "paid_low";
};

export const isUpdateToALifetimePrice = (
  to: plansConfig.PriceType,
  priceConfig: Record<plansConfig.PriceType, plansConfig.PriceConfigType>
): boolean => {
  return priceConfig[to].recurrence === "lifetime";
};
