import logger from "../external-services/loggerService";
import Decimal from "decimal.js";
import { unlink } from "fs/promises";
import path from "path";
import * as v8 from "v8";
import { createReadStream } from "fs";
import CloudflareService, { BucketsEnum, ContentTypeEnum } from "../external-services/cloudflareService";

const MEMORY_MEASURING_INTERVAL_MILLISECONDS = 2000;

interface MemoryMetrics {
  rss: number;
  heapTotal: number;
  heapUsed: number;
  external: number;
}

interface MemorySnapshot {
  timestamp: number;
  metrics: MemoryMetrics;
}

export default class PerformanceUtil {
  /**
   * Wraps a function execution with performance and memory usage tracking.
   *
   * @param processorFn The function to execute and measure
   * @param displayName A name to identify this operation in logs
   * @param memoryCapturePeriodMs Milliseconds between memory captures
   *
   * @returns The result of the function execution
   */
  public static async withPerformance<T>(
    processorFn: () => Promise<T>,
    displayName: string,
    memoryCapturePeriodMs: number = MEMORY_MEASURING_INTERVAL_MILLISECONDS
  ): Promise<T> {
    const memorySnapshots: MemorySnapshot[] = [];

    const start = performance.now();
    const initialMemory = PerformanceUtil.getMemoryMetrics();

    // First snapshot at start
    memorySnapshots.push({
      timestamp: start,
      metrics: initialMemory
    });

    const memoryCapturingInterval = setInterval(() => {
      const now = performance.now();
      memorySnapshots.push({
        timestamp: now,
        metrics: PerformanceUtil.getMemoryMetrics()
      });
    }, memoryCapturePeriodMs);

    try {
      // Execute the function
      const data = await processorFn();

      // Final snapshot
      const end = performance.now();
      const finalMemory = PerformanceUtil.getMemoryMetrics();

      // Add the final snapshot
      memorySnapshots.push({
        timestamp: end,
        metrics: finalMemory
      });

      // Calculate differences
      const timeTaken = Decimal.sub(end, start).div(1000).toDecimalPlaces(3).toNumber();

      // Find highest memory usage
      const maxMemory = memorySnapshots.reduce(
        (max, snapshot) => {
          return snapshot.metrics.heapUsed > max.heapUsed ? snapshot.metrics : max;
        },
        { heapUsed: 0, rss: 0, heapTotal: 0, external: 0 }
      );

      // Log results
      logger.info(`Performance for ${displayName} was recorded!`, {
        module: "PerformanceUtil",
        method: "withPerformance",
        data: {
          timeTaken,
          maxMemory,
          snapshots: memorySnapshots,
          limits: {
            totalAvailable: v8.getHeapStatistics().total_available_size / 1024 / 1024,
            heapSizeLimit: v8.getHeapStatistics().heap_size_limit / 1024 / 1024
          }
        }
      });

      return data;
    } finally {
      clearInterval(memoryCapturingInterval);
    }
  }

  public static async recordHeapDump(context: string): Promise<void> {
    logger.info(`Collecting heap dump for ${context}`, {
      module: "PerformanceUtil",
      method: "recordHeapDump",
      data: {
        context
      }
    });

    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filePathToAddSnapshot = `/tmp/heapdump-${context}-${timestamp}.heapsnapshot`;

    const outputFile = v8.writeHeapSnapshot(filePathToAddSnapshot);

    const fileStream = createReadStream(outputFile);
    const cloudflareFileName = `${process.env.NODE_ENV}/${path.basename(outputFile)}`;

    fileStream.on("error", (err) => {
      logger.error("Error with file stream while collecting heap dump!", {
        module: "PerformanceUtil",
        method: "recordHeapDump",
        data: {
          context,
          err
        }
      });
      fileStream.destroy();
    });

    await CloudflareService.Instance.uploadObject(BucketsEnum.HEAP_DUMPS, cloudflareFileName, fileStream, {
      contentType: ContentTypeEnum.APPLICATION_JSON
    });

    fileStream.destroy();

    logger.info(`Successfully uploaded heap dump to Cloudflare: ${cloudflareFileName}`, {
      module: "PerformanceUtil",
      method: "recordHeapDump",
      data: {
        context
      }
    });

    await unlink(outputFile);
  }

  /**
   * Gets current memory metrics in a standardized format (MB)
   * @returns Memory metrics object with values in MB
   */
  private static getMemoryMetrics(): MemoryMetrics {
    const memoryUsage = process.memoryUsage();

    return {
      rss: Math.round((memoryUsage.rss / 1024 / 1024) * 10) / 10,
      heapTotal: Math.round((memoryUsage.heapTotal / 1024 / 1024) * 10) / 10,
      heapUsed: Math.round((memoryUsage.heapUsed / 1024 / 1024) * 10) / 10,
      external: Math.round((memoryUsage.external / 1024 / 1024) * 10) / 10
    };
  }
}
