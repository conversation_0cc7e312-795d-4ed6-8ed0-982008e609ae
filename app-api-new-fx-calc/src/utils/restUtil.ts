import { CustomRequest, CustomResponse } from "custom";
import { OrderDocument } from "../models/Order";
import OrderService from "../services/orderService";
import DbUtil from "./dbUtil";
import { PortfolioDocument } from "../models/Portfolio";
import PortfolioService from "../services/portfolioService";
import { AppRatingDocument } from "../models/AppRating";
import AppRatingService from "../services/appRatingService";
import SubscriptionService from "../services/subscriptionService";
import { SubscriptionDocument } from "../models/Subscription";

export default class RestUtil {
  public static readonly getAppRatingFromResponse = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<AppRatingDocument> => {
    if (!res.locals.appRating) {
      return AppRatingService.getAppRating(req.params.id);
    }

    return res.locals.appRating;
  };

  public static readonly getOrderFromResponse = async (
    req: CustomRequest,
    res: CustomResponse,
    populate: { transaction: boolean }
  ): Promise<OrderDocument> => {
    if (!res.locals.order) {
      return OrderService.getOrder(req.params.id, populate);
    }

    const order = res.locals.order;

    if (Object.entries(populate).some(([field, shouldPopulate]) => shouldPopulate && !order.populated(field))) {
      return order.populate(DbUtil.getPopulationString(populate));
    } else return order;
  };

  public static readonly getSubscriptionFromResponse = async (
    req: CustomRequest,
    res: CustomResponse
  ): Promise<SubscriptionDocument> => {
    if (!res.locals.subscription) {
      return SubscriptionService.getSubscription(req.user.id);
    }

    return res.locals.subscription;
  };

  public static readonly getPortfolioFromResponse = async (
    req: CustomRequest,
    res: CustomResponse,
    populateTickers = false
  ): Promise<PortfolioDocument> => {
    if (!res.locals.portfolio) {
      return PortfolioService.getPortfolio(req.params.id, populateTickers);
    }

    if (populateTickers) {
      return PortfolioService.populateTickersAndOwner(res.locals.portfolio);
    } else return res.locals.portfolio;
  };
}
