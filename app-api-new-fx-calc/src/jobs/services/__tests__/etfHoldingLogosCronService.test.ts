import { EtfHoldingLogosCronService } from "./../etfHoldingLogosCronService";
import { BucketsEnum } from "../../../external-services/__mocks__/cloudflareService";
import { buildEtfFundamentalsResponse, buildStockFundamentalsResponse } from "../../../tests/utils/generateEod";
import eodService from "../../../external-services/eodService";
import axios from "axios";
import { Readable } from "stream";
import CloudflareService from "../../../external-services/cloudflareService";
import logger from "../../../external-services/loggerService";
import { RedisClientService } from "../../../loaders/redis";

function createMockStream(data) {
  const stream = new Readable();
  stream.push(data);
  stream.push(null); // Indicates the end of the stream
  return stream;
}

describe("EtfHoldingLogosCronService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("storeMissingEtfHoldingLogos", () => {
    describe("when eod holding logo is already stored in cloudflare", () => {
      beforeAll(() => {
        jest.spyOn(CloudflareService.Instance, "doesObjectExists").mockResolvedValue(true);
        jest.spyOn(CloudflareService.Instance, "uploadObject");

        const eodEtfDataResponse = buildEtfFundamentalsResponse();
        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodEtfDataResponse);
      });

      it("should not try to re-upload it", async () => {
        await EtfHoldingLogosCronService.storeMissingEtfHoldingLogos();

        expect(CloudflareService.Instance.uploadObject).not.toHaveBeenCalled();
      });
    });

    describe("when eod holding logo is missing from cloudflare and eod provides a logo url", () => {
      let mockStream: Readable;

      beforeAll(() => {
        jest.spyOn(CloudflareService.Instance, "doesObjectExists").mockResolvedValue(false);
        jest.spyOn(CloudflareService.Instance, "uploadObject");

        const eodEtfDataResponse = buildEtfFundamentalsResponse();
        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodEtfDataResponse);

        const eodStockDataResponse = buildStockFundamentalsResponse();
        jest
          .spyOn(eodService, "getAssetFundamentalsDataByFundamentalsTicker")
          .mockResolvedValue(eodStockDataResponse);

        jest.mock("axios");
        mockStream = createMockStream("My image data");
        jest.spyOn(axios, "get").mockResolvedValue({
          data: mockStream
        });
      });

      it("should upload it the missing holding logo to Cloudflare", async () => {
        await EtfHoldingLogosCronService.storeMissingEtfHoldingLogos();

        expect(CloudflareService.Instance.uploadObject).toHaveBeenCalledWith(
          BucketsEnum.ETF_HOLDING_LOGOS,
          "eod/FSLR.png",
          mockStream,
          { contentType: "image/png" }
        );
      });
    });

    describe("when eod holding logo is missing from cloudflare and eod does NOT provide a logo url", () => {
      beforeEach(async () => {
        jest.spyOn(CloudflareService.Instance, "doesObjectExists").mockResolvedValue(false);
        jest.spyOn(CloudflareService.Instance, "uploadObject");

        const eodEtfDataResponse = buildEtfFundamentalsResponse();
        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodEtfDataResponse);

        const eodStockDataResponse = buildStockFundamentalsResponse({
          General: {
            Exchange: "NASDAQ",
            GicIndustry: "Software",
            WebURL: "https://www.microsoft.com",
            FullTimeEmployees: 22000,
            AddressData: {
              City: "Redmond",
              State: "WA",
              Country: "United States"
            },
            Description:
              "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
            Officers: {
              0: {
                Name: "Mr. Satya  Nadella",
                Title: "Chairman & CEO"
              }
            },
            LogoURL: "" // emptry logoURL
          }
        });
        jest
          .spyOn(eodService, "getAssetFundamentalsDataByFundamentalsTicker")
          .mockResolvedValue(eodStockDataResponse);
      });

      it("should log an error", async () => {
        await EtfHoldingLogosCronService.storeMissingEtfHoldingLogos();

        expect(logger.error).toHaveBeenCalledWith("Could not find logo from eod for FSLR holding", {
          module: "EtfHoldingLogosCronService",
          method: "_processHoldingLogo",
          data: {
            eodHolding: {
              Code: "FSLR",
              Exchange: "US",
              Name: "First Solar Inc",
              "Assets_%": 7.64461
            }
          }
        });
      });
    });
  });

  describe("cacheCustomEtfHoldingLogos", () => {
    describe("when ETF holding has a custom logo", () => {
      beforeAll(() => {
        jest.spyOn(CloudflareService.Instance, "doesObjectExists").mockResolvedValue(true);

        const eodEtfDataResponse = buildEtfFundamentalsResponse();
        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodEtfDataResponse);
      });

      it("should cache the custom logo url to redis", async () => {
        await EtfHoldingLogosCronService.cacheCustomEtfHoldingLogos();

        const etfHoldingCodesToCustomLogos = await RedisClientService.Instance.get(
          "eod:customLogos:equities_global"
        );
        expect(etfHoldingCodesToCustomLogos).toEqual(
          expect.objectContaining({
            FSLR: "https://etf-holdings-logos.wealthyhood.dev/FSLR.png"
          })
        );
      });
    });

    describe("when ETF holding does not have a custom logo", () => {
      beforeAll(() => {
        jest.spyOn(CloudflareService.Instance, "doesObjectExists").mockResolvedValue(false);

        const eodEtfDataResponse = buildEtfFundamentalsResponse();
        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(eodEtfDataResponse);
      });

      it("should not anything to redis", async () => {
        await EtfHoldingLogosCronService.cacheCustomEtfHoldingLogos();

        const etfHoldingCodesToCustomLogos = await RedisClientService.Instance.get(
          "eod:customLogos:equities_global"
        );
        expect(etfHoldingCodesToCustomLogos).toEqual({});
      });
    });
  });
});
