import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import StockNewsService, { StockNewsItem } from "../../../external-services/stockNewsService";
import { buildStockNewsAssetResponse } from "../../../tests/utils/generateStockNews";
import { faker } from "@faker-js/faker";
import { buildAssetNews, buildInvestmentProduct } from "../../../tests/utils/generateModels";
import { AssetNews, AssetNewsDocument } from "../../../models/AssetNews";
import { InvestmentProductDocument } from "../../../models/InvestmentProduct";
import AssetNewsCronService from "../assetNewsCronService";
import InvestmentProductService from "../../../services/investmentProductService";

describe("AssetNewsCronService", () => {
  beforeAll(async () => await connectDb("AssetNewsCronService"));
  afterAll(async () => await closeDb());

  describe("createAssetNews", () => {
    describe("when the news item does not exist in our database", () => {
      let newsItemResponse: StockNewsItem;
      let investmentProduct: InvestmentProductDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        const EXTERNAL_SERVICE_NEWS_ID = faker.string.uuid();
        newsItemResponse = buildStockNewsAssetResponse({ news_id: EXTERNAL_SERVICE_NEWS_ID });
        investmentProduct = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        jest.spyOn(InvestmentProductService, "getInvestmentProducts").mockResolvedValue([investmentProduct]);
        jest.spyOn(StockNewsService, "getStockNews").mockResolvedValue([newsItemResponse]);
        jest.spyOn(StockNewsService, "getEtfNews").mockResolvedValue([newsItemResponse]);

        await AssetNewsCronService.createAssetNews();
      });

      it("should store the news in our database", async () => {
        const assetNews = await AssetNews.find();

        expect(assetNews.length).toEqual(1);
        expect(assetNews[0]).toMatchObject(
          expect.objectContaining({
            investmentProducts: expect.arrayContaining([investmentProduct._id]),
            providers: expect.objectContaining({
              stockNews: expect.objectContaining({
                id: newsItemResponse.news_id
              })
            }),
            newsUrl: newsItemResponse.news_url,
            imageUrl: newsItemResponse.image_url,
            title: newsItemResponse.title,
            text: newsItemResponse.text,
            source: newsItemResponse.source_name,
            topics: newsItemResponse.topics,
            date: new Date(newsItemResponse.date),
            sentiment: newsItemResponse.sentiment,
            type: newsItemResponse.type
          })
        );
      });
    });

    describe("when the news item exists in our database for this investment product", () => {
      let newsItemResponse: StockNewsItem;
      let investmentProduct: InvestmentProductDocument;
      let existingAssetNewsDocument: AssetNewsDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        const EXTERNAL_SERVICE_NEWS_ID = faker.string.uuid();
        investmentProduct = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        newsItemResponse = buildStockNewsAssetResponse({ news_id: EXTERNAL_SERVICE_NEWS_ID });
        jest.spyOn(InvestmentProductService, "getInvestmentProducts").mockResolvedValue([investmentProduct]);
        jest.spyOn(StockNewsService, "getStockNews").mockResolvedValue([newsItemResponse]);
        jest.spyOn(StockNewsService, "getEtfNews").mockResolvedValue([newsItemResponse]);

        existingAssetNewsDocument = await buildAssetNews({
          investmentProducts: [investmentProduct._id],
          providers: {
            stockNews: {
              id: EXTERNAL_SERVICE_NEWS_ID
            }
          }
        });

        await AssetNewsCronService.createAssetNews();
      });

      it("should not store the news item in our database", async () => {
        const assetNews = await AssetNews.find();

        expect(assetNews.length).toEqual(1);
        expect(assetNews[0].investmentProducts.length).toEqual(1);
        expect(assetNews[0].toJSON()).toEqual(existingAssetNewsDocument.toJSON());
      });
    });

    describe("when the news exist in our database but for another investment product", () => {
      let newsItemResponse: StockNewsItem;
      let investmentProduct1: InvestmentProductDocument;
      let investmentProduct2: InvestmentProductDocument;
      let existingAssetNewsDocument: AssetNewsDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        const EXTERNAL_SERVICE_NEWS_ID = faker.string.uuid();
        investmentProduct1 = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });
        investmentProduct2 = await buildInvestmentProduct(false, {
          assetId: "equities_apple"
        });

        newsItemResponse = buildStockNewsAssetResponse({ news_id: EXTERNAL_SERVICE_NEWS_ID });
        jest.spyOn(InvestmentProductService, "getInvestmentProducts").mockResolvedValue([investmentProduct2]);
        jest.spyOn(StockNewsService, "getStockNews").mockResolvedValue([newsItemResponse]);
        jest.spyOn(StockNewsService, "getEtfNews").mockResolvedValue([newsItemResponse]);

        existingAssetNewsDocument = await buildAssetNews({
          investmentProducts: [investmentProduct1._id],
          providers: {
            stockNews: {
              id: EXTERNAL_SERVICE_NEWS_ID
            }
          }
        });

        await AssetNewsCronService.createAssetNews();
      });

      it("should add the investment product id to the existing news item in our database", async () => {
        const assetNews = await AssetNews.find();

        expect(assetNews.length).toEqual(1);
        expect(assetNews[0].investmentProducts.length).toEqual(2);
        expect(assetNews[0]).toMatchObject(
          expect.objectContaining({
            _id: existingAssetNewsDocument._id,
            investmentProducts: expect.arrayContaining([investmentProduct1._id, investmentProduct2._id]),
            providers: expect.objectContaining({
              stockNews: expect.objectContaining({
                id: newsItemResponse.news_id
              })
            })
          })
        );
      });
    });
  });
});
