import { faker } from "@faker-js/faker";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { UserDocument } from "../../../models/User";
import { buildUser, buildWallet } from "../../../tests/utils/generateModels";
import { ProviderEnum } from "../../../configs/providersConfig";
import { Wallet, WalletDocument } from "../../../models/Wallet";
import WalletCronService from "../walletCronService";
import { DevengoService } from "../../../external-services/devengoService";
import DateUtil from "../../../utils/dateUtil";
import { jest } from "@jest/globals";

describe("WalletCronService", () => {
  beforeAll(async () => await connectDb("WalletCronService"));
  afterAll(async () => await closeDb());

  describe("createAllDevengoWallets", () => {
    describe("when wallet has already been submitted in Devengo", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(DevengoService.Instance, "createAccount");

        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);

        await buildWallet({
          owner: user.id,
          createdAt: DateUtil.getDateOfMinutesAgo(20),
          activeProviders: [ProviderEnum.DEVENGO],
          providers: {
            [ProviderEnum.DEVENGO]: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });

        await WalletCronService.createAllDevengoWallets();
      });
      afterAll(async () => await clearDb());

      it("should not call Devengo", async () => {
        expect(DevengoService.Instance.createAccount).not.toHaveBeenCalled();
      });
    });

    describe("when wallet has not been submitted in Devengo but was created less than 10 minutes ago", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(DevengoService.Instance, "createAccount");

        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);

        await buildWallet({
          owner: user.id,
          createdAt: DateUtil.getDateOfMinutesAgo(5),
          activeProviders: [ProviderEnum.DEVENGO]
        });

        await WalletCronService.createAllDevengoWallets();
      });
      afterAll(async () => await clearDb());

      it("should not call Devengo", async () => {
        expect(DevengoService.Instance.createAccount).not.toHaveBeenCalled();
      });
    });

    describe("when wallet has not been submitted in Devengo", () => {
      let user: UserDocument;
      let wallet: WalletDocument;

      const DEVENGO_ID = faker.string.uuid();
      const IBAN = faker.finance.iban();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(DevengoService.Instance, "createAccount").mockResolvedValue({
          account: {
            id: DEVENGO_ID,
            status: "created",
            identifiers: [
              {
                type: "iban",
                iban: IBAN
              }
            ]
          }
        });

        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);
        wallet = await buildWallet({
          owner: user.id,
          activeProviders: [ProviderEnum.DEVENGO],
          createdAt: DateUtil.getDateOfMinutesAgo(20)
        });

        await WalletCronService.createAllDevengoWallets();
      });
      afterAll(async () => await clearDb());

      it("should call Devengo", async () => {
        expect(DevengoService.Instance.createAccount).toHaveBeenCalledWith({
          currency: "EUR",
          metadata: { wealthyhoodId: wallet.id },
          name: user.id
        });
      });

      it("should update the wallet document", async () => {
        const updatedWallet = await Wallet.findById(wallet.id);
        expect(updatedWallet.toObject()).toEqual(
          expect.objectContaining({
            iban: IBAN,
            providers: {
              devengo: { id: DEVENGO_ID, status: "created" }
            }
          })
        );
      });
    });
  });

  describe("syncAllDevengoWallets", () => {
    describe("when wallet is already active in Devengo", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(DevengoService.Instance, "getAccount");

        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);

        await buildWallet({
          owner: user.id,
          createdAt: DateUtil.getDateOfMinutesAgo(20),
          activeProviders: [ProviderEnum.DEVENGO],
          providers: {
            [ProviderEnum.DEVENGO]: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });

        await WalletCronService.syncAllDevengoWallets();
      });
      afterAll(async () => await clearDb());

      it("should not call Devengo", async () => {
        expect(DevengoService.Instance.getAccount).not.toHaveBeenCalled();
      });
    });

    describe("when wallet is not submitted to Devengo", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(DevengoService.Instance, "getAccount");

        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);

        await buildWallet({
          owner: user.id,
          createdAt: DateUtil.getDateOfMinutesAgo(20),
          activeProviders: [ProviderEnum.DEVENGO]
        });

        await WalletCronService.syncAllDevengoWallets();
      });
      afterAll(async () => await clearDb());

      it("should not call Devengo", async () => {
        expect(DevengoService.Instance.getAccount).not.toHaveBeenCalled();
      });
    });

    describe("when wallet is was created less than 10 minutes ago", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(DevengoService.Instance, "getAccount");

        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);

        await buildWallet({
          owner: user.id,
          createdAt: DateUtil.getDateOfMinutesAgo(5),
          activeProviders: [ProviderEnum.DEVENGO]
        });

        await WalletCronService.syncAllDevengoWallets();
      });
      afterAll(async () => await clearDb());

      it("should not call Devengo", async () => {
        expect(DevengoService.Instance.getAccount).not.toHaveBeenCalled();
      });
    });

    describe("when wallet is pending and was created more than 10 minutes ago", () => {
      let user: UserDocument;
      let wallet: WalletDocument;

      const ACCOUNT_ID = faker.string.uuid();
      const IBAN = faker.finance.iban();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(DevengoService.Instance, "getAccount").mockResolvedValue({
          account: {
            id: ACCOUNT_ID,
            status: "active",
            identifiers: [
              {
                type: "iban",
                iban: IBAN
              }
            ]
          }
        });

        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);

        wallet = await buildWallet({
          owner: user.id,
          createdAt: DateUtil.getDateOfMinutesAgo(20),
          activeProviders: [ProviderEnum.DEVENGO],
          providers: {
            [ProviderEnum.DEVENGO]: {
              id: ACCOUNT_ID,
              status: "created"
            }
          }
        });

        await WalletCronService.syncAllDevengoWallets();
      });
      afterAll(async () => await clearDb());

      it("should call Devengo", async () => {
        expect(DevengoService.Instance.getAccount).toHaveBeenCalledWith(ACCOUNT_ID);
      });

      it("should update the wallet document", async () => {
        const updatedWallet = await Wallet.findById(wallet.id);
        expect(updatedWallet.toObject()).toEqual(
          expect.objectContaining({
            iban: IBAN,
            providers: {
              devengo: { id: ACCOUNT_ID, status: "active" }
            }
          })
        );
      });
    });
  });
});
