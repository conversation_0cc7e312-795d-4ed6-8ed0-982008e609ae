import { CurrencyEnum } from "../../../external-services/wealthkernelService";
import { Order } from "../../../models/Order";
import {
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument
} from "../../../models/Transaction";
import { closeDb, connectDb, clearDb } from "../../../tests/utils/db";
import {
  buildPortfolio,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildUser
} from "../../../tests/utils/generateModels";
import OrderCronService from "../orderCronService";

describe("OrderCronService", () => {
  beforeAll(async () => await connectDb("OrderCronService"));
  afterAll(async () => await closeDb());

  describe("createSavingsOrderDocuments", () => {
    describe("when a Pending savings topup exists", () => {
      const TODAY = new Date("2024-05-09T10:00:00+01:00"); // Inside order submission hours
      const ORDER_AMOUNT = 1000;
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: CurrencyEnum.GBP,
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );

        await OrderCronService.createSavingsOrderDocuments();
      });
      afterAll(async () => {
        jest.resetAllMocks();
        await clearDb();
      });

      it("should create only one savings order", async () => {
        const orders = await Order.find({ transaction: savingsTopup.id });

        expect(orders.length).toEqual(1);
        expect(orders[0]).toMatchObject({
          transaction: savingsTopup._id,
          commonId: savingsTopup.savingsProduct,
          side: "Buy",
          consideration: expect.objectContaining({
            currency: CurrencyEnum.GBP,
            amount: ORDER_AMOUNT,
            originalAmount: ORDER_AMOUNT
          })
        });
      });
    });

    describe("when a Pending savings topup exists outside of submission hours", () => {
      const TODAY = new Date("2024-05-09T12:00:00+01:00"); // Outside of order submission hours
      const ORDER_AMOUNT = 1000;
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: CurrencyEnum.GBP,
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );

        await OrderCronService.createSavingsOrderDocuments();
      });
      afterAll(async () => {
        jest.resetAllMocks();
        await clearDb();
      });

      it("should not create any orders", async () => {
        const orders = await Order.find({ transaction: savingsTopup.id });

        expect(orders.length).toEqual(0);
      });
    });

    describe("when a Settled savings topup exists", () => {
      const TODAY = new Date("2024-05-09T10:00:00+01:00"); // Inside order submission hours
      const ORDER_AMOUNT = 1000;
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsTopup = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: ORDER_AMOUNT
          },
          status: "Settled",
          settledAt: new Date()
        });

        await OrderCronService.createSavingsOrderDocuments();
      });
      afterAll(async () => {
        jest.resetAllMocks();
        await clearDb();
      });

      it("should not create any new order", async () => {
        const orders = await Order.find({ transaction: savingsTopup.id });

        expect(orders.length).toEqual(1);
        expect(orders[0]).toMatchObject({
          _id: savingsTopup.orders[0]._id
        });
      });
    });

    describe("when a Pending savings withdrawal exists", () => {
      const TODAY = new Date("2024-05-09T10:00:00+01:00"); // Inside order submission hours
      const ORDER_AMOUNT = 1000;
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: CurrencyEnum.GBP,
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );

        await OrderCronService.createSavingsOrderDocuments();
      });
      afterAll(async () => {
        jest.resetAllMocks();
        await clearDb();
      });

      it("should create only one savings order", async () => {
        const orders = await Order.find({ transaction: savingsWithdrawal.id });

        expect(orders.length).toEqual(1);
        expect(orders[0]).toMatchObject({
          transaction: savingsWithdrawal._id,
          commonId: savingsWithdrawal.savingsProduct,
          side: "Sell",
          consideration: expect.objectContaining({
            currency: CurrencyEnum.GBP,
            amount: ORDER_AMOUNT,
            originalAmount: ORDER_AMOUNT
          })
        });
      });
    });

    describe("when a Pending savings withdrawal exists outside of submission hours", () => {
      const TODAY = new Date("2024-05-09T12:00:00+01:00"); // Outside of order submission hours
      const ORDER_AMOUNT = 1000;
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: CurrencyEnum.GBP,
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );

        await OrderCronService.createSavingsOrderDocuments();
      });
      afterAll(async () => {
        jest.resetAllMocks();
        await clearDb();
      });

      it("should not create any order", async () => {
        const orders = await Order.find({ transaction: savingsWithdrawal.id });

        expect(orders.length).toEqual(0);
      });
    });

    describe("when a PendingTopUp savings withdrawal exists", () => {
      const TODAY = new Date("2024-05-09T10:00:00+01:00"); // Inside order submission hours
      const ORDER_AMOUNT = 1000;
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: CurrencyEnum.GBP,
              amount: ORDER_AMOUNT
            },
            status: "PendingTopUp"
          },
          {},
          false
        );

        await OrderCronService.createSavingsOrderDocuments();
      });
      afterAll(async () => {
        jest.resetAllMocks();
        await clearDb();
      });

      it("should not create any order", async () => {
        const orders = await Order.find({ transaction: savingsWithdrawal.id });

        expect(orders.length).toEqual(0);
      });
    });

    describe("when a Settled savings withdrawal exists", () => {
      const TODAY = new Date("2024-05-09T10:00:00+01:00"); // Inside order submission hours
      const ORDER_AMOUNT = 1000;
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsWithdrawal = await buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: ORDER_AMOUNT
          },
          status: "Settled",
          settledAt: new Date()
        });

        await OrderCronService.createSavingsOrderDocuments();
      });
      afterAll(async () => {
        jest.resetAllMocks();
        await clearDb();
      });

      it("should not create any new order", async () => {
        const orders = await Order.find({ transaction: savingsWithdrawal.id });

        expect(orders.length).toEqual(1);
        expect(orders[0]).toMatchObject({
          _id: savingsWithdrawal.orders[0]._id
        });
      });
    });

    describe("when the cron method runs twice", () => {
      const TODAY = new Date("2024-05-09T10:00:00+01:00"); // Inside order submission hours
      const ORDER_AMOUNT = 1000;
      let savingsTopup: SavingsTopupTransactionDocument;
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.getTime());
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: CurrencyEnum.GBP,
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );
        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: CurrencyEnum.GBP,
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );

        await OrderCronService.createSavingsOrderDocuments();
        await OrderCronService.createSavingsOrderDocuments();
      });

      it("should create only one savings buy order", async () => {
        const orders = await Order.find({ transaction: savingsTopup.id });

        expect(orders.length).toEqual(1);
        expect(orders[0]).toMatchObject({
          transaction: savingsTopup._id,
          commonId: savingsTopup.savingsProduct,
          side: "Buy",
          consideration: expect.objectContaining({
            currency: CurrencyEnum.GBP,
            amount: ORDER_AMOUNT,
            originalAmount: ORDER_AMOUNT
          })
        });
      });

      it("should create only one savings sell order", async () => {
        const orders = await Order.find({ transaction: savingsWithdrawal.id });

        expect(orders.length).toEqual(1);
        expect(orders[0]).toMatchObject({
          transaction: savingsWithdrawal._id,
          commonId: savingsWithdrawal.savingsProduct,
          side: "Sell",
          consideration: expect.objectContaining({
            currency: CurrencyEnum.GBP,
            amount: ORDER_AMOUNT,
            originalAmount: ORDER_AMOUNT
          })
        });
      });
    });
  });
});
