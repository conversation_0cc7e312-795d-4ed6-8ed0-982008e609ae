import { faker } from "@faker-js/faker";
import { GoCardlessPaymentsService } from "../../../external-services/goCardlessPaymentsService";
import { BankAccountDocument, BankAccount } from "../../../models/BankAccount";
import { UserDocument } from "../../../models/User";
import { connectDb, closeDb, clearDb } from "../../../tests/utils/db";
import { buildUser, buildBankAccount } from "../../../tests/utils/generateModels";
import DateUtil from "../../../utils/dateUtil";
import BankAccountCronService from "../bankAccountCronService";

describe("BankAccountCronService", () => {
  beforeAll(async () => await connectDb("BankAccountCronService"));
  afterAll(async () => await closeDb());

  describe("createAllGoCardlessBankAccounts", () => {
    describe("when bank account owner does not have a GoCardless customer", () => {
      let user: UserDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createCustomerBankAccount");

        user = await buildUser({}, false);
        bankAccount = await buildBankAccount({ owner: user, createdAt: DateUtil.getDateOfMinutesAgo(15) });

        await BankAccountCronService.createAllGoCardlessBankAccounts();
      });
      afterAll(async () => await clearDb());

      it("should not call GoCardless", async () => {
        expect(GoCardlessPaymentsService.Instance.createCustomerBankAccount).not.toHaveBeenCalled();
      });

      it("should not update the bank account document", async () => {
        const updatedBankAccount = await BankAccount.findById(bankAccount.id);

        expect(updatedBankAccount?.providers?.gocardless?.id).toBe(undefined);
      });
    });

    describe("when bank account has been created < 10 minutes ago", () => {
      let user: UserDocument;

      const GOCARDLESS_ID = "BA123";
      const IBAN = faker.finance.iban();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest
          .spyOn(GoCardlessPaymentsService.Instance, "createCustomerBankAccount")
          .mockResolvedValue({ id: GOCARDLESS_ID });

        user = await buildUser({ providers: { gocardless: { id: "CU123" } } }, false);

        await buildBankAccount({
          iban: IBAN,
          owner: user,
          providers: { wealthkernel: { id: faker.string.uuid() } },
          createdAt: DateUtil.getDateOfMinutesAgo(5)
        });

        await BankAccountCronService.createAllGoCardlessBankAccounts();
      });
      afterAll(async () => await clearDb());

      it("should NOT call GoCardless to create the bank account entity", async () => {
        expect(GoCardlessPaymentsService.Instance.createCustomerBankAccount).not.toHaveBeenCalled();
      });
    });

    describe("when eligible bank account has not been created in GoCardless", () => {
      let user: UserDocument;
      let bankAccount: BankAccountDocument;

      const GOCARDLESS_ID = "BA123";
      const IBAN = faker.finance.iban();
      const BANK_ACCOUNT_NAME = faker.finance.accountName();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest
          .spyOn(GoCardlessPaymentsService.Instance, "createCustomerBankAccount")
          .mockResolvedValue({ id: GOCARDLESS_ID });

        user = await buildUser({ providers: { gocardless: { id: "CU123" } } }, false);
        bankAccount = await buildBankAccount({
          name: BANK_ACCOUNT_NAME,
          iban: IBAN,
          owner: user,
          providers: { wealthkernel: { id: faker.string.uuid() } },
          createdAt: DateUtil.getDateOfMinutesAgo(15)
        });

        await BankAccountCronService.createAllGoCardlessBankAccounts();
      });
      afterAll(async () => await clearDb());

      it("should call GoCardless to create the bank account entity", async () => {
        expect(GoCardlessPaymentsService.Instance.createCustomerBankAccount).toHaveBeenCalledTimes(1);
        expect(GoCardlessPaymentsService.Instance.createCustomerBankAccount).toHaveBeenCalledWith({
          account_holder_name: BANK_ACCOUNT_NAME,
          iban: IBAN,
          metadata: {
            wealthyhoodId: bankAccount.id
          },
          links: {
            customer: user?.providers?.gocardless?.id
          }
        });
      });

      it("should update the bank account document to have the new GoCardless ID", async () => {
        const updatedBankAccount = await BankAccount.findById(bankAccount.id);
        expect(updatedBankAccount?.toObject()).toEqual(
          expect.objectContaining({
            providers: {
              wealthkernel: updatedBankAccount?.providers?.wealthkernel,
              gocardless: { id: GOCARDLESS_ID }
            }
          })
        );
      });
    });

    describe("when bank account has already been created in GoCardless", () => {
      let user: UserDocument;
      let bankAccount: BankAccountDocument;

      const goCardlessID = "BA123";

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createCustomerBankAccount");

        user = await buildUser({ providers: { gocardless: { id: "CU123" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user,
          providers: { gocardless: { id: goCardlessID } },
          createdAt: DateUtil.getDateOfMinutesAgo(15)
        });

        await BankAccountCronService.createAllGoCardlessBankAccounts();
      });
      afterAll(async () => await clearDb());

      it("should not call GoCardless", async () => {
        expect(GoCardlessPaymentsService.Instance.createCustomerBankAccount).not.toHaveBeenCalled();
      });

      it("should not update the bank account document", async () => {
        const updatedBankAccount = await BankAccount.findById(bankAccount.id);
        expect(updatedBankAccount?.providers?.gocardless?.id).toBe(goCardlessID);
      });
    });
  });
});
