import { faker } from "@faker-js/faker";
import { connectDb, closeDb, clearDb } from "../../../tests/utils/db";
import { buildContentEntry } from "../../../tests/utils/generateModels";
import DateUtil from "../../../utils/dateUtil";
import { buildFinimizeContentPiece } from "../../../tests/utils/generateFinimize";
import FinimizeService, { FinimizeContentTypeEnum } from "../../../external-services/finimizeService";
import {
  ContentEntry,
  ContentEntryCategoryEnum,
  ContentEntryContentTypeEnum,
  ContentEntryDocument
} from "../../../models/ContentEntry";
import { ContentIngestionCronService } from "../contentIngestionCronService";
import { ProviderEnum } from "../../../configs/providersConfig";
import ContentfulManagementService from "../../../external-services/contentfulManagementService";
import { buildContentfulContentEntryCreationResponse } from "../../../tests/utils/generateContentful";
import logger from "../../../external-services/loggerService";
import { removeMarkdownLinksFromString } from "../../../utils/stringUtil";
import eventEmitter from "../../../loaders/eventEmitter";
import events from "../../../event-handlers/events";

describe("ContentIngestionCronService", () => {
  beforeAll(async () => await connectDb("ContentIngestionCronService"));
  afterAll(async () => await closeDb());

  describe("createAnalystInsightContentEntries", () => {
    describe("when there are no new analyst insights to ingest", () => {
      const NOW = new Date("2024-10-11");
      let existingContentEntries: ContentEntryDocument[];

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        const finimizeContentPieces = [
          buildFinimizeContentPiece({
            publishedAt: DateUtil.getYesterday().toISOString(),
            title: "Nvidia's big bet on AI"
          }),
          buildFinimizeContentPiece({
            publishedAt: DateUtil.getYesterday().toISOString(),
            title: "Inflation peaks in Tokyo"
          })
        ];

        jest.spyOn(FinimizeService, "executeForContent").mockImplementation(async (data, callback) => {
          await callback(finimizeContentPieces);
        });

        existingContentEntries = await Promise.all(
          finimizeContentPieces.map((finimizeContentPiece) =>
            buildContentEntry({
              title: finimizeContentPiece.title,
              providers: {
                finimize: {
                  id: finimizeContentPiece.contentPieceId,
                  contentType: finimizeContentPiece.contentPieceTypeId,
                  publishedAt: new Date(finimizeContentPiece.publishedAt)
                },
                contentful: {
                  id: faker.string.uuid(),
                  spaceId: faker.string.uuid(),
                  environmentId: faker.string.uuid()
                }
              }
            })
          )
        );

        await ContentIngestionCronService.createAnalystInsightContentEntries();
      });
      afterAll(async () => await clearDb());

      it("should not ingest any new analyst insights", async () => {
        const contentEntries = await ContentEntry.find();

        expect(contentEntries).toHaveLength(2);
        expect(contentEntries).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: existingContentEntries[0].id
            }),
            expect.objectContaining({
              id: existingContentEntries[1].id
            })
          ])
        );
      });

      it("should call finimize with the correct parameters", async () => {
        expect(FinimizeService.executeForContent).toHaveBeenCalledWith(
          {
            types: [
              FinimizeContentTypeEnum.INSIGHT,
              FinimizeContentTypeEnum.WEEKLY_BRIEF,
              FinimizeContentTypeEnum.QUICK_TAKE
            ],
            publishedAfter: DateUtil.getDateOfDaysAgo(NOW, 5)
          },
          expect.any(Function)
        );
      });
    });

    describe("when there are new analyst insights to ingest", () => {
      const NOW = new Date("2024-10-11");
      const finimizeContentPieces = [
        buildFinimizeContentPiece({
          contentPieceTypeId: FinimizeContentTypeEnum.INSIGHT,
          publishedAt: new Date("2024-12-09").toISOString(),
          title: "Market Analysis: Tech Sector Outlook for 2025"
        }),
        buildFinimizeContentPiece({
          contentPieceTypeId: FinimizeContentTypeEnum.QUICK_TAKE,
          publishedAt: new Date("2024-12-10").toISOString(),
          title: "Breaking: Fed Announces Interest Rate Decision"
        }),
        buildFinimizeContentPiece({
          contentPieceTypeId: FinimizeContentTypeEnum.WEEKLY_BRIEF,
          publishedAt: new Date("2024-12-13").toISOString(),
          title: "Weekly Market Recap: Key Events and Trends"
        }),
        buildFinimizeContentPiece({
          contentPieceTypeId: FinimizeContentTypeEnum.WEEKLY_BRIEF,
          publishedAt: new Date("2024-12-15").toISOString(),
          title: "This Week in Markets: Global Economic Updates"
        }),
        buildFinimizeContentPiece({
          contentPieceTypeId: FinimizeContentTypeEnum.RESEARCH,
          publishedAt: new Date("2024-12-09").toISOString(),
          title: "Deep Dive: Emerging Market Opportunities"
        })
      ];

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());
        jest.spyOn(FinimizeService, "executeForContent").mockImplementation(async (data, callback) => {
          await callback(finimizeContentPieces);
        });

        await ContentIngestionCronService.createAnalystInsightContentEntries();
      });
      afterAll(async () => await clearDb());

      it("should ingest new analyst insights and create DB documents", async () => {
        const contentEntries = await ContentEntry.find();

        expect(contentEntries).toHaveLength(5);

        expect(contentEntries).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              contentType: ContentEntryContentTypeEnum.ANALYSIS,
              category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
              activeProviders: [ProviderEnum.FINIMIZE],
              shouldNotifyUsers: true,
              publishAt: new Date("2024-12-10T07:15:00.000Z"),
              providers: expect.objectContaining({
                finimize: expect.objectContaining({
                  id: finimizeContentPieces[4].contentPieceId,
                  publishedAt: new Date(finimizeContentPieces[4].publishedAt),
                  contentType: finimizeContentPieces[4].contentPieceTypeId
                })
              })
            }),
            expect.objectContaining({
              contentType: ContentEntryContentTypeEnum.ANALYSIS,
              category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
              activeProviders: [ProviderEnum.FINIMIZE],
              shouldNotifyUsers: true,
              publishAt: new Date("2024-12-10T07:15:00.000Z"),
              providers: expect.objectContaining({
                finimize: expect.objectContaining({
                  id: finimizeContentPieces[0].contentPieceId,
                  publishedAt: new Date(finimizeContentPieces[0].publishedAt),
                  contentType: finimizeContentPieces[0].contentPieceTypeId
                })
              })
            }),
            expect.objectContaining({
              contentType: ContentEntryContentTypeEnum.QUICK_TAKE,
              category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
              activeProviders: [ProviderEnum.FINIMIZE],
              shouldNotifyUsers: true,
              publishAt: new Date("2024-12-10T16:30:00.000Z"),
              providers: expect.objectContaining({
                finimize: expect.objectContaining({
                  id: finimizeContentPieces[1].contentPieceId,
                  publishedAt: new Date(finimizeContentPieces[1].publishedAt),
                  contentType: finimizeContentPieces[1].contentPieceTypeId
                })
              })
            }),
            expect.objectContaining({
              contentType: ContentEntryContentTypeEnum.WEEKLY_REVIEW,
              category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
              activeProviders: [ProviderEnum.FINIMIZE],
              shouldNotifyUsers: true,
              publishAt: new Date("2024-12-15T09:30:00.000Z"),
              providers: expect.objectContaining({
                finimize: expect.objectContaining({
                  id: finimizeContentPieces[2].contentPieceId,
                  publishedAt: new Date(finimizeContentPieces[2].publishedAt),
                  contentType: finimizeContentPieces[2].contentPieceTypeId
                })
              })
            }),
            expect.objectContaining({
              contentType: ContentEntryContentTypeEnum.WEEKLY_REVIEW,
              category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
              activeProviders: [ProviderEnum.FINIMIZE],
              shouldNotifyUsers: true,
              publishAt: new Date("2024-12-15T09:30:00.000Z"),
              providers: expect.objectContaining({
                finimize: expect.objectContaining({
                  id: finimizeContentPieces[3].contentPieceId,
                  publishedAt: new Date(finimizeContentPieces[3].publishedAt),
                  contentType: finimizeContentPieces[3].contentPieceTypeId
                })
              })
            })
          ])
        );
      });

      it("should call finimize with the correct parameters", async () => {
        expect(FinimizeService.executeForContent).toHaveBeenCalledWith(
          {
            types: [
              FinimizeContentTypeEnum.INSIGHT,
              FinimizeContentTypeEnum.WEEKLY_BRIEF,
              FinimizeContentTypeEnum.QUICK_TAKE
            ],
            publishedAfter: DateUtil.getDateOfDaysAgo(NOW, 5)
          },
          expect.any(Function)
        );
      });

      it("should not emit finimizeRefIdentified events", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.general.finimizeRefIdentified.eventId,
          expect.anything()
        );
      });
    });
  });

  describe("uploadAnalystInsightsToContentful", () => {
    describe("when there are no content entries to process", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date().getTime());

        jest.spyOn(FinimizeService, "executeForContent");
        jest.spyOn(ContentfulManagementService.LearnHubInstance, "createContentEntry");

        await ContentIngestionCronService.uploadAnalystInsightsToContentful();
      });
      afterAll(async () => await clearDb());

      it("should not call contentful or finimize", async () => {
        expect(FinimizeService.executeForContent).not.toHaveBeenCalled();
        expect(ContentfulManagementService.LearnHubInstance.createContentEntry).not.toHaveBeenCalled();
      });
    });

    describe("when there are content entries but they have already been uploaded to Contentful", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date().getTime());

        jest.spyOn(FinimizeService, "executeForContent");
        jest.spyOn(ContentfulManagementService.LearnHubInstance, "createContentEntry");

        await Promise.all([
          buildContentEntry({ title: "This Company’s Shares Are Up Nearly 200% This Year, Thanks To Bitcoin" }),
          buildContentEntry({ title: "This Company’s Shares Are Up Nearly 200% This Year, Thanks To Ethereum" })
        ]);

        await ContentIngestionCronService.uploadAnalystInsightsToContentful();
      });
      afterAll(async () => await clearDb());

      it("should not call contentful or finimize", async () => {
        expect(FinimizeService.executeForContent).not.toHaveBeenCalled();
        expect(ContentfulManagementService.LearnHubInstance.createContentEntry).not.toHaveBeenCalled();
      });

      it("should not create a new entry", async () => {
        const contentEntries = await ContentEntry.find();

        expect(contentEntries).toHaveLength(2);
      });
    });

    describe("when a content entry exists with no contentful provider data", () => {
      let contentEntry: ContentEntryDocument;
      const finimizeContentPiece = buildFinimizeContentPiece();
      const contentfulResponse = buildContentfulContentEntryCreationResponse();

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date().getTime());

        jest.spyOn(FinimizeService, "retrieveContentPiece").mockResolvedValue(finimizeContentPiece);
        jest
          .spyOn(ContentfulManagementService.LearnHubInstance, "createContentEntry")
          .mockResolvedValue(contentfulResponse);

        contentEntry = await buildContentEntry({
          providers: {
            finimize: {
              id: finimizeContentPiece.contentPieceId,
              contentType: finimizeContentPiece.contentPieceTypeId,
              publishedAt: new Date(finimizeContentPiece.publishedAt)
            }
          }
        });

        await ContentIngestionCronService.uploadAnalystInsightsToContentful();
      });
      afterAll(async () => await clearDb());

      it("should call contentful with finimize data", async () => {
        const contentMarkdown = finimizeContentPiece.blocks
          .filter((block) => block.type !== "quote")
          .map((block) => {
            if (block.type === "text") {
              return removeMarkdownLinksFromString(block.textMarkdown);
            } else if (block.type === "image") {
              return `![${block.caption}](${block.image.full})\n`;
            }
          })
          .join("\n");

        const wordCount = contentMarkdown.split(/\s+/).length;
        const readingTime = `${Math.ceil(wordCount / 200)} minutes`;

        expect(ContentfulManagementService.LearnHubInstance.createContentEntry).toHaveBeenCalledWith(
          expect.objectContaining({
            subtitle: finimizeContentPiece.subtitle,
            slug: finimizeContentPiece.slug,
            summary: finimizeContentPiece.summary,
            notificationBody: finimizeContentPiece.notificationBodyText,
            notificationTitle: finimizeContentPiece.notificationTitleText,
            headerImage: finimizeContentPiece.headerImage.full,
            bannerImage: finimizeContentPiece.headerImage.full,
            publishedAt: finimizeContentPiece.publishedAt,
            content: contentMarkdown,
            contentType: ContentEntryContentTypeEnum.ANALYSIS,
            category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
            readingTime: readingTime
          }),
          { publishAfterCreation: true }
        );
      });

      it("should create a new entry", async () => {
        const contentEntries = await ContentEntry.find();

        expect(contentEntries).toHaveLength(1);
        expect(contentEntries[0]).toEqual(
          expect.objectContaining({
            id: contentEntry.id,
            providers: expect.objectContaining({
              contentful: expect.objectContaining({
                id: contentfulResponse.id,
                spaceId: contentfulResponse.spaceId,
                environmentId: contentfulResponse.environmentId
              })
            })
          })
        );
      });
    });

    describe("when a content entry exists with no contentful provider data but the publishAt is 2 hours in the future", () => {
      const finimizeContentPiece = buildFinimizeContentPiece();
      const contentfulResponse = buildContentfulContentEntryCreationResponse();

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date().getTime());
        jest.spyOn(FinimizeService, "retrieveContentPiece").mockResolvedValue(finimizeContentPiece);
        jest
          .spyOn(ContentfulManagementService.LearnHubInstance, "createContentEntry")
          .mockResolvedValue(contentfulResponse);

        await buildContentEntry({
          publishAt: DateUtil.getDateOfMinutesFromNow(120),
          providers: {
            finimize: {
              id: finimizeContentPiece.contentPieceId,
              contentType: finimizeContentPiece.contentPieceTypeId,
              publishedAt: new Date(finimizeContentPiece.publishedAt)
            }
          }
        });

        await ContentIngestionCronService.uploadAnalystInsightsToContentful();
      });
      afterAll(async () => await clearDb());

      it("should not call contentful", async () => {
        expect(ContentfulManagementService.LearnHubInstance.createContentEntry).not.toHaveBeenCalled();
      });

      it("should not create a new entry", async () => {
        const contentEntries = await ContentEntry.find();

        expect(contentEntries).toHaveLength(1);
        expect(contentEntries[0].providers?.contentful?.id).toBeUndefined();
      });
    });

    describe("when a content entry exists with no contentful provider data but fails but is not found in finimize", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date().getTime());
        jest
          .spyOn(FinimizeService, "retrieveContentPiece")
          .mockRejectedValue(new Error("Finimize request failed"));

        await buildContentEntry({
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.INSIGHT,
              publishedAt: new Date()
            }
          }
        });

        await ContentIngestionCronService.uploadAnalystInsightsToContentful();
      });
      afterAll(async () => await clearDb());

      it("should log an error", async () => {
        expect(logger.error).toHaveBeenCalled();
      });
    });

    describe("when a content entry exists with no contentful provider data and contains references to Finimize", () => {
      const finimizeContentPiece = buildFinimizeContentPiece({
        title: "Analysis with Finimize mention",
        blocks: [
          {
            type: "text",
            textPlain: "Content mentioning Finimize here",
            textHtml: "<p>Content mentioning Finimize here</p>",
            textMarkdown: "Content mentioning [Finimize](https://finimize.com) here"
          }
        ]
      });
      let contentEntry: ContentEntryDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date().getTime());
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(FinimizeService, "retrieveContentPiece").mockResolvedValue(finimizeContentPiece);
        jest
          .spyOn(ContentfulManagementService.LearnHubInstance, "createContentEntry")
          .mockResolvedValue(buildContentfulContentEntryCreationResponse());

        contentEntry = await buildContentEntry({
          providers: {
            finimize: {
              id: finimizeContentPiece.contentPieceId,
              contentType: finimizeContentPiece.contentPieceTypeId,
              publishedAt: new Date(finimizeContentPiece.publishedAt)
            }
          }
        });

        await ContentIngestionCronService.uploadAnalystInsightsToContentful();
      });
      afterAll(async () => await clearDb());

      it("should emit finimizeRefIdentified event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(events.general.finimizeRefIdentified.eventId, {
          contentPieceId: finimizeContentPiece.contentPieceId,
          title: finimizeContentPiece.title
        });
      });

      it("should update the content entry with contentful provider data", async () => {
        const updatedEntry = await ContentEntry.findById(contentEntry.id);
        expect(updatedEntry?.providers?.contentful?.id).toBeDefined();
        expect(updatedEntry?.providers?.contentful?.spaceId).toBeDefined();
        expect(updatedEntry?.providers?.contentful?.environmentId).toBeDefined();
      });
    });
  });
});
