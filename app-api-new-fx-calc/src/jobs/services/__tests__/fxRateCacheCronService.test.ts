import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import eodService from "../../../external-services/eodService";
import { RedisClientService } from "../../../loaders/redis";
import FXRateCacheCronService from "../fxRateCacheCronService";

describe("FXRateCacheCronService", () => {
  beforeAll(async () => await connectDb("AssetDiscoveryDataCronService"));
  afterAll(async () => await closeDb());

  describe("updateFXRateCache", () => {
    describe("when fetching FX rates from EOD succeeds", () => {
      const MOCK_RATES = {
        USD: {
          GBP: 0.7917,
          EUR: 0.9266,
          USD: 1
        },
        EUR: {
          GBP: 0.8542,
          USD: 1.0792,
          EUR: 1
        },
        GBP: {
          EUR: 1.1704,
          USD: 1.2631,
          GBP: 1
        }
      };

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eodService, "getLatestFXRates").mockResolvedValue(MOCK_RATES);

        await FXRateCacheCronService.updateFXRateCache();
      });
      afterAll(async () => await clearDb());

      it("should store the FX rates in Redis", async () => {
        const cachedRates = await RedisClientService.Instance.get("fxRates");
        expect(cachedRates).toEqual(expect.objectContaining(MOCK_RATES));
      });
    });

    describe("when cron task is ran twice and EOD succeeds both times", () => {
      const MOCK_RATES_FIRST_RUN = {
        USD: {
          GBP: 0.7917,
          EUR: 0.9266,
          USD: 1
        },
        EUR: {
          GBP: 0.8542,
          USD: 1.0792,
          EUR: 1
        },
        GBP: {
          EUR: 1.1704,
          USD: 1.2631,
          GBP: 1
        }
      };
      const MOCK_RATES_SECOND_RUN = {
        USD: {
          GBP: 0.8172,
          EUR: 0.9333,
          USD: 1
        },
        EUR: {
          GBP: 0.8555,
          USD: 1.0798,
          EUR: 1
        },
        GBP: {
          EUR: 1.1798,
          USD: 1.3002,
          GBP: 1
        }
      };

      beforeAll(async () => {
        jest.resetAllMocks();
      });
      afterAll(async () => await clearDb());

      it("should have overwritten the FX rates in Redis", async () => {
        jest.spyOn(eodService, "getLatestFXRates").mockResolvedValue(MOCK_RATES_FIRST_RUN);

        await FXRateCacheCronService.updateFXRateCache();

        const cachedRatesFirstTime = await RedisClientService.Instance.get("fxRates");
        expect(cachedRatesFirstTime).toEqual(expect.objectContaining(MOCK_RATES_FIRST_RUN));

        // Then the method is called again!
        jest.spyOn(eodService, "getLatestFXRates").mockResolvedValue(MOCK_RATES_SECOND_RUN);

        await FXRateCacheCronService.updateFXRateCache();

        const cachedRatesSecondTime = await RedisClientService.Instance.get("fxRates");
        expect(cachedRatesSecondTime).toEqual(expect.objectContaining(MOCK_RATES_SECOND_RUN));
      });
    });
  });
});
