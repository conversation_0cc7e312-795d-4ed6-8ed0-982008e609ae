import { faker } from "@faker-js/faker";
import { Account } from "../../../models/Account";
import AccountCronService from "../accountCronService";
import DateUtil from "../../../utils/dateUtil";
import { countriesConfig } from "@wealthyhood/shared-configs";
import { buildUser, buildAccount } from "../../../tests/utils/generateModels";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { ProviderEnum } from "../../../configs/providersConfig";
import { WealthkernelService, AccountType } from "../../../external-services/wealthkernelService";
import { buildWealthkernelAccountResponse } from "../../../tests/utils/generateWealthkernel";

describe("AccountCronService", () => {
  beforeAll(async () => await connectDb("AccountCronService"));
  afterAll(async () => await closeDb());

  describe("createAllWkAccounts", () => {
    beforeEach(async () => {
      await clearDb();
      jest.resetAllMocks();
    });

    it("should create WK accounts for eligible users", async () => {
      // Create test user
      const user = await buildUser({
        providers: { wealthkernel: { id: faker.string.uuid() } },
        nationalities: ["GB"],
        submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(15),
        dateOfBirth: DateUtil.getDateOfYearsAgo(new Date(), 25)
      });

      // Create test account
      const account = await buildAccount({
        owner: user._id,
        activeProviders: [ProviderEnum.WEALTHKERNEL]
      });

      const wkAccountId = faker.string.uuid();

      // Mock WealthKernel service
      jest
        .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
        .mockImplementation(async (): Promise<AccountType[]> => {
          return [];
        });

      jest
        .spyOn(WealthkernelService.UKInstance, "createAccount")
        .mockImplementation(async (): Promise<AccountType> => {
          return buildWealthkernelAccountResponse({
            id: wkAccountId,
            status: "Pending"
          });
        });

      // Execute
      await AccountCronService.createAllWkAccounts();

      // Verify
      const updatedAccount = await Account.findById(account._id);
      expect(updatedAccount?.providers?.wealthkernel?.id).toBe(wkAccountId);
      expect(updatedAccount?.providers?.wealthkernel?.status).toBe("Pending");
    });

    it("should not create WK accounts for users with forbidden nationalities", async () => {
      // Create test user with forbidden nationality
      const user = await buildUser({
        providers: { wealthkernel: { id: faker.string.uuid() } },
        nationalities: [countriesConfig.forbiddenNationalitiesArray[0]],
        submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(15),
        dateOfBirth: DateUtil.getDateOfYearsAgo(new Date(), 25)
      });

      // Create test account
      const account = await buildAccount({
        owner: user._id,
        activeProviders: [ProviderEnum.WEALTHKERNEL]
      });

      // Mock WealthKernel service
      jest
        .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
        .mockImplementation(async (): Promise<AccountType[]> => {
          return [];
        });

      const createAccountMock = jest
        .spyOn(WealthkernelService.UKInstance, "createAccount")
        .mockImplementation(async (): Promise<AccountType> => {
          return buildWealthkernelAccountResponse();
        });

      // Execute
      await AccountCronService.createAllWkAccounts();

      // Verify
      const updatedAccount = await Account.findById(account._id);
      expect(updatedAccount?.providers?.wealthkernel?.id).toBeUndefined();
      expect(createAccountMock).not.toHaveBeenCalled();
    });

    it("should not create WK accounts for users who submitted required info less than 10 minutes ago", async () => {
      // Create test user
      const user = await buildUser({
        providers: { wealthkernel: { id: faker.string.uuid() } },
        nationalities: ["GB"],
        submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5),
        dateOfBirth: DateUtil.getDateOfYearsAgo(new Date(), 25)
      });

      // Create test account
      const account = await buildAccount({
        owner: user._id,
        activeProviders: [ProviderEnum.WEALTHKERNEL]
      });

      // Mock WealthKernel service
      jest
        .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
        .mockImplementation(async (): Promise<AccountType[]> => {
          return [];
        });

      const createAccountMock = jest
        .spyOn(WealthkernelService.UKInstance, "createAccount")
        .mockImplementation(async (): Promise<AccountType> => {
          return buildWealthkernelAccountResponse();
        });

      // Execute
      await AccountCronService.createAllWkAccounts();

      // Verify
      const updatedAccount = await Account.findById(account._id);
      expect(updatedAccount?.providers?.wealthkernel?.id).toBeUndefined();
      expect(createAccountMock).not.toHaveBeenCalled();
    });

    it("should not create WK accounts for accounts updated more than 30 days ago", async () => {
      // Create test user
      const user = await buildUser({
        providers: { wealthkernel: { id: faker.string.uuid() } },
        nationalities: ["GB"],
        submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(15),
        dateOfBirth: DateUtil.getDateOfYearsAgo(new Date(), 25)
      });

      // Create test account with old updatedAt date
      const account = await buildAccount({
        owner: user._id,
        activeProviders: [ProviderEnum.WEALTHKERNEL]
      });

      // Mock WealthKernel service
      jest
        .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
        .mockImplementation(async (): Promise<AccountType[]> => {
          return [];
        });

      const createAccountMock = jest
        .spyOn(WealthkernelService.UKInstance, "createAccount")
        .mockImplementation(async (): Promise<AccountType> => {
          return buildWealthkernelAccountResponse();
        });

      // Manually update the updatedAt field to be 2 months ago
      // Directly setting updatedAt won't work because it's a schema timestamp that Mongoose manages.
      await Account.findByIdAndUpdate(
        account._id,
        {
          $set: { updatedAt: DateUtil.getDateOfMonthsAgo(new Date(), 2) }
        },
        { timestamps: false }
      );

      // Execute
      await AccountCronService.createAllWkAccounts();

      // Verify
      const updatedAccount = await Account.findById(account._id);
      expect(updatedAccount?.providers?.wealthkernel?.id).toBeUndefined();
      expect(createAccountMock).not.toHaveBeenCalled();
    });
  });
});
