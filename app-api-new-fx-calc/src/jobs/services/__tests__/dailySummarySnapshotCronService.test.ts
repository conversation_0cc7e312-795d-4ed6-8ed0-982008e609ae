import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildDailyPortfolioSavingsTicker,
  buildDailySummarySnapshot,
  buildDepositCashTransaction,
  buildHoldingDTO,
  buildIntraDayPortfolioTicker,
  buildInvestmentProduct,
  buildPortfolio,
  buildSavingsProduct,
  buildSavingsTopup,
  buildSubscription,
  buildUser
} from "../../../tests/utils/generateModels";
import DailySummarySnapshotCronService from "../dailySummarySnapshotCronService";
import {
  DailySummarySnapshot,
  DailySummarySnapshotDocument,
  IndividualSentimentScoreComponentEnum,
  TotalSentimentScoreComponentEnum
} from "../../../models/DailySummarySnapshot";
import { UserDocument } from "../../../models/User";
import { RedisClientService } from "../../../loaders/redis";
import DateUtil from "../../../utils/dateUtil";
import Decimal from "decimal.js";
import eodService from "../../../external-services/eodService";
import { buildStockFundamentalsResponse } from "../../../tests/utils/generateEod";
import { DateTime } from "luxon";
import logger from "../../../external-services/loggerService";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import ConfigCatService from "../../../external-services/configCatService";

describe("DailySummarySnapshotCronService", () => {
  beforeAll(async () => await connectDb("DailySummarySnapshotCronService"));
  afterAll(async () => await closeDb());

  describe("createDailySummarySnapshotsForConvertedUsers", () => {
    describe("when there are no users to create snapshots for", () => {
      beforeAll(async () => {
        jest.clearAllMocks();

        await Promise.all([
          buildUser({ portfolioConversionStatus: "notStarted" }),
          buildUser({ portfolioConversionStatus: "inProgress" }),
          buildUser({ email: "<EMAIL>", kycStatus: "passed" })
        ]);

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });
      afterAll(async () => await clearDb());

      it("should not create any daily summary snapshots", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(0);
      });
    });

    describe("when there is an invested user and we have already recorded a snapshot for them for that day", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });

        await Promise.all([
          buildPortfolio({
            owner: user.id
          }),
          buildDailySummarySnapshot({
            metadata: {
              owner: user.id
            },
            date: DateUtil.getDateOfHoursAgo(TODAY, 1)
          })
        ]);

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });
      afterAll(async () => await clearDb());

      it("should NOT create a daily summary snapshot", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
      });
    });

    describe("when there is a fully withdrawn user without cash/savings", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });
        await buildSubscription({ owner: user.id });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: []
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 0 }
        });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });
      afterAll(async () => await clearDb());

      it("should create a daily summary snapshot", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            metadata: {
              owner: user._id
            },
            portfolio: {
              cash: {
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              savings: {
                value: {
                  amount: 0,
                  currency: "GBP"
                },
                unrealisedInterest: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              holdings: {
                assets: [],
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              total: {
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            }
          })
        );
      });
    });

    describe("when there is a fully withdrawn user with cash", () => {
      let user: UserDocument;

      const CASH = 10;

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: {
            GBP: {
              available: CASH,
              reserved: 0,
              settled: CASH
            }
          }
        });

        await buildSubscription({ owner: user.id });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 0 }
        });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });
      afterAll(async () => await clearDb());

      it("should create a daily summary snapshot", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            metadata: {
              owner: user._id
            },
            portfolio: {
              cash: {
                value: {
                  amount: CASH,
                  currency: "GBP"
                }
              },
              savings: {
                value: {
                  amount: 0,
                  currency: "GBP"
                },
                unrealisedInterest: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              holdings: {
                assets: [],
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              total: {
                value: {
                  amount: CASH,
                  currency: "GBP"
                }
              }
            }
          })
        );
      });
    });

    describe("when there is a fully withdrawn user with savings", () => {
      let user: UserDocument;

      const SAVINGS_AMOUNT = 500; // In GBX

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });

        const savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        // The user has gathered £0.03 in daily accruals (3 days, £0.01 each).
        await Promise.all([
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2022-08-01"),
            dailyAccrual: 1
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2022-08-02"),
            dailyAccrual: 1
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2022-08-03"),
            dailyAccrual: 1
          }),
          buildSubscription({ owner: user.id }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: new Date(),
            pricePerCurrency: { GBP: 0 }
          })
        ]);

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });
      afterAll(async () => await clearDb());

      it("should create a daily summary snapshot", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            metadata: {
              owner: user._id
            },
            portfolio: {
              cash: {
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              savings: {
                value: {
                  amount: Decimal.div(SAVINGS_AMOUNT, 100).toNumber(),
                  currency: "GBP"
                },
                unrealisedInterest: {
                  amount: 0.03,
                  currency: "GBP"
                }
              },
              holdings: {
                assets: [],
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              total: {
                value: {
                  amount: Decimal.div(SAVINGS_AMOUNT, 100).toNumber(),
                  currency: "GBP"
                }
              }
            }
          })
        );
      });
    });

    describe("when there is an invested user", () => {
      let user: UserDocument;

      const PORTFOLIO_VALUE_AT_START_OF_DAY = 99;
      const PORTFOLIO_VALUE_NOW = 100;

      const APPLE_PRICE = 60;
      const MICROSOFT_PRICE = 40;
      const APPLE_DAILY_RETURNS = 0.01;
      const MICROSOFT_DAILY_RETURNS = 0.02;

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([
            buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: APPLE_PRICE },
              { dailyReturnPercentage: APPLE_DAILY_RETURNS }
            ),
            buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: MICROSOFT_PRICE },
              { dailyReturnPercentage: MICROSOFT_DAILY_RETURNS }
            )
          ])
        });

        await Promise.all([
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.25),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.25),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.25)
        ]);

        await Promise.all([
          buildSubscription({ owner: user.id }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: DateUtil.getStartOfDay(TODAY),
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_AT_START_OF_DAY }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: TODAY,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_NOW }
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" })
        ]);

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });
      afterAll(
        async () =>
          await Promise.all([
            RedisClientService.Instance.del("sentiment_scores:equities_apple:analyst"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:price_momentum"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:news"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:analyst"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:price_momentum"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:news"),
            clearDb()
          ])
      );

      it("should create a daily summary snapshot", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            metadata: {
              owner: user._id
            },
            portfolio: {
              cash: {
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              savings: {
                value: {
                  amount: 0,
                  currency: "GBP"
                },
                unrealisedInterest: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              holdings: {
                assets: [
                  {
                    assetId: "equities_apple",
                    dailyReturnPercentage: APPLE_DAILY_RETURNS,
                    holdingWeightPercentage: 0.6,
                    latestPrice: {
                      amount: APPLE_PRICE,
                      currency: "GBP"
                    },
                    quantity: 1
                  },
                  {
                    assetId: "equities_microsoft",
                    dailyReturnPercentage: MICROSOFT_DAILY_RETURNS,
                    holdingWeightPercentage: 0.4,
                    latestPrice: {
                      amount: MICROSOFT_PRICE,
                      currency: "GBP"
                    },
                    quantity: 1
                  }
                ],
                value: {
                  amount: PORTFOLIO_VALUE_NOW,
                  currency: "GBP"
                },
                dailyReturnPercentage: expect.closeTo(0.01010101010101006, 3), // Due to MWRR this is an approximation
                dailyUpBy: 1
              },
              total: {
                value: {
                  amount: PORTFOLIO_VALUE_NOW,
                  currency: "GBP"
                }
              }
            },
            sentimentScore: {
              [IndividualSentimentScoreComponentEnum.ANALYST]: 0.55,
              [IndividualSentimentScoreComponentEnum.NEWS]: 0.55,
              [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.55,
              [TotalSentimentScoreComponentEnum.TOTAL]: 0.55
            }
          })
        );
      });
    });

    describe("when there is an invested user with different sentiment score component weights", () => {
      let user: UserDocument;

      const PORTFOLIO_VALUE = 100;
      const APPLE_PRICE = 60;
      const MICROSOFT_PRICE = 40;

      const APPLE_SENTIMENT = {
        news: 0.9,
        analyst: 0.8,
        price_momentum: 0.7
      };
      const MICROSOFT_SENTIMENT = {
        news: 0.3,
        analyst: 0.2,
        price_momentum: 0.1
      };

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([
            buildHoldingDTO(true, "equities_apple", 1, { price: APPLE_PRICE }, { dailyReturnPercentage: 0 }),
            buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: MICROSOFT_PRICE },
              { dailyReturnPercentage: 0 }
            )
          ])
        });

        await Promise.all([
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", APPLE_SENTIMENT.analyst),
          RedisClientService.Instance.set(
            "sentiment_scores:equities_apple:price_momentum",
            APPLE_SENTIMENT.price_momentum
          ),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", APPLE_SENTIMENT.news),
          RedisClientService.Instance.set(
            "sentiment_scores:equities_microsoft:analyst",
            MICROSOFT_SENTIMENT.analyst
          ),
          RedisClientService.Instance.set(
            "sentiment_scores:equities_microsoft:price_momentum",
            MICROSOFT_SENTIMENT.price_momentum
          ),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", MICROSOFT_SENTIMENT.news)
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: TODAY,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE }
        });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        await buildSubscription({ owner: user.id });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });

      afterAll(
        async () =>
          await Promise.all([
            RedisClientService.Instance.del("sentiment_scores:equities_apple:analyst"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:price_momentum"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:news"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:analyst"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:price_momentum"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:news"),
            clearDb()
          ])
      );

      it("should calculate the correct sentiment score", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);

        const sentimentScore = dailySummarySnapshots[0].sentimentScore;

        // Apple has 60% weight, Microsoft has 40% weight
        // For each component:
        // Apple component * 0.6 + Microsoft component * 0.4
        expect(sentimentScore[IndividualSentimentScoreComponentEnum.NEWS]).toBe(0.66);
        expect(sentimentScore[IndividualSentimentScoreComponentEnum.ANALYST]).toBe(0.56);
        expect(sentimentScore[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]).toBe(0.46);

        // Total score = news * 0.3 + analyst * 0.2 + momentum * 0.5
        expect(sentimentScore[TotalSentimentScoreComponentEnum.TOTAL]).toBeCloseTo(0.54);
      });
    });

    describe("when there is an invested user with disabled price momentum sentiment and only ETFs in their portfolio", () => {
      let user: UserDocument;

      const PORTFOLIO_VALUE_YESTERDAY = 50;
      const PORTFOLIO_VALUE_TODAY = 100;

      const APPLE_PRICE = 60;
      const APPLE_DAILY_RETURNS = 0.01;

      beforeAll(async () => {
        jest.clearAllMocks();

        jest.spyOn(ConfigCatService, "isPriceMomentumSentimentEnabled").mockReturnValue(false);

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({
          portfolioConversionStatus: "completed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([
            buildHoldingDTO(
              true,
              "equities_us",
              1,
              { price: APPLE_PRICE },
              { dailyReturnPercentage: APPLE_DAILY_RETURNS }
            )
          ])
        });

        await Promise.all([RedisClientService.Instance.set("sentiment_scores:equities_us:price_momentum", 0.75)]);

        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: DateUtil.getDateOfDaysAgo(TODAY, 1),
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_YESTERDAY }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: TODAY,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_TODAY }
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }),
          buildSubscription({ owner: user.id })
        ]);

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });
      afterAll(
        async () =>
          await Promise.all([
            RedisClientService.Instance.del("sentiment_scores:equities_us:price_momentum"),
            clearDb()
          ])
      );

      it("should create a daily summary snapshot without sentiment score", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.not.objectContaining({
            sentimentScore: expect.anything()
          })
        );
      });
    });

    describe("when there is an invested user with disabled price momentum sentiment and only stocks in their portfolio", () => {
      let user: UserDocument;

      const PORTFOLIO_VALUE_YESTERDAY = 50;
      const PORTFOLIO_VALUE_TODAY = 100;

      const APPLE_PRICE = 60;
      const MICROSOFT_PRICE = 40;
      const APPLE_DAILY_RETURNS = 0.01;
      const MICROSOFT_DAILY_RETURNS = 0.02;

      beforeAll(async () => {
        jest.clearAllMocks();

        jest.spyOn(ConfigCatService, "isPriceMomentumSentimentEnabled").mockReturnValue(false);

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({
          portfolioConversionStatus: "completed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([
            buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: APPLE_PRICE },
              { dailyReturnPercentage: APPLE_DAILY_RETURNS }
            ),
            buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: MICROSOFT_PRICE },
              { dailyReturnPercentage: MICROSOFT_DAILY_RETURNS }
            )
          ])
        });

        await Promise.all([
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.25),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.25),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.25)
        ]);

        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: DateUtil.getDateOfDaysAgo(TODAY, 1),
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_YESTERDAY }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: TODAY,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_TODAY }
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }),
          buildSubscription({ owner: user.id })
        ]);

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });
      afterAll(
        async () =>
          await Promise.all([
            RedisClientService.Instance.del("sentiment_scores:equities_apple:analyst"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:price_momentum"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:news"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:analyst"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:price_momentum"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:news"),
            clearDb()
          ])
      );

      it("should create a daily summary snapshot with no price momentum", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            sentimentScore: {
              [IndividualSentimentScoreComponentEnum.ANALYST]: 0.55,
              [IndividualSentimentScoreComponentEnum.NEWS]: 0.55,
              [TotalSentimentScoreComponentEnum.TOTAL]: 0.55
            }
          })
        );
      });
    });

    describe("when there is an invested user with disabled price momentum sentiment and both stocks & ETFs in their portfolio", () => {
      let user: UserDocument;

      const PORTFOLIO_VALUE_YESTERDAY = 50;
      const PORTFOLIO_VALUE_TODAY = 100;

      const APPLE_PRICE = 60;
      const EQUITIES_US_PRICE = 40;
      const APPLE_DAILY_RETURNS = 0.01;
      const EQUITIES_US_RETURNS = 0.02;

      beforeAll(async () => {
        jest.clearAllMocks();

        jest.spyOn(ConfigCatService, "isPriceMomentumSentimentEnabled").mockReturnValue(false);

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({
          portfolioConversionStatus: "completed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([
            buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: APPLE_PRICE },
              { dailyReturnPercentage: APPLE_DAILY_RETURNS }
            ),
            buildHoldingDTO(
              true,
              "equities_us",
              1,
              { price: EQUITIES_US_PRICE },
              { dailyReturnPercentage: EQUITIES_US_RETURNS }
            )
          ])
        });

        await Promise.all([
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_us:price_momentum", 0.25)
        ]);

        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: DateUtil.getDateOfDaysAgo(TODAY, 1),
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_YESTERDAY }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: TODAY,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_TODAY }
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }),
          buildSubscription({ owner: user.id })
        ]);

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers();
      });
      afterAll(
        async () =>
          await Promise.all([
            RedisClientService.Instance.del("sentiment_scores:equities_apple:analyst"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:price_momentum"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:news"),
            RedisClientService.Instance.del("sentiment_scores:equities_us:price_momentum"),
            clearDb()
          ])
      );

      it("should create a daily summary snapshot with no price momentum", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            sentimentScore: {
              [IndividualSentimentScoreComponentEnum.ANALYST]: 0.75,
              [IndividualSentimentScoreComponentEnum.NEWS]: 0.75,
              [TotalSentimentScoreComponentEnum.TOTAL]: 0.75
            }
          })
        );
      });
    });
  });

  describe("createDailySummarySnapshotsForUsersWithCashOrSavings", () => {
    describe("when there is a fully withdrawn user without cash/savings", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: []
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 0 }
        });
        await buildSubscription({ owner: user.id });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings();
      });
      afterAll(async () => await clearDb());

      it("should not create any daily summary snapshots", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(0);
      });
    });

    describe("when there is a fully withdrawn user with cash", () => {
      let user: UserDocument;

      const CASH = 10;

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: {
            GBP: {
              available: CASH,
              reserved: 0,
              settled: CASH
            }
          }
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 0 }
        });
        await buildSubscription({ owner: user.id });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings();
      });
      afterAll(async () => await clearDb());

      it("should not create any daily summary snapshots", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(0);
      });
    });

    describe("when there is a fully withdrawn user with savings", () => {
      let user: UserDocument;

      const SAVINGS_AMOUNT = 500; // In GBX

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 0 }
        });
        await buildSubscription({ owner: user.id });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings();
      });
      afterAll(async () => await clearDb());

      it("should not create any daily summary snapshots", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(0);
      });
    });

    describe("when there is an invested user", () => {
      let user: UserDocument;

      const PORTFOLIO_VALUE_YESTERDAY = 50;
      const PORTFOLIO_VALUE_TODAY = 100;

      const APPLE_PRICE = 60;
      const MICROSOFT_PRICE = 40;
      const APPLE_DAILY_RETURNS = 0.01;
      const MICROSOFT_DAILY_RETURNS = 0.02;

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([
            buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: APPLE_PRICE },
              { dailyReturnPercentage: APPLE_DAILY_RETURNS }
            ),
            buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: MICROSOFT_PRICE },
              { dailyReturnPercentage: MICROSOFT_DAILY_RETURNS }
            )
          ])
        });

        await Promise.all([
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.75),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.25),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.25),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.25)
        ]);

        await Promise.all([
          buildSubscription({ owner: user.id }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: DateUtil.getDateOfDaysAgo(TODAY, 1),
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_YESTERDAY }
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            timestamp: TODAY,
            pricePerCurrency: { GBP: PORTFOLIO_VALUE_TODAY }
          })
        ]);

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings();
      });
      afterAll(
        async () =>
          await Promise.all([
            RedisClientService.Instance.del("sentiment_scores:equities_apple:analyst"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:price_momentum"),
            RedisClientService.Instance.del("sentiment_scores:equities_apple:news"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:analyst"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:price_momentum"),
            RedisClientService.Instance.del("sentiment_scores:equities_microsoft:news"),
            clearDb()
          ])
      );

      it("should not create any daily summary snapshots", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(0);
      });
    });

    describe("when there is a user who is not converted but has cash", () => {
      let user: UserDocument;

      const CASH = 10;

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "notStarted" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: {
            GBP: {
              available: CASH,
              reserved: 0,
              settled: CASH
            }
          }
        });
        await buildSubscription({ owner: user.id });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        await buildDepositCashTransaction({ portfolio: portfolio.id, status: "Settled" });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings();
      });
      afterAll(async () => await clearDb());

      it("should create a daily summary snapshot", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            metadata: {
              owner: user._id
            },
            portfolio: {
              cash: {
                value: {
                  amount: CASH,
                  currency: "GBP"
                }
              },
              savings: {
                value: {
                  amount: 0,
                  currency: "GBP"
                },
                unrealisedInterest: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              holdings: {
                assets: [],
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              total: {
                value: {
                  amount: CASH,
                  currency: "GBP"
                }
              }
            }
          })
        );
      });
    });

    describe("when there is a user who is not converted but has savings", () => {
      let user: UserDocument;

      const SAVINGS_AMOUNT = 500;

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "notStarted" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
        await buildSubscription({ owner: user.id });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        await buildSavingsTopup({ portfolio: portfolio.id, status: "Settled" });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings();
      });
      afterAll(async () => await clearDb());

      it("should create a daily summary snapshot", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            metadata: {
              owner: user._id
            },
            portfolio: {
              cash: {
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              savings: {
                value: {
                  amount: Decimal.div(SAVINGS_AMOUNT, 100).toNumber(),
                  currency: "GBP"
                },
                unrealisedInterest: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              holdings: {
                assets: [],
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              total: {
                value: {
                  amount: Decimal.div(SAVINGS_AMOUNT, 100).toNumber(),
                  currency: "GBP"
                }
              }
            }
          })
        );
      });
    });

    describe("when there is a user who is not converted and has no cash/savings but has a pending savings top-up", () => {
      let user: UserDocument;

      const PENDING_SAVINGS_AMOUNT = 500; // In GBX

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "notStarted" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: []
        });
        await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: PENDING_SAVINGS_AMOUNT,
            currency: "GBP"
          }
        });
        await buildSubscription({ owner: user.id });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings();
      });
      afterAll(async () => await clearDb());

      it("should create a daily summary snapshot", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            metadata: {
              owner: user._id
            },
            portfolio: {
              cash: {
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              savings: {
                value: {
                  amount: 5,
                  currency: "GBP"
                },
                unrealisedInterest: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              holdings: {
                assets: [],
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              total: {
                value: {
                  amount: 5,
                  currency: "GBP"
                }
              }
            }
          })
        );
      });
    });

    describe("when there is a user who is not converted and has no cash/savings but had a settled cash deposit in the past", () => {
      let user: UserDocument;

      const SETTLED_CASH_DEPOSIT_AMOUNT = 500; // In GBX

      beforeAll(async () => {
        jest.clearAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ portfolioConversionStatus: "notStarted" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: []
        });
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          consideration: {
            amount: SETTLED_CASH_DEPOSIT_AMOUNT,
            currency: "GBP"
          }
        });
        await buildSubscription({ owner: user.id });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings();
      });
      afterAll(async () => await clearDb());

      it("should create a daily summary snapshot", async () => {
        const dailySummarySnapshots: DailySummarySnapshotDocument[] = await DailySummarySnapshot.find();
        expect(dailySummarySnapshots).toHaveLength(1);
        expect(dailySummarySnapshots[0].toObject()).toEqual(
          expect.objectContaining({
            metadata: {
              owner: user._id
            },
            portfolio: {
              cash: {
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              savings: {
                value: {
                  amount: 0,
                  currency: "GBP"
                },
                unrealisedInterest: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              holdings: {
                assets: [],
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              total: {
                value: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            }
          })
        );
      });
    });
  });

  describe("updateSentimentScoresCache", () => {
    describe("when there is an ETF", () => {
      const NOW = new Date("2024-10-11");
      const TWO_MONTHS_AGO = DateUtil.getDateOfMonthsAgo(NOW, 2);

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        const eodHistoricalDailyData = DateUtil.getWeekDaysBetween(NOW, TWO_MONTHS_AGO).map((date) => {
          return { date: DateTime.fromJSDate(date).toISODate(), close: 5 };
        });
        await Promise.all([
          RedisClientService.Instance.set("eod:historical:equities_uk", eodHistoricalDailyData),
          buildInvestmentProduct(true, { assetId: "equities_uk" })
        ]);

        await DailySummarySnapshotCronService.updateSentimentScoresCache();
      });
      afterAll(async () => {
        await Promise.all([
          RedisClientService.Instance.del("eod:historical:equities_uk"),
          RedisClientService.Instance.del("sentiment_scores:equities_uk:price_momentum"),
          clearDb()
        ]);
      });

      it("should cache the price momentum of the asset", async () => {
        const cachedPriceMomentum = await RedisClientService.Instance.get(
          "sentiment_scores:equities_uk:price_momentum"
        );
        expect(cachedPriceMomentum).toBe(0.6666666666666666);
      });
    });

    describe("when there is an ETF but there are no historical prices", () => {
      const NOW = new Date("2024-10-11");

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        await buildInvestmentProduct(true, { assetId: "equities_uk" });

        await DailySummarySnapshotCronService.updateSentimentScoresCache();
      });
      afterAll(async () => await clearDb());

      it("should NOT cache the price momentum of the asset", async () => {
        const cachedPriceMomentum = await RedisClientService.Instance.get(
          "sentiment_scores:equities_uk:price_momentum"
        );
        expect(cachedPriceMomentum).toBeUndefined();
      });
    });

    describe("when there is a stock with no sentiment scores", () => {
      const NOW = new Date("2024-10-11");

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        await DailySummarySnapshotCronService.updateSentimentScoresCache();
      });
      afterAll(async () => await clearDb());

      it("should NOT cache any data", async () => {
        const cachedPriceMomentum = await RedisClientService.Instance.get(
          "sentiment_scores:equities_apple:price_momentum"
        );
        expect(cachedPriceMomentum).toBeUndefined();

        const cachedNewsSentiment = await RedisClientService.Instance.get("sentiment_scores:equities_apple:news");
        expect(cachedNewsSentiment).toBeUndefined();

        const cachedAnalystSentiment = await RedisClientService.Instance.get(
          "sentiment_scores:equities_apple:analyst"
        );
        expect(cachedAnalystSentiment).toBeUndefined();
      });
    });

    describe("when there is a stock and some of the analyst ratings are null", () => {
      const NOW = new Date("2024-10-11");
      const TWO_MONTHS_AGO = DateUtil.getDateOfMonthsAgo(NOW, 2);

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        const eodHistoricalDailyData = DateUtil.getWeekDaysBetween(NOW, TWO_MONTHS_AGO).map((date) => {
          return { date: DateTime.fromJSDate(date).toISODate(), close: 5 };
        });
        await Promise.all([
          RedisClientService.Instance.set("eod:historical:equities_apple", eodHistoricalDailyData),
          buildInvestmentProduct(true, { assetId: "equities_apple" })
        ]);

        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(
          buildStockFundamentalsResponse({
            AnalystRatings: {
              StrongBuy: null,
              Buy: 5,
              Hold: 40,
              Sell: 20,
              StrongSell: 20
            }
          })
        );

        await DailySummarySnapshotCronService.updateSentimentScoresCache();
      });
      afterAll(async () => {
        await Promise.all([
          RedisClientService.Instance.del("eod:historical:equities_apple"),
          RedisClientService.Instance.del("eod:fundamentals:equities_apple"),
          RedisClientService.Instance.del("eod:sentiments:equities_apple"),
          RedisClientService.Instance.del("sentiment_scores:equities_apple:price_momentum"),
          clearDb()
        ]);
      });

      it("should cache the price momentum of the asset", async () => {
        const cachedPriceMomentum = await RedisClientService.Instance.get(
          "sentiment_scores:equities_apple:price_momentum"
        );
        expect(cachedPriceMomentum).toEqual(0.6666666666666666);
      });

      it("should NOT cache news/analyst sentiments of the asset", async () => {
        const cachedNewsSentiment = await RedisClientService.Instance.get("sentiment_scores:equities_apple:news");
        expect(cachedNewsSentiment).toBeUndefined();

        const cachedAnalystSentiment = await RedisClientService.Instance.get(
          "sentiment_scores:equities_apple:analyst"
        );
        expect(cachedAnalystSentiment).toBeUndefined();
      });

      it("should NOT log error", async () => {
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a stock with only a price momentum", () => {
      const NOW = new Date("2024-10-11");
      const TWO_MONTHS_AGO = DateUtil.getDateOfMonthsAgo(NOW, 2);

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        const eodHistoricalDailyData = DateUtil.getWeekDaysBetween(NOW, TWO_MONTHS_AGO).map((date) => {
          return { date: DateTime.fromJSDate(date).toISODate(), close: 5 };
        });
        await Promise.all([
          RedisClientService.Instance.set("eod:historical:equities_apple", eodHistoricalDailyData),
          buildInvestmentProduct(true, { assetId: "equities_apple" })
        ]);

        await DailySummarySnapshotCronService.updateSentimentScoresCache();
      });
      afterAll(async () => {
        await Promise.all([
          RedisClientService.Instance.del("eod:historical:equities_apple"),
          RedisClientService.Instance.del("sentiment_scores:equities_apple:price_momentum"),
          clearDb()
        ]);
      });

      it("should cache the price momentum of the asset", async () => {
        const cachedPriceMomentum = await RedisClientService.Instance.get(
          "sentiment_scores:equities_apple:price_momentum"
        );
        expect(cachedPriceMomentum).toEqual(0.6666666666666666);
      });

      it("should NOT cache news/analyst sentiments of the asset", async () => {
        const cachedNewsSentiment = await RedisClientService.Instance.get("sentiment_scores:equities_apple:news");
        expect(cachedNewsSentiment).toBeUndefined();

        const cachedAnalystSentiment = await RedisClientService.Instance.get(
          "sentiment_scores:equities_apple:analyst"
        );
        expect(cachedAnalystSentiment).toBeUndefined();
      });

      it("should NOT log error", async () => {
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a stock and no data is cached", () => {
      const NOW = new Date("2024-10-11");
      const TWO_MONTHS_AGO = DateUtil.getDateOfMonthsAgo(NOW, 2);

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        const eodHistoricalDailyData = DateUtil.getWeekDaysBetween(NOW, TWO_MONTHS_AGO).map((date) => {
          return { date: DateTime.fromJSDate(date).toISODate(), close: 5 };
        });

        jest.spyOn(eodService, "getHistoricalPrices").mockResolvedValue(eodHistoricalDailyData);
        jest.spyOn(eodService, "getAssetSentiments").mockResolvedValue([
          {
            date: DateUtil.formatDateToYYYYMMDD(NOW),
            count: 1,
            normalized: 0.5
          },
          {
            date: DateUtil.formatDateToYYYYMMDD(DateUtil.getDateOfDaysAgo(NOW, 1)),
            count: 2,
            normalized: 0.35
          }
        ]);
        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(
          buildStockFundamentalsResponse({
            AnalystRatings: {
              StrongBuy: 5,
              Buy: 5,
              Hold: 40,
              Sell: 20,
              StrongSell: 20
            }
          })
        );

        await DailySummarySnapshotCronService.updateSentimentScoresCache();
      });
      afterAll(async () => {
        await Promise.all([
          RedisClientService.Instance.del("eod:historical:equities_apple"),
          RedisClientService.Instance.del("eod:sentiments:equities_apple"),
          RedisClientService.Instance.del("eod:fundamentals:equities_apple"),
          RedisClientService.Instance.del("sentiment_scores:equities_apple:price_momentum"),
          RedisClientService.Instance.del("sentiment_scores:equities_apple:news"),
          RedisClientService.Instance.del("sentiment_scores:equities_apple:analyst"),
          clearDb()
        ]);
      });

      it("should cache the price momentum of the asset", async () => {
        const cachedPriceMomentum = await RedisClientService.Instance.get(
          "sentiment_scores:equities_apple:price_momentum"
        );
        expect(cachedPriceMomentum).toEqual(0.6666666666666666);
      });

      it("should cache the news sentiment of the asset", async () => {
        const cachedNewsSentiment = await RedisClientService.Instance.get<number>(
          "sentiment_scores:equities_apple:news"
        );
        expect(new Decimal(cachedNewsSentiment).toDecimalPlaces(2).toNumber()).toEqual(0.53);
      });

      it("should cache the analyst sentiment of the asset", async () => {
        const cachedAnalystSentiment = await RedisClientService.Instance.get<number>(
          "sentiment_scores:equities_apple:analyst"
        );
        expect(new Decimal(cachedAnalystSentiment).toDecimalPlaces(2).toNumber()).toEqual(0.39);
      });
    });

    describe("when there is a stock and all data is cached", () => {
      const NOW = new Date("2024-10-11");
      const TWO_MONTHS_AGO = DateUtil.getDateOfMonthsAgo(NOW, 2);

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        const eodHistoricalDailyData = DateUtil.getWeekDaysBetween(NOW, TWO_MONTHS_AGO).map((date) => {
          return { date: DateTime.fromJSDate(date).toISODate(), close: 5 };
        });
        await Promise.all([
          RedisClientService.Instance.set("eod:historical:equities_apple", eodHistoricalDailyData),
          RedisClientService.Instance.set("eod:sentiments:equities_apple", [
            {
              date: DateUtil.formatDateToYYYYMMDD(NOW),
              count: 1,
              normalized: 0.5
            },
            {
              date: DateUtil.formatDateToYYYYMMDD(DateUtil.getDateOfDaysAgo(NOW, 1)),
              count: 2,
              normalized: 0.35
            }
          ]),
          RedisClientService.Instance.set(
            "eod:fundamentals:equities_apple",
            buildStockFundamentalsResponse({
              AnalystRatings: {
                StrongBuy: 5,
                Buy: 5,
                Hold: 40,
                Sell: 20,
                StrongSell: 20
              }
            })
          ),
          buildInvestmentProduct(true, { assetId: "equities_apple" })
        ]);

        await DailySummarySnapshotCronService.updateSentimentScoresCache();
      });
      afterAll(async () => {
        await Promise.all([
          RedisClientService.Instance.del("eod:historical:equities_apple"),
          RedisClientService.Instance.del("eod:sentiments:equities_apple"),
          RedisClientService.Instance.del("eod:fundamentals:equities_apple"),
          RedisClientService.Instance.del("sentiment_scores:equities_apple:price_momentum"),
          RedisClientService.Instance.del("sentiment_scores:equities_apple:news"),
          RedisClientService.Instance.del("sentiment_scores:equities_apple:analyst"),
          clearDb()
        ]);
      });

      it("should cache the price momentum of the asset", async () => {
        const cachedPriceMomentum = await RedisClientService.Instance.get(
          "sentiment_scores:equities_apple:price_momentum"
        );
        expect(cachedPriceMomentum).toEqual(0.6666666666666666);
      });

      it("should cache the news sentiment of the asset", async () => {
        const cachedNewsSentiment = await RedisClientService.Instance.get<number>(
          "sentiment_scores:equities_apple:news"
        );
        expect(new Decimal(cachedNewsSentiment).toDecimalPlaces(2).toNumber()).toEqual(0.53);
      });

      it("should cache the analyst sentiment of the asset", async () => {
        const cachedAnalystSentiment = await RedisClientService.Instance.get<number>(
          "sentiment_scores:equities_apple:analyst"
        );
        expect(new Decimal(cachedAnalystSentiment).toDecimalPlaces(2).toNumber()).toEqual(0.39);
      });
    });
  });
});
