import Decimal from "decimal.js";
import { captureException } from "@sentry/node";
import { TenorEnum } from "../../configs/durationConfig";
import { ProviderEnum } from "../../configs/providersConfig";
import { LearningNotificationEventEnum } from "../../event-handlers/notificationEvents";
import { Notification, NotificationDocument, NotificationMetadataTypeEnum } from "../../models/Notification";
import logger from "../../external-services/loggerService";
import { RedisClientService } from "../../loaders/redis";
import { SundownDigestDocument } from "../../models/SundownDigest";
import { KycStatusEnum, UserDocument } from "../../models/User";
import NotificationService from "../../services/notificationService";
import UserService from "../../services/userService";
import SundownDigestService from "../../services/sundownDigestService";
import DateUtil from "../../utils/dateUtil";

// Notification time constant: 7:00 AM UTC (9:00 AM EET/Greek time in winter)
// Note: During summer (DST), 9:00 AM EET would be 6:00 AM UTC
const NOTIFICATION_TIME_HOUR_UTC = 7;

const USER_BATCH_SIZE_DAILY_RECAP_NOTIFICATION = 500;
const USER_BATCH_SIZE = 500;
const CONTENT_NOTIFICATION_IDS = [
  LearningNotificationEventEnum.QUICK_TAKE_CREATED,
  LearningNotificationEventEnum.WEEKLY_REVIEW_CREATED,
  LearningNotificationEventEnum.ANALYSIS_CREATED,
  LearningNotificationEventEnum.GUIDE_CREATED
];

export class NotificationCronService {
  // ===============
  // PUBLIC METHODS
  // ===============

  public static async sendPendingAppContentNotifications(): Promise<void> {
    const timeWindow = {
      $gte: DateUtil.getDateOfMinutesAgo(90),
      $lte: DateUtil.getDateOfMinutesAgo(1)
    };

    // 1. Check which users have already been sent a 'learn' notification today.
    // This will help us avoid sending multiple 'learn' notifications to the same user
    const startOfDay = DateUtil.getStartOfDay(new Date(Date.now()));
    const usersWithNotificationsToday = await Notification.distinct("owner", {
      status: "Sent",
      method: "app",
      "providers.onesignal.notificationId": {
        $in: CONTENT_NOTIFICATION_IDS
      },
      notifyAt: { $gte: startOfDay }
    });

    // 2. Get users with pending notifications but exclude those who already received a 'learn' notification today
    const pendingNotificationUsers = await Notification.distinct("owner", {
      status: "Pending",
      method: "app",
      "providers.onesignal.notificationId": {
        $in: CONTENT_NOTIFICATION_IDS
      },
      notifyAt: timeWindow,
      owner: { $nin: usersWithNotificationsToday } // Exclude users who already got a notification today
    });

    // 3. Select the first 'learn' notification for each user (there may be multiple pending)
    const selectedNotifications: NotificationDocument[] = [];
    for (let i = 0; i < pendingNotificationUsers.length; i += USER_BATCH_SIZE) {
      const userIdBatch = pendingNotificationUsers.slice(i, i + USER_BATCH_SIZE);

      const batchNotifications = await Notification.find({
        owner: { $in: userIdBatch },
        status: "Pending",
        method: "app",
        "providers.onesignal.notificationId": {
          $in: CONTENT_NOTIFICATION_IDS
        },
        notifyAt: timeWindow
      }).populate({
        path: "owner",
        populate: {
          path: "notificationSettings"
        }
      });

      // Take the first notification for each user
      const notificationByUserId = new Map<string, NotificationDocument>();
      batchNotifications.forEach((notification) => {
        const userId = notification.owner.id || notification.owner.toString();
        if (!notificationByUserId.has(userId)) {
          notificationByUserId.set(userId, notification);
        }
      });

      selectedNotifications.push(...notificationByUserId.values());
    }

    // 4. Group the selected notifications by content ID and send them in bulk
    if (selectedNotifications.length > 0) {
      const notificationsByContentId =
        NotificationCronService._groupNotificationsByContentId(selectedNotifications);

      await Promise.all(
        Array.from(notificationsByContentId.entries()).map(async ([contentId, groupedNotifications]) => {
          try {
            // Send notifications for this content ID in bulk
            await NotificationService.sendBulkAppNotifications(groupedNotifications);
          } catch (err) {
            captureException(err);
            logger.error(`Failed to send batch of notifications for content ${contentId}`, {
              module: "NotificationCronService",
              method: "sendPendingAppContentNotifications",
              data: { contentId, error: err }
            });
          }
        })
      );
    }
  }

  public static async markStagnantNotificationsAsSkipped(): Promise<void> {
    const startOfToday = DateUtil.getStartOfDay(new Date(Date.now()));

    const notifications = await Notification.find({
      status: "Pending",
      notifyAt: { $lte: startOfToday }
    });

    for (let i = 0; i < notifications.length; i++) {
      const notification = notifications[i];

      try {
        await NotificationService.markNotificationAsSkipped(notifications[i]);
      } catch (err) {
        captureException(err);
        logger.error(`Marking notification as skipped failed for notification ${notification.id}`, {
          module: "NotificationCronService",
          method: "markStagnantNotificationsAsSkipped"
        });
      }
    }
  }

  /**
   * Creates daily market recap notifications for all users
   * - Fetches the latest sundown digest to get market overview
   * - Creates different notifications for invested and uninvested users
   * - For invested users, includes portfolio return in the notification title
   * @returns {Promise<void>}
   */
  public static async createDailyMarketRecapNotifications(): Promise<void> {
    try {
      // Check if a daily recap notification already exists for today (for any user)
      const startOfDay = DateUtil.getStartOfDay(new Date(Date.now()));

      const existingNotification = await Notification.findOne({
        method: "app",
        "providers.onesignal.notificationId": LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
        notifyAt: { $gte: startOfDay }
      });
      if (existingNotification) {
        logger.info("Daily market recap notification already exists for today. Skipping creation.", {
          module: "NotificationCronService",
          method: "createDailyMarketRecapNotifications"
        });
        return;
      }

      // 1. Get latest sundown digest and validate it
      const latestSundownDigest = await SundownDigestService.getLatestSundownDigest();
      if (!NotificationCronService._validateSundownDigest(latestSundownDigest)) {
        return;
      }

      // 2. Process uninvested users
      await NotificationCronService._processUninvestedUsersDailyRecapNotifications(
        "notStarted",
        latestSundownDigest
      );
      await NotificationCronService._processUninvestedUsersDailyRecapNotifications(
        "inProgress",
        latestSundownDigest
      );

      // 3. Process invested users (portfolioConversionStatus 'completed')
      // Note: This also handles fully withdrawn users
      await NotificationCronService._processInvestedUsersDailyRecapNotifications(latestSundownDigest);

      logger.info("Successfully created daily market recap notifications", {
        module: "NotificationCronService",
        method: "createDailyMarketRecapNotifications"
      });
    } catch (error) {
      captureException(error);
      logger.error("Failed to create daily market recap notifications", {
        module: "NotificationCronService",
        method: "createDailyMarketRecapNotifications",
        data: { error }
      });
    }
  }

  /**
   * Sends all pending daily recap notifications (app method)
   */
  public static async sendPendingDailyRecapNotifications(): Promise<void> {
    try {
      // Define time window for sending notifications (e.g., last 90 to 1 minute after)
      const timeWindow = {
        $gte: DateUtil.getDateOfMinutesAgo(90),
        $lte: DateUtil.getDateOfMinutesFromNow(1)
      };

      // Find all pending daily recap notifications within the time window
      const pendingRecapNotifications = await Notification.find({
        status: "Pending",
        method: "app",
        "providers.onesignal.notificationId": LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
        notifyAt: timeWindow
      }).populate({ path: "owner", populate: { path: "notificationSettings" } });

      if (pendingRecapNotifications.length > 0) {
        for (const notification of pendingRecapNotifications) {
          await NotificationService.sendSingleAppNotification(notification);
        }
      }
    } catch (err) {
      captureException(err);
      logger.error("Failed to send pending daily recap notifications", {
        module: "NotificationCronService",
        method: "sendPendingDailyRecapNotifications",
        data: { error: err }
      });
    }
  }

  // ===============
  // PRIVATE METHODS
  // ===============

  /**
   * Process uninvested users with a specific portfolioConversionStatus
   * @param status The portfolioConversionStatus to filter by
   * @param sundownDigest The sundown digest to use for notifications
   * @private
   */
  private static async _processUninvestedUsersDailyRecapNotifications(
    status: "notStarted" | "inProgress",
    sundownDigest: SundownDigestDocument
  ): Promise<void> {
    await UserService.getUsersStreamed({
      kycStatus: KycStatusEnum.PASSED,
      portfolioConversionStatus: status
    }).eachAsync(
      async (users: UserDocument[]) => {
        const operations = users
          .filter((user) => user.hasDeviceToken)
          .map((user) =>
            NotificationCronService._createUninvestedUserNotificationDbOperation(user, sundownDigest)
          );

        if (operations.length > 0) {
          try {
            await Notification.bulkWrite(operations);
          } catch (error) {
            captureException(error);
            logger.error(`Failed to process batch of uninvested users with status ${status}`, {
              module: "NotificationCronService",
              method: "_processUninvestedUsers",
              data: { status, error }
            });
          }
        }
      },
      { batchSize: USER_BATCH_SIZE_DAILY_RECAP_NOTIFICATION }
    );
  }

  /**
   * Process invested users (portfolioConversionStatus 'completed')
   * This also handles fully withdrawn users (users with portfolioConversionStatus 'completed' but no holdings)
   * @param sundownDigest The sundown digest to use for notifications
   * @private
   */
  private static async _processInvestedUsersDailyRecapNotifications(
    sundownDigest: SundownDigestDocument
  ): Promise<void> {
    await UserService.getUsersStreamed(
      {
        kycStatus: KycStatusEnum.PASSED,
        portfolioConversionStatus: "completed"
      },
      [
        {
          path: "portfolios"
        }
      ]
    ).eachAsync(
      async (users: UserDocument[]) => {
        try {
          const operations = await Promise.all(
            users
              .filter((user) => user.hasDeviceToken)
              .map(async (user) => {
                try {
                  // Check if user is fully withdrawn using UserService
                  const portfolioStatus = await UserService.getPortfolioConversionStatus(user);

                  if (portfolioStatus === "fullyWithdrawn") {
                    // Send uninvested notification for fully withdrawn users
                    return NotificationCronService._createUninvestedUserNotificationDbOperation(
                      user,
                      sundownDigest
                    );
                  }

                  // Try to get portfolio returns from Redis cache
                  const portfolioId = user.portfolios[0]?._id;
                  const cachedReturns = portfolioId
                    ? await RedisClientService.Instance.get<Record<TenorEnum, number>>(
                        `portfolios:mwrr:${portfolioId}`
                      )
                    : null;

                  if (
                    cachedReturns &&
                    cachedReturns[TenorEnum.ONE_DAY] !== undefined &&
                    cachedReturns[TenorEnum.ONE_DAY] !== 0
                  ) {
                    // Create notification with cached portfolio return
                    const portfolioReturn = new Decimal(cachedReturns[TenorEnum.ONE_DAY]).mul(100).toNumber();
                    return NotificationCronService._createInvestedUserNotificationDbOperation(
                      user,
                      portfolioReturn,
                      sundownDigest
                    );
                  }

                  // Fall back to uninvested user notification if no cached returns
                  return NotificationCronService._createUninvestedUserNotificationDbOperation(user, sundownDigest);
                } catch (error) {
                  captureException(error);

                  // Fall back to uninvested user notification in case of error
                  return NotificationCronService._createUninvestedUserNotificationDbOperation(user, sundownDigest);
                }
              })
          );

          // Bulk write operations if any
          if (operations.length > 0) {
            await Notification.bulkWrite(operations);
          }
        } catch (error) {
          captureException(error);
          logger.error("Failed to process batch of invested users", {
            module: "NotificationCronService",
            method: "_processInvestedUsers",
            data: { error }
          });
        }
      },
      { batchSize: USER_BATCH_SIZE_DAILY_RECAP_NOTIFICATION }
    );
  }

  /**
   * Validates that the sundown digest exists and is from yesterday
   * @param sundownDigest The sundown digest to validate
   * @returns {boolean} True if the digest is valid, false otherwise
   * @private
   */
  private static _validateSundownDigest(sundownDigest: SundownDigestDocument): boolean {
    if (!sundownDigest || !sundownDigest.formattedContent?.overview) {
      logger.warn(
        "Cannot create daily market recap notifications, no sundown digest or valid formatted content found",
        {
          module: "NotificationCronService",
          method: "_validateSundownDigest"
        }
      );
      return false;
    }

    // Check if digest is recent (from the day before)
    // this is intended to run Tuesday - Saturday
    const yesterday = DateUtil.getYesterday();
    const digestDate = sundownDigest.date;

    if (!DateUtil.datesAreEqual(digestDate, yesterday)) {
      logger.warn("Cannot create daily market recap notifications, sundown digest is not from yesterday", {
        module: "NotificationCronService",
        method: "_validateSundownDigest",
        data: {
          digestDate,
          yesterday
        }
      });
      return false;
    }

    return true;
  }

  /**
   * Gets the notification time (9:00 AM Greek time / 7:00 AM UTC for that day)
   * @returns {Date} The notification time
   * @private
   */
  private static _getDailyRecapNotificationTime(): Date {
    const notificationTime = new Date(Date.now());
    notificationTime.setUTCHours(NOTIFICATION_TIME_HOUR_UTC, 0, 0, 0);
    return notificationTime;
  }

  /**
   * Creates a notification for an uninvested user
   * @param user The user to create the notification for
   * @param sundownDigest The sundown digest to use for the notification content
   * @returns {Object} The database operation to create the notification
   * @private
   */
  private static _createUninvestedUserNotificationDbOperation(
    user: UserDocument,
    sundownDigest: SundownDigestDocument
  ): any {
    const notificationProps = new Map<string, string>();

    notificationProps.set("title", "Daily Market Recap");

    notificationProps.set("body", `${sundownDigest.formattedContent.overview} Open the app to read more.`);

    return {
      insertOne: {
        document: {
          owner: user.id,
          method: "app",
          status: "Pending",
          notifyAt: NotificationCronService._getDailyRecapNotificationTime(),
          providers: {
            [ProviderEnum.ONESIGNAL]: {
              notificationId: LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
              properties: notificationProps,
              metadata: {
                notificationType: NotificationMetadataTypeEnum.OTHER,
                documentId: sundownDigest.id
              }
            }
          }
        }
      }
    };
  }

  /**
   * Creates a notification for an invested user with portfolio return information
   * @param user The user to create the notification for
   * @param portfolioReturn The portfolio return percentage
   * @param sundownDigest The sundown digest to use for the notification content
   * @returns {Object} The database operation to create the notification
   * @private
   */
  private static _createInvestedUserNotificationDbOperation(
    user: UserDocument,
    portfolioReturn: number,
    sundownDigest: SundownDigestDocument
  ): any {
    const notificationProps = new Map<string, string>();

    const returnValue = new Decimal(portfolioReturn).abs().toFixed(2);

    notificationProps.set(
      "title",
      portfolioReturn >= 0 ? `📈 Daily recap: +${returnValue}%` : `📉 Daily recap: -${returnValue}%`
    );

    notificationProps.set("body", `${sundownDigest.formattedContent.overview} Open the app to read more.`);

    return {
      insertOne: {
        document: {
          owner: user.id,
          method: "app",
          status: "Pending",
          notifyAt: NotificationCronService._getDailyRecapNotificationTime(),
          providers: {
            [ProviderEnum.ONESIGNAL]: {
              notificationId: LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
              properties: notificationProps,
              metadata: {
                notificationType: NotificationMetadataTypeEnum.OTHER,
                documentId: sundownDigest.id
              }
            }
          }
        }
      }
    };
  }

  /**
   * Groups notifications by their content ID.
   * @param notifications Array of notifications to group
   * @returns Map of content ID to array of notifications
   * @private
   */
  private static _groupNotificationsByContentId(
    notifications: NotificationDocument[]
  ): Map<string, NotificationDocument[]> {
    const notificationsByContentId = new Map<string, NotificationDocument[]>();

    notifications.forEach((notification) => {
      const contentId = notification.providers.onesignal.metadata.documentId;
      if (!notificationsByContentId.has(contentId)) {
        notificationsByContentId.set(contentId, []);
      }
      notificationsByContentId.get(contentId).push(notification);
    });

    return notificationsByContentId;
  }
}
