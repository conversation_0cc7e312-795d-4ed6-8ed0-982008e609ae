import { captureException } from "@sentry/node";
import logger from "../../external-services/loggerService";
import { FMPService } from "../../external-services/fmpService";
import crypto from "crypto";
import Decimal from "decimal.js/decimal";
import { LearnNews } from "../../models/LearnNews";
import LearnNewsCleaner from "../../lib/learnNewsCleaner";
import { DateTime } from "luxon";
import DateUtil from "../../utils/dateUtil";

export default class LearnNewsCronService {
  /**
   * @description Fetches and stores learn news from FMP, filtering and deduplicating as needed.
   */
  public static async createLearnNews(): Promise<void> {
    try {
      const articles = await FMPService.Instance.getNewsArticles({ includeOnlyStockTickers: true });
      if (!articles) return;

      for (let i = 0; i < articles.length; i++) {
        const article = articles[i];

        // Convert EST date to UTC
        const date = DateTime.fromFormat(article.date, "yyyy-MM-dd HH:mm:ss", {
          zone: "America/New_York"
        })
          .toUTC()
          .toJSDate();
        if (!DateUtil.isToday(date)) {
          continue;
        }

        const articleUniqueHash = LearnNewsCronService._getUniqueHash(article.title, article.date);
        const existingLearnNews = await LearnNews.findOne({ hash: articleUniqueHash });
        if (existingLearnNews) {
          continue;
        }

        const cleanedContent = await LearnNewsCleaner.clean(article.content.replace("\n", "").replace("\r", ""));
        if (!cleanedContent) {
          continue; // If we have failed to clean the HTML content, we should not store the news article.
        }

        const data = {
          hash: articleUniqueHash,
          imageUrl: article.image,
          title: article.title,
          htmlContent: cleanedContent,
          tickers: article.tickers,
          date: date,
          readingTime: LearnNewsCronService._estimateReadingTime(article.content)
        };

        await new LearnNews(data).save();
      }
    } catch (err) {
      captureException(err);
      logger.error("Failed while retrieving and storing learn news", {
        module: "LearnNewsCronService",
        method: "createLearnNews"
      });
    }
  }

  /**
   * To estimate the reading time, we assume an average human can read around 200 words/minute.
   *
   * @param text
   * @private
   */
  private static _estimateReadingTime(text: string): string {
    const words = text ? text.split(/\s+/).length : 0;

    const minutes = Decimal.div(words, 200).ceil().clampedTo(1, 59).toNumber();

    return `${minutes} min${minutes > 1 ? "s" : ""}`;
  }

  private static _getUniqueHash(title: string, date: string): string {
    return crypto.createHash("sha256").update(`${title}|${date}`).digest("hex");
  }
}
