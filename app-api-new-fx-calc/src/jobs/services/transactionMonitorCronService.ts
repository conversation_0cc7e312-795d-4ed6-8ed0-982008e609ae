import { captureException } from "@sentry/node";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { TransactionMonitorService } from "../../services/transactionMonitorService";
import UserService from "../../services/userService";
import logger from "../../external-services/loggerService";

export class TransactionMonitorCronService {
  public static async checkUsersForSuspiciousTransactionActivity(): Promise<void> {
    await UserService.getUsersStreamed({ kycStatus: KycStatusEnum.PASSED }, "latestRiskAssessment").eachAsync(
      async (users: UserDocument[]) => {
        const checkPromises = users.map(async (user) => {
          try {
            if (user.latestRiskAssessment?.classification === "Prohibited") {
              logger.warn(`User ${user.id} is prohibited. Skipping suspicious transaction activity check...`, {
                module: "TransactionMonitorCronService",
                method: "checkUsersForSuspiciousTransactionActivity"
              });
              return;
            }

            await TransactionMonitorService.checkSuspiciousTransactionActivity(user);
          } catch (err) {
            captureException(err);
            logger.error(`Error checking for suspicious transaction activity for user ${user._id}`, {
              module: "TransactionMonitorCronService",
              method: "checkUsersForSuspiciousTransactionActivity"
            });
          }
        });

        await Promise.all(checkPromises);
      },
      { batchSize: 10 }
    );
  }
}
