import { captureException } from "@sentry/node";
import {
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument,
  Transaction,
  TransactionDocument
} from "../../models/Transaction";
import OrderService from "../../services/orderService";
import logger from "../../external-services/loggerService";
import { UserDocument } from "../../models/User";
import SubmissionWindowUtil from "../../utils/submissionWindowUtil";

export default class OrderCronService {
  /**
   * @description
   * Creates orders in database for pending Savings Topups/Widrawals
   */
  public static async createSavingsOrderDocuments(): Promise<void> {
    if (!SubmissionWindowUtil.isCurrentTimeWithinSavingsProductSubmissionWindow()) return;

    const pendingTransactions: TransactionDocument[] = await Transaction.find({
      $or: [
        {
          category: "SavingsTopupTransaction",
          status: { $in: ["Pending"] }
        },
        {
          category: "SavingsWithdrawalTransaction",
          status: { $in: ["Pending"] }
        }
      ]
    }).populate("orders owner");

    for (const transaction of pendingTransactions) {
      try {
        await OrderService.createSavingsOrder(
          transaction as SavingsTopupTransactionDocument | SavingsWithdrawalTransactionDocument
        );
      } catch (err) {
        logger.error(`Order creation failed for ${transaction._id}`, {
          module: "OrderCronService",
          method: "createSavingsOrderDocuments",
          data: { transactionId: transaction._id, userId: (transaction.owner as UserDocument)._id, error: err }
        });
        captureException(err);
      }
    }
  }
}
