import {
  BalanceSheetEodResponse,
  BalanceSheetsType,
  CashFlowEodResponse,
  CashFlowsType,
  EarningsType,
  EodGeneratedStockDataType,
  EtfDataType,
  IncomeStatementEodResponse,
  IncomeStatementsType,
  ProcessedBalanceSheet,
  ProcessedCashFlow,
  ProcessedIncomeStatement,
  StockDataType
} from "publicAssetData";
import {
  currenciesConfig,
  investmentUniverseConfig,
  localeConfig,
  publicInvestmentUniverseConfig
} from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";
import CurrencyUtil from "../../utils/currencyUtil";
import eodService, {
  EodETFFundamentalsResponseType,
  EodFundamentalsResponseType,
  EodStockFundamentalsResponseType
} from "../../external-services/eodService";
import InvestmentProductService from "../../services/investmentProductService";
import logger from "../../external-services/loggerService";
import * as CacheUtil from "../../utils/cacheUtil";
import DateUtil from "../../utils/dateUtil";
import { formatPercentage, numberToTwoDecimalsStr } from "../../utils/formatterUtil";
import { MainCurrencyType } from "@wealthyhood/shared-configs/dist/currencies";
import { captureException } from "@sentry/node";
import EodUtil from "../../utils/eodUtil";
import { RedisClientService } from "../../loaders/redis";
import { getBaseCurrency } from "../../utils/investmentUniverseUtil";

const { CURRENCY_SYMBOLS } = currenciesConfig;

const { AssetArrayConst } = investmentUniverseConfig;
const { PUBLIC_ASSET_CONFIG, PublicAssetArrayConst } = publicInvestmentUniverseConfig;

const FUNDAMENTALS_FORMAL_TICKER_CACHE_EXPIRATION = 60 * 60 * 12; // 12 hours
const PUBLIC_ASSET_FUNDAMENTALS_CACHING_TTL_SECONDS = 60 * 60 * 24; // 1 day

const BATCH_SIZE = 2;

enum TenorEnum {
  ANNUAL = "annual",
  Quarterly = "quarterly"
}

export default class PublicAssetDataCronService {
  // ===============
  // PUBLIC METHODS
  // ===============
  /**
   * @description Iterates over all available assets (including our public and app universe), generates page data, and stores them in Redis.
   */
  public static async storeAssetData(): Promise<void> {
    const assets = []
      .concat(AssetArrayConst)
      .concat(PublicAssetArrayConst) as publicInvestmentUniverseConfig.PublicAssetType[];

    // Split the investment products into batches
    for (let i = 0; i < assets.length; i += BATCH_SIZE) {
      const batch = assets.slice(i, i + BATCH_SIZE);
      const redisKeyValuePairs: Record<string, EtfDataType | StockDataType> = {};

      const batchPromises = batch.map(async (asset: publicInvestmentUniverseConfig.PublicAssetType) => {
        try {
          if (PUBLIC_ASSET_CONFIG[asset].category === "stock") {
            redisKeyValuePairs[`public_asset_data:stock:${asset}`] =
              await PublicAssetDataCronService._getStockData(asset, "en");
          } else if (PUBLIC_ASSET_CONFIG[asset].category === "etf") {
            redisKeyValuePairs[`public_asset_data:etf:${asset}`] = await PublicAssetDataCronService._getEtfData(
              asset,
              "en"
            );
          }
        } catch (err) {
          captureException(err);
          logger.error(`Failed while retrieving asset page data for asset ${asset}`, {
            module: "PublicAssetDataCronService",
            method: "storeAssetData",
            data: { error: err }
          });
        }
      });

      // Await all promises in the current batch before proceeding to the next batch
      await Promise.all(batchPromises);

      // Store the batch data in Redis
      await RedisClientService.Instance.mSet(redisKeyValuePairs);

      logger.info(
        `Finished processing public asset data batch - assets stored: ${i + BATCH_SIZE}/${assets.length}`,
        { module: "PublicAssetDataCronService", method: "storeAssetData" }
      );
    }
  }

  // ===============
  // PRIVATE METHODS
  // ===============
  private static async _getEtfData(
    asset: publicInvestmentUniverseConfig.PublicAssetType,
    userLocale: localeConfig.LocaleType
  ): Promise<EtfDataType> {
    const { formalTicker, formalExchange } = PUBLIC_ASSET_CONFIG[asset];

    const etfFundamentals = (await CacheUtil.getCachedDataWithFallback<EodETFFundamentalsResponseType>(
      `eod:fundamentals:${asset}`,
      async (): Promise<EodETFFundamentalsResponseType> => {
        return eodService.getAssetFundamentalsData(asset) as Promise<EodETFFundamentalsResponseType>;
      },
      (_) => PUBLIC_ASSET_FUNDAMENTALS_CACHING_TTL_SECONDS
    )) as EodETFFundamentalsResponseType;

    // For certain metrics (e.g. expense ratio), we need to retrieve EOD fundamentals from the formal ticker instead
    // of the fundamentals ticker.
    let etfFormalTickerFundamentals: EodETFFundamentalsResponseType;
    if (
      "fundamentalsTicker" in PUBLIC_ASSET_CONFIG[asset] &&
      PUBLIC_ASSET_CONFIG[asset].fundamentalsTicker !== `${formalTicker}.${formalExchange}`
    ) {
      etfFormalTickerFundamentals = (await CacheUtil.getCachedDataWithFallback<EodFundamentalsResponseType>(
        `eod:fundamentals:formalticker:${asset}`,
        async (): Promise<EodFundamentalsResponseType> => {
          return eodService.getAssetFundamentalsData(asset, {
            useFormalTicker: true
          }) as Promise<EodFundamentalsResponseType>;
        },
        (_) => FUNDAMENTALS_FORMAL_TICKER_CACHE_EXPIRATION
      )) as EodETFFundamentalsResponseType;
    }

    const topHoldings = await EodUtil.getTopHoldings(etfFundamentals, asset, userLocale);
    const tradedCurrency = etfFundamentals.General.CurrencyCode as MainCurrencyType;
    const regionalBreakdown = InvestmentProductService.getGeographyDistribution(etfFundamentals);
    const sectorBreakdown = InvestmentProductService.getSectorDistribution(etfFundamentals);

    return {
      key: asset,
      about: {
        tradedCurrency,
        baseCurrency: getBaseCurrency(asset),
        ticker: formalTicker,
        tradingOn: etfFundamentals.General.Exchange,
        isin: etfFundamentals.ETF_Data.ISIN,
        fundManager: etfFundamentals.ETF_Data.Company_Name,
        fundManagerWebsite: etfFundamentals.ETF_Data.Company_URL,
        website: etfFundamentals.ETF_Data.ETF_URL,
        description: etfFundamentals.General.Description,
        name: etfFundamentals.General.Name
      },
      basics: {
        fundSize: this._formatLargeNumber(
          parseFloat(etfFundamentals.ETF_Data.TotalAssets),
          tradedCurrency,
          userLocale
        ),
        expenseRatio: formatPercentage(
          parseFloat(
            etfFormalTickerFundamentals?.ETF_Data?.Ongoing_Charge ?? etfFundamentals.ETF_Data.Ongoing_Charge
          ),
          userLocale
        ),
        income: etfFundamentals.ETF_Data.Dividend_Paying_Frequency ? "Distributing" : "Accumulating",
        dividendYieldFactor:
          etfFundamentals.ETF_Data.Valuations_Growth.Valuations_Rates_Portfolio?.["Dividend-Yield Factor"]
      },
      advanced: {
        price: {
          week52High: etfFundamentals.Technicals["52WeekHigh"]
            ? CurrencyUtil.formatCurrency(etfFundamentals.Technicals["52WeekHigh"], tradedCurrency, userLocale)
            : null,
          week52Low: etfFundamentals.Technicals["52WeekLow"]
            ? CurrencyUtil.formatCurrency(etfFundamentals.Technicals["52WeekLow"], tradedCurrency, userLocale)
            : null,
          day50Ma: etfFundamentals.Technicals["50DayMA"]
            ? CurrencyUtil.formatCurrency(etfFundamentals.Technicals["50DayMA"], tradedCurrency, userLocale)
            : null,
          day200Ma: etfFundamentals.Technicals["200DayMA"]
            ? CurrencyUtil.formatCurrency(etfFundamentals.Technicals["200DayMA"], tradedCurrency, userLocale)
            : null
        },
        valuation: {
          priceToProspectiveEarnings: numberToTwoDecimalsStr(
            parseFloat(
              etfFundamentals.ETF_Data.Valuations_Growth.Valuations_Rates_Portfolio?.["Price/Prospective Earnings"]
            ) || null,
            userLocale
          ),
          priceToBook: numberToTwoDecimalsStr(
            parseFloat(etfFundamentals.ETF_Data.Valuations_Growth.Valuations_Rates_Portfolio?.["Price/Sales"]) ||
              null,
            userLocale
          ),
          priceToSales: numberToTwoDecimalsStr(
            parseFloat(etfFundamentals.ETF_Data.Valuations_Growth.Valuations_Rates_Portfolio?.["Price/Book"]) ||
              null,
            userLocale
          ),
          priceToCashflow: numberToTwoDecimalsStr(
            parseFloat(
              etfFundamentals.ETF_Data.Valuations_Growth.Valuations_Rates_Portfolio?.["Price/Cash Flow"]
            ) || null,
            userLocale
          )
        },
        technicals: {
          ytd: etfFundamentals.ETF_Data.Performance.Returns_YTD
            ? formatPercentage(
                Decimal.div(parseFloat(etfFundamentals.ETF_Data.Performance.Returns_YTD), 100).toNumber(),
                userLocale
              )
            : null,
          year1: etfFundamentals.ETF_Data.Performance.Returns_1Y
            ? formatPercentage(
                Decimal.div(parseFloat(etfFundamentals.ETF_Data.Performance.Returns_1Y), 100).toNumber(),
                userLocale
              )
            : null,
          year3: etfFundamentals.ETF_Data.Performance.Returns_3Y
            ? formatPercentage(
                Decimal.div(parseFloat(etfFundamentals.ETF_Data.Performance.Returns_3Y), 100).toNumber(),
                userLocale
              )
            : null,
          year5: etfFundamentals.ETF_Data.Performance.Returns_5Y
            ? formatPercentage(
                Decimal.div(parseFloat(etfFundamentals.ETF_Data.Performance.Returns_5Y), 100).toNumber(),
                userLocale
              )
            : null,
          year10: etfFundamentals.ETF_Data.Performance.Returns_10Y
            ? formatPercentage(
                Decimal.div(parseFloat(etfFundamentals.ETF_Data.Performance.Returns_10Y), 100).toNumber(),
                userLocale
              )
            : null,
          volatilityYear1: etfFundamentals.ETF_Data.Performance?.["1y_Volatility"]
            ? formatPercentage(
                Decimal.div(parseFloat(etfFundamentals.ETF_Data.Performance?.["1y_Volatility"]), 100).toNumber(),
                userLocale
              )
            : null,
          volatilityYear3: etfFundamentals.ETF_Data.Performance?.["3y_Volatility"]
            ? formatPercentage(
                Decimal.div(parseFloat(etfFundamentals.ETF_Data.Performance?.["3y_Volatility"]), 100).toNumber(),
                userLocale
              )
            : null,
          expectedReturnYear3: etfFundamentals.ETF_Data.Performance?.["3y_ExpReturn"]
            ? formatPercentage(
                Decimal.div(parseFloat(etfFundamentals.ETF_Data.Performance?.["3y_ExpReturn"]), 100).toNumber(),
                userLocale
              )
            : null,
          sharpRatio: etfFundamentals.ETF_Data.Performance?.["3y_SharpRatio"]
            ? formatPercentage(
                Decimal.div(parseFloat(etfFundamentals.ETF_Data.Performance?.["3y_SharpRatio"]), 100).toNumber(),
                userLocale
              )
            : null
        },
        growth: {
          longTermProtectedEarnings: etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.[
            "Long-Term Projected Earnings Growth"
          ]
            ? formatPercentage(
                Decimal.div(
                  parseFloat(
                    etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.[
                      "Long-Term Projected Earnings Growth"
                    ]
                  ),
                  100
                ).toNumber(),
                userLocale
              )
            : null,
          historicalEarnings: etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.[
            "Historical Earnings Growth"
          ]
            ? formatPercentage(
                Decimal.div(
                  parseFloat(
                    etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.[
                      "Historical Earnings Growth"
                    ]
                  ),
                  100
                ).toNumber(),
                userLocale
              )
            : null,
          sales: etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.["Sales Growth"]
            ? formatPercentage(
                Decimal.div(
                  parseFloat(etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.["Sales Growth"]),
                  100
                ).toNumber(),
                userLocale
              )
            : null,
          cashflow: etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.["Cash-Flow Growth"]
            ? formatPercentage(
                Decimal.div(
                  parseFloat(
                    etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.["Cash-Flow Growth"]
                  ),
                  100
                ).toNumber(),
                userLocale
              )
            : null,
          bookValue: etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.["Book-Value Growth"]
            ? formatPercentage(
                Decimal.div(
                  parseFloat(
                    etfFundamentals.ETF_Data.Valuations_Growth.Growth_Rates_Portfolio?.["Book-Value Growth"]
                  ),
                  100
                ).toNumber(),
                userLocale
              )
            : null
        }
      },
      regionalBreakdown,
      sectorBreakdown,
      topHoldings
    };
  }

  private static async _getStockData(
    asset: publicInvestmentUniverseConfig.PublicAssetType,
    userLocale: localeConfig.LocaleType
  ): Promise<StockDataType> {
    const [assetFundamentals, assetHistoricalVolume] = await Promise.all([
      CacheUtil.getCachedDataWithFallback<EodStockFundamentalsResponseType>(
        `eod:fundamentals:${asset}`,
        async (): Promise<EodStockFundamentalsResponseType> => {
          return eodService.getAssetFundamentalsData(asset) as Promise<EodStockFundamentalsResponseType>;
        },
        (_) => PUBLIC_ASSET_FUNDAMENTALS_CACHING_TTL_SECONDS
      ),
      eodService.getHistoricalVolume(asset, {
        from: DateUtil.getYearAndMonthAndDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 30))
      })
    ]);

    const totalVolume = assetHistoricalVolume.reduce(
      (acc, entry) => Decimal.add(acc, new Decimal(entry.volume)),
      new Decimal(0)
    );
    const averageDailyVolume = Decimal.div(totalVolume, new Decimal(assetHistoricalVolume.length)).toNumber();

    const eodStockData: EodGeneratedStockDataType = PublicAssetDataCronService._generateStockDataFromEODResponse(
      assetFundamentals,
      userLocale,
      averageDailyVolume
    );

    return {
      ...eodStockData,
      key: asset
    };
  }

  private static _generateStockDataFromEODResponse(
    data: any,
    locale: localeConfig.LocaleType,
    averageDailyVolume: number
  ): EodGeneratedStockDataType {
    const currencyCode = data.General.CurrencyCode;

    const doesPayDividend = data.Highlights.DividendShare > 0;

    const peRatio = data.Highlights.PERatio
      ? new Decimal(data.Highlights.PERatio).toNumber().toLocaleString(locale, { maximumFractionDigits: 2 })
      : data.Highlights.PERatio;

    const beta = numberToTwoDecimalsStr(data.Technicals.Beta, locale);
    let earnings;

    try {
      earnings = this._getEarnings(data.Earnings, data.Highlights.MostRecentQuarter, locale, currencyCode);
    } catch (error) {
      logger.warn(`Failed while retrieving earning data for asset ${data.General.Code}`, {
        module: "PublicAssetDataCronService",
        method: "_generateStockDataFromEODResponse",
        data: error.message
      });
    }

    const financials = {
      incomeStatements: this._getIncomeStatements(data.Financials.Income_Statement, currencyCode, locale),
      cashFlows: this._getCashFlows(data.Financials.Cash_Flow, currencyCode, locale),
      balanceSheets: this._getBalanceSheets(data.Financials.Balance_Sheet, currencyCode, locale)
    };

    return {
      about: {
        logoUrl: `https://eodhd.com${data.General.LogoURL}`,
        name: data.General.Name,
        currency: data.General.CurrencyCode,
        ticker: data.General.Code,
        tradingOn: data.General.Exchange,
        isin: data.General.ISIN,
        industry: data.General.Industry,
        sector: data.General.Sector,
        doesPayDividend,
        ceo: EodUtil.getCEO(data),
        headquarters: data.General.AddressData
          ? `${data.General.AddressData?.Street}, ${data.General.AddressData?.City}, ${data.General.AddressData?.State}, ${data.General.AddressData?.Country}, ${data.General.AddressData?.ZIP}`
          : null,
        employees: data.General.FullTimeEmployees?.toLocaleString(locale),
        website: data.General.WebURL?.replace("https://www.", ""),
        description: data.General.Description
      },
      analystViews: InvestmentProductService.getStockAnalystViews(data, currencyCode, locale),
      basics: {
        marketCap: this._formatLargeNumber(data.Highlights.MarketCapitalization, currencyCode, locale),
        peRatio,
        eps: CurrencyUtil.formatCurrency(data.Highlights.EarningsShare, currencyCode, locale),
        dividendYield: formatPercentage(data.Highlights.DividendYield, locale),
        beta,
        forwardPeRatio: numberToTwoDecimalsStr(data.Valuation.ForwardPE, locale),
        ebitda: this._formatLargeNumber(data.Highlights.EBITDA, currencyCode, locale),
        exDividendDate: data.SplitsDividends.ExDividendDate
      },
      advanced: {
        priceVolume: {
          marketCap: this._formatLargeNumber(data.Highlights.MarketCapitalization, currencyCode, locale),
          averageDailyVolume: this._formatLargeNumber(averageDailyVolume, null, locale)
        },
        dividends: {
          dividendPerShare: CurrencyUtil.formatCurrency(data.Highlights.DividendShare, currencyCode, locale),
          dividendYield: formatPercentage(data.Highlights?.DividendYield, locale),
          forwardDividendPerShare: CurrencyUtil.formatCurrency(
            data.SplitsDividends.ForwardAnnualDividendRate,
            currencyCode,
            locale
          ),
          forwardDividendYield: formatPercentage(data.SplitsDividends.ForwardAnnualDividendYield, locale),
          payoutRatio: formatPercentage(data.SplitsDividends?.PayoutRatio, locale),
          exDividendDate: DateUtil.formatDateToDDMONYYYY(new Date(data.SplitsDividends.ExDividendDate)),
          dividendDate: DateUtil.formatDateToDDMONYYYY(new Date(data.SplitsDividends.DividendDate))
        },
        valuation: {
          peRatio: numberToTwoDecimalsStr(data.Valuation.TrailingPE, locale),
          forwardPe: numberToTwoDecimalsStr(data.Valuation.ForwardPE, locale),
          pegRatio: numberToTwoDecimalsStr(data.Highlights.PEGRatio, locale),
          trailingPe: numberToTwoDecimalsStr(data.Valuation.TrailingPE, locale),
          priceToSales: numberToTwoDecimalsStr(data.Valuation.PriceSalesTTM, locale),
          priceToBook: numberToTwoDecimalsStr(data.Valuation.PriceBookMRQ, locale)
        },
        earnings: {
          eps: CurrencyUtil.formatCurrency(data.Highlights.EarningsShare, currencyCode, locale),
          epsEstimateCurrent: CurrencyUtil.formatCurrency(
            data.Highlights.EPSEstimateCurrentQuarter,
            currencyCode,
            locale
          ),
          epsEstimateNext: CurrencyUtil.formatCurrency(
            data.Highlights.EPSEstimateNextQuarter,
            currencyCode,
            locale
          ),
          ebitda: this._formatLargeNumber(data.Highlights.EBITDA, currencyCode, locale),
          revenueTtm: this._formatLargeNumber(data.Highlights.RevenueTTM, currencyCode, locale),
          revenuePerShareTtm: CurrencyUtil.formatCurrency(data.Highlights.RevenuePerShareTTM, currencyCode, locale)
        },
        technicals: {
          beta,
          week52High: CurrencyUtil.formatCurrency(data.Technicals["52WeekHigh"], currencyCode, locale),
          week52Low: CurrencyUtil.formatCurrency(data.Technicals["52WeekLow"], currencyCode, locale),
          day50Ma: CurrencyUtil.formatCurrency(data.Technicals["50DayMA"], currencyCode, locale),
          day200Ma: CurrencyUtil.formatCurrency(data.Technicals["200DayMA"], currencyCode, locale),
          shortRatio: numberToTwoDecimalsStr(data.Technicals.ShortRatio, locale),
          shortPercent: formatPercentage(data.Technicals.ShortPercent, locale)
        },
        managementEffectiveness: {
          roeTtm: formatPercentage(data.Highlights.ReturnOnEquityTTM, locale),
          roaTtm: formatPercentage(data.Highlights.ReturnOnAssetsTTM, locale),
          profitMargin: formatPercentage(data.Highlights.ProfitMargin, locale),
          grossProfit: this._formatLargeNumber(data.Highlights.GrossProfitTTM, currencyCode, locale),
          operatingMargin: formatPercentage(data.Highlights.OperatingMarginTTM, locale)
        },
        sharesStats: {
          outstandingShares: this._formatLargeNumber(data.SharesStats.SharesOutstanding, null, locale),
          float: this._formatLargeNumber(data.SharesStats.SharesFloat, null, locale),
          insiders: data.SharesStats.PercentInsiders
            ? formatPercentage(Decimal.div(new Decimal(data.SharesStats.PercentInsiders), 100).toNumber(), locale)
            : null,
          institutions: data.SharesStats.PercentInstitutions
            ? formatPercentage(
                Decimal.div(new Decimal(data.SharesStats.PercentInstitutions), 100).toNumber(),
                locale
              )
            : null
        },
        growth: {
          quarterlyEarningsGrowthYoy: formatPercentage(data.Highlights.QuarterlyEarningsGrowthYOY, locale),
          quarterlyRevenueGrowthYoy: formatPercentage(data.Highlights.QuarterlyRevenueGrowthYOY, locale)
        }
      },
      earnings,
      financials
    };
  }

  private static _getEarnings(
    earningsData: any,
    lastReportedQuarter: string,
    locale: localeConfig.LocaleType,
    tradedCurrency: currenciesConfig.MainCurrencyType
  ): EarningsType {
    const earningsAnnual = earningsData.Annual;
    const earningsTrend = earningsData.Trend;
    const earningsQuarterly = earningsData.History;

    // We sort the dates in a descending order e.g. '1996-03-31', '1996-06-30', ... '2025-01-31'
    const sortedQuarterKeys = Object.keys(earningsQuarterly).sort(
      (date, anotherDate) => new Date(date).valueOf() - new Date(anotherDate).valueOf()
    );

    // We get the index of the last reported quarter.
    const indexOfLastReportedQuarter = sortedQuarterKeys.findIndex((value) => value === lastReportedQuarter);

    // We want to start 3 quarters before the last reported so that we include those 3, and slice until the
    // current quarter.
    const last5QuarterKeys = sortedQuarterKeys.slice(
      Math.max(indexOfLastReportedQuarter - 3, 0),
      Math.min(indexOfLastReportedQuarter + 2, sortedQuarterKeys.length - 1)
    );

    // Last 5 years annual keys in ascending order
    const annualKey5YearsAgo = Object.keys(earningsAnnual).sort().slice(-5);

    return {
      quarterly: {
        chart: last5QuarterKeys.map((date) => {
          const data = earningsQuarterly[date];
          return {
            date: data.date,
            epsActual: data.epsActual,
            epsEstimate: data.epsEstimate,
            surprisePercent: data.surprisePercent,
            displayDate: DateUtil.getQuarterlyDisplayDate(data.date)
          };
        }),
        table: last5QuarterKeys.map((date) => {
          const data = earningsQuarterly[date];
          return {
            date: data.date,
            epsActual: data.epsActual
              ? CurrencyUtil.formatCurrency(data.epsActual, tradedCurrency, locale, true)
              : null,
            epsEstimate: data.epsEstimate
              ? CurrencyUtil.formatCurrency(data.epsEstimate, tradedCurrency, locale, true)
              : null,
            surprisePercent: data.surprisePercent
              ? formatPercentage(new Decimal(data.surprisePercent).div(100).toNumber(), locale)
              : null,
            displayDate: DateUtil.getQuarterlyDisplayDate(data.date)
          };
        })
      },

      annual: {
        table: annualKey5YearsAgo.map((date, index) => {
          const isCurrentYear = index === annualKey5YearsAgo.length - 1;
          const actual = earningsAnnual[date];
          const estimates = earningsTrend[date];
          return {
            date: estimates?.date || actual?.date,
            epsActual: !isCurrentYear
              ? CurrencyUtil.formatCurrency(actual.epsActual, tradedCurrency, locale, true)
              : null,
            epsEstimate: estimates
              ? CurrencyUtil.formatCurrency(estimates.epsTrend60daysAgo, tradedCurrency, locale, true)
              : null,
            surprisePercent:
              !isCurrentYear && estimates
                ? formatPercentage(
                    PublicAssetDataCronService._calculateGrowth(actual.epsActual, estimates.epsTrend60daysAgo),
                    locale
                  )
                : null,
            displayDate: DateUtil.getAnnualDisplayDate(estimates?.date || actual?.date)
          };
        }),
        chart: annualKey5YearsAgo.map((date, index) => {
          const isCurrentYear = index === annualKey5YearsAgo.length - 1;
          const actual = earningsAnnual[date];
          const estimates = earningsTrend[date];
          return {
            date: estimates?.date || actual?.date,
            epsActual: !isCurrentYear ? actual.epsActual : null,
            epsEstimate: parseFloat(estimates?.epsTrend60daysAgo || null),
            surprisePercent:
              !isCurrentYear && estimates
                ? PublicAssetDataCronService._calculateGrowth(actual.epsActual, estimates.epsTrend60daysAgo)
                : null,
            displayDate: DateUtil.getAnnualDisplayDate(estimates?.date || actual?.date)
          };
        })
      }
    };
  }

  private static _getIncomeStatements(
    incomeStatementsData: any,
    tradedCurrency: currenciesConfig.CurrencyType,
    locale: localeConfig.LocaleType
  ): IncomeStatementsType {
    const quarterlyIncomeStatements = incomeStatementsData.quarterly;
    const annualIncomeStatements = incomeStatementsData.yearly;
    const quartersKeysSorted = Object.keys(quarterlyIncomeStatements).sort(); // Sort
    const annualKeysSorted = Object.keys(annualIncomeStatements).sort(); // Sort in ascending order

    const quarterlyDataEOD = quartersKeysSorted.slice(-4); // Get the last 4 quarters
    const annualDataEOD = annualKeysSorted.slice(-4); // Get the last 4 years

    const quarterlyDataProcessed = quarterlyDataEOD.map((date, index, array) => {
      const data: IncomeStatementEodResponse = quarterlyIncomeStatements[date];
      const previousData: IncomeStatementEodResponse | null =
        index > 0 ? quarterlyIncomeStatements[array[index - 1]] : null;
      return PublicAssetDataCronService._processEODIncomeStatement(data, previousData, TenorEnum.Quarterly);
    });

    const annualDataProcessed = annualDataEOD.map((date, index, array) => {
      const data = annualIncomeStatements[date];
      const previousData = index > 0 ? annualIncomeStatements[array[index - 1]] : null;
      return PublicAssetDataCronService._processEODIncomeStatement(data, previousData, TenorEnum.ANNUAL);
    });

    return {
      quarterly: PublicAssetDataCronService._formatIncomeStatements(
        quarterlyDataProcessed,
        TenorEnum.Quarterly,
        tradedCurrency,
        locale
      ),
      annual: PublicAssetDataCronService._formatIncomeStatements(
        annualDataProcessed,
        TenorEnum.ANNUAL,
        tradedCurrency,
        locale
      )
    };
  }

  private static _processEODIncomeStatement(
    data: IncomeStatementEodResponse,
    previousData: IncomeStatementEodResponse | null,
    tenor: TenorEnum
  ): ProcessedIncomeStatement {
    const profitMargin =
      data.netIncome && data.totalRevenue
        ? Decimal.div(new Decimal(data.netIncome), data.totalRevenue).toNumber()
        : null;
    const previousProfitMargin =
      previousData && previousData.totalRevenue && previousData.netIncome
        ? Decimal.div(new Decimal(previousData.netIncome), previousData.totalRevenue).toNumber()
        : null;

    return {
      totalRevenue: parseFloat(data.totalRevenue),
      netIncome: parseFloat(data.netIncome),
      profitMargin: profitMargin,
      ebitda: parseFloat(data.ebitda),
      grossProfit: parseFloat(data.grossProfit),
      totalRevenueGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(
            parseFloat(data.totalRevenue),
            parseFloat(previousData.totalRevenue)
          )
        : null,
      netIncomeGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(
            parseFloat(data.netIncome),
            parseFloat(previousData.netIncome)
          )
        : null,
      profitMarginGrowth:
        profitMargin && previousProfitMargin
          ? PublicAssetDataCronService._calculateGrowth(profitMargin, previousProfitMargin)
          : null,
      ebitdaGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(parseFloat(data.ebitda), parseFloat(previousData.ebitda))
        : null,
      grossProfitGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(
            parseFloat(data.grossProfit),
            parseFloat(previousData.grossProfit)
          )
        : null,
      displayDate:
        tenor === TenorEnum.Quarterly
          ? DateUtil.getQuarterlyDisplayDate(data.date)
          : DateUtil.getAnnualDisplayDate(data.date),
      fillingDate: data.filing_date
    };
  }

  private static _formatIncomeStatements(
    data: ProcessedIncomeStatement[],
    tenor: TenorEnum,
    tradedCurrency: currenciesConfig.CurrencyType,
    locale: localeConfig.LocaleType
  ) {
    // We need at least two tenors to be able to calculate financial metrics.
    if (data.length < 2) {
      return;
    }

    const lastTwoTenors = data.slice(-2);
    const lastTwoTenorData = lastTwoTenors.map((incomeStatement) => ({
      totalRevenue: PublicAssetDataCronService._formatLargeNumber(
        incomeStatement.totalRevenue,
        tradedCurrency,
        locale
      ),
      netIncome: PublicAssetDataCronService._formatLargeNumber(incomeStatement.netIncome, tradedCurrency, locale),
      profitMargin: formatPercentage(incomeStatement.profitMargin, locale),
      ebitda: PublicAssetDataCronService._formatLargeNumber(incomeStatement.ebitda, tradedCurrency, locale),
      grossProfit: PublicAssetDataCronService._formatLargeNumber(
        incomeStatement.grossProfit,
        tradedCurrency,
        locale
      ),
      displayDate: incomeStatement.displayDate,
      fillingDate: incomeStatement.fillingDate
    }));
    const tableData = [
      ...lastTwoTenorData,
      {
        totalRevenue: formatPercentage(lastTwoTenors[1].totalRevenueGrowth, locale),
        netIncome: formatPercentage(lastTwoTenors[1].netIncomeGrowth, locale),
        profitMargin: formatPercentage(lastTwoTenors[1].profitMarginGrowth, locale),
        ebitda: formatPercentage(lastTwoTenors[1].ebitdaGrowth, locale),
        grossProfit: formatPercentage(lastTwoTenors[1].grossProfitGrowth, locale),
        displayDate: tenor === TenorEnum.Quarterly ? "QoQ growth" : "YoY growth",
        fillingDate: lastTwoTenors[1].fillingDate
      }
    ];

    const chartData = data.map((incomeStatement) => ({
      totalRevenue: incomeStatement.totalRevenue,
      netIncome: incomeStatement.netIncome,
      profitMargin: incomeStatement.profitMargin,
      ebitda: incomeStatement.ebitda,
      grossProfit: incomeStatement.grossProfit,
      displayDate: incomeStatement.displayDate,
      fillingDate: incomeStatement.fillingDate
    }));
    return {
      chart: chartData,
      table: tableData
    };
  }

  private static _getBalanceSheets(
    balanceSheetsData: any,
    tradedCurrency: currenciesConfig.CurrencyType,
    locale: localeConfig.LocaleType
  ): BalanceSheetsType {
    const quarterlyBalanceSheets = balanceSheetsData.quarterly;
    const annualBalanceSheets = balanceSheetsData.yearly;
    const quartersKeysSorted = Object.keys(quarterlyBalanceSheets).sort(); // Sort
    const annualKeysSorted = Object.keys(annualBalanceSheets).sort(); // Sort in ascending order

    const quarterlyDataEOD = quartersKeysSorted.slice(-4); // Get the last 4 quarters
    const annualDataEOD = annualKeysSorted.slice(-4); // Get the last 4 years

    const quarterlyDataProcessed = quarterlyDataEOD.map((date, index, array) => {
      const data: BalanceSheetEodResponse = quarterlyBalanceSheets[date];
      const previousData: BalanceSheetEodResponse | null =
        index > 0 ? quarterlyBalanceSheets[array[index - 1]] : null;
      return PublicAssetDataCronService._processEODBalanceSheet(data, previousData, TenorEnum.Quarterly);
    });

    const annualDataProcessed = annualDataEOD.map((date, index, array) => {
      const data = annualBalanceSheets[date];
      const previousData = index > 0 ? annualBalanceSheets[array[index - 1]] : null;
      return PublicAssetDataCronService._processEODBalanceSheet(data, previousData, TenorEnum.ANNUAL);
    });

    return {
      quarterly: PublicAssetDataCronService._formatBalanceSheets(
        quarterlyDataProcessed,
        TenorEnum.Quarterly,
        tradedCurrency,
        locale
      ),
      annual: PublicAssetDataCronService._formatBalanceSheets(
        annualDataProcessed,
        TenorEnum.ANNUAL,
        tradedCurrency,
        locale
      )
    };
  }
  private static _processEODBalanceSheet(
    data: BalanceSheetEodResponse,
    previousData: BalanceSheetEodResponse | null,
    tenor: TenorEnum
  ): ProcessedBalanceSheet {
    const debtToAssets =
      data.totalAssets && data.totalLiab
        ? Decimal.div(new Decimal(data.totalLiab), data.totalAssets).toNumber()
        : null;
    const previousDebtToAssets =
      previousData && previousData.totalAssets && previousData.totalLiab
        ? Decimal.div(new Decimal(previousData.totalLiab), previousData.totalAssets).toNumber()
        : null;

    return {
      totalAssets: parseFloat(data.totalAssets),
      totalLiabilities: parseFloat(data.totalLiab),
      debtToAssets: debtToAssets,
      totalAssetsGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(
            parseFloat(data.totalAssets),
            parseFloat(previousData.totalAssets)
          )
        : null,
      totalLiabilitiesGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(
            parseFloat(data.totalLiab),
            parseFloat(previousData.totalLiab)
          )
        : null,
      debtToAssetsGrowth:
        debtToAssets && previousDebtToAssets
          ? PublicAssetDataCronService._calculateGrowth(debtToAssets, previousDebtToAssets)
          : null,
      displayDate:
        tenor === TenorEnum.Quarterly
          ? DateUtil.getQuarterlyDisplayDate(data.date)
          : DateUtil.getAnnualDisplayDate(data.date),
      fillingDate: data.filing_date
    };
  }

  private static _formatBalanceSheets(
    data: ProcessedBalanceSheet[],
    tenor: TenorEnum,
    tradedCurrency: currenciesConfig.CurrencyType,
    locale: localeConfig.LocaleType
  ) {
    // We need at least two tenors to be able to calculate financial metrics.
    if (data.length < 2) {
      return;
    }

    const lastTwoTenors = data.slice(-2);
    const lastTwoTenorData = lastTwoTenors.map((balanceSheet) => ({
      totalAssets: PublicAssetDataCronService._formatLargeNumber(balanceSheet.totalAssets, tradedCurrency, locale),
      totalLiabilities: PublicAssetDataCronService._formatLargeNumber(
        balanceSheet.totalLiabilities,
        tradedCurrency,
        locale
      ),
      debtToAssets: formatPercentage(balanceSheet.debtToAssets, locale),
      displayDate: balanceSheet.displayDate,
      fillingDate: balanceSheet.fillingDate
    }));
    const tableData = [
      ...lastTwoTenorData,
      {
        totalAssets: formatPercentage(lastTwoTenors[1].totalAssetsGrowth, locale),
        totalLiabilities: formatPercentage(lastTwoTenors[1].totalLiabilitiesGrowth, locale),
        debtToAssets: formatPercentage(lastTwoTenors[1].debtToAssetsGrowth, locale),
        displayDate: tenor === TenorEnum.Quarterly ? "QoQ growth" : "YoY growth",
        fillingDate: lastTwoTenors[1].fillingDate
      }
    ];

    const chartData = data.map((balanceSheet) => ({
      totalAssets: balanceSheet.totalAssets,
      totalLiabilities: balanceSheet.totalLiabilities,
      debtToAssets: balanceSheet.debtToAssets,
      displayDate: balanceSheet.displayDate,
      fillingDate: balanceSheet.fillingDate
    }));
    return {
      chart: chartData,
      table: tableData
    };
  }

  private static _getCashFlows(
    cashFlowsData: any,
    tradedCurrency: currenciesConfig.CurrencyType,
    locale: localeConfig.LocaleType
  ): CashFlowsType {
    const quarterlyCashFlows = cashFlowsData.quarterly;
    const annualCashFlows = cashFlowsData.yearly;
    const quartersKeysSorted = Object.keys(quarterlyCashFlows).sort(); // Sort
    const annualKeysSorted = Object.keys(annualCashFlows).sort(); // Sort in ascending order

    const quarterlyDataEOD = quartersKeysSorted.slice(-4); // Get the last 4 quarters
    const annualDataEOD = annualKeysSorted.slice(-4); // Get the last 4 years

    const quarterlyDataProcessed = quarterlyDataEOD.map((date, index, array) => {
      const data: CashFlowEodResponse = quarterlyCashFlows[date];
      const previousData: CashFlowEodResponse | null = index > 0 ? quarterlyCashFlows[array[index - 1]] : null;
      return PublicAssetDataCronService._processEODCashFlow(data, previousData, TenorEnum.Quarterly);
    });

    const annualDataProcessed = annualDataEOD.map((date, index, array) => {
      const data = annualCashFlows[date];
      const previousData = index > 0 ? annualCashFlows[array[index - 1]] : null;
      return PublicAssetDataCronService._processEODCashFlow(data, previousData, TenorEnum.ANNUAL);
    });

    return {
      quarterly: PublicAssetDataCronService._formatCashFlows(
        quarterlyDataProcessed,
        TenorEnum.Quarterly,
        tradedCurrency,
        locale
      ),
      annual: PublicAssetDataCronService._formatCashFlows(
        annualDataProcessed,
        TenorEnum.ANNUAL,
        tradedCurrency,
        locale
      )
    };
  }

  private static _processEODCashFlow(
    data: CashFlowEodResponse,
    previousData: CashFlowEodResponse | null,
    tenor: TenorEnum
  ): ProcessedCashFlow {
    return {
      operatingActivities: parseFloat(data.totalCashFromOperatingActivities),
      investments: parseFloat(data.investments),
      financingActivities: parseFloat(data.totalCashFromFinancingActivities),
      freeCashFlow: parseFloat(data.freeCashFlow),
      operatingActivitiesGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(
            parseFloat(data.totalCashFromOperatingActivities),
            parseFloat(previousData.totalCashFromOperatingActivities)
          )
        : null,
      investmentsGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(
            parseFloat(data.investments),
            parseFloat(previousData.investments)
          )
        : null,
      financingActivitiesGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(
            parseFloat(data.totalCashFromFinancingActivities),
            parseFloat(previousData.totalCashFromFinancingActivities)
          )
        : null,
      freeCashFlowGrowth: previousData
        ? PublicAssetDataCronService._calculateGrowth(
            parseFloat(data.freeCashFlow),
            parseFloat(previousData.freeCashFlow)
          )
        : null,

      displayDate:
        tenor === TenorEnum.Quarterly
          ? DateUtil.getQuarterlyDisplayDate(data.date)
          : DateUtil.getAnnualDisplayDate(data.date),
      fillingDate: data.filing_date
    };
  }

  private static _formatCashFlows(
    data: ProcessedCashFlow[],
    tenor: TenorEnum,
    tradedCurrency: currenciesConfig.CurrencyType,
    locale: localeConfig.LocaleType
  ) {
    // We need at least two tenors to be able to calculate financial metrics.
    if (data.length < 2) {
      return;
    }

    const lastTwoTenors = data.slice(-2);
    const lastTwoTenorData = lastTwoTenors.map((incomeStatement) => ({
      operatingActivities: PublicAssetDataCronService._formatLargeNumber(
        incomeStatement.operatingActivities,
        tradedCurrency,
        locale
      ),
      investments: PublicAssetDataCronService._formatLargeNumber(
        incomeStatement.investments,
        tradedCurrency,
        locale
      ),
      financingActivities: PublicAssetDataCronService._formatLargeNumber(
        incomeStatement.financingActivities,
        tradedCurrency,
        locale
      ),
      freeCashFlow: PublicAssetDataCronService._formatLargeNumber(
        incomeStatement.freeCashFlow,
        tradedCurrency,
        locale
      ),
      displayDate: incomeStatement.displayDate,
      fillingDate: incomeStatement.fillingDate
    }));
    const tableData = [
      ...lastTwoTenorData,
      {
        operatingActivities: lastTwoTenors[1].operatingActivitiesGrowth
          ? formatPercentage(lastTwoTenors[1].operatingActivitiesGrowth, locale)
          : null,
        investments: lastTwoTenors[1].investmentsGrowth
          ? formatPercentage(lastTwoTenors[1].investmentsGrowth, locale)
          : null,
        financingActivities: formatPercentage(lastTwoTenors[1].financingActivitiesGrowth, locale),
        freeCashFlow: formatPercentage(lastTwoTenors[1].freeCashFlowGrowth, locale),
        displayDate: tenor === TenorEnum.Quarterly ? "QoQ growth" : "YoY growth",
        fillingDate: lastTwoTenors[1].fillingDate
      }
    ];

    const chartData = data.map((incomeStatement) => ({
      operatingActivities: incomeStatement.operatingActivities,
      investments: incomeStatement.investments,
      financingActivities: incomeStatement.financingActivities,
      freeCashFlow: incomeStatement.freeCashFlow,
      displayDate: incomeStatement.displayDate,
      fillingDate: incomeStatement.fillingDate
    }));
    return {
      chart: chartData,
      table: tableData
    };
  }

  private static _formatLargeNumber(
    number: number | null | undefined,
    tradedCurrency: currenciesConfig.CurrencyType | null,
    locale: localeConfig.LocaleType
  ): string {
    if (number === null || number === undefined) return null;

    const numberDecimal = new Decimal(number);
    const absNumberDecimal = numberDecimal.abs();
    const formatOptions = [
      { threshold: 1e8, divisor: 1e6, suffix: "M", maximumFractionDigits: 1 },
      { threshold: 1e9, divisor: 1e6, suffix: "M", maximumFractionDigits: 0 },
      { threshold: 1e10, divisor: 1e9, suffix: "B", maximumFractionDigits: 2 },
      { threshold: 1e11, divisor: 1e9, suffix: "B", maximumFractionDigits: 1 },
      { threshold: 1e12, divisor: 1e9, suffix: "B", maximumFractionDigits: 0 },
      { threshold: 1e13, divisor: 1e12, suffix: "T", maximumFractionDigits: 2 },
      { threshold: 1e14, divisor: 1e12, suffix: "T", maximumFractionDigits: 1 }
    ];

    for (const { threshold, divisor, suffix, maximumFractionDigits } of formatOptions) {
      if (absNumberDecimal.lessThan(threshold)) {
        const formattedNumber = numberDecimal.div(divisor).toNumber().toLocaleString(locale, {
          maximumFractionDigits
        });
        return `${tradedCurrency ? CURRENCY_SYMBOLS[tradedCurrency] : ""}${formattedNumber}${suffix}`;
      }
    }

    return null;
  }

  private static _calculateGrowth = (current: number, previous: number): number => {
    return Decimal.div(new Decimal(current - previous), previous).toNumber();
  };
}
