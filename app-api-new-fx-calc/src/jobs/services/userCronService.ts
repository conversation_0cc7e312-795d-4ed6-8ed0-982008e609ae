import UserService from "../../services/userService";
import { User } from "../../models/User";
import { ProviderEnum } from "../../configs/providersConfig";
import DateUtil from "../../utils/dateUtil";

export default class UserCronService {
  /**
   * @description Creates GoCardless customers for all eligible users that are missing a GoCardless entity.
   * */
  public static async createAllGoCardlessCustomers(): Promise<void> {
    const usersMissingGoCardlessEntity = await User.find({
      activeProviders: ProviderEnum.GOCARDLESS,
      $or: [{ "providers.gocardless.id": { $exists: false } }, { "providers.gocardless.id": { $eq: undefined } }],
      submittedRequiredInfoAt: { $lt: DateUtil.getDateOfMinutesAgo(10) }
    });

    for (let i = 0; i < usersMissingGoCardlessEntity.length; i++) {
      await UserService.createGCEntry(usersMissingGoCardlessEntity[i]);
    }
  }
}
