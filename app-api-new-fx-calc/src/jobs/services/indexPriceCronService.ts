import logger from "../../external-services/loggerService";
import { indexesConfig } from "@wealthyhood/shared-configs";
import { captureException } from "@sentry/node";
import DateUtil from "../../utils/dateUtil";
import { Entries } from "utils";
import { RedisClientService } from "../../loaders/redis";
import { IndexPrice } from "../../models/IndexPrice";
import IndexPriceUtil from "../../utils/indexPriceUtil";
import { getIndexIdFromTicker } from "../../utils/investmentUniverseUtil";
import Decimal from "decimal.js";
import { FMPService, QuoteType } from "../../external-services/fmpService";

const { IndexArrayConst, INDEX_CONFIG_GLOBAL } = indexesConfig;

export default class IndexPriceCronService {
  /**
   * @description Creates all index returns for our pre-defined list of tracked indexes.
   *
   * Prices stored are retrieved from a cache of realtime prices we keep from FMP realtime data.
   */
  public static async storeIndexPricesFromRealtimeCache(): Promise<void> {
    logger.info("Storing index prices from realtime cache...", {
      module: "IndexPriceCronService",
      method: "storeIndexPricesFromRealtimeCache"
    });

    const latestIndexPrices = await IndexPriceUtil.getCachedCurrentIndexPrices();
    if (Object.keys(latestIndexPrices).length === 0) return;

    const dbOperations = await Promise.all(
      (
        Object.entries(latestIndexPrices) as Entries<
          Record<indexesConfig.IndexType, { close: number; timestamp: number; dailyReturnPercentage: number }>
        >
      ).map(
        async ([indexId, { close, timestamp, dailyReturnPercentage }]: [
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        ]) => {
          return {
            insertOne: {
              document: {
                index: indexId,
                date: new Date(timestamp),
                price: close,
                dailyReturnPercentage: dailyReturnPercentage
              }
            }
          };
        }
      )
    );

    await IndexPrice.bulkWrite(dbOperations.filter((operation) => !!operation));
  }

  /**
   * @description Method that caches current index prices using FMP API.
   */
  public static async cacheCurrentIndexPrices() {
    try {
      const symbols = [...IndexArrayConst].map((index) => INDEX_CONFIG_GLOBAL[index].symbol);
      const quotes = await FMPService.Instance.getBatchQuote(symbols);

      if (quotes.length !== symbols.length) {
        logger.warn("FMP index response size does not match request size", {
          module: "IndexPriceCronService",
          method: "cacheCurrentIndexPrices",
          data: {
            requested: symbols,
            received: quotes.map((q) => q.symbol)
          }
        });
      }

      const latestIndexPrices = Object.fromEntries(
        quotes.map((quote: QuoteType) => [
          getIndexIdFromTicker(quote.symbol),
          {
            close: quote.price,
            timestamp: Decimal.mul(quote.timestamp, 1000).toNumber(), // We store timestamps in Redis as milliseconds
            dailyReturnPercentage: Decimal.div(quote.changePercentage, 100).toNumber()
          }
        ])
      );

      // Filter out any empty prices & prices that are not today's
      const { start } = DateUtil.getStartAndEndOfToday();
      const filteredIndexPrices = Object.fromEntries(
        Object.entries(latestIndexPrices).filter(
          ([, entry]) => entry && entry.close > 0 && entry.timestamp > new Date(start).getTime()
        )
      );

      if (Object.keys(filteredIndexPrices).length > 0) {
        await RedisClientService.Instance.set("fmp:today:latest_index_prices", filteredIndexPrices);
      }
    } catch (err) {
      captureException(err);
      logger.error("Caching today's index prices failed.", {
        module: "IndexPriceCronService",
        method: "cacheCurrentIndexPrices",
        data: { error: err }
      });
    }
  }
}
