import { removeSpaces } from "./../../utils/stringUtil";
import type { Readable } from "stream";
import eodService, {
  EOD_BASE_URL,
  EodETFFundamentalsResponseType,
  EodETFHoldingDataType,
  EodStockFundamentalsResponseType
} from "./../../external-services/eodService";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import axios from "axios";
import logger from "../../external-services/loggerService";
import CloudflareSerivce, {
  BUCKET_URL_CONFIG,
  BucketsEnum,
  ContentTypeEnum
} from "../../external-services/cloudflareService";
import { captureException } from "@sentry/node";
import { RedisClientService } from "../../loaders/redis";
import { NotFoundError } from "../../models/ApiErrors";

const { ASSET_CONFIG, AssetArrayConst } = investmentUniverseConfig;

const ASSET_CLASSES_TO_INCLUDE: investmentUniverseConfig.AssetClassType[] = ["equities"];

export class EtfHoldingLogosCronService {
  /**
   * @description
   *  Stores etf holding logos provided by EOD to Cloudflare
   */
  public static async storeMissingEtfHoldingLogos(): Promise<void> {
    const etfs = AssetArrayConst.filter(
      (assetCommonId) =>
        ASSET_CONFIG[assetCommonId].category === "etf" &&
        ASSET_CLASSES_TO_INCLUDE.includes(ASSET_CONFIG[assetCommonId].assetClass)
    );

    for (let i = 0; i < etfs.length; i++) {
      const etf = etfs[i];
      const fundamentalsData = (await eodService.getAssetFundamentalsData(etf)) as EodETFFundamentalsResponseType;

      await Promise.all(
        Object.entries(fundamentalsData.ETF_Data.Top_10_Holdings).map(
          EtfHoldingLogosCronService._processHoldingLogo
        )
      );
    }
  }

  /**
   *  @description
   *  Caches etf holdings that have custom logos
   */
  public static async cacheCustomEtfHoldingLogos(): Promise<void> {
    const etfs = AssetArrayConst.filter(
      (assetCommonId) =>
        ASSET_CONFIG[assetCommonId].category === "etf" &&
        ASSET_CLASSES_TO_INCLUDE.includes(ASSET_CONFIG[assetCommonId].assetClass)
    );

    for (let i = 0; i < etfs.length; i++) {
      const etf = etfs[i];
      const fundamentalsData = (await eodService.getAssetFundamentalsData(etf)) as EodETFFundamentalsResponseType;

      const holdingWithCustomLogoPairs = await Promise.all(
        Object.values(fundamentalsData.ETF_Data.Top_10_Holdings).map(
          EtfHoldingLogosCronService._getCustomHoldingLogo
        )
      );

      const etfHoldingCodesToCustomLogos: Record<string, string> = Object.fromEntries(
        holdingWithCustomLogoPairs.filter(
          ([eodHoldingCode, customLogoUrl]: [string, string]) => customLogoUrl != null
        )
      );

      await RedisClientService.Instance.set(`eod:customLogos:${etf}`, etfHoldingCodesToCustomLogos);
    }
  }

  /**
   * @description
   * Logic:
   * Check if logo is stored in cloudflare.
   * If not try getting the logoUrl from EOD.
   * If that succeeds upload missing logo to cloudflare,
   * otherwise report missing so that it can be replaced manually.
   */
  private static async _processHoldingLogo([fundamentalsTicker, eodHolding]: [
    string,
    EodETFHoldingDataType
  ]): Promise<void> {
    const isLogoStoredInCF = await EtfHoldingLogosCronService._isEodLogoStoredInCloudflare(eodHolding.Code);
    if (isLogoStoredInCF) return;

    const eodLogoUrl = await EtfHoldingLogosCronService._getHoldingLogoFromEOD(fundamentalsTicker);
    if (!eodLogoUrl) {
      logger.error(`Could not find logo from eod for ${eodHolding.Code} holding`, {
        module: "EtfHoldingLogosCronService",
        method: "_processHoldingLogo",
        data: {
          eodHolding
        }
      });
      return;
    }

    await EtfHoldingLogosCronService._storeLogoToCloudflare(eodHolding.Code, eodLogoUrl);
  }

  private static _isEodLogoStoredInCloudflare(etfHoldingCode: string): Promise<boolean> {
    return CloudflareSerivce.Instance.doesObjectExists(
      BucketsEnum.ETF_HOLDING_LOGOS,
      EtfHoldingLogosCronService._getEodLogoPathFromEodHoldingCode(etfHoldingCode)
    );
  }

  private static async _getHoldingLogoFromEOD(fundamentalsTicker: string): Promise<string | null> {
    try {
      const holdingFundamentals = (await eodService.getAssetFundamentalsDataByFundamentalsTicker(
        fundamentalsTicker
      )) as EodStockFundamentalsResponseType;

      const eodLogoUrl = holdingFundamentals.General.LogoURL;
      if (!eodLogoUrl) return null;

      return `${EOD_BASE_URL}/${eodLogoUrl}`;
    } catch (err) {
      // Do not report error if it's 404
      if (err instanceof NotFoundError) return null;

      captureException(err);
      logger.error(`Unexpected error while comminicated with EOD for ${fundamentalsTicker}`, {
        module: "EtfHoldingLogosCronService",
        method: "_getHoldingLogoFromEOD",
        data: {
          fundamentalsTicker,
          error: JSON.stringify(err)
        }
      });
      return null;
    }
  }

  private static async _storeLogoToCloudflare(code: string, logoUrl: string): Promise<void> {
    try {
      logger.info(`About to upload ${code} logo`, {
        module: "EtfHoldingLogosCronService",
        method: "_storeLogoToCloudflare",
        data: {
          code,
          logoUrl
        }
      });
      const response = await axios.get<Readable>(logoUrl, { responseType: "stream" });

      const eodHoldingLogoPath = EtfHoldingLogosCronService._getEodLogoPathFromEodHoldingCode(code);
      await CloudflareSerivce.Instance.uploadObject(
        BucketsEnum.ETF_HOLDING_LOGOS,
        eodHoldingLogoPath,
        response.data,
        {
          contentType: ContentTypeEnum.IMAGE_PNG
        }
      );
    } catch (err) {
      captureException(err);
      logger.error(`${code} logo upload failed`, {
        module: "EtfHoldingLogosCronService",
        method: "_storeLogoToCloudflare",
        data: {
          code,
          logoUrl,
          error: JSON.stringify(err)
        }
      });
    }
  }

  private static async _getCustomHoldingLogo(eodHolding: EodETFHoldingDataType): Promise<[string, string | null]> {
    const customLogoPath = EtfHoldingLogosCronService._getCustomLogoPathFromEodHoldingCode(eodHolding.Code);
    const doesCustomHoldingLogoExists = await CloudflareSerivce.Instance.doesObjectExists(
      BucketsEnum.ETF_HOLDING_LOGOS,
      customLogoPath
    );

    if (doesCustomHoldingLogoExists) {
      return [eodHolding.Code, `${BUCKET_URL_CONFIG[BucketsEnum.ETF_HOLDING_LOGOS]}/${customLogoPath}`];
    } else {
      return [eodHolding.Code, null];
    }
  }

  private static _getEodLogoPathFromEodHoldingCode(eodHoldingCode: string): string {
    return `eod/${removeSpaces(eodHoldingCode)}.png`;
  }

  private static _getCustomLogoPathFromEodHoldingCode(eodHoldingCode: string): string {
    return `${removeSpaces(eodHoldingCode)}.png`;
  }
}
