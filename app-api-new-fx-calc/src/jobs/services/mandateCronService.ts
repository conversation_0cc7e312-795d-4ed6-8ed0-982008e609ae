import { BankAccountDocument } from "../../models/BankAccount";
import { Mandate, MandateDocument } from "../../models/Mandate";
import { UserDocument } from "../../models/User";
import MandateService from "../../services/mandateService";
import { ProviderEnum } from "../../configs/providersConfig";
import { envIsProd } from "../../utils/environmentUtil";

export default class MandateCronService {
  public static async createAllWkMandates(): Promise<void> {
    const mandatesPendingWKEntry = await Mandate.find({
      category: "Top-Up",
      activeProviders: ProviderEnum.WEALTHKERNEL,
      "providers.wealthkernel.id": { $exists: false }
    }).populate([
      {
        path: "owner"
      },
      {
        path: "bankAccount"
      }
    ]);

    const mandatePromises = mandatesPendingWKEntry
      .filter(MandateCronService._filterMandateToSubmit)
      .map(MandateService.createWkEntry);

    await Promise.all(mandatePromises);
  }

  public static async createAllGoCardlessMandates(): Promise<void> {
    const mandatesPendingWKEntry = await Mandate.find({
      category: "Top-Up",
      activeProviders: ProviderEnum.GOCARDLESS,
      "providers.gocardless.id": { $exists: false }
    }).populate([
      {
        path: "owner",
        populate: {
          path: "addresses"
        }
      },
      {
        path: "bankAccount"
      }
    ]);

    const mandatePromises = mandatesPendingWKEntry
      .filter(MandateCronService._filterMandateToSubmit)
      .map(MandateService.createGoCardlessEntry);

    await Promise.all(mandatePromises);
  }

  public static async syncPendingWkMandates(): Promise<void> {
    const mandatesPendingWkStatus = await Mandate.find({
      category: "Top-Up",
      "providers.wealthkernel.id": { $exists: true, $ne: null },
      "providers.wealthkernel.status": "Pending"
    });

    for (let i = 0; i < mandatesPendingWkStatus.length; i++) {
      const mandate = mandatesPendingWkStatus[i];
      await MandateService.syncWkEntry(mandate);
    }
  }

  public static async syncActiveWkMandates(): Promise<void> {
    await Mandate.find({
      category: "Top-Up",
      "providers.wealthkernel.id": { $exists: true, $ne: null },
      "providers.wealthkernel.status": "Active"
    })
      .cursor()
      .addCursorFlag("noCursorTimeout", envIsProd())
      .eachAsync((mandate) => MandateService.syncWkEntry(mandate));
  }

  private static _filterMandateToSubmit(mandate: MandateDocument): boolean {
    if (mandate.activeProviders.includes(ProviderEnum.WEALTHKERNEL)) {
      const user = mandate.owner as UserDocument;
      const bankAccount = mandate.bankAccount as BankAccountDocument;

      return (
        !!user?.providers?.wealthkernel?.id &&
        bankAccount.providers?.wealthkernel?.status === "Active" &&
        !!bankAccount.providers?.wealthkernel?.id
      );
    }

    if (mandate.activeProviders.includes(ProviderEnum.GOCARDLESS)) {
      const user = mandate.owner as UserDocument;
      const bankAccount = mandate.bankAccount as BankAccountDocument;

      return !!user?.providers?.gocardless?.id && !!bankAccount?.providers?.gocardless?.id;
    }

    return false;
  }
}
