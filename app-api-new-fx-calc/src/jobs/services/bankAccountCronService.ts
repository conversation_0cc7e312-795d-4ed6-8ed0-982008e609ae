import { ProviderEnum } from "../../configs/providersConfig";
import { BankAccount, BankAccountDocument } from "../../models/BankAccount";
import { UserDocument } from "../../models/User";
import BankAccountService from "../../services/bankAccountService";
import DateUtil from "../../utils/dateUtil";

export default class BankAccountCronService {
  /**
   * @description Creates GoCardless bank accounts for all bank accounts that should have one (i.e. have a
   * GoCardless active provider) and do not already have a GoCardless entity.
   */
  public static async createAllGoCardlessBankAccounts(): Promise<void> {
    const bankAccountsPendingGCEntry = await BankAccount.find({
      activeProviders: ProviderEnum.GOCARDLESS,
      $or: [{ "providers.gocardless.id": { $exists: false } }, { "providers.gocardless.id": { $eq: undefined } }],
      createdAt: { $lte: DateUtil.getDateOfMinutesAgo(10) }
    }).populate("owner");

    for (let i = 0; i < bankAccountsPendingGCEntry.length; i++) {
      const bankAccount = bankAccountsPendingGCEntry[i] as BankAccountDocument;
      const owner = bankAccount.owner as UserDocument;

      if (owner?.providers?.gocardless?.id) {
        await BankAccountService.createGCEntry(bankAccount);
      }
    }
  }
}
