import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import eodService, {
  EodETFFundamentalsResponseType,
  EodFundamentalsResponseType,
  EodStockFundamentalsResponseType,
  StockSplitType
} from "../../external-services/eodService";
import CorporateEventService from "../../services/corporateEventService";
import { getPublicAssetIdFromTickerAndExchange } from "../../utils/investmentUniverseUtil";
import DateUtil from "../../utils/dateUtil";
import logger from "../../external-services/loggerService";
import { captureException } from "@sentry/node";
import { ASSET_CONFIG, AssetType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import * as CacheUtil from "../../utils/cacheUtil";
import InvestmentProductService from "../../services/investmentProductService";

const DAYS_TO_RETRIEVE_SPLITS_IN_PAST = 7;
const DAYS_TO_RETRIEVE_SPLITS_IN_FUTURE = 7;
const FUNDAMENTALS_FORMAL_TICKER_CACHE_EXPIRATION = 60 * 60 * 12; // 12 hours

export default class CorporateEventCronService {
  public static async createAllStockSplits(): Promise<void> {
    const datesToGetSplitsFor = DateUtil.getAllDatesBetweenTwoDates(
      DateUtil.getDateOfDaysAgo(new Date(Date.now()), DAYS_TO_RETRIEVE_SPLITS_IN_PAST),
      DateUtil.getDateAfterNdays(new Date(Date.now()), DAYS_TO_RETRIEVE_SPLITS_IN_FUTURE)
    );

    // We retrieve all splits for the above dates.
    const stockSplits: StockSplitType[] = (
      await Promise.all(
        datesToGetSplitsFor.flatMap((date) =>
          investmentUniverseConfig.ExchangeArrayConst.map((exchange) => eodService.getStockSplits(exchange, date))
        )
      )
    ).flat();

    for (let i = 0; i < stockSplits.length; i++) {
      const { code, exchange, date, split } = stockSplits[i];
      const assetId = getPublicAssetIdFromTickerAndExchange(code, exchange);

      try {
        if (assetId) {
          await CorporateEventService.createStockSplit({
            asset: assetId,
            date: new Date(date),
            splitRatio: split
          });
        }
      } catch (err) {
        logger.error(`Creation of stock split for ${assetId} failed!`, {
          module: "CorporateEventCronService",
          method: "createAllStockSplits"
        });
        captureException(err);
      }
    }
  }

  public static async createAllAssetIsinChanges(): Promise<void> {
    const investmentProducts = await InvestmentProductService.getInvestmentProducts({
      populateTicker: false,
      useCache: true,
      listedOnly: true
    });

    await Promise.all(
      investmentProducts.map(async (product) => {
        const assetId = product.commonId;
        const asset = ASSET_CONFIG[assetId];
        const existingISIN = asset.isin;

        try {
          const assetFundamentalsData =
            asset.category === "stock"
              ? await CacheUtil.getCachedDataWithFallback<EodFundamentalsResponseType>(
                  `eod:fundamentals:${assetId}`,
                  async (): Promise<EodFundamentalsResponseType> => {
                    return eodService.getAssetFundamentalsData(assetId) as Promise<EodFundamentalsResponseType>;
                  }
                )
              : await CacheUtil.getCachedDataWithFallback<EodFundamentalsResponseType>(
                  `eod:fundamentals:formalticker:${assetId}`,
                  async (): Promise<EodFundamentalsResponseType> => {
                    return eodService.getAssetFundamentalsData(assetId, {
                      useFormalTicker: true
                    }) as Promise<EodFundamentalsResponseType>;
                  },
                  (_) => FUNDAMENTALS_FORMAL_TICKER_CACHE_EXPIRATION
                );

          const retrievedISIN =
            asset.category === "stock"
              ? (assetFundamentalsData as EodStockFundamentalsResponseType).General.ISIN
              : (assetFundamentalsData as EodETFFundamentalsResponseType).ETF_Data.ISIN;

          if (retrievedISIN && retrievedISIN !== existingISIN) {
            await CorporateEventService.createAssetIsinChange({
              asset: assetId as AssetType,
              oldISIN: existingISIN,
              newISIN: retrievedISIN,
              date: new Date(Date.now())
            });
          }
        } catch (err) {
          logger.error(`Creation of asset isin change for ${assetId} failed!`, {
            module: "CorporateEventCronService",
            method: "createAllAssetIsinChanges"
          });
          captureException(err);
        }
      })
    );
  }
}
