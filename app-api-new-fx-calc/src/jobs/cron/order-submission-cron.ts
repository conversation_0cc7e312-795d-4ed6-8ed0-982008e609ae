import logger from "../../external-services/loggerService";
import RewardService from "../../services/rewardService";
import OrderService from "../../services/orderService";
import OrderCronService from "../services/orderCronService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class OrderSubmissionCronJob extends CronJob {
  cronName = CronJobNameEnum.ORDER_SUBMISSION;

  /**
   * @description Cron tasks:
   * 1. Create Savings order documents
   * 2. Aggregate orders and send them to Wealthkernel
   * Currently, runs every 10 minutes at work days.
   */
  async processFn(): Promise<void> {
    logger.info("🚀 Creating savings orders for submission...", { module: `cron:${this.cronName}` });
    await OrderCronService.createSavingsOrderDocuments();
    logger.info("✅ Completed savings order creation", { module: `cron:${this.cronName}` });

    logger.info("🚀 Initiating submission of orders...", { module: `cron:${this.cronName}` });
    await OrderService.createMissingAggregateWealthkernelOrders();

    await OrderService.createFallbackRealtimeWealthkernelOrders();

    await RewardService.createRewardOrders();
    logger.info("✅ Completed submission of orders", { module: `cron:${this.cronName}` });
  }
}

new OrderSubmissionCronJob().run();
