import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import PublicAssetDataCronService from "../services/publicAssetDataCronService";
import logger from "../../external-services/loggerService";

class PublicAssetDataCronJob extends CronJob {
  cronName = CronJobNameEnum.PUBLIC_ASSET_DATA;

  /**
   * @description Cron for updating historical price & fundamental asset data from EOD.
   */
  async processFn(): Promise<void> {
    logger.info("Caching public asset data...", {
      module: `cron:${this.cronName}`
    });
    await PublicAssetDataCronService.storeAssetData();
    logger.info("✅ Completed caching public asset data!", {
      module: `cron:${this.cronName}`
    });
  }
}

new PublicAssetDataCronJob().run();
