import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import DailyTickerCronService from "../services/dailyTickerCronService";
import { CronJobNameEnum } from "../configs/cronNames";

class SavingsProductTickerCronJob extends CronJob {
  cronName = CronJobNameEnum.DAILY_SAVINGS_PRODUCT_TICKER;

  /**
   * @description Cron daily savings product tickers (Monday - Sunday)
   */
  async processFn(): Promise<void> {
    logger.info("🚀 Creating daily savings product tickers...", { module: `cron:${this.cronName}` });
    await DailyTickerCronService.createDailySavingsProductTickers();
    logger.info("✅ Completed creating daily savings product tickers", { module: `cron:${this.cronName}` });
  }
}

new SavingsProductTickerCronJob().run();
