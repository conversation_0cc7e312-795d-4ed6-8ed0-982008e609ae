import logger from "../../external-services/loggerService";
import GiftService from "../../services/giftService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class GiftCapabilitiesCronJob extends CronJob {
  cronName = CronJobNameEnum.GIFT_CAPABILITIES;

  /**
   * @description Cron job that gives each user that belongs to a specific mailchimp segment
   * the capability to send gifts for a given period.
   *
   * The cron should be running once every hour on a daily basis.
   */
  async processFn(): Promise<void> {
    logger.info("🎁 Initiating gift capabilities task...", { module: `cron:${this.cronName}` });
    await GiftService.createAllGiftingCapabilities();
    logger.info("✅ Completed gift capabilities task...", { module: `cron:${this.cronName}` });
  }
}

new GiftCapabilitiesCronJob().run();
