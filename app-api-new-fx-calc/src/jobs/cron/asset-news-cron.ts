import logger from "../../external-services/loggerService";
import <PERSON>ron<PERSON><PERSON> from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import AssetNewsCronService from "../services/assetNewsCronService";

class AssetNewsCronJob extends CronJob {
  cronName = CronJobNameEnum.ASSET_NEWS;

  /**
   * @description Cron for portfolio ticker calculation.
   */
  async processFn(): Promise<void> {
    logger.info("📰️ Fetching and saving asset news...", { module: `cron:${this.cronName}` });
    await AssetNewsCronService.createAssetNews();
    logger.info("✅ Created asset news", { module: `cron:${this.cronName}` });
  }
}

new AssetNewsCronJob().run();
