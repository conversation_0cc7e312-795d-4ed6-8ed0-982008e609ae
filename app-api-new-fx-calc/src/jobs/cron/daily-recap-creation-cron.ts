import CronJob from "./cronJob";
import logger from "../../external-services/loggerService";
import { CronJobNameEnum } from "../configs/cronNames";
import { NotificationCronService } from "../services/notificationCronService";

class DailyRecapCreationCronJob extends CronJob {
  cronName = CronJobNameEnum.DAILY_RECAP_CREATION;

  /**
   * @description Creates daily market recap notifications for all users
   * This runs once per day, ideally in the early morning before 9am EET
   */
  async processFn(): Promise<void> {
    logger.info("🔔 Creating daily market recap notifications...", {
      module: `cron:${this.cronName}`
    });

    await NotificationCronService.createDailyMarketRecapNotifications();

    logger.info("✅ Daily market recap notifications created successfully", {
      module: `cron:${this.cronName}`
    });
  }
}

// Start the cron job
new DailyRecapCreationCronJob().run();
