import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import SavingsProductsDataCronService from "../services/savingsProductsDataCronService";
import { CronJobNameEnum } from "../configs/cronNames";

class SavingsProductsDataCronJob extends CronJob {
  cronName = CronJobNameEnum.SAVINGS_PRODUCTS_DATA;

  /**
   * @description Cron for updating savings products data used by clients.
   */
  async processFn(): Promise<void> {
    logger.info("Updating savings products data...", {
      module: `cron:${this.cronName}`
    });
    await SavingsProductsDataCronService.updateSavingsProductsData();
    logger.info("✅ Completed updating savings products data!", {
      module: `cron:${this.cronName}`
    });
  }
}

new SavingsProductsDataCronJob().run();
