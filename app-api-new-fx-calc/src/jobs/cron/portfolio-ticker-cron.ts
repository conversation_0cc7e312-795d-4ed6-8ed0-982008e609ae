import logger from "../../external-services/loggerService";
import DailyTickerService from "../../services/dailyTickerService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class PortfolioTickerCronJob extends CronJob {
  cronName = CronJobNameEnum.PORTFOLIO_TICKER;

  /**
   * @description Cron for portfolio ticker calculation.
   */
  async processFn(): Promise<void> {
    logger.info("💼  Creating daily portfolio tickers...", { module: `cron:${this.cronName}` });
    await DailyTickerService.createDailyPortfolioTickers();
    logger.info("✅ Created daily portfolio tickers", { module: `cron:${this.cronName}` });
  }
}

new PortfolioTickerCronJob().run();
