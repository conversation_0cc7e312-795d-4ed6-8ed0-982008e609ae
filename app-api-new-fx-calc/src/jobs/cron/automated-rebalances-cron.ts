import logger from "../../external-services/loggerService";
import AutomationService from "../../services/automationService";
import DateUtil from "../../utils/dateUtil";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class AutomatedRebalancesCronJob extends CronJob {
  cronName = CronJobNameEnum.AUTOMATED_REBALANCES;

  /**
   * @description Cron for creating automated rebalances at the start of every month.
   * The cron runs the first 10 days of the month (but the automated rebalances will only be created on the first Monday
   * of the month or later)
   */
  async processFn(): Promise<void> {
    if (!DateUtil.isFirstMondayOfTheMonthOrLater()) {
      logger.info("💔 It is not the first Monday of the month or later yet, skipping!", {
        module: `cron:${this.cronName}`
      });
    } else {
      logger.info(
        "✅ It is the first Monday of the month or later, proceeding with creating automated rebalances...",
        {
          module: `cron:${this.cronName}`
        }
      );
      await AutomationService.createAllAutomatedRebalances();
      logger.info("✅ Completed creating automated rebalances!", {
        module: `cron:${this.cronName}`
      });
    }
  }
}

new AutomatedRebalancesCronJob().run();
