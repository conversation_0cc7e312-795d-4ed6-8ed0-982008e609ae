import logger from "../../external-services/loggerService";
import RiskAssessmentService from "../../services/riskAssessmentService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class RiskAssessmentCronJob extends CronJob {
  cronName = CronJobNameEnum.RISK_ASSESSMENT;

  /**
   * @description Cron for portfolio ticker calculation.
   */
  async processFn(): Promise<void> {
    logger.info("💼  Creating users risk assessments...", { module: `cron:${this.cronName}` });
    await RiskAssessmentService.createRiskAssessments();
    logger.info("✅ Created user risk assessments", { module: `cron:${this.cronName}` });
  }
}

new RiskAssessmentCronJob().run();
