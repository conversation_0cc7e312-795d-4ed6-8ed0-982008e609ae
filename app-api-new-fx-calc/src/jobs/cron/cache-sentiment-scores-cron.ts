import logger from "../../external-services/loggerService";
import <PERSON><PERSON><PERSON><PERSON> from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import DailySummarySnapshotCronService from "../services/dailySummarySnapshotCronService";

class SentimentScoreCachingCronJob extends CronJob {
  cronName = CronJobNameEnum.CACHE_SENTIMENT_SCORES;

  /**
   * @description Cron for adding sentiment scores per asset to our Redis.
   */
  async processFn(): Promise<void> {
    logger.info("Updating sentiment scores cache...", {
      module: `cron:${this.cronName}`
    });
    await DailySummarySnapshotCronService.updateSentimentScoresCache();
    logger.info("✅ Completed updating sentiment scores cache!", {
      module: `cron:${this.cronName}`
    });
  }
}

new SentimentScoreCachingCronJob().run();
