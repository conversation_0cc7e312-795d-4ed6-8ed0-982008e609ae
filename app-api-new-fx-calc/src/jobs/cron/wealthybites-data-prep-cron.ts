import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import WealthybitesService from "../../services/wealthybitesService";

class WealthybitesDataPrepCronJob extends CronJob {
  cronName = CronJobNameEnum.WEALTHYBITES_DATA_PREP;

  /**
   * @description Cron for Wealthybites data preparation.
   */
  async processFn(): Promise<void> {
    logger.info("🛠️ Preparing Wealthybites data...", { module: `cron:${this.cronName}` });
    await WealthybitesService.precalculateSnapshotsForAllUsers();
    logger.info("✅ Wealthybites data prepared", { module: `cron:${this.cronName}` });
  }
}

new WealthybitesDataPrepCronJob().run();
