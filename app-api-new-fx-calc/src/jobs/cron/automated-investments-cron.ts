import logger from "../../external-services/loggerService";
import AutomationService from "../../services/automationService";
import AutomationCronService from "../services/automationCronService";
import CronJob from "./cronJob";
import { TransactionCronService } from "../services/transactionCronService";
import { CronJobNameEnum } from "../configs/cronNames";

class AutomatedInvestmentsCronJob extends CronJob {
  cronName = CronJobNameEnum.AUTOMATED_INVESTMENTS;

  /**
   * @description Cron for creating direct debit payments with Wealthkernel.
   * These payments refer to deposits needed for repeating investments.
   * As Wealthkernel submits payments to BACS daily at around 4pm UK time, we run this cron daily at 3pm UK time.
   */
  async processFn(): Promise<void> {
    logger.info("🧹 Clearing past activateAt field on automation docs...", {
      module: `cron:${this.cronName}`
    });
    await AutomationCronService.clearPastInitialisedAt();
    logger.info("✅ Clearing past activateAt field on automation docs", {
      module: `cron:${this.cronName}`
    });

    logger.info("📆 Creating deposits & asset transactions for recurring top-ups...", {
      module: `cron:${this.cronName}`
    });
    await AutomationService.createAllRecurringTopUps();
    logger.info("✅ Completed deposits & asset transactions for recurring top-ups", {
      module: `cron:${this.cronName}`
    });

    logger.info("📆 Creating deposits & savings transactions for recurring savings top-ups...", {
      module: `cron:${this.cronName}`
    });
    await AutomationService.createAllRecurringSavingsTopUps();
    logger.info("✅ Completed deposits & asset transactions for recurring savings top-ups", {
      module: `cron:${this.cronName}`
    });

    logger.info("💰 Creating Wealthkernel direct debit payments...", {
      module: `cron:${this.cronName}`
    });
    await TransactionCronService.createWealthkernelDirectDebitPayments();
    logger.info("✅ Completed Wealthkernel direct debit payments", {
      module: `cron:${this.cronName}`
    });

    logger.info("🏦 Initiating GoCardless direct-debit payments...", { module: `cron:${this.cronName}` });
    await TransactionCronService.createGoCardlessDirectDebitPayments();
    logger.info("✅ Completed GoCardless direct-debit payments", { module: `cron:${this.cronName}` });
  }
}

new AutomatedInvestmentsCronJob().run();
