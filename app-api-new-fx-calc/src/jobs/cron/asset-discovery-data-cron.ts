import logger from "../../external-services/loggerService";
import <PERSON>ron<PERSON><PERSON> from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import AssetDiscoveryDataCronService from "../services/assetDiscoveryDataCronService";

class AssetDiscoveryDataCronJob extends CronJob {
  cronName = CronJobNameEnum.ASSET_DISCOVERY_DATA;

  /**
   * @description Cron for updating asset discovery page data.
   */
  async processFn(): Promise<void> {
    logger.info("Updating asset discovery data...", {
      module: `cron:${this.cronName}`
    });
    await AssetDiscoveryDataCronService.updateAssetDiscoveryData();
    logger.info("✅ Completed updating asset discovery data!", {
      module: `cron:${this.cronName}`
    });
  }
}

new AssetDiscoveryDataCronJob().run();
