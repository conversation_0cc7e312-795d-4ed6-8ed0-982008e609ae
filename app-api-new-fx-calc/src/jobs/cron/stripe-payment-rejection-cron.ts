import <PERSON>ron<PERSON>ob from "./cronJob";
import logger from "../../external-services/loggerService";
import { TransactionService } from "../../services/transactionService";
import { CronJobNameEnum } from "../configs/cronNames";

class StripePaymentRejectionCronJob extends CronJob {
  cronName = CronJobNameEnum.STRIPE_PAYMENT_REJECTION;

  /**
   * @description Cron going through our Stripe payments that have not been settled in 7 + 1 days, marking them as rejected.
   */
  async processFn(): Promise<void> {
    logger.info("🎁 Initiating Stripe transaction rejection tasks...", { module: `cron:${this.cronName}` });
    await TransactionService.rejectFailedStripeCharges();
    logger.info("✅ Completed Stripe transaction rejection tasks", { module: `cron:${this.cronName}` });
  }
}

new StripePaymentRejectionCronJob().run();
