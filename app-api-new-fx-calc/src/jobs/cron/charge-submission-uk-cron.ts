import logger from "../../external-services/loggerService";
import DateUtil from "../../utils/dateUtil";
import { TransactionService } from "../../services/transactionService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import { entitiesConfig } from "@wealthyhood/shared-configs";

class ChargeSubmissionUKCronJob extends CronJob {
  cronName = CronJobNameEnum.CHARGE_SUBMISSION_UK;

  /**
   * @description Cron for submission of charges (as specified by the created charge transaction documents) to WK.
   */
  async processFn(): Promise<void> {
    logger.info("💰 Initiating charges creation task...", { module: `cron:${this.cronName}` });
    await TransactionService.submitChargesToWK(
      DateUtil.getFirstDayOfLastMonth(),
      DateUtil.getFirstDayOfThisMonth(),
      entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
    );
    logger.info("✅ Completed charges creation task", { module: `cron:${this.cronName}` });
  }
}

new ChargeSubmissionUKCronJob().run();
