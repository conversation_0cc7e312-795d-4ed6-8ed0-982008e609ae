import logger from "../../external-services/loggerService";
import <PERSON>ron<PERSON>ob from "./cronJob";
import { TransactionMonitorCronService } from "../services/transactionMonitorCronService";
import { CronJobNameEnum } from "../configs/cronNames";

class TransactionMonitorCronJob extends CronJob {
  cronName = CronJobNameEnum.TRANSACTION_MONITOR;

  /**
   * @description Cron running transaction monitor checks for suspicious activity.
   */
  async processFn(): Promise<void> {
    logger.info("🎁 Checking for suspicious transaction activity...", { module: `cron:${this.cronName}` });
    await TransactionMonitorCronService.checkUsersForSuspiciousTransactionActivity();
    logger.info("✅ Completed for suspicious transaction activity", { module: `cron:${this.cronName}` });
  }
}

new TransactionMonitorCronJob().run();
