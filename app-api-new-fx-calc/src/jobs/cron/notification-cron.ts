import <PERSON><PERSON><PERSON><PERSON> from "./cronJob";
import logger from "../../external-services/loggerService";
import { CronJobNameEnum } from "../configs/cronNames";
import { NotificationCronService } from "../services/notificationCronService";

class NotificationCronJob extends CronJob {
  cronName = CronJobNameEnum.NOTIFICATION;

  /**
   * @description Sends out all pending notifications and marks stagnant ones as skipped.
   */
  async processFn(): Promise<void> {
    logger.info("🔔 Sending all pending notifications...", {
      module: `cron:${this.cronName}`
    });
    await NotificationCronService.sendPendingAppContentNotifications();
    logger.info("✅ Sending all pending notifications", {
      module: `cron:${this.cronName}`
    });

    // Send all pending daily recap notifications
    logger.info("📅 Sending all pending daily recap notifications...", {
      module: `cron:${this.cronName}`
    });
    await NotificationCronService.sendPendingDailyRecapNotifications();
    logger.info("✅ Sent all pending daily recap notifications", {
      module: `cron:${this.cronName}`
    });

    logger.info("❌ Marking stagnant notification as skipped...", {
      module: `cron:${this.cronName}`
    });
    await NotificationCronService.markStagnantNotificationsAsSkipped();
    logger.info("✅ Marking stagnant notification as skipped", {
      module: `cron:${this.cronName}`
    });
  }
}

new NotificationCronJob().run();
