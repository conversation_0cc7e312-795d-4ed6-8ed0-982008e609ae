import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import PortfolioCronService from "../services/portfolioCronService";
import { CronJobNameEnum } from "../configs/cronNames";

class CachePortfolioMWRRCronJob extends CronJob {
  cronName = CronJobNameEnum.CACHE_PORTFOLIO_MWRR_AND_UP_BY_VALUES;

  /**
   * @description Cron for caching MWRR and Up by values for all portfolios with holdings.
   */
  async processFn(): Promise<void> {
    logger.info("Initiating calculation & caching of MWRR and Up by values for all portfolios...", {
      module: `cron:${this.cronName}`
    });
    await PortfolioCronService.cacheAllPortfolioMWRRsAndUpByValues();
    logger.info("✅ Completed calculation & caching of MWRR and Up by values for all portfolios!", {
      module: `cron:${this.cronName}`
    });
  }
}

new CachePortfolioMWRRCronJob().run();
