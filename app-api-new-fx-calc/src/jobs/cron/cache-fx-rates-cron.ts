import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import FXRateCacheCronService from "../services/fxRateCacheCronService";
import { CronJobNameEnum } from "../configs/cronNames";

class FXRatesCachingCronJob extends CronJob {
  cronName = CronJobNameEnum.CACHE_FX_RATES;

  /**
   * @description Cron for adding FX rates to our Redis.
   */
  async processFn(): Promise<void> {
    logger.info("Updating FX rate cache...", {
      module: `cron:${this.cronName}`
    });
    await FXRateCacheCronService.updateFXRateCache();
    logger.info("✅ Completed updating FX rate cache!", {
      module: `cron:${this.cronName}`
    });
  }
}

new FXRatesCachingCronJob().run();
