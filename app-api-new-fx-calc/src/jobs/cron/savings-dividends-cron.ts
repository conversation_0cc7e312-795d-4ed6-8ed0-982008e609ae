import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import { TransactionCronService } from "../services/transactionCronService";
import { CronJobNameEnum } from "../configs/cronNames";

class SavingsDividendsCronJob extends CronJob {
  cronName = CronJobNameEnum.SAVINGS_DIVIDENDS;

  /**
   * @description Cron job that fetches dividend transactions from Wealthkernel,
   * creates the corresponding dividend documents that are linked to savings
   * and reinvests them.
   *
   * The cron should be running at the start of every month, when the dividends are paid out.
   */
  async processFn(): Promise<void> {
    logger.info("🛍️ Creating Wealthkernel savings dividend...", {
      module: `cron:${this.cronName}`
    });
    await TransactionCronService.createWealthkernelSavingsDividends();
    logger.info("✅ Completed creating Wealthkernel savings dividend", { module: `cron:${this.cronName}` });

    logger.info("🛍️ Reinvesting savings dividends...", {
      module: `cron:${this.cronName}`
    });
    await TransactionCronService.reinvestSavingsDividends();
    logger.info("✅ Completed reinvesting savings dividends", { module: `cron:${this.cronName}` });
  }
}

new SavingsDividendsCronJob().run();
