import logger from "../../external-services/loggerService";
import UserDataRequestService from "../../services/userDataRequestService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class UserDataRequestCronJob extends CronJob {
  cronName = CronJobNameEnum.USER_DATA_REQUESTS;

  /**
   * @description Cron for processing user data requests (disassociations/GDPR)
   */
  async processFn(): Promise<void> {
    logger.info("💼 Initiating processing of user data requests...", { module: `cron:${this.cronName}` });
    await UserDataRequestService.processAllUserDisassociations();
    await UserDataRequestService.processAllUserGDPRDeletions();
    logger.info("✅ Completed processing of user data requests", { module: `cron:${this.cronName}` });
  }
}

new UserDataRequestCronJob().run();
