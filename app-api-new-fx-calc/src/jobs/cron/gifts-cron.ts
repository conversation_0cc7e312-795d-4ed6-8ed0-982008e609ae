import CronJob from "./cronJob";
import logger from "../../external-services/loggerService";
import GiftService from "../../services/giftService";
import { CronJobNameEnum } from "../configs/cronNames";

class GiftsCronJob extends CronJob {
  cronName = CronJobNameEnum.GIFTS;

  /**
   * @description Cron running gift bonus creation and syncing (hourly, every day)
   */
  async processFn(): Promise<void> {
    logger.info("🎁 Initiating gift tasks...", { module: `cron:${this.cronName}` });
    // We sync bonuses first because if we create them and then immediately try to sync them, we receive 404s from WK.
    await GiftService.syncPendingGiftDeposits();
    await GiftService.createGiftDeposits();
    logger.info("✅ Completed gifting tasks", { module: `cron:${this.cronName}` });
  }
}

new GiftsCronJob().run();
