import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import DailyTickerCronService from "../services/dailyTickerCronService";
import { CronJobNameEnum } from "../configs/cronNames";

class PortfolioSavingsCronJob extends CronJob {
  cronName = CronJobNameEnum.DAILY_PORTFOLIO_SAVINGS_TICKER;

  /**
   * @description Cron daily user savings tickers (Monday - Sunday)
   */
  async processFn(): Promise<void> {
    logger.info("🚀 Creating daily user savings tickers...", { module: `cron:${this.cronName}` });
    await DailyTickerCronService.createDailyPortfolioSavingsTicker();
    logger.info("✅ Completed creating daily user savings tickers", { module: `cron:${this.cronName}` });
  }
}

new PortfolioSavingsCronJob().run();
