import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import InvestmentProductService from "../../services/investmentProductService";

class CacheAssetsWeeklyReturnsCronJob extends CronJob {
  cronName = CronJobNameEnum.CACHE_ASSETS_WEEKLY_RETURNS;

  /**
   * @description Cron for caching asset weekly returns.
   */
  async processFn(): Promise<void> {
    logger.info("💰 Caching asset weekly returns...", { module: `cron:${this.cronName}` });
    await InvestmentProductService.cacheAllAssetsWeeklyReturns();
    logger.info("✅ Asset weekly returns cached", { module: `cron:${this.cronName}` });
  }
}

new CacheAssetsWeeklyReturnsCronJob().run();
