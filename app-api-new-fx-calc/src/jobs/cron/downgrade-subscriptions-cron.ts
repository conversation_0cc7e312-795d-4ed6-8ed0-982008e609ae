import logger from "../../external-services/loggerService";
import SubscriptionService from "../../services/subscriptionService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class DowngradeSubscriptionsCronJob extends CronJob {
  cronName = CronJobNameEnum.DOWNGRADE_SUBSCRIPTIONS;

  /**
   * @description Cron running every day on a nightly schedule to downgrade subscriptions - separated from nightly cron
   * as we wouldn't want it to be affected by running time of other cron tasks there.
   */
  async processFn(): Promise<void> {
    logger.info("😢 Starting downgrading subscriptions...", {
      module: `cron:${this.cronName}`
    });
    await SubscriptionService.downgradeDirectDebitExpiredSubscriptions();
    logger.info("✅ Completed downgrading subscriptions.", {
      module: `cron:${this.cronName}`
    });
  }
}

new DowngradeSubscriptionsCronJob().run();
