import logger from "../../external-services/loggerService";
import <PERSON>ronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import LearnNewsCronService from "../services/learnNewsCronService";

class LearnNewsCronJob extends CronJob {
  cronName = CronJobNameEnum.LEARN_NEWS;

  /**
   * @description Cron for ingesting learn news from FMP.
   */
  async processFn(): Promise<void> {
    logger.info("📰️ Fetching and storing learn news...", { module: `cron:${this.cronName}` });
    await LearnNewsCronService.createLearnNews();
    logger.info("✅ Finished fetching and storing learn news!", { module: `cron:${this.cronName}` });
  }
}

new LearnNewsCronJob().run();
