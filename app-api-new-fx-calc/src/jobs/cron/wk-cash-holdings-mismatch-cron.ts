import logger from "../../external-services/loggerService";
import DataVerificationService from "../../services/dataVerificationService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import WealthkernelUtil from "../../utils/wealthkernelUtil";
import { captureException } from "@sentry/node";

class WkCashHoldingsMismatchCronJob extends CronJob {
  cronName = CronJobNameEnum.WK_CASH_HOLDINGS_MISMATCH;

  /**
   * @description Cron running after Wealthkernel morning valuation to check our data against WK cash & holdings.
   */
  async processFn(): Promise<void> {
    await WealthkernelUtil.onEachInstance(async (wealthkernelService) => {
      try {
        logger.info("🏦 Checking if latest Wealthkernel valuation exists before proceeding...", {
          module: `cron:${this.cronName}`
        });
        await WealthkernelUtil.checkLatestWealthkernelValuationExists(wealthkernelService);
        logger.info("✅ Latest Wealthkernel valuation does exist!", { module: `cron:${this.cronName}` });

        logger.info("🧔 Running data checks for cash & holdings mismatches...", {
          module: `cron:${this.cronName}`
        });
        await DataVerificationService.findCashHoldingsMismatches(wealthkernelService);
        logger.info("✅ Completed data checks for cash & holdings mismatches", {
          module: `cron:${this.cronName}`
        });
      } catch (err) {
        captureException(err);
        logger.error("💔 Processing wk cash holding mismatches failed", {
          module: `cron:${this.cronName}`,
          data: {
            wealthkernelRegion: wealthkernelService.region
          }
        });
      }
    });
  }
}

new WkCashHoldingsMismatchCronJob().run();
