import { EtfHoldingLogosCronService } from "../services/etfHoldingLogosCronService";
import C<PERSON><PERSON><PERSON> from "./cronJob";
import logger from "../../external-services/loggerService";
import { CronJobNameEnum } from "../configs/cronNames";

class EtfHoldingLogosCronJob extends CronJob {
  cronName = CronJobNameEnum.ETF_HOLDINGS_LOGOS;

  /**
   * @description Store missing etf holding logos to cloudflare
   */
  async processFn(): Promise<void> {
    logger.info("Storing missing etf holding logos to Cloudflare...", {
      module: `cron:${this.cronName}`
    });
    await EtfHoldingLogosCronService.storeMissingEtfHoldingLogos();
    logger.info("✅ Completed storing missing etf holding logos to Cloudflare...", {
      module: `cron:${this.cronName}`
    });

    logger.info("Caching custom etf holding logos to redis...", {
      module: `cron:${this.cronName}`
    });
    await EtfHoldingLogosCronService.cacheCustomEtfHoldingLogos();
    logger.info("✅ Caching custom etf holding logos to redis...", {
      module: `cron:${this.cronName}`
    });
  }
}

new EtfHoldingLogosCronJob().run();
