import logger from "../../external-services/loggerService";
import IntraDayTickerCronService from "../services/intraDayTickerCronService";
import Cron<PERSON>ob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class StaleAssetTickerCronJob extends CronJob {
  cronName = CronJobNameEnum.STALE_ASSET_TICKER;

  /**
   * @description Cron that checks for stale asset tickers and notifies us.
   */
  async processFn(): Promise<void> {
    logger.info("🔎 Check for stale intra-day asset tickers...", { module: `cron:${this.cronName}` });
    await IntraDayTickerCronService.checkStaleTickers();
    logger.info("✅ Completed checking for stale intra-day asset tickers", { module: `cron:${this.cronName}` });
  }
}

new StaleAssetTickerCronJob().run();
