import { CronJobNameEnum } from "./cronNames";

const STAGING_CRON_CONFIG: {
  name: CronJobNameEnum;
  cron: string;
  closeWorkerAfterMs?: number;
  lockExpirationSec?: number;
  memoryMonitoringSampleRate?: number;
}[] = [
  { name: CronJobNameEnum.INTRA_DAY_ASSET_TICKER, cron: "*/5 6-22 * * 1-5" },
  { name: CronJobNameEnum.CACHE_FX_RATES, cron: "35 9-22 * * *" },
  { name: CronJob<PERSON>ameEnum.SUNDOWN_DIGEST, cron: "10/30 0-2 * * *" },
  { name: CronJobNameEnum.CACHE_INDEX_PRICES, cron: "5/30 2-4 * * *" },
  { name: CronJobNameEnum.STORE_INDEX_PRICES, cron: "5/15 22 * * *" },
  { name: CronJobNameEnum.PORTFOLIO_TICKER, cron: "30 */3 * * 1-5" },
  { name: CronJobNameEnum.DAILY_SUMMARY_SNAPSHOT, cron: "0 */3 * * 1-5" },
  { name: CronJobNameEnum.ASSET_FUNDAMENTALS_DATA, cron: "0 4 * * 1-6" },
  { name: CronJobNameEnum.ASSET_HISTORICAL_PRICES, cron: "0 3 * * 1-6" },
  { name: CronJobNameEnum.ONGOING, cron: "2/10 * * * 1-5" },
  { name: CronJobNameEnum.ORDER_SUBMISSION, cron: "5/20 * * * 1-5" },
  { name: CronJobNameEnum.SYNC_TRANSACTIONS_WITH_ORDERS, cron: "30 */2 * * 1-5" },
  { name: CronJobNameEnum.SAVINGS_DIVIDENDS, cron: "*/25 8 1-10 * *" },
  { name: CronJobNameEnum.DAILY_SAVINGS_PRODUCT_TICKER, cron: "2/10 22 * * 1-5" },
  { name: CronJobNameEnum.DAILY_PORTFOLIO_SAVINGS_TICKER, cron: "34/10 22 * * *" }, // this depends on DAILY_SAVINGS_PRODUCT_TICKER
  { name: CronJobNameEnum.CHARGE_CREATION, cron: "50 */2 * * 1-5" },
  { name: CronJobNameEnum.STRIPE_PAYMENT_REJECTION, cron: "*/30 21-22 * * *" },
  { name: CronJobNameEnum.CLEAN_TRANSACTIONS, cron: "15 */2 * * 1-5" },
  { name: CronJobNameEnum.INTRA_DAY_PORTFOLIO_TICKER, cron: "8/30 10-20 * * 1-5" },
  { name: CronJobNameEnum.CONTENT_INGESTION, cron: "15 10-20/2 * * *" },
  { name: CronJobNameEnum.LEARN_NEWS, cron: "0 5 * * *" }
];

export default STAGING_CRON_CONFIG;
