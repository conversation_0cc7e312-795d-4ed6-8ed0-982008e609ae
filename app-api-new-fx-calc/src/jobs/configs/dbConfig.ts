import { CronJ<PERSON><PERSON><PERSON>Enum } from "./cronNames";
import { PartialRecord } from "utils";
import { MongoReadPreferenceEnum } from "../../utils/dbUtil";

export const CRON_NAME_TO_PRIORITY_NODE: PartialRecord<CronJobNameEnum, MongoReadPreferenceEnum> = {
  [CronJobNameEnum.ASSET_DISCOVERY_DATA]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.ASSET_NEWS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.ASSET_FUNDAMENTALS_DATA]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.ASSET_HISTORICAL_PRICES]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.AUTOMATED_REBALANCES]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.AUTOMATED_INVESTMENTS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.CACHE_FX_RATES]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.CHARGE_CREATION]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.CHARGE_SUBMISSION_UK]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.CHARGE_SUBMISSION_EU]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.DOWNGRADE_SUBSCRIPTIONS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.INTRA_DAY_ASSET_TICKER]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.STALE_ASSET_TICKER]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.DAILY_SUMMARY_SNAPSHOT]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.DAILY_PORTFOLIO_SAVINGS_TICKER]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.ONGOING]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.ORDER_SUBMISSION]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.SAVINGS_DIVIDENDS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.DAILY_SAVINGS_PRODUCT_TICKER]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.STRIPE_PAYMENT_REJECTION]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.WK_CASH_HOLDINGS_MISMATCH]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.CACHE_PORTFOLIO_MWRR_AND_UP_BY_VALUES]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.CACHE_SENTIMENT_SCORES]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.ETF_HOLDINGS_LOGOS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.SUNDOWN_DIGEST]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.CACHE_INDEX_PRICES]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.STORE_INDEX_PRICES]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.GIFTS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.GIFT_CAPABILITIES]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.INVESTMENT_DIVIDENDS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.INTRA_DAY_PORTFOLIO_TICKER]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.NIGHTLY]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.STOCK_SPLIT]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.PORTFOLIO_TICKER]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.REWARDS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.RISK_ASSESSMENT]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.SAVINGS_PRODUCTS_DATA]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.CUSTODY_CHARGES_CREATION]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.SYNC_TRANSACTIONS_WITH_ORDERS]: MongoReadPreferenceEnum.PRIMARY,
  [CronJobNameEnum.TRANSACTION_MONITOR]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.USER_DATA_REQUESTS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.WH_DIVIDEND_BONUS]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.WH_DIVIDEND_CREATION]: MongoReadPreferenceEnum.SECONDARY,
  [CronJobNameEnum.LEARN_NEWS]: MongoReadPreferenceEnum.SECONDARY
};
