import { DailyPortfolioSavingsTicker, DailyPortfolioSavingsTickerDocument } from "../models/DailyTicker";

export default class DailyTickerRepository {
  static async getLatestSavingsPortfolioTicker(portfolioId: string): Promise<DailyPortfolioSavingsTickerDocument> {
    return (
      await DailyPortfolioSavingsTicker.find({
        portfolio: portfolioId
      })
        .sort({ createdAt: -1 })
        .limit(1)
    )[0];
  }
}
