import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Reward } from "../models/Reward";
import { AssetDividendTransaction, DividendTransaction } from "../models/Transaction";

const { ASSET_CONFIG } = investmentUniverseConfig;
class AddIsinFieldRewardsDividendsRunner extends ScriptRunner {
  scriptName = "add-isin-to-rewards-dividends";

  async processFn(): Promise<void> {
    logger.info("Updating all rewards to have isin...", {
      module: `script:${this.scriptName}`
    });

    const uniqueRewardsAssetIds = (await Reward.distinct("asset")) as investmentUniverseConfig.AssetType[];
    for (const assetId of uniqueRewardsAssetIds) {
      let isin = ASSET_CONFIG[assetId].isin;
      if (assetId === "equities_blackrock") {
        // there are 2 rewards with the new blackrock isin that need to be manually overwritten
        isin = ASSET_CONFIG["equities_blackrock_deprecated_1"].isin;
      }
      await Reward.updateMany({ asset: assetId }, { $set: { isin } });
    }

    logger.info("✅ Finished updating all rewards to have isin!", {
      module: `script:${this.scriptName}`
    });

    logger.info("Updating all dividends to have isin...", {
      module: `script:${this.scriptName}`
    });

    const uniqueDividendsAssetIds = (await DividendTransaction.distinct(
      "asset"
    )) as investmentUniverseConfig.AssetType[];
    for (const assetId of uniqueDividendsAssetIds) {
      let isin = ASSET_CONFIG[assetId].isin;
      if (assetId === "equities_blackrock") {
        isin = ASSET_CONFIG["equities_blackrock_deprecated_1"].isin;
      }
      await DividendTransaction.updateMany({ asset: assetId }, { $set: { isin } });
    }

    logger.info("✅ Finished updating all dividends to have isin!", {
      module: `script:${this.scriptName}`
    });

    logger.info("Updating all asset dividends to have isin...", {
      module: `script:${this.scriptName}`
    });

    const uniqueAssetDividendsAssetIds = (await AssetDividendTransaction.distinct(
      "asset"
    )) as investmentUniverseConfig.AssetType[];
    for (const assetId of uniqueAssetDividendsAssetIds) {
      const isin = ASSET_CONFIG[assetId].isin;
      await AssetDividendTransaction.updateMany({ asset: assetId }, { $set: { isin } });
    }

    logger.info("✅ Finished updating all asset dividends to have isin!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddIsinFieldRewardsDividendsRunner().run();
