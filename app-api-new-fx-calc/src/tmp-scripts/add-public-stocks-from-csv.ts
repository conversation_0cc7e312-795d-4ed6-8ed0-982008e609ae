import axios from "axios";
import * as fs from "fs";
import { writeFile } from "fs/promises";

// eslint-disable-next-line @typescript-eslint/no-var-requires
require("dotenv").config({ path: "dev.env" });

type StockCSVHeaderType = "Ticker" | "Icon name" | "Exchange";

const CSV_HEADER_TO_KEY_MAPPINGS: Record<StockCSVHeaderType, string> = {
  Ticker: "formalTicker",
  Exchange: "formalExchange",
  "Icon name": "keyName"
};

/**
 * When we add new **public** stocks, we can run this script to retrieve EOD sectors and translate them to our sectors.
 */
async function printStocksFromCSV(): Promise<void> {
  const FILE_NAME = "stocks.csv";

  const csv = fs.readFileSync(`./src/tmp-scripts/${FILE_NAME}`, { encoding: "utf8" });
  const lines = csv.split("\n");
  const headers = lines[0].split(",");

  const result = [];
  for (let i = 1; i < lines.length; i++) {
    const config: any = {};
    const currentLine = lines[i].split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);

    console.log(`Processing line ${currentLine}`);

    for (let j = 0; j < headers.length; j++) {
      config[CSV_HEADER_TO_KEY_MAPPINGS[headers[j].trim() as StockCSVHeaderType]] = currentLine[j]
        .trim()
        .replace(/"+/g, "");
    }

    try {
      const response = await axios.get(
        `https://eodhistoricaldata.com/api/fundamentals/${config.formalTicker}.${config.formalExchange}?api_token=${process.env.EOD_DATA_TOKEN}&fmt=json`
      );

      config.tradedCurrency = response.data.General.CurrencyCode;

      if (!["USD", "GBP", "EUR"].includes(config.tradedCurrency)) {
        console.log(`${config.keyName} - ${config.tradedCurrency}`);
      }
    } catch (err) {
      console.log(`Could not add currency for ${config.keyName}`);
      console.log(err);
    }

    // Apart from what's in the CSV, we also add tradedCurrency, formalExchange (always USD and US respectively), category, assetClass and sector.
    config.category = "stock";
    config.assetClass = "equities";

    result.push(config);
  }

  const items = JSON.stringify(
    Object.fromEntries(
      result.map((config) => {
        const { keyName, ...configWithoutKeyname } = config;
        return [keyName, configWithoutKeyname];
      })
    )
  );

  // The content of these two files go in the shared-configs project.
  await writeFile("assetConfig.json", items, "utf8");
  await writeFile("assets.json", JSON.stringify(result.map((config) => config.keyName)), "utf8");
}

(async () => {
  try {
    await printStocksFromCSV();
    console.log("success");
    process.exit(1);
  } catch (err) {
    console.log("error");
    console.log(err);
  }
})();
