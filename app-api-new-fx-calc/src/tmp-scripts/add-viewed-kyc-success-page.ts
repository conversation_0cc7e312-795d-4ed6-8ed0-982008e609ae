import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";

class AddViewedKYCSuccessPageFieldScriptRunner extends ScriptRunner {
  scriptName = "add-viewed-kyc-success-page-to-users";

  async processFn(): Promise<void> {
    logger.info("Adding viewedKYCSuccessPage to verified users...", {
      module: `script:${this.scriptName}`
    });

    await User.updateMany(
      { kycStatus: "passed" },
      {
        viewedKYCSuccessPage: true
      }
    );

    logger.info("Finished adding viewedKYCSuccessPage to verified users!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddViewedKYCSuccessPageFieldScriptRunner().run();
