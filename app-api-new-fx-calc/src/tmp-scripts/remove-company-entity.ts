import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User, UserDocument } from "../models/User";
import { Address } from "../models/Address";
import { KycOperation } from "../models/KycOperation";

class RemoveCompanyEntity extends ScriptRunner {
  scriptName = "remove-company-entity";

  async processFn(): Promise<void> {
    logger.info("Removing company entity and related info from users without residency country...", {
      module: `script:${this.scriptName}`
    });

    await User.find({ residencyCountry: { $exists: false }, companyEntity: { $exists: true } })
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(
        async (users, index) => {
          logger.info(`Removing batch with index ${index}...`, {
            module: `script:${this.scriptName}`
          });

          const dbOperationPromises = users.map((owner: UserDocument) => {
            const userUpdate = User.findByIdAndUpdate(owner.id, {
              $unset: {
                companyEntity: 1,
                firstName: 1,
                lastName: 1,
                dateOfBirth: 1,
                submittedRequiredInfoAt: 1,
                activeProviders: 1,
                nationalities: 1,
                isPassportMatchingKycProvider: 1,
                isPotentiallyDuplicateAccount1: 1,
                providers: 1,
                currency: 1,
                employmentInfo: 1,
                w8BenForm: 1,
                taxResidency: 1
              },
              $set: {
                kycStatus: "pending"
              }
            });

            const addressDelete = Address.findOneAndDelete({ owner: owner.id });
            const kycOperationDelete = KycOperation.deleteMany({ owner: owner.id });

            return [userUpdate, addressDelete, kycOperationDelete];
          });

          await Promise.all(dbOperationPromises.flat());
        },
        { batchSize: 10 }
      );

    logger.info("Removed company entity and related info from users without residency country!", {
      module: `script:${this.scriptName}`
    });
  }
}

new RemoveCompanyEntity().run();
