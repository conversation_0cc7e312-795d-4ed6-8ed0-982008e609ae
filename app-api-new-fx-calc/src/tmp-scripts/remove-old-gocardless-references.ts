import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { ProviderEnum } from "../configs/providersConfig";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { User } from "../models/User";
import { Mandate } from "../models/Mandate";

export class RemoveOldGoCardlessReferencesScriptRunner extends ScriptRunner {
  scriptName = "remove-old-gocardless-references";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Removing old GoCardless References...", {
      module: `script:${this.scriptName}`
    });

    await User.updateMany(
      {
        companyEntity: { $nin: [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE] }
      },
      {
        activeProviders: { $pull: ProviderEnum.GOCARDLESS },
        $unset: { "providers.gocardless": "" }
      }
    );
    await User.updateMany(
      {
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      },
      {
        $addToSet: { activeProviders: ProviderEnum.GOCARDLESS }
      }
    );
    await Mandate.updateMany(
      {},
      {
        activeProviders: { $pull: ProviderEnum.GOCARDLESS },
        $unset: { "providers.gocardless": "" }
      }
    );

    logger.info("Removed old GoCardless References...", {
      module: `script:${this.scriptName}`
    });
  }
}

new RemoveOldGoCardlessReferencesScriptRunner().run();
