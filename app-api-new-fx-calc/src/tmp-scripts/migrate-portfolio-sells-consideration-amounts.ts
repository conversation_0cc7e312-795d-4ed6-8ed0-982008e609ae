import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { AssetTransaction } from "../models/Transaction";
import DateUtil from "../utils/dateUtil";
import Decimal from "decimal.js";

class MigrationPortfolioSellConsiderationAmountScriptRunner extends ScriptRunner {
  scriptName = "portfolio-sell-consideration-amount-migration";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating portfolio sell transaction consideration amount...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };
    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    const transactions = await AssetTransaction.find({
      portfolioTransactionCategory: "sell",
      status: "Settled",
      createdAt: { $gte: DateUtil.getDateOfMonthsAgo(new Date(Date.now()), 6) },
      "consideration.amount": { $exists: true }
    }).populate("orders");

    logger.info(`Found ${transactions.length} transactions!`, {
      module: `script:${this.scriptName}`
    });

    for (let i = 0; i < transactions.length; i++) {
      logger.info(`Going through ${i + 1}/${transactions.length} transaction!`, {
        module: `script:${this.scriptName}`
      });

      const transaction = transactions[i];

      if (!transaction.orders.every((order) => order.status === "Matched")) {
        logger.warn(`Transaction ${transaction.id} is settled but it has unmatched orders..`, {
          module: `script:${this.scriptName}`
        });
        continue;
      } else if (
        !transaction.orders.every((order) => order.consideration?.amount || order.consideration?.amount === 0)
      ) {
        logger.warn(`Transaction ${transaction.id} is settled but it has orders without consideration amount..`, {
          module: `script:${this.scriptName}`
        });
        continue;
      }

      // For each transaction, we want to sum the orders consideration amounts.
      const totalOrderAmountPostFees = transaction.orders
        .map(({ consideration }) => consideration.amount)
        .reduce((sum, amount) => Decimal.add(sum, amount), new Decimal(0));
      const currentConsiderationAmount = new Decimal(transaction.consideration.amount);

      if (totalOrderAmountPostFees === currentConsiderationAmount) {
        continue;
      }

      // Calculate the difference as a percentage of the first number
      const percentageDifference = totalOrderAmountPostFees
        .minus(currentConsiderationAmount)
        .dividedBy(totalOrderAmountPostFees)
        .absoluteValue();

      const PERCENTAGE_DIFFERENCE_ALLOWED = 0.015;
      if (percentageDifference.greaterThan(new Decimal(PERCENTAGE_DIFFERENCE_ALLOWED))) {
        logger.warn(`Transaction ${transaction.id} has difference > ${PERCENTAGE_DIFFERENCE_ALLOWED}`, {
          module: `script:${this.scriptName}`
        });
        continue;
      }

      if (!this.options.dryRun) {
        logger.info(
          `Going to update transaction ${transaction.id} with new amount ${totalOrderAmountPostFees}..`,
          {
            module: `script:${this.scriptName}`,
            data: {
              totalOrderAmountPostFees,
              currentConsiderationAmount,
              percentageDifference
            }
          }
        );

        await AssetTransaction.findByIdAndUpdate(transaction.id, {
          "consideration.amount": totalOrderAmountPostFees
        });
      } else {
        logger.info(`Would update transaction ${transaction.id} with new amount ${totalOrderAmountPostFees}..`, {
          module: `script:${this.scriptName}`,
          data: {
            dryRun: this.options.dryRun,
            totalOrderAmountPostFees,
            currentConsiderationAmount,
            percentageDifference
          }
        });
      }
    }

    logger.info("Finished migrating portfolio sell transaction consideration amount!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationPortfolioSellConsiderationAmountScriptRunner().run();
