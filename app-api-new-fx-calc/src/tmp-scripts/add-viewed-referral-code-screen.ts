import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import { Portfolio } from "../models/Portfolio"; // Adjust the import path to where your Portfolio model is located

class AddViewedReferralCodeScreenFieldScriptRunner extends ScriptRunner {
  scriptName = "add-viewed-referral-code-screen-to-users";

  async processFn(): Promise<void> {
    logger.info("Starting to add viewedReferralCodeScreen to users...", {
      module: `script:${this.scriptName}`
    });

    const ownersWithAllocation = await Portfolio.aggregate([
      {
        $match: {
          "initialHoldingsAllocation.0": { $exists: true }
        }
      },
      {
        $group: {
          _id: null,
          owners: { $addToSet: "$owner" }
        }
      }
    ]);

    const ownerIds = ownersWithAllocation.length > 0 ? ownersWithAllocation[0].owners : [];

    const updateResult = await User.updateMany(
      {
        $or: [
          { referredByEmail: { $exists: true } },
          { submitRequiredInfoAt: { $exists: true } },
          { _id: { $in: ownerIds } }
        ]
      },
      {
        $set: { viewedReferralCodeScreen: true }
      }
    );

    // Log the number of documents updated
    logger.info(
      `Finished adding viewedReferralCodeScreen to users. Documents updated: ${updateResult.modifiedCount}`,
      {
        module: `script:${this.scriptName}`
      }
    );
  }
}

new AddViewedReferralCodeScreenFieldScriptRunner().run();
