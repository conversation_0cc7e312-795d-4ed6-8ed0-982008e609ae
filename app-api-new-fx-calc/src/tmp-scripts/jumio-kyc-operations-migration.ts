import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { KycOperation } from "../models/KycOperation";
import { ProviderEnum } from "../configs/providersConfig";
import { User } from "../models/User";

class RemoveJumioKYCOperationsScriptRunner extends ScriptRunner {
  scriptName = "jumio-kyc-operations-migration";

  async processFn(): Promise<void> {
    logger.info("Deleting Jumio pending KYC operations...", {
      module: `script:${this.scriptName}`
    });

    await KycOperation.deleteMany({
      activeProviders: [ProviderEnum.JUMIO],
      status: "Pending"
    });

    logger.info("Finished deleting Jumio pending KYC operations!", {
      module: `script:${this.scriptName}`
    });

    logger.info("Migrating passport match field...", {
      module: `script:${this.scriptName}`
    });

    await User.updateMany({ isPassportMatchingJumio: { $exists: true } }, [
      { $set: { isPassportMatchingKycProvider: "$isPassportMatchingJumio" } }
    ]);

    logger.info("Finished migrating passport match field!", {
      module: `script:${this.scriptName}`
    });
  }
}

new RemoveJumioKYCOperationsScriptRunner().run();
