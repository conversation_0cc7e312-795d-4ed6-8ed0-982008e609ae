import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import MailchimpService, { AudienceIdEnum } from "../external-services/mailchimpService";
import axios from "axios";
import { User } from "../models/User";
import { mixpanelUsers } from "./mixpanel-users";

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));
class BackfillMailchimpAndMixpanelCompanyEntity extends ScriptRunner {
  scriptName = "backfill-mailchimp-and-mixpanel-company-entity";
  private updatedCount = 0;

  async processFn(): Promise<void> {
    logger.info("Starting to backfill mailchimp and mixpanel users with company entity...", {
      module: `script:${this.scriptName}`
    });

    const mixpanelUsersByEmail = new Map();

    mixpanelUsers.forEach((user) => {
      if (user.email) {
        mixpanelUsersByEmail.set(user.email, user.distinct_id);
      }
    });

    await User.find({ companyEntity: { $exists: true } })
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(async (user) => {
        try {
          const mixpanelUserDistinctId = mixpanelUsersByEmail.get(user.email);

          await Promise.all([
            MailchimpService.updateMember(
              user.email,
              {
                merge_fields: { COMPANYENT: user.companyEntity, RESIDENCYC: user.residencyCountry }
              },
              AudienceIdEnum.WEALTHYHOOD,
              { silent: true }
            ),
            mixpanelUserDistinctId &&
              axios({
                method: "POST",
                url: "https://api.mixpanel.com/engage#profile-set",
                data: [
                  {
                    $token: process.env.MIXPANEL_PROJECT_TOKEN,
                    $distinct_id: mixpanelUserDistinctId,
                    $set: {
                      companyEntity: user.companyEntity,
                      residencyCountry: user.residencyCountry
                    }
                  }
                ]
              })
          ]);

          this.updatedCount++;

          // We are keeping count so that we know when to wait 5 seconds (every 20 identify calls)
          if (this.updatedCount % 20 === 0) {
            logger.info(`We have migrated ${this.updatedCount} users!`, {
              module: `script:${this.scriptName}`
            });

            await delay(5000);
          }
        } catch (error) {
          logger.error(`Failed to update user ${user.email}`, {
            module: `script:${this.scriptName}`,
            data: {
              email: user.email,
              error: error.message
            }
          });
        }
      });

    logger.info(`✅ Finished updating ${this.updatedCount} users!`, {
      module: `script:${this.scriptName}`
    });
  }
}

new BackfillMailchimpAndMixpanelCompanyEntity().run();
