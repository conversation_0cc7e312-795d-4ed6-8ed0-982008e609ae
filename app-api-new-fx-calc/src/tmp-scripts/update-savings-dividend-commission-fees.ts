import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { SavingsDividendTransaction } from "../models/Transaction";
import Decimal from "decimal.js";

class UpdateSavingsDividendCommissionFeesRunner extends ScriptRunner {
  scriptName = "update-savings-dividend-commission-fees";

  async processFn(): Promise<void> {
    logger.info("Updating savings dividend commission fees...", {
      module: `script:${this.scriptName}`
    });

    const dividends = await SavingsDividendTransaction.find({
      "fees.commission.amount": { $exists: true, $gt: 0 }
    });

    logger.info(`Found ${dividends.length} savings dividend transactions to update`, {
      module: `script:${this.scriptName}`
    });

    for (const dividend of dividends) {
      const currentAmount = dividend.fees.commission.amount;
      const newAmount = new Decimal(currentAmount).div(10000).toNumber();

      await SavingsDividendTransaction.updateOne({ _id: dividend._id }, { "fees.commission.amount": newAmount });
    }

    logger.info("✅ Finished updating savings dividend commission fees!", {
      module: `script:${this.scriptName}`
    });
  }
}

new UpdateSavingsDividendCommissionFeesRunner().run();
