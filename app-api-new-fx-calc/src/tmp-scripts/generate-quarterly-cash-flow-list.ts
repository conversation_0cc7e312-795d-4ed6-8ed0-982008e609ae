import "./dependencies";
import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Transaction, TransactionDocument } from "../models/Transaction";
import Decimal from "decimal.js";

const fs = require("fs");

class GenerateQuarterlyCashFlowListRunner extends ScriptRunner {
  scriptName = "generate-quarterly-order-list";

  async processFn(): Promise<void> {
    logger.info("Generating quarterly cash flow list...", {
      module: `script:${this.scriptName}`
    });

    const periodStart = "2023-10-1";
    const periodEnd = "2024-1-1";

    // Add headers first
    const quarterlyActivityRows: string[] = [
      "Transaction ID,Customer ID,Depo/Withdrawal,Transaction Timestamp,Amount,Currency,Status"
    ];

    const transactionQuery = {
      $or: [{ status: "Settled" }, { "providers.wealthkernel.status": "Settled" }],
      settledAt: { $gte: new Date(periodStart), $lte: new Date(periodEnd) },
      category: { $in: ["DepositCashTransaction", "WithdrawalCashTransaction"] }
    };

    const numberOfTransactions = await Transaction.countDocuments(transactionQuery);
    await Transaction.find(transactionQuery)
      .cursor()
      .addCursorFlag("noCursorTimeout", false)
      .eachAsync(async (transaction: TransactionDocument, index: number) => {
        if (index % 20 === 0) {
          logger.info(`Going through transaction ${index + 1}/${numberOfTransactions}...`, {
            module: `script:${this.scriptName}`
          });
        }

        quarterlyActivityRows.push(
          [
            transaction.id,
            transaction.owner.toString(),
            transaction.category === "DepositCashTransaction" ? "Deposit" : "Withdrawal",
            transaction.settledAt.toISOString(),
            Decimal.div(transaction.consideration.amount, 100).toDecimalPlaces(2).toNumber(),
            transaction.consideration.currency,
            "Settled"
          ].join(",")
        );
      });

    logger.info("Finished building quarterly cash flow list...", {
      module: `script:${this.scriptName}`
    });

    const writeStream = fs.createWriteStream("./quarterly-cash-flow-activity.csv");

    quarterlyActivityRows.forEach((data) => {
      writeStream.write(data + "\n"); // Write the data and a newline to start the next row
    });

    writeStream.end();

    writeStream.on("finish", () => {
      logger.info("✅ Generated quarterly transaction list!", {
        module: `script:${this.scriptName}`
      });
    });

    writeStream.on("error", (err: Error) => {
      logger.info("Something went wrong!", {
        module: `script:${this.scriptName}`,
        data: {
          error: err
        }
      });
    });
  }
}

new GenerateQuarterlyCashFlowListRunner().run();
