import ScriptRunner from "../jobs/services/scriptRunner";
import { BankAccount } from "../models/BankAccount";
import BanksUtil from "../utils/banksUtil";

class AddBankIdToBankAccounts extends ScriptRunner {
  scriptName = "add-bank-id-to-bank-accounts";

  async processFn(): Promise<void> {
    const bankAccounts = await BankAccount.find({ "providers.truelayer.bankId": { $exists: true } });

    for (const account of bankAccounts) {
      const truelayerBankId = account.providers.truelayer.bankId;
      const bankId = BanksUtil.getBankFromTruelayerProviderId(truelayerBankId);

      await BankAccount.findByIdAndUpdate(account.id, { bankId });
    }
  }
}

new AddBankIdToBankAccounts().run();
