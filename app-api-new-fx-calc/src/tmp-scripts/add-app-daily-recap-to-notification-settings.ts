import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { NotificationSettings, AppNotificationSettingEnum } from "../models/NotificationSettings";

class MigrationAddAppDailyRecapToNotificationSettingsScriptRunner extends ScriptRunner {
  scriptName = "add-app-daily-recap-to-notification-settings";

  async processFn(): Promise<void> {
    logger.info("Migrating notification settings to add app_daily_recap field...", {
      module: `script:${this.scriptName}`
    });

    const numberOfDocs = await NotificationSettings.countDocuments({});

    logger.info(`Going to update ${numberOfDocs} notification settings documents...`, {
      module: `script:${this.scriptName}`
    });

    const updateResult = await NotificationSettings.updateMany({}, [
      {
        $set: {
          [`app.settings.${AppNotificationSettingEnum.DAILY_RECAP}`]: "$app.deviceNotificationsEnabled"
        }
      }
    ]);

    logger.info(`Updated ${updateResult.modifiedCount} notification settings documents!`, {
      module: `script:${this.scriptName}`
    });

    logger.info("Finished migrating notification settings to add app_daily_recap field!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationAddAppDailyRecapToNotificationSettingsScriptRunner().run();
