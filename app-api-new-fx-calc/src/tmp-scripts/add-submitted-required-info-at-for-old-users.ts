import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import DateUtil from "../utils/dateUtil";

class AddSubmittedRequiredInfoAtScriptRunner extends ScriptRunner {
  scriptName = "add-submitted-required-info-at";

  async processFn(): Promise<void> {
    logger.info("Migrating users without submittedRequiredInfoAt...", {
      module: `script:${this.scriptName}`
    });

    const users = await User.find({
      kycStatus: { $in: ["passed", "failed"] },
      submittedRequiredInfoAt: { $exists: false }
    });

    for (let i = 0; i < users.length; i++) {
      const user = users[i];

      logger.info(`Adding submittedRequiredInfoAt for ${user.id}...`, {
        module: `script:${this.scriptName}`
      });

      try {
        await User.findByIdAndUpdate(user.id, {
          submittedRequiredInfoAt: DateUtil.getDateAfterNdays(user.createdAt, 2)
        });

        logger.info(`Added submittedRequiredInfoAt for ${user.id}!`, {
          module: `script:${this.scriptName}`
        });
      } catch (err) {
        logger.error(`Failed to add submittedRequiredInfoAt for ${user.id}...`, {
          module: `script:${this.scriptName}`,
          data: { error: err }
        });
      }
    }

    logger.info("Finished migrating users without submittedRequiredInfoAt!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddSubmittedRequiredInfoAtScriptRunner().run();
