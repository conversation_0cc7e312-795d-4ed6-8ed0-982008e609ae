import <PERSON>riptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import axios from "axios";
import Decimal from "decimal.js";

/**
 * Exported events from Mixpanel
 * Note:
 * Filters used:
 *  1. Event: Portfolio Valuated
 *  2. savingsGBP > 0
 * The only property that exlcluded here is $distinct_id_before_identity
 */
const OLD_PORTFOLIO_VALUATED_EVENTS = [
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715123029,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "087c26e8-3fd3-56e6-b34e-41e61ac6ec5d",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715123031228,
      cash: 44.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715123031293,
      portfolioValue: 463.91,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715123021,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "cfb852ff-3b48-590b-a14d-07cd483588d8",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715123024641,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715123024735,
      portfolioValue: 12.86,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715123006,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "54552cc5-4eae-5d08-9f11-228a955a543d",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715123009631,
      cash: 0.18,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715123009651,
      portfolioValue: 851.61,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715122852,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "35202972-dfb5-51f7-b90a-f2169477e983",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715122856256,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715122856316,
      portfolioValue: 334.65,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715122845,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "46ef88d9-0317-5092-84f9-c9f925007dd3",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715122847965,
      cash: 11.04,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715122848012,
      portfolioValue: 5265.6,
      repeatingTopupAmount: 150,
      savingsGBP: 1500000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715122845,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "a9eed8d9-4cde-51fc-bf5b-12f94d0bc89a",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715122847673,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715122847691,
      portfolioValue: 302.03,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715119460,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "a74fee4d-1390-5f7a-b805-0c3ae1672fd1",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715119462838,
      cash: 44.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715119462907,
      portfolioValue: 463.91,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715119452,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "6ff00690-4444-526e-a98a-5bb37947447b",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715119456590,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715119456633,
      portfolioValue: 12.86,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715119438,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "c2427720-1be4-51c2-8cff-9afae7120c34",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715119441222,
      cash: 0.18,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715119441244,
      portfolioValue: 851.61,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715119288,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "6729ab0c-129a-54ba-ba06-36976a5d99e0",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715119291679,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715119291732,
      portfolioValue: 334.65,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715119281,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "90e55de4-397f-546e-ae9e-e456df71a989",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715119285340,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715119285375,
      portfolioValue: 302.03,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715119281,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "7848dbde-79ce-5719-9046-7312dd4c6cfb",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715119284326,
      cash: 11.04,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715119284400,
      portfolioValue: 5265.6,
      repeatingTopupAmount: 150,
      savingsGBP: 1500000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715115832,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "da50e200-86eb-58e8-8eee-d82b2cd7e403",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715115834554,
      cash: 44.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715115834665,
      portfolioValue: 463.91,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715115824,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "557e2665-cf15-5b82-a7ab-64479c5b33b7",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715115827174,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715115827227,
      portfolioValue: 12.86,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715115810,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "08aa5dfb-8985-57ba-ad8c-f0564d79bea1",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715115813296,
      cash: 0.18,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715115813512,
      portfolioValue: 851.61,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715115661,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "970b70a6-d6ec-5b0b-82ac-b774012aed06",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715115664150,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715115664186,
      portfolioValue: 334.65,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715115654,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "ec0eb9d0-be9b-5e4d-ba34-53631c853209",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715115656402,
      cash: 11.04,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715115656471,
      portfolioValue: 5265.6,
      repeatingTopupAmount: 150,
      savingsGBP: 1500000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715115653,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "dbabe964-33d5-5467-b159-caa3da91cf10",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715115656180,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715115656243,
      portfolioValue: 302.03,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715112271,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "ee0ce0ca-84b0-504b-8bc5-530b48b88400",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715112273684,
      cash: 44.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715112273740,
      portfolioValue: 463.95,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715112263,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "024d28c3-fe5b-576b-a070-89ecd0cd5bbe",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715112265820,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715112265832,
      portfolioValue: 12.8,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715112248,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "0440d3f9-2089-5125-bfa6-4cac522e91e6",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715112250668,
      cash: 0.18,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715112250731,
      portfolioValue: 851.42,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715112098,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "05a5ca21-453a-559a-a1ff-3a2bea723a72",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715112101650,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715112101700,
      portfolioValue: 334.75,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715112091,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "648b9659-b269-5640-8857-4b3425b2c161",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715112093839,
      cash: 11.04,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715112094019,
      portfolioValue: 5267.4,
      repeatingTopupAmount: 150,
      savingsGBP: 1500000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715112090,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "0f726ebf-5b51-58df-a4ae-c01f0d4e0f84",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715112093525,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715112093560,
      portfolioValue: 302.02,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715036624,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "8e9b022f-9a28-5cd9-9d36-8df142185bf6",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715036626918,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715036638303,
      portfolioValue: 458.42,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715036617,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "37fc4331-f858-5356-8560-cad346f17d26",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715036619459,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715036640853,
      portfolioValue: 12.9,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715036603,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "87bd6a08-a124-5d97-982f-1961a2a517ea",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715036606509,
      cash: 0.14,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715036633782,
      portfolioValue: 851.85,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715036464,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "27807968-44f9-5691-b349-ea94408d22ec",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715036467119,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715036560209,
      portfolioValue: 336.3,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715036457,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "1e163799-11ed-5f72-91c0-d81593b1294f",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715036460402,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715036488383,
      portfolioValue: 5042.68,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715036456,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "3b00d162-d053-5eeb-bfbf-ae41e8975008",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715036459492,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715036481143,
      portfolioValue: 299.01,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715033027,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "b0fdf03b-23d2-518e-ba74-b8a109e1c811",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715033029754,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715033029789,
      portfolioValue: 458.42,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715033019,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "a6d702ac-d362-551f-9a4d-b19f2f4bd616",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715033021657,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715033021782,
      portfolioValue: 12.9,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715033004,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "4553ec2f-2f89-5a7b-be1f-58ae143bc97e",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715033006990,
      cash: 0.14,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715033007029,
      portfolioValue: 851.85,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715032845,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "8add8f1b-8125-506c-8b6c-39da509cdcc0",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715032847850,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715032847902,
      portfolioValue: 336.3,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715032838,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "6480bfa6-818f-5c52-8b86-e603ae866d20",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715032840532,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715032840572,
      portfolioValue: 5042.68,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715032837,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "281e7721-44f4-592a-8e67-1d77022c8331",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715032840720,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715032840793,
      portfolioValue: 299.01,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715029454,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "4c0005a9-f829-539b-8ed6-5429aafe62fe",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715029456776,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715029456833,
      portfolioValue: 458.42,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715029446,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "75a44490-a6d6-5e2d-b291-b5d4eb8a5043",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715029450215,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715029450284,
      portfolioValue: 12.9,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715029433,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "c62a1a3f-5ff6-5900-b619-bb86857a2fd0",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715029435671,
      cash: 0.14,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715029435704,
      portfolioValue: 851.85,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715029289,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "3f4f74f3-ea75-5a85-8699-e3f5a9e59b2b",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715029292051,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715029292112,
      portfolioValue: 336.3,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715029283,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "4b888ed2-b3d8-5eae-9b57-a923fb0603be",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715029285299,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715029285359,
      portfolioValue: 5042.68,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715029282,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "405e9781-e098-5c17-80dc-f662a05a165f",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715029284117,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715029284174,
      portfolioValue: 299.01,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715025841,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "d22ff862-f3f9-5890-b706-e7527ebe13cd",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715025845240,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715025845276,
      portfolioValue: 458.43,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715025833,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "0f4c3667-e1d9-5c59-9f5d-cc204b83f8b7",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715025836481,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715025836566,
      portfolioValue: 12.83,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715025819,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "77c046e8-b948-53fc-b477-986d42fa7e9f",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715025821406,
      cash: 0.14,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715025821460,
      portfolioValue: 850,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715025666,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "59e9acc0-bb2a-5a9f-b7e6-e0e46dd4d2f3",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715025668812,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715025668846,
      portfolioValue: 336.01,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715025659,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "b7dd9f71-2b52-5e8f-b399-2850ebefdf77",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715025661143,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715025661206,
      portfolioValue: 298.83,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1715025659,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "a0e48717-dca1-52a7-a008-c48d878482ec",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715025662974,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715025663008,
      portfolioValue: 5038.61,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714950295,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "ea1fda16-b7cf-5e58-8ddb-87ad4c364eaa",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714950298505,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714950298539,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714950286,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "cda87c44-2807-5e51-a59c-025689eba8c3",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714950289318,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714950289350,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714950271,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "37898d48-33de-5f03-af7b-6ca0b3e3a955",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714950273920,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714950274030,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714950115,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "6cc8eca1-b2ab-535b-9173-0daca6541349",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714950118550,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714950165058,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714950108,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "22b0a139-befe-5a4c-bcd9-873bfb8e9263",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714950111526,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714950155101,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714950107,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "e426e86a-f6cd-5c74-8747-8f0cf36cd00a",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714950109829,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714950147481,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714946653,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "5363602d-0a18-54b7-b8c7-f0f48ee1c223",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714946657072,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714946657125,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714946645,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "fc0da35b-c6d4-5e21-8004-aea166b19567",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714946648551,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714946648571,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714946629,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "bf5c22ea-df73-5166-b172-9d584ef66434",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714946632521,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714946632580,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714946468,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "a6355f53-4c03-5301-a122-65830e4fbea8",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714946472354,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714946473623,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714946461,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "69d6c927-fce5-54db-8a14-fc09b7250ab9",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714946463610,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714946463643,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714946461,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "1e0c7e50-1c62-59cd-b643-874d5e6153dd",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714946464248,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714946464277,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714943055,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "f8c92668-41d1-5162-b55a-339542f65f61",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714943057392,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714943057709,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714943047,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "91d92a50-a6b3-5994-ae70-3acbfe5ae224",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714943050739,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714943050769,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714943032,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "dcdfccb1-0d21-5024-aa6c-dae79ccc95b1",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714943036169,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714943036215,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714942879,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "c8ecfc86-b131-5667-8292-a568f0b07854",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714942883228,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714942883320,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714942873,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "53af9e61-e8ea-5f7d-af02-feef7d3a731a",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714942875170,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714942875223,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714942872,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "600f7077-79a7-599a-bb39-5d13aba6b60b",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714942877418,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714942877453,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714939414,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "cde474c2-a10d-5916-8fa0-5941ea9a08a5",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714939416980,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714939417018,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714939406,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "96b73785-9979-51a4-8893-314285321df6",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714939408554,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714939408616,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714939392,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "e6e62463-5548-567e-aa9a-a3cfbbe2bb9c",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714939394220,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714939394261,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714939241,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "7c4d6e0d-b5c3-5e4c-9232-51da7be090f3",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714939243120,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714939243402,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714939234,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "61f0cb69-9095-58b8-b306-7c214232691d",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714939237769,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714939237853,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714939233,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "6983bdc4-b6b7-5647-9be0-e2d502608a3e",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714939236144,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714939236181,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714863848,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "8a615135-910c-5cbb-873e-0ea7f95a5ae9",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714863850614,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714863856711,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714863839,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "899ab6a5-7355-5ae5-8432-df7572d61011",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714863842300,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714863848502,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714863824,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "d369c915-1257-50a5-a3ab-9110977bde97",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714863826903,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714863837366,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714863666,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "4d7fc84c-b911-54a4-8e16-27d992cc7124",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714863669085,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714863702688,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714863659,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "bbe658b3-3d7a-5be9-baf9-83c18a20218a",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714863661033,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714863678919,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714863659,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "3133915a-a849-5aeb-834c-b4cf2ac62812",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714863662020,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714863692715,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714860294,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "d663eefc-1d6f-55ca-9ced-b13218eae0b5",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714860297674,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714860297705,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714860286,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "fde6a899-2c17-5e01-8797-9c72edf9ea29",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714860288718,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714860288797,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714860271,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "c590d36f-d976-5f89-beab-f7a41f535d69",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714860274362,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714860274397,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714860117,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "416816ae-5f0a-56de-8e5e-65f630696926",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714860120626,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714860120666,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714860110,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "b99eade9-2830-54e2-a0b5-2e17a9aac9d4",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714860112819,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714860112870,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714860109,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "766d2e6d-e980-5b9d-b94a-e44146e9f6d4",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714860111926,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714860111966,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714856656,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "e44852be-71b2-5e2f-9d3d-b8ebfee0cf5e",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714856658171,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714856658203,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714856647,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "5cd06b24-a0a8-54a0-a32b-0dde0064a875",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714856651599,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714856651648,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714856632,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "80c985f1-9c3c-561a-af70-0f5d919353eb",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714856634975,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714856635002,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714856475,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "821703fb-b66c-58f8-9d5d-9e57b5681940",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714856477929,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714856478256,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714856468,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "2a11ca2e-ac1c-5ea5-a33a-8e7f6c09783c",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714856471104,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714856471122,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714856467,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "4bff1029-c59c-5a85-8e4c-b26e902394e5",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714856471038,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714856471094,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714853070,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "5789f89f-0266-5e6e-9936-57bf75245e08",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714853073674,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714853073759,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714853062,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "24e209d4-c133-5955-9dc3-def89e356321",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714853064295,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714853064490,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714853048,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "5b41f418-cf56-539c-ba4b-ee2748f3cb3d",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714853050987,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714853051335,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714852897,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "3fc253ae-94a5-547c-802d-7c783e963401",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714852899800,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714852899862,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714852890,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "e69d0f0e-d0b5-560a-b82a-0520ff3510da",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714852893139,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714852893174,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714852889,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "ff2df677-0678-5d1d-820e-606549250493",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714852891482,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714852891749,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714777648,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "7e92013a-5555-5d59-923b-4313db0b76f4",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714777651766,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714777651801,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714777640,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "08992308-4292-599a-8cc7-38ca0c46a71b",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714777642222,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714777642256,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714777624,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "db16a219-33fd-569f-a663-e0608a853c26",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714777627601,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714777627628,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714777466,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "705a69fd-ddc9-5386-a53a-21a9a0b19015",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714777468778,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714777468835,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714777458,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "f52898db-8529-5256-b1f9-a24cd61e6974",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714777461379,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714777461436,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714777458,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "7078fdb5-32f9-5db3-b3ad-178854fbd0f7",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714777461821,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714777461877,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714774008,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "3cd0a6ec-787c-5ca4-852e-3b7fa1c7e105",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714774010090,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714774010165,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714774000,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "f0d45a0b-59e1-5e77-9ce0-88f35b81b06a",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714774002863,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714774002907,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714773985,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "1977bb43-511f-5116-8581-dd27ba9143ab",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714773988564,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714773988681,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714773827,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "3f64d692-ce68-5a36-a9d6-66009174dcbb",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714773829935,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714773829993,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714773820,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "1e98f013-0232-5790-ba1d-d2ab31ca75d8",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714773822954,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714773823263,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714773819,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "6e49c24f-112e-57cf-a2a9-00083ab8279b",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714773822396,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714773822426,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714770339,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "b0a4449d-a706-59dc-a91c-38b4108d519d",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714770341806,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714770341905,
      portfolioValue: 457.98,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714770331,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "39d90c4c-996c-5ba7-b3ac-c8fa29981ca9",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714770334128,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714770334186,
      portfolioValue: 12.55,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714770317,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "4f5dfb48-cbb7-5381-a661-12ae4e639b60",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714770319517,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714770319558,
      portfolioValue: 842.58,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714770165,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "04b53400-7378-5833-9d1b-8e8beb8edb8f",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714770167354,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714770167439,
      portfolioValue: 332.4,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714770158,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "3e4c063d-e412-560a-a0ca-66caa0e1eba5",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714770161058,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714770161104,
      portfolioValue: 4877.51,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714770158,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "ed4018d4-81a2-523d-8287-d607da23e277",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714770160007,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714770160053,
      portfolioValue: 297.47,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714766650,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "aa869558-6fe3-59da-a29c-69769aeab5e2",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714766652167,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714766652395,
      portfolioValue: 458.02,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714766641,
      distinct_id: "1704217325729-2154664349861271131",
      $insert_id: "f3190553-a153-5923-a5c2-cd6e678d9241",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714766643874,
      cash: 0.62,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: true,
      id: "65944af9448bc4215dc385f7",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714766643927,
      portfolioValue: 12.58,
      repeatingTopupAmount: 650,
      savingsGBP: 30000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714766627,
      distinct_id: "1697177406231-5998536736422874069",
      $insert_id: "32befe13-7ee5-5ccd-86cc-942b1023ed96",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714766629119,
      cash: 0.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "6528df58539bc552a3b24a9a",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714766629169,
      portfolioValue: 842.5,
      savingsGBP: 780
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714766473,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "fdd051da-5b28-52b1-9c9b-18b146b1f5e6",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714766474759,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714766476920,
      portfolioValue: 332.53,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714766465,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "2da110bf-cc56-5463-8569-20be8c1c15ca",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714766469414,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714766470099,
      portfolioValue: 4880.18,
      repeatingTopupAmount: 150,
      savingsGBP: 1000000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714766464,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "95021331-bcc9-582b-95ab-f47ea937d5f4",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714766467678,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714766467721,
      portfolioValue: 297.43,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714691224,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "7a27e149-a898-5013-81c6-a9de3edc3fad",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714691226001,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714691226076,
      portfolioValue: 455.34,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714691052,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "b8d2b0ae-2439-59bd-9a43-4f717f9204e5",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714691054571,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714691054749,
      portfolioValue: 326.08,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714691045,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "009644e7-fc2d-5686-a1a7-79bf7a3d5860",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714691047369,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714691047469,
      portfolioValue: 4837.99,
      repeatingTopupAmount: 150,
      savingsGBP: 500000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714691044,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "b9fb0325-f3da-5da1-8de2-046a8d4eb062",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714691047554,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714691047594,
      portfolioValue: 293.14,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714687623,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "63305319-7e8c-5c77-9345-dc770448ed1f",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714687626289,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714687626336,
      portfolioValue: 455.34,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714687449,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "ed9cd385-5178-57a5-854a-6874ce9bbd4b",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714687452534,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714687452664,
      portfolioValue: 326.08,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714687442,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "570ddc6c-c579-5674-b90c-4fbcedee4509",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714687444459,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714687444521,
      portfolioValue: 293.14,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714687442,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "5f66787d-bf62-5fd7-98d4-d5086f4ae137",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714687445082,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714687445133,
      portfolioValue: 4837.99,
      repeatingTopupAmount: 150,
      savingsGBP: 500000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714683985,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "b2359223-bf47-5005-9250-b16136f3efb6",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714683988319,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714683988357,
      portfolioValue: 455.34,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714683808,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "d48237fc-7257-5c20-93b4-aafdce00ab39",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714683812368,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714683812409,
      portfolioValue: 326.08,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714683801,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "685ce105-2ebf-501a-a6d0-2a212beeda86",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714683803992,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714683804024,
      portfolioValue: 293.14,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714683801,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "62ef2401-8e37-5891-adab-07bd509e151c",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714683804372,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714683805311,
      portfolioValue: 4837.99,
      repeatingTopupAmount: 150,
      savingsGBP: 500000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714680259,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "3cd37215-eef4-5dd3-b0fd-2d994d1b56ed",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714680261849,
      cash: 54.19,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714680261904,
      portfolioValue: 455.4,
      savingsGBP: 5000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714680077,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "b84574a3-1858-5a48-b303-70bb66b67b2e",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714680080419,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714680080565,
      portfolioValue: 326.12,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714680070,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "38444672-e059-5624-9730-bae18b414e34",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714680073562,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714680073595,
      portfolioValue: 4835.56,
      repeatingTopupAmount: 150,
      savingsGBP: 500000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714680069,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "8ee3b670-bcc4-5fe2-b1ac-466e818e9265",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714680071920,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714680072236,
      portfolioValue: 293.16,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714604942,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "e62d5daa-d073-5193-8373-9148c57f6511",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714604945814,
      cash: 34.76,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714604945836,
      portfolioValue: 462.34,
      savingsGBP: 4000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714604763,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "1b457322-ad72-51c9-8707-58e6bc60e579",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714604765969,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714604765983,
      portfolioValue: 322.31,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714604756,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "c9a8ac2b-264e-505c-9f26-e58c26fd583a",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714604758728,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714604758794,
      portfolioValue: 4814.99,
      repeatingTopupAmount: 150,
      savingsGBP: 300000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714604755,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "e6116a21-2c6d-56e7-8a81-61343d70a5a5",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714604758301,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714604758365,
      portfolioValue: 290.86,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714601292,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "a68bfc8c-d295-5faa-ab5d-e13244ba2a39",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714601295136,
      cash: 34.76,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714601295145,
      portfolioValue: 462.34,
      savingsGBP: 4000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714601120,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "030dbdf4-2213-5fa8-99a9-2fa70a15ed3b",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714601122705,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714601122757,
      portfolioValue: 322.31,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714601113,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "5a7dd07e-561e-5b96-93a8-f916232830c0",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714601115850,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714601115908,
      portfolioValue: 4814.99,
      repeatingTopupAmount: 150,
      savingsGBP: 300000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714601112,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "86aa77e9-8384-576b-a517-233c935859bd",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714601114946,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714601114980,
      portfolioValue: 290.86,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714597641,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "a6341bab-1254-52f8-a295-476da1dd96e3",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714597643310,
      cash: 34.76,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714597643373,
      portfolioValue: 462.34,
      savingsGBP: 4000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714597459,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "fac98281-b2aa-5f31-b912-c4d62c82786f",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714597462044,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714597462108,
      portfolioValue: 322.31,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714597452,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "f5c59601-93d4-53dc-a3e3-085b6aac20f5",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714597455671,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714597455716,
      portfolioValue: 4814.99,
      repeatingTopupAmount: 150,
      savingsGBP: 300000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714597451,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "e6cebb24-7c35-5dfd-835d-8ee253a4d2f7",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714597454309,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714597454360,
      portfolioValue: 290.86,
      savingsGBP: 800
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714593857,
      distinct_id: "1706618269499-7648650978998875336",
      $insert_id: "71fe59fb-d200-53cb-a12d-38afee5770f6",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714593859852,
      cash: 34.76,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "65b8ee061006bd544ff40298",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714593859894,
      portfolioValue: 462.57,
      savingsGBP: 4000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714593664,
      distinct_id: "52867f8f3369eb1e598a87f3c00567b24890160bf68af31e40d127861878bc7a",
      $insert_id: "b0c09266-4596-548d-b5bd-ba0ce4063699",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714593666782,
      cash: 2.05,
      email: "<EMAIL>",
      hasRepeatingRebalance: true,
      hasRepeatingTopup: false,
      id: "620cf7a55f65a5004a9cf05d",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714593666824,
      portfolioValue: 324.82,
      savingsGBP: 3000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714593656,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "091e6452-254d-584c-9581-82d717235e90",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714593659628,
      cash: 10.11,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: true,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714593661023,
      portfolioValue: 4832.07,
      repeatingTopupAmount: 150,
      savingsGBP: 300000
    }
  },
  {
    event: "Portfolio Valuated",
    properties: {
      time: 1714593655,
      distinct_id: "60901dd468d3be003ecacd64",
      $insert_id: "cc949059-e33d-5926-bdd2-2fd091fa6f1c",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714593658848,
      cash: 11.25,
      email: "<EMAIL>",
      hasRepeatingRebalance: false,
      hasRepeatingTopup: false,
      id: "60901dd468d3be003ecacd64",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714593658885,
      portfolioValue: 291.44,
      savingsGBP: 800
    }
  }
];

const NEW_UUIDS = [
  "537b522c-4ab1-4c13-a0f6-07e42a34a0a2",
  "4b2ae91d-9330-4404-8a45-5d73bc0ba7c6",
  "0851c6c9-4f95-4092-8df0-5a6576b8c0b7",
  "7987ec11-a8a6-4f23-b5d3-e9a9334202c5",
  "cd1642a9-13b9-45ed-899a-e31600197735",
  "f1be7eee-3fa0-4f23-a805-7889fe51327d",
  "e34bcf8e-3502-47bb-83f2-3d680a6f68ff",
  "234dab52-a102-4625-a6f1-d6cfd966fc32",
  "704569d0-c409-47bd-b7fa-49fc3ba42228",
  "ff625809-b1e6-4171-97e4-949748b1c9ac",
  "471b41eb-4955-4cfe-9cf1-a0f31fbe78c8",
  "14c4cc8a-a021-479a-9181-51811904e8b9",
  "8859f4ac-e803-4f18-8f1f-cbd36efe745f",
  "d6576670-ddd2-4153-ab8b-686bccdc6ef2",
  "e42300c6-def1-4230-a7fc-3802852f9ad1",
  "bf6948a0-a263-4c99-9b09-d6f7ba3e4f54",
  "e7122593-ef03-409a-b21e-cb3cd00cd0b6",
  "ac4b0b97-ed23-4469-86ae-cac08133a094",
  "fea4e3e9-1262-4c44-bd7c-aa2657c455c9",
  "cfa5cc35-af33-4e6d-84cd-622b2ded19f4",
  "d3a87473-a504-4141-a2a8-6a281bec1b3c",
  "79462c73-0183-456d-8f98-a1f22da45ba1",
  "fca0e33f-eb9a-4c6b-9a9c-813bd7a02da6",
  "41b551b6-e2e0-4f85-aba2-d0ead81f1a64",
  "a2f8739b-8a1d-4915-9ff1-06a5c6e1565b",
  "06676d42-d634-4764-8308-8f08816656df",
  "1595c3f4-f53a-480b-92f4-e423a53811df",
  "f7e41d53-e235-4358-a531-a093005ffb1c",
  "cc471458-453c-4e91-904d-c0712c8a9711",
  "a86d89af-cdc3-4a70-865f-a6a3cd97beae",
  "2c2640aa-4c01-45c5-90d7-6ba45dbeb888",
  "0bed9d03-eb49-4d1f-aad0-44c9978b462f",
  "3d5b7ef5-577a-4872-85bf-fb231c7d8d58",
  "ff9987df-c3e8-4433-8bb8-469f9e6dd2cc",
  "e2b5060a-2dca-4cfc-8345-e4f37a3bb699",
  "b1d4d479-43dc-41e8-be36-ec5ffcbbf8d2",
  "6262b4f9-c927-40d4-9918-0f3ea37e4a33",
  "1af403a9-8992-4192-9e33-5e4dfb9b3ff8",
  "1d31ae31-50af-48ee-b76b-75841ecfdf4d",
  "b23f8d34-7e77-45ad-a534-7685c0a93dac",
  "fe5682f1-774f-4be4-b285-ff85e4fb250f",
  "9f39359f-71fc-4996-a168-14f8db6056f5",
  "6e790293-2ef1-4f3d-ab61-3243bc678ace",
  "4756134d-40cf-4dea-a0ad-6244af0dc855",
  "76e32275-d4c4-4190-93a7-98f4f78d0d66",
  "b7e62836-5ecd-492a-bdf4-45aef6b652f2",
  "f0cc3fe5-88ec-44f0-b17c-323b15880d22",
  "0b3b29bc-b3ba-4749-932d-876aae22b88d",
  "21625117-90e3-479c-aae4-c1d4bf73ac88",
  "9d628e27-2d67-476b-a19e-3c176cd8813c",
  "e9dc5296-16ba-491d-9e59-52ffe914db7f",
  "0fdd4905-0b63-4230-99bc-00f78e42a2e1",
  "1bd3f134-88d5-4c70-9d88-97571c3ef800",
  "a63ebd08-8949-46a4-b171-deb66fbc2340",
  "fcea99a8-9b23-44f4-84a7-56e3ccbb18f9",
  "a1e09685-638c-4961-a6a5-08cdd192db68",
  "583d7084-bd29-402d-8f3a-dc6494335859",
  "13ce6f76-cf83-493f-b18a-e4797070cb3d",
  "a128b629-220c-4432-92f8-5de12628a807",
  "cfe072d8-335e-44c7-82ef-bc35bffdd4a2",
  "969317c9-9b6f-4313-8bda-17166414b85b",
  "62d944ae-d0fd-452c-ac6e-aed115e5cb16",
  "c954ffc8-a735-4da7-b3a0-749aead93429",
  "1cab71e2-0934-4149-acb6-428d78f7634e",
  "1f587aa6-ba72-4e76-8834-ca25e7554179",
  "db35b98e-040b-4718-bb6e-4a42b2182998",
  "b6f75a09-9add-4b63-b148-5290882a726b",
  "26c1523f-44e8-4f97-9154-747e1a714851",
  "daf4e1ee-41b3-4dfa-952f-caac523bbb89",
  "51d872fe-24b9-469b-99ff-41523d8d8b6b",
  "535ad5e0-bb14-4be7-8405-5a4321adae5f",
  "9e581810-2672-464e-942d-8de2a8eb3dd2",
  "cee03434-3666-4de9-aad4-de57295ff34c",
  "db665efe-520b-4b3f-8ec5-f1bd30be2f33",
  "21f093ff-4344-41de-a480-c7f490b8cd61",
  "a2ef56d5-1e86-4164-b45e-3aec6b13c07f",
  "12dd4bcc-f580-4a97-98ce-58c5810c9af5",
  "edcaba7c-661a-4191-8c08-b89917ae5337",
  "d0950749-28f6-47fc-9f27-cc5aa1f8ef68",
  "e4ccb169-6306-429d-96b4-578c9eb8d7b1",
  "66fbfbd0-aab8-4804-8b8a-f21bf4ecda06",
  "bb8d529f-3cb7-4fb1-a1da-a5dad2cb39ee",
  "8302c6d9-6411-46f9-89b3-06e4632311eb",
  "95edb551-66af-4f3a-bd32-74a20a18ecc1",
  "4464bfa5-fa58-4e2f-8fe3-e40ca7ae0a75",
  "cb16985b-8e27-4b6f-9fdf-386f73166d8b",
  "0cdbfdae-f879-4ba6-90b4-ea779b8777f5",
  "1477a232-6984-4f0d-a7d0-7a0c4a9841a8",
  "31a8d38c-e988-4f0d-a310-d7732991d524",
  "594b5d86-717a-4245-8a52-bb76dfb808b3",
  "870dc4e2-f4d3-4d2e-b0ca-06c0e7ce0e8e",
  "2b3a643c-a539-4a83-98f4-38b2c440c1cd",
  "23d27d73-2c68-49b3-ba97-e79260b7b822",
  "185df4df-5208-4573-8f2e-83fee6816bb8",
  "90711ee3-d153-48e5-aac4-0d4ce4adb73e",
  "a0a82d9b-8fdf-41c0-b7b3-d2fad0547869",
  "f7556247-11e1-4f5c-a952-3c5091d9c3a2",
  "547fd981-c803-4346-8ad5-2408212c05ef",
  "7a33c1de-22b9-4792-aa77-166250245951",
  "d6d8a7ec-6cf3-4acf-af62-faeceb947b75",
  "26c6b7d9-5a22-44d0-9cb8-3a73571bec02",
  "3e072c02-81d2-4242-a161-8708dc0ceb33",
  "a48e6fff-db7c-4aae-aa25-4b2643e54a28",
  "754dd805-2ba6-4ff4-a523-ed798778e095",
  "c60911d8-3718-4f50-8a68-27f0769a04f7",
  "45063d2c-02a7-471d-bb41-6686b19ee655",
  "4c92cf65-889d-4124-be87-1fbe1f46ee7b",
  "9d06a6c0-965c-4529-a875-9b2dd301831e",
  "3a864f71-a427-4ac7-9f39-05fd0115e198",
  "8ffeeb32-ea85-435c-94fc-f4c9f90413c6",
  "dbd5a763-b30e-4bef-8a15-d47f1da60e69",
  "c28f95c1-4065-43af-b7ff-25efc7dff3f4",
  "0d8384d0-2c6b-4257-8f0f-1bc545fb70dd",
  "a2ddcd8d-012d-415f-a71e-22f1247255c6",
  "110c7bc8-2754-42d6-84c6-f873354fd063",
  "db07e709-7fa8-416b-801d-75b451bfcc99",
  "ece3d2ff-9e32-4d02-8ef3-4fee45448923",
  "d7df4613-41ed-4d83-b950-754b06eaee6e",
  "af80abce-67b8-44a7-9411-a100d56045f6",
  "b98e5c67-89ad-4ae3-b0d9-9038285672e0",
  "6ea665c9-ecb5-4531-93c8-e67085c04fc7",
  "c42a9c0d-0870-48c6-8e5b-ccf094294464",
  "db53daaa-f7ad-416c-abf4-5135d02afc82",
  "9c96b042-81ef-42fb-af65-0419ecffd04c",
  "8e43a2fb-1819-4470-8227-d2e9c2e8f7fa",
  "e1240596-f16e-4d7d-9cc9-7df51c25bbfb",
  "477f3733-9937-4654-9fd0-80fe744dcd37",
  "c061cb24-7bc6-4571-a612-9a8403790f68",
  "ece1d313-9a93-4e1f-becb-18a87cd6a6eb",
  "ee584494-5c96-4b75-a3cb-c44a209aa668",
  "001e22b8-2a55-48b6-8181-2bb0556f7003",
  "9a3b13b0-d698-47c0-ba68-ecf8567dca4f",
  "ff697f52-cc37-42b5-bc1d-70c578fcf7fc",
  "29af522b-a129-46b0-9c64-6f59282203d0",
  "99071721-7b6f-4b5a-a7bf-c417e7f51c5d",
  "e0ed9deb-67f4-41e3-8aef-e3dbc9f149d1",
  "c14c6160-2537-4796-bfc4-eb2aab2b988f",
  "553fbdb7-9ad1-4aee-8897-e957207635cd",
  "46f0d688-30f1-4eab-a0da-d72fef926496",
  "0281781c-c420-4b2a-9685-2c5ffdd22489",
  "4fabb180-2b05-4185-a937-477100720afb",
  "a7da79cf-710e-4ed3-b098-2b3e68d223d3",
  "3097c39f-ba2a-42c4-83f7-9e076137942c",
  "b5033da7-f959-4138-96fb-c26e9e681f97",
  "b14b2d94-5b63-4798-8ad9-233ac57c50b0",
  "36aba2c7-10db-4563-971e-4d93671c213a",
  "9a000b51-f624-41e3-be6a-aa9cba686bc6",
  "104d6b76-ae76-4df8-8a9e-62c67935eb35",
  "0657ea4c-931c-44d4-8bb2-d9ace7272657",
  "740269a1-8193-42bd-889c-9f3c7ec30e46",
  "f8d8ecf6-f570-4708-bf96-0b610d8fff7f",
  "305e7855-5019-41ec-853d-29b6a0e5eaf9"
];

class MigratePortfolioValuationMixpanelEventsWithInflatedSavingsScriptRunner extends ScriptRunner {
  scriptName = "migrate-portfolio-valuation-mixpanel-events-with-inflated-savings";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating portfolio valuation mixpanel events with inflated savings...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`
      });
    }

    logger.info(`About to migrate ${OLD_PORTFOLIO_VALUATED_EVENTS.length} events...`, {
      module: `script:${this.scriptName}`
    });
    for (let i = 0; i < OLD_PORTFOLIO_VALUATED_EVENTS.length; i++) {
      await this.migrateEvent(OLD_PORTFOLIO_VALUATED_EVENTS[i], NEW_UUIDS[i]);
    }

    logger.info("Finished migrating portfolio valuation mixpanel events with inflated savings...", {
      module: `script:${this.scriptName}`
    });
  }

  private async migrateEvent(event: any, newUUID: string) {
    const oldUUID = event.properties.$insert_id;

    const newEvent = {
      event: event.event,
      properties: {
        ...event.properties,
        $insert_id: newUUID,
        savingsGBP: Decimal.div(event.properties.savingsGBP, 100).toNumber()
      }
    };
    logger.info(`Migrating event with old UUID: ${oldUUID} to new UUID: ${newUUID}`, {
      module: `script:${this.scriptName}`,
      data: {
        old: event,
        new: newEvent
      }
    });

    if (this.options.dryRun) {
      return;
    }

    await axios.post("https://api.mixpanel.com/import?strict=1&project_id=2537575", [newEvent], {
      auth: {
        username: process.env.MIXPANEL_PROJECT_TOKEN,
        password: ""
      }
    });
  }
}

new MigratePortfolioValuationMixpanelEventsWithInflatedSavingsScriptRunner().run();
