import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Order } from "../models/Order";

class ChangeRejectedOrdersToPendingRunner extends ScriptRunner {
  scriptName = "change-rejected-orders-to-pending";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Changing rejected orders to pending...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    const docCount = await Order.countDocuments({ status: "Rejected", rejectionReason: "Rejected by broker" });

    logger.info(`Found ${docCount} rejected orders to change to pending...`, {
      module: `script:${this.scriptName}`
    });

    if (!this.options.dryRun) {
      await Order.updateMany({ status: "Rejected", rejectionReason: "Rejected by broker" }, { status: "Pending" });
    }

    logger.info("✅ Finished changing rejected orders to pending!", {
      module: `script:${this.scriptName}`
    });
  }
}

new ChangeRejectedOrdersToPendingRunner().run();
