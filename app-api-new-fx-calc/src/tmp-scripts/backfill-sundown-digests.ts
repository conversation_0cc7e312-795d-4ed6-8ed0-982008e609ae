import <PERSON><PERSON>tRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import StockNewsService from "../external-services/stockNewsService";
import { SundownDigest, SundownDigestDTOInterface } from "../models/SundownDigest";

// The last digest we should backfill is 148, which belongs to January 29th 2024
const ID_OF_LAST_DIGEST = 148;

class BackfillSundownDigestsRunner extends ScriptRunner {
  scriptName = "backfill-sundown-digests";

  async processFn(): Promise<void> {
    logger.info("Backfilling sundown digests...", {
      module: `script:${this.scriptName}`
    });

    let page = 1;
    let foundLastDigest = false;

    while (!foundLastDigest) {
      const digests = await StockNewsService.getSundownDigests(50, page);

      for (let i = 0; i < digests.length; i++) {
        const sundownDigest = digests[i];

        // Break the loop if we've reached our target digest
        if (sundownDigest.id === ID_OF_LAST_DIGEST) {
          foundLastDigest = true;
          break;
        }

        // Check if digest already exists
        const existingSundownDigest = await SundownDigest.findOne({
          "providers.stockNews.id": sundownDigest.id
        });

        if (!existingSundownDigest) {
          const data: SundownDigestDTOInterface = {
            content: BackfillSundownDigestsRunner._formatSundownDigestText(sundownDigest.text),
            date: new Date(sundownDigest.date),
            providers: {
              stockNews: {
                id: sundownDigest.id.toString()
              }
            }
          };

          await new SundownDigest(data).save();
        }
      }

      page++;
    }

    logger.info("✅ Backfilled sundown digests!", {
      module: `script:${this.scriptName}`
    });
  }

  /**
   * Finds all text within parentheses and replaces it with a bold version e.g. (AAPL) -> **(AAPL)**
   * @param text
   * @private
   */
  private static _formatSundownDigestText(text: string): string {
    return text.replace(/\((.*?)\)/g, "**($1)**");
  }
}

new BackfillSundownDigestsRunner().run();
