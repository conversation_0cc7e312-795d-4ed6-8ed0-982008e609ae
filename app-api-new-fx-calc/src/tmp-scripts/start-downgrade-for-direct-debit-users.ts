import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import SubscriptionService from "../services/subscriptionService";
import { Subscription } from "../models/Subscription";

class DowngradeDirectDebitUsersRunner extends ScriptRunner {
  scriptName = "downgrade-direct-debit-users";

  async processFn(): Promise<void> {
    logger.info("Downgrading direct debit users...", {
      module: `script:${this.scriptName}`
    });

    const subscriptions = await Subscription.find({
      category: "DirectDebitSubscription",
      expiration: { $exists: false }
    });

    for (let i = 0; i < subscriptions.length; i++) {
      const subscription = subscriptions[i];

      await SubscriptionService.startDowngrade(subscription, "free_monthly");

      logger.info(`Started downgrade for subscription ${subscription.id}`, {
        module: `script:${this.scriptName}`
      });
    }

    logger.info("✅ Finished downgrading direct debit users!", {
      module: `script:${this.scriptName}`
    });
  }
}

new DowngradeDirectDebitUsersRunner().run();
