import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { KycStatusType, UserDocument } from "../models/User";
import UserService from "../services/userService";
import { EmploymentStatusType } from "../external-services/wealthkernelService";
import { plansConfig } from "@wealthyhood/shared-configs";
import { AssetTransaction } from "../models/Transaction";
import fs from "fs";
import DateUtil from "../utils/dateUtil";
import Decimal from "decimal.js";

const { getPriceConfig, getPlanConfig } = plansConfig;

type UserDataType = {
  email: string;
  fullName: string;
  nationality: string;
  kycStatus: KycStatusType;
  age: string;
  platform: "web" | "ios" | "android";
  plan: string;
  planRecurrence: "monthly" | "yearly" | "lifetime" | "-";
  hasInvested: boolean;
  portfolioCash: string;
  portfolioInvestments: string;
  portfolioSavingsGBP: string;
  // employment info
  sourcesOfWealth: string;
  industry: string;
  annualIncome: string;
  employmentStatus: EmploymentStatusType | "-";
};

export class ExportUserDataScriptRunner extends ScriptRunner {
  scriptName = "export-user-data";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Exporting user data...", {
      module: `script:${this.scriptName}`
    });

    const usersData: UserDataType[] = [];
    let count = 0;

    await UserService.getUsersStreamed({}, ["subscription", "portfolios"]).eachAsync(
      async (user: UserDocument) => {
        const portfolio = user.portfolios?.[0];
        const subscription = user.subscription;
        const assetTransactionExists = await AssetTransaction.exists({ owner: user._id });

        const userData: UserDataType = {
          email: user.email,
          fullName: user.fullName ?? "-",
          platform: user.lastLoginPlatform ?? "web",
          nationality: user.nationalities?.[0] ?? "-",
          kycStatus: user.kycStatus,
          age: user.dateOfBirth ? DateUtil.dateDiffInYears(user.dateOfBirth, new Date()).toString() : "-",
          //subscription
          planRecurrence: "-",
          plan: "-",
          // portfolio
          hasInvested: !!assetTransactionExists,
          portfolioCash: "-",
          portfolioInvestments: "-",
          portfolioSavingsGBP: "-",
          // employment info
          sourcesOfWealth: "-",
          industry: "-",
          annualIncome: "-",
          employmentStatus: "-"
        };

        /**
         * Some users may not have subscription
         */
        if (subscription) {
          userData.planRecurrence = getPriceConfig(user.companyEntity)[subscription.price].recurrence;
          userData.plan = getPlanConfig(user.companyEntity)[subscription.plan].name;
        }

        /**
         * Some users may not have portfolio
         */
        if (portfolio) {
          await portfolio.populate("currentTicker");
          userData.portfolioCash = portfolio.cash?.[portfolio.currency]?.available?.toFixed(2) ?? "-";
          userData.portfolioSavingsGBP =
            Decimal.div(portfolio.savings?.get("mmf_dist_gbp")?.amount ?? 0, 100)?.toFixed(2) ?? "-";
          userData.portfolioInvestments =
            portfolio.currentTicker?.pricePerCurrency?.[portfolio.currency]?.toFixed(2) ?? "-";
        }

        /**
         * Some users have mock data for employment info with annual income amount equal to 1
         */
        if (user.employmentInfo?.annualIncome?.amount > 1) {
          userData.annualIncome = user.employmentInfo?.annualIncome?.amount.toString();
          userData.sourcesOfWealth = user.employmentInfo?.sourcesOfWealth.reduce(
            (acc, source) => `${acc}${source} `,
            ""
          );
          userData.industry = user.employmentInfo?.industry;
          userData.employmentStatus = user.employmentInfo?.employmentStatus;
        }

        usersData.push(userData);

        if (count++ % 1000 === 0) {
          logger.info(`Exported ${count} users...`, {
            module: `script:${this.scriptName}`
          });
        }
      },
      { parallel: 1000 }
    );

    this._saveUsersDataToCSV(usersData);

    logger.info("Finished exporting user data...", {
      module: `script:${this.scriptName}`
    });
  }

  private _saveUsersDataToCSV(users: UserDataType[]): void {
    const header = [
      "email",
      "fullName",
      "nationality",
      "kycStatus",
      "age",
      "platform",
      "plan",
      "planRecurrence",
      "hasInvested",
      "portfolioCash",
      "portfolioInvestments",
      "portfolioSavingsGBP",
      "sourcesOfWealth",
      "industry",
      "annualIncome",
      "employmentStatus"
    ];

    const csvRows = [];

    // Create the header row
    csvRows.push(header.join(","));

    // Add the data rows
    users.forEach((user) => {
      const row = [
        user.email,
        user.fullName,
        user.nationality,
        user.kycStatus,
        user.age,
        user.platform,
        user.plan,
        user.planRecurrence,
        user.hasInvested,
        user.portfolioCash,
        user.portfolioInvestments,
        user.portfolioSavingsGBP,
        user.sourcesOfWealth,
        user.industry, // handle optional fields
        user.annualIncome,
        user.employmentStatus
      ];

      csvRows.push(row.join(","));
    });

    // Combine rows into a single string
    const csvContent = csvRows.join("\n");

    // Write the CSV content to a file
    fs.writeFileSync("users-data.csv", csvContent);
  }
}

new ExportUserDataScriptRunner().run();
