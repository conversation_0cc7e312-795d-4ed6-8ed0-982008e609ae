import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import UserDataRequestService from "../services/userDataRequestService";
import { UserDataRequestReasonEnum } from "../models/UserDataRequest";

const INACTIVE_USER_EMAILS = ["<EMAIL>", "<EMAIL>"];

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

class CloseInactiveUsersScriptRunner extends ScriptRunner {
  scriptName = "close-inactive-users";

  async processFn(): Promise<void> {
    logger.info("Closing inactive users...", {
      module: `script:${this.scriptName}`
    });

    for (let i = 0; i < INACTIVE_USER_EMAILS.length; i++) {
      const email = INACTIVE_USER_EMAILS[i];

      try {
        const user = await User.findOne({ email });

        if (!user) {
          throw new Error(`Could not find user with e-mail ${email}`);
        }

        await UserDataRequestService.createUserDataRequest(
          user,
          "disassociation",
          UserDataRequestReasonEnum.INACTIVE_USER
        );
        if (i % 20 === 0) {
          await delay(5000);
        }
      } catch (err) {
        logger.error(`Could not create user data request for inactive user ${email}`, {
          module: `script:${this.scriptName}`,
          method: "processFn"
        });
      }
    }

    await delay(5000);

    logger.info("Closed inactive users!", {
      module: `script:${this.scriptName}`
    });
  }
}

new CloseInactiveUsersScriptRunner().run();
