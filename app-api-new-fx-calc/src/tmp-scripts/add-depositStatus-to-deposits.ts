import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { DepositCashTransaction, DepositCashTransactionDocument } from "../models/Transaction";
import { AutomationDocument } from "../models/Automation";

class MigrationAddStatustoDepositsScriptRunner extends ScriptRunner {
  scriptName = "add-status-to-deposits-migration";
  private options: { dryRun: boolean };

  private _getDepositStatus(deposit: DepositCashTransactionDocument): string {
    if (deposit.providers?.truelayer) {
      if (!deposit.providers?.truelayer?.status) {
        return "Pending";
      }

      if (deposit.providers?.truelayer.status === "new") {
        return "Cancelled";
      }

      if (
        ["authorization_required", "authorizing", "authorized", "submitted"].includes(
          deposit.providers?.truelayer.status
        )
      ) {
        return "Pending";
      } else if (deposit.providers?.truelayer.status === "failed") {
        if (deposit.providers?.truelayer.failureReason === "canceled") {
          return "Cancelled";
        } else {
          return "Rejected";
        }
      } else if (deposit.providers?.truelayer.status === "cancelled") {
        return "Cancelled";
      } else if (deposit.providers?.truelayer.status === "rejected") {
        return "Rejected";
      } else if (deposit.providers?.truelayer.status === "executed") {
        if (!deposit.providers?.wealthkernel?.status) {
          return "Pending";
        }
        if (["Created", "Active"].includes(deposit.providers?.wealthkernel?.status)) {
          return "Pending";
        } else if (deposit.providers?.wealthkernel?.status === "Cancelled") {
          return "Cancelled";
        } else if (deposit.providers?.wealthkernel?.status === "Rejected") {
          return "Rejected";
        } else if (deposit.providers?.wealthkernel?.status === "Settled") {
          return "Settled";
        }
      }
    } else if (deposit.linkedAutomation) {
      // Deposit is backed by a Wealthkernel direct debit payment
      if (
        !deposit.directDebit?.providers?.wealthkernel?.status ||
        ["Pending", "Collecting", "Collected"].includes(deposit.directDebit?.providers?.wealthkernel?.status)
      ) {
        return "Pending";
      } else if (deposit.directDebit?.providers?.wealthkernel?.status === "Cancelled") {
        return "Cancelled";
      } else if (deposit.directDebit?.providers?.wealthkernel?.status === "Failed") {
        return "Rejected";
      } else if (deposit.directDebit?.providers?.wealthkernel?.status === "Completed") {
        return "Settled";
      }
    }
  }
  async processFn(): Promise<void> {
    logger.info("Migrating deposits to add status field...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will appy!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }
    const deposits = await DepositCashTransaction.find({ status: { $exists: false } }).populate(
      "linkedAutomation"
    );

    logger.info(`About to add depositStatus field to ${deposits.length} deposits`, {
      module: `script:${this.scriptName}`
    });

    for (const deposit of deposits) {
      const depositStatus = this._getDepositStatus(deposit);
      if (this.options.dryRun) {
        logger.info(`Would update Deposit document with id ${deposit.id} to status ${depositStatus}`, {
          module: `script:${this.scriptName}`,
          method: "processFn"
        });
      } else {
        await DepositCashTransaction.updateOne({ _id: deposit._id }, { status: depositStatus });
        logger.info(`Updated status field to ${depositStatus} for ${deposit.id}`, {
          module: `script:${this.scriptName}`
        });
      }
    }

    logger.info("Migrated deposits to add status field...", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationAddStatustoDepositsScriptRunner().run();
