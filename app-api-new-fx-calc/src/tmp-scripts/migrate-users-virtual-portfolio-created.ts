import <PERSON><PERSON>tRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import fs from "fs";
import readline from "readline";
import axios from "axios";

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

class MigrateUsersWithVirtualPortfolioCreatedScriptRunner extends ScriptRunner {
  scriptName = "migrate-users-with-virtual-portfolio-created";

  private options: { testRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating users...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      testRun: process.argv.includes("--test-run")
    };

    let fileName;
    if (this.options.testRun) {
      logger.info(
        "Running in test-run mode, file selected will be users-with-virtual-portfolio-created-status-test-run!",
        {
          module: `script:${this.scriptName}`,
          method: "processFn"
        }
      );

      fileName = "./src/tmp-scripts/users-with-virtual-portfolio-created-status-test-run";
    } else {
      fileName = "./src/tmp-scripts/users-with-virtual-portfolio-created-status";
    }

    const fileStream = fs.createReadStream(fileName);
    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Infinity
    });

    // We are keeping count so that we know when to wait 5 seconds (every 20 identify calls)
    let count = 0;

    for await (const distinctId of rl) {
      await axios({
        method: "POST",
        url: "https://api.mixpanel.com/engage#profile-set",
        data: [
          {
            $token: process.env.MIXPANEL_PROJECT_TOKEN,
            $distinct_id: distinctId,
            $set: {
              status: "Signed Up"
            }
          }
        ]
      });

      count++;

      if (count % 20 === 0) {
        logger.info(`We have migrated ${count} users!`, {
          module: `script:${this.scriptName}`
        });

        await delay(5000);
      }
    }

    logger.info("✅ Finished migrating users!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrateUsersWithVirtualPortfolioCreatedScriptRunner().run();
