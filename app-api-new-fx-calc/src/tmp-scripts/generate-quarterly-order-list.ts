import "./dependencies";
import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Order, OrderDocument } from "../models/Order";
import Decimal from "decimal.js";
import { TransactionDocument } from "../models/Transaction";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import * as fs from "fs";

const { ASSET_CONFIG } = investmentUniverseConfig;

class GenerateQuarterlyOrderListRunner extends ScriptRunner {
  scriptName = "generate-quarterly-order-list";

  async processFn(): Promise<void> {
    logger.info("Generating quarterly order list...", {
      module: `script:${this.scriptName}`
    });

    // 1st April - 30th June
    const periodStart = "2024-4-1";
    const periodEnd = "2024-7-1";

    // Add headers first
    const quarterlyActivityRows: string[] = [
      "Order ID,User ID,Executing Broker,Ticker,ISIN/CUSIP,Order Status,Side (Buy/Sell),Create date (date-time),Update date (date-time),Order Amount,Order Currency,Order Quantity,Order Price,FX Conversion rate,FX Fee (spread),FX Commission,Trade Fee (spread),Trade Commission,FX Buy Currency,FX Sell Currency,FX Sell Amount,FX Buy Amount,Security Trade Amount,Security Trade Amount,Security Trade Price,Security Trade Currency,Security Trade Price Timestamp,FX Trade Timestamp"
    ];

    const orderQuery = {
      "providers.wealthkernel.status": "Matched",
      updatedAt: { $gte: new Date(periodStart), $lte: new Date(periodEnd) }
    };

    const numberOfOrders = await Order.countDocuments(orderQuery);

    await Order.find(orderQuery)
      .populate("transaction")
      .cursor()
      .addCursorFlag("noCursorTimeout", false)
      .eachAsync(async (order: OrderDocument, index: number) => {
        try {
          if (index % 20 === 0) {
            logger.info(`Going through order ${index + 1}/${numberOfOrders}...`, {
              module: `script:${this.scriptName}`
            });
          }

          const transaction = order.transaction as TransactionDocument;
          if (
            ![
              "AssetTransaction",
              "RebalanceTransaction",
              "SavingsTopupTransaction",
              "SavingsWithdrawalTransaction"
            ].includes(transaction.category)
          ) {
            return;
          } else if (order.consideration.amount === 0) {
            return;
          }

          const isStockOrder = order.assetCategory === "stock";

          const orderAmount =
            order.side === "Buy" && order.consideration?.originalAmount
              ? order.consideration.originalAmount
              : order.consideration.amount;

          quarterlyActivityRows.push(
            [
              order.id,
              transaction.owner.toString(),
              "Wealthkernel Ltd",
              order.isInvestmentOrder
                ? ASSET_CONFIG[order.commonId as investmentUniverseConfig.AssetType].formalTicker
                : "-",
              order.isin,
              "Settled",
              order.side,
              order.createdAt.toISOString(),
              order.updatedAt.toISOString(),
              Decimal.div(orderAmount, 100).toNumber(),
              order.consideration.currency,
              order.quantity,
              order.displayUnitPrice?.amount,
              isStockOrder ? new Decimal(order.exchangeRate).toDecimalPlaces(4).toNumber() : undefined,
              isStockOrder ? order.fees?.fx?.amount : undefined
            ].join(",")
          );
        } catch (err) {
          logger.error(`Could not add order ${order.id} in report!`, {
            module: `script:${this.scriptName}`,
            data: {
              error: err
            }
          });
        }
      });

    logger.info("Finished building quarterly order list...", {
      module: `script:${this.scriptName}`
    });

    const writeStream = fs.createWriteStream("./quarterly-activity.csv");

    quarterlyActivityRows.forEach((data) => {
      writeStream.write(data + "\n"); // Write the data and a newline to start the next row
    });

    writeStream.end();

    writeStream.on("finish", () => {
      logger.info("✅ Generated quarterly transaction list!", {
        module: `script:${this.scriptName}`
      });
    });

    writeStream.on("error", (err: Error) => {
      logger.info("Something went wrong!", {
        module: `script:${this.scriptName}`,
        data: {
          error: err
        }
      });
    });
  }
}

new GenerateQuarterlyOrderListRunner().run();
