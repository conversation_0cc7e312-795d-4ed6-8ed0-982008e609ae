import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { DepositCashTransaction } from "../models/Transaction";
import ProviderService, { ProviderScopeEnum } from "../services/providerService";
import { CompanyEntityEnum } from "@wealthyhood/shared-configs/dist/entities";

class AddActiveProvidersToAutomatedDepositsScriptRunner extends ScriptRunner {
  scriptName = "add-active-providers-to-automated-depsoits";

  async processFn(): Promise<void> {
    logger.info("Adding missing active providers to all automated deposits...", {
      module: `script:${this.scriptName}`
    });

    /**
     * It's inteded to add the active providers to all automated deposits,
     * because those that do have activeProviders have also 'truelayer' which is not needed.
     */
    await DepositCashTransaction.updateMany(
      {
        linkedAutomation: { $exists: true }
      },
      {
        activeProviders: ProviderService.getProviders(CompanyEntityEnum.WEALTHYHOOD_UK, [
          ProviderScopeEnum.DIRECT_DEBIT_DEPOSIT_PAYMENTS
        ])
      }
    );

    logger.info("Finished adding missing active providers to all automated deposits!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddActiveProvidersToAutomatedDepositsScriptRunner().run();
