import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Reward } from "../models/Reward";

class MigrationAddStatusToRewardsScriptRunner extends ScriptRunner {
  scriptName = "add-status-to-rewards-migration";

  async processFn(): Promise<void> {
    logger.info("Migrating rewards to add status field...", {
      module: `script:${this.scriptName}`
    });

    const numberOfDocs = await Reward.countDocuments({});

    logger.info(`Going to update status field for ${numberOfDocs} rewards...`, {
      module: `script:${this.scriptName}`
    });

    await Reward.updateMany(
      {}, // This empty filter will match all documents
      [
        {
          $set: {
            status: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ["$deposit.providers.wealthkernel.status", "Settled"] },
                    { $eq: ["$order.providers.wealthkernel.status", "Matched"] }
                  ]
                },
                then: "Settled",
                else: "Pending"
              }
            }
          }
        }
      ]
    );

    logger.info("Finished migrating rewards to add status field!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationAddStatusToRewardsScriptRunner().run();
