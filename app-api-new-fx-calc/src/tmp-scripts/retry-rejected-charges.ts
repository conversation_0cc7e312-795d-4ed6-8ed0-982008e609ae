import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { ChargeTransaction } from "../models/Transaction";

const REJECTED_CHARGES = [
  "chg-36io3mh3n242qc",
  "chg-36io3mmr3242qc",
  "chg-36io3mrx6242qc",
  "chg-36io3msak242qc",
  "chg-36io3ng7k242qc",
  "chg-36io3ngey242qc",
  "chg-36io3nglb242qc",
  "chg-36io3ngty242qc",
  "chg-36io3nily242qc",
  "chg-36io3nire242qc",
  "chg-36io3nlkj242qc",
  "chg-36io3nlqy242qc",
  "chg-36io3nlwz242qc",
  "chg-36io3nm3j242qc",
  "chg-36io3nmb3242qc",
  "chg-36io3nmk4242qc",
  "chg-36io3nmrx242qc",
  "chg-36io3nzmq242qc",
  "chg-36io3o26i242qc",
  "chg-36io3o2ei242qc",
  "chg-36io3objy242qc",
  "chg-36io3obot242qc",
  "chg-36io3ogbt242qc",
  "chg-36io3ogl3242qc",
  "chg-36io3otxb242qc",
  "chg-36io3ou2h242qc",
  "chg-36io3ou7t242qc",
  "chg-36io3oufh242qc",
  "chg-36io3owa5242qc",
  "chg-36io3owew242qc",
  "chg-36io3ozcr242qc",
  "chg-36io3ozhf242qc",
  "chg-36io3ozkr242qc",
  "chg-36io3ozop242qc",
  "chg-36io3ozsf242qc",
  "chg-36io3ozyi242qc",
  "chg-36io3p27k242qc",
  "chg-36io3pc5v242qc",
  "chg-36io3pcl7242qc",
  "chg-36io3pcp4242qc",
  "chg-36io3pj72242qc",
  "chg-36io3pjc6242qc",
  "chg-36io3pplg242qc",
  "chg-36io3pvd5242qc",
  "chg-36io3pvgy242qc",
  "chg-36io3pzt5242qc",
  "chg-36io3pzwu242qc",
  "chg-36io3qfje242qc",
  "chg-36io3qfs7242qc",
  "chg-36io3qico242qc",
  "chg-36io3qkca242qc",
  "chg-36io3qrmp242qc",
  "chg-36io3qrpr242qc",
  "chg-36io3qsgt242qc",
  "chg-36io3qszt242qc",
  "chg-36io3qtc6242qc",
  "chg-36iqnytov242qc",
  "chg-36iqnyy24242qc",
  "chg-36iqnyyzc242qc",
  "chg-36iqnyzcn242qc",
  "chg-36iqnz5fr242qc",
  "chg-36iqnz6iw242qc",
  "chg-36iqnzfj5242qc",
  "chg-36itaet2t242qc",
  "chg-36itaevlz242qc",
  "chg-36itaevq2242qc",
  "chg-36itaexbx242qc",
  "chg-36itaeyba242qc",
  "chg-36itaez5l242qc",
  "chg-36itaf2xe242qc",
  "chg-36itaf32v242qc",
  "chg-36itaf44d242qc",
  "chg-36itaf52d242qc",
  "chg-36itaf6md242qc",
  "chg-36itaf7py242qc",
  "chg-36itafb45242qc",
  "chg-36itafbdn242qc",
  "chg-36itafcqj242qc",
  "chg-36itafdce242qc",
  "chg-36itafdnd242qc",
  "chg-36j2xkran242qc",
  "chg-36j2xkrh4242qc"
];

class RetryRejectedChargesScriptRunner extends ScriptRunner {
  scriptName = "retry-rejected-charges";

  async processFn(): Promise<void> {
    logger.info("Retrying rejected charges...", {
      module: `script:${this.scriptName}`
    });

    for (let i = 0; i < REJECTED_CHARGES.length; i++) {
      const wkChargeID = REJECTED_CHARGES[i];

      logger.info(`Retrying charge ${wkChargeID}...`, {
        module: `script:${this.scriptName}`
      });

      await ChargeTransaction.findOneAndUpdate(
        { "providers.wealthkernel.id": wkChargeID, status: "Rejected" },
        { status: "Pending", $unset: { providers: 1 } }
      );
    }

    logger.info("Finished retrying rejected charges!", {
      module: `script:${this.scriptName}`
    });
  }
}

new RetryRejectedChargesScriptRunner().run();
