import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { DailyPortfolioTicker } from "../models/DailyTicker";

class MigrationDailyPortfolioTickerScriptRunner extends ScriptRunner {
  scriptName = "migrate-daily-portfolio-tickers";

  async processFn(): Promise<void> {
    logger.info("Migrating daily portfolio tickers...", {
      module: `script:${this.scriptName}`
    });

    const numberOfDocs = await DailyPortfolioTicker.countDocuments({
      pricePerCurrency: {
        $exists: false
      }
    });

    logger.info(`Going to migrate ${numberOfDocs}...`, {
      module: `script:${this.scriptName}`
    });

    await DailyPortfolioTicker.updateMany(
      //  In case script fails for memory issues
      {
        pricePerCurrency: {
          $exists: false
        }
      },
      [
        {
          $set: {
            pricePerCurrency: { GBP: "$price" },
            openingPricePerCurrency: { GBP: "$openingPrice" },
            closingPricePerCurrency: { GBP: "$closingPrice" }
          }
        }
      ]
    );

    logger.info("Finished migrating daily portfolio tickers!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationDailyPortfolioTickerScriptRunner().run();
