import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Portfolio } from "../models/Portfolio";
import { envIsDev } from "../utils/environmentUtil";

export class MigrateLegacyAssetClassesScriptRunner extends ScriptRunner {
  scriptName = "migrate-legacy-asset-classes";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating portfolios with legacy asset classes...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    const documentCount = await Portfolio.countDocuments({
      $or: [
        { "personalisationPreferences.assetClasses": "governmentBonds" },
        { "personalisationPreferences.assetClasses": "corporateBonds" }
      ]
    });

    logger.info(`About to migrate ${documentCount} portfolios`, {
      module: `script:${this.scriptName}`
    });

    if (!this.options.dryRun) {
      // 1. Add `bonds` asset class to portfolios that have `governmentBonds` or `corporateBonds`
      await Portfolio.updateMany(
        {
          $or: [
            { "personalisationPreferences.assetClasses": "governmentBonds" },
            { "personalisationPreferences.assetClasses": "corporateBonds" }
          ]
        },
        {
          $addToSet: {
            "personalisationPreferences.assetClasses": "bonds"
          }
        }
      );

      // 2. Remove `governmentBonds` and `corporateBonds` asset classes from portfolios
      await Portfolio.updateMany(
        {
          $or: [
            { "personalisationPreferences.assetClasses": "governmentBonds" },
            { "personalisationPreferences.assetClasses": "corporateBonds" }
          ]
        },
        {
          $pullAll: {
            "personalisationPreferences.assetClasses": ["governmentBonds", "corporateBonds"]
          }
        }
      );
    }

    logger.info("Migrated portfolios with legacy asset classes...", {
      module: `script:${this.scriptName}`
    });
  }
}

if (!envIsDev()) new MigrateLegacyAssetClassesScriptRunner().run();
