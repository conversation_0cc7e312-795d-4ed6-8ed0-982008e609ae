import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { ChargeTransaction, ChargeTransactionDTOInterface } from "../models/Transaction";
import { TransactionService } from "../services/transactionService";
import mongoose from "mongoose";

const PENDING_CHARGES = [
  "654d098a6ae1d4f639e34ad9",
  "654d09896ae1d4f639e34ac6",
  "654cfed03ba0c74b4426aefd",
  "654cfb5a2c91428088471dca",
  "654cfb5a2c91428088471dc5",
  "654cf7e2b9a36e8a25993eb4",
  "654c91db885deab99bb6010b",
  "654a5c183c0f9498d03ed9c6",
  "654a5c173c0f9498d03ed9bc",
  "654a5c173c0f9498d03ed9a3",
  "654a58f31efd355f3362e089",
  "654a58f31efd355f3362e075",
  "6549073ee7d8047bd7b9047a",
  "6549037c5e165e6bea833b28",
  "6549073fe7d8047bd7b904a7",
  "654520912de46560d3b69d2c",
  "654a5c173c0f9498d03ed9b7",
  "654288462ba41dd99f67f7bd",
  "654a5c173c0f9498d03ed9c1",
  "654288402ba41dd99f67f739",
  "654b78845cc0781c90663af3",
  "6545209a2de46560d3b69d99",
  "6549074ce7d8047bd7b90506",
  "654d097b6ae1d4f639e34aa6",
  "654d097b6ae1d4f639e34a8e",
  "654cfec33ba0c74b4426aeef",
  "654cfb4a2c91428088471d64",
  "654cfb4a2c91428088471d5f",
  "654cf7d5b9a36e8a25993e95",
  "654c91ce885deab99bb60104",
  "654bb52ba8a0e00bb4879fa5",
  "654ba664b837e991cbc3991c",
  "654a5c063c0f9498d03ed96c",
  "654a5c063c0f9498d03ed962",
  "654a5c053c0f9498d03ed93f",
  "654a54f2935e87ed8ae14aee",
  "6549072ce7d8047bd7b903c7",
  "654a58e11efd355f3362dfe2",
  "654a5c053c0f9498d03ed94e",
  "6549072ce7d8047bd7b903c2",
  "654a58e11efd355f3362dfce",
  "6549072ce7d8047bd7b903bd",
  "6549036e5e165e6bea833acd",
  "6549036f5e165e6bea833ad2",
  "654520832de46560d3b69ca4",
  "6549072de7d8047bd7b90403",
  "654a5c053c0f9498d03ed944",
  "6549072be7d8047bd7b903b3",
  "654a5c063c0f9498d03ed95d",
  "6542882e2ba41dd99f67f695",
  "654a5c063c0f9498d03ed967",
  "6542882a2ba41dd99f67f611",
  "654e4cf43efa4d39e9843a3b",
  "654e493d7f32296b641f63ea",
  "654d09686ae1d4f639e34a5b",
  "654cfeb13ba0c74b4426aee1",
  "654cfb362c91428088471cf9",
  "654cfb362c91428088471cf4",
  "654cf7c5b9a36e8a25993e76",
  "654c91be885deab99bb600fd",
  "654bb517a8a0e00bb4879f66",
  "654ba653b837e991cbc39905",
  "654a5bf13c0f9498d03ed8fe",
  "654a5bf13c0f9498d03ed8f9",
  "654a5bf03c0f9498d03ed8d6",
  "654a54e1935e87ed8ae14abf",
  "65490710e7d8047bd7b90300",
  "654a58cc1efd355f3362df3b",
  "654a5bf03c0f9498d03ed8e5",
  "65490710e7d8047bd7b902fb",
  "654a58cb1efd355f3362df27",
  "65490710e7d8047bd7b902f6",
  "6549035e5e165e6bea833a7c",
  "6549035e5e165e6bea833a77",
  "654520732de46560d3b69c14",
  "65490712e7d8047bd7b9033c",
  "654a5bf03c0f9498d03ed8db",
  "65490710e7d8047bd7b902ec",
  "654a5bf13c0f9498d03ed8f4",
  "654288132ba41dd99f67f56d",
  "6542880d2ba41dd99f67f4e9",
  "6542768a56ed0bdc4a571bc4"
];

class CreateNewChargeTransactionDocumentsScriptRunner extends ScriptRunner {
  scriptName = "create-new-charge-transaction-documents";

  async processFn(): Promise<void> {
    logger.info("Creating new transaction documents for re run charge transactions...", {
      module: `script:${this.scriptName}`
    });

    for (let i = 0; i < PENDING_CHARGES.length; i++) {
      const transactionId = PENDING_CHARGES[i];
      logger.info(
        `Searching for charge transaction document with id ${transactionId} for re run charge transactions...`,
        {
          module: `script:${this.scriptName}`
        }
      );
      try {
        const chargeTransaction = await ChargeTransaction.findById(transactionId);
        if (!chargeTransaction) {
          logger.info(`Charge transaction document with ID ${transactionId} not found`, {
            module: `script:${this.scriptName}`
          });
          continue;
        }

        logger.info(
          `Found charge transaction document with id ${chargeTransaction._id} and about to create new charge transaction document...`,
          {
            module: `script:${this.scriptName}`
          }
        );

        const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt"> = {
          consideration: chargeTransaction.consideration,
          status: chargeTransaction.status,
          chargeType: chargeTransaction.chargeType,
          chargeMethod: chargeTransaction.chargeMethod,
          originalChargeAmount: chargeTransaction.originalChargeAmount,
          activeProviders: chargeTransaction.activeProviders,
          linkedTransaction: chargeTransaction.linkedTransaction as mongoose.Types.ObjectId,
          owner: chargeTransaction.owner as mongoose.Types.ObjectId,
          portfolio: chargeTransaction.portfolio
        };

        const newChargeTransaction = await TransactionService.createChargeTransaction(transactionData);

        logger.info(`Created new charge transaction document ${newChargeTransaction._id}`, {
          module: `script:${this.scriptName}`
        });

        await ChargeTransaction.findByIdAndDelete(transactionId);

        logger.info(
          `Deleted old charge transaction document ${chargeTransaction._id} from owner: ${chargeTransaction.owner}`,
          {
            module: `script:${this.scriptName}`
          }
        );
      } catch (error) {
        logger.error(`Error occurred: ${error.message}`, {
          module: `script:${this.scriptName}`
        });
      }
    }

    logger.info("Finished creating new documents to update the id field!", {
      module: `script:${this.scriptName}`
    });
  }
}

new CreateNewChargeTransactionDocumentsScriptRunner().run();
