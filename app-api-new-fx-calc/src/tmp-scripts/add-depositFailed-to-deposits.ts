import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { AssetTransaction, CashbackTransaction } from "../models/Transaction";
import { DepositCashTransaction } from "../models/Transaction";

class MigrationAddStatusToRewardsScriptRunner extends ScriptRunner {
  scriptName = "add-depositfailed-to-deposits-migration";

  async processFn(): Promise<void> {
    logger.info("Migrating deposits to add status field...", {
      module: `script:${this.scriptName}`
    });

    // Migrate TrueLayer deposits
    logger.info("Migrating TrueLayer deposits...", {
      module: `script:${this.scriptName}`
    });
    const depositsToInvestigate = await DepositCashTransaction.find({
      "providers.truelayer.status": { $in: ["failed", "rejected", "cancelled"] }
    });
    logger.info(`Gonna investigate ${depositsToInvestigate.length} deposits...`, {
      module: `script:${this.scriptName}`
    });
    // Assuming depositsToInvestigate is an array of deposit documents
    const depositIds = depositsToInvestigate.map((deposit) => deposit._id);

    // Update asset transactions
    const assetUpdateResult = await AssetTransaction.updateMany(
      {
        pendingDeposit: { $in: depositIds },
        status: "PendingDeposit"
      },
      {
        $set: { status: "DepositFailed" }
      }
    );

    logger.info(`Updated ${assetUpdateResult.modifiedCount} assetTransactions to DepositFailed`, {
      module: `script:${this.scriptName}`
    });

    const assetTransactions = await AssetTransaction.find({
      pendingDeposit: { $in: depositIds },
      status: "DepositFailed" // Adjusted to match the new status after the update
    }).populate("orders cashback");

    // Determine which asset transactions meet your conditions
    const eligibleAssetTransactions = assetTransactions.filter((at) => {
      return (
        at.portfolioTransactionCategory === "buy" ||
        (at.portfolioTransactionCategory === "update" && at.orders[0].side === "Buy")
      );
    });

    // Extract cashback IDs to cancel
    const cashbackIdsToCancel = eligibleAssetTransactions.filter((at) => at.cashback).map((at) => at.cashback.id);

    // Then, update cashback transactions based on extracted IDs
    if (cashbackIdsToCancel.length > 0) {
      const cashbackUpdateResult = await CashbackTransaction.updateMany(
        { _id: { $in: cashbackIdsToCancel } },
        { $set: { status: "Rejected" } }
      );

      logger.info(`Rejected ${cashbackUpdateResult.modifiedCount} cashbacks`, {
        module: `script:${this.scriptName}`
      });
    }

    // Migrate WK deposits
    logger.info("Migrating WK deposits...", {
      module: `script:${this.scriptName}`
    });
    const directDebitTransactionsPendingOnFailedDeposits = await AssetTransaction.aggregate()
      .match({
        status: "PendingDeposit",
        linkedAutomation: { $exists: true }
      })
      .lookup({
        from: "transactions",
        localField: "pendingDeposit",
        foreignField: "_id",
        as: "lookedUpPendingDeposit"
      })
      .match({
        "lookedUpPendingDeposit.directDebit.providers.wealthkernel.status": { $in: ["Cancelled", "Failed"] }
      });

    await AssetTransaction.populate(directDebitTransactionsPendingOnFailedDeposits, {
      path: "owner pendingDeposit portfolio cashback"
    });

    logger.info(
      `Gonna update ${directDebitTransactionsPendingOnFailedDeposits.length} assetTransactions with direct debit`,
      {
        module: `script:${this.scriptName}`
      }
    );
    let cashbackCount = 0;
    for (const transaction of directDebitTransactionsPendingOnFailedDeposits) {
      const linkedAssetTransaction = await AssetTransaction.findByIdAndUpdate(transaction._id, {
        status: "DepositFailed"
      });

      if (!linkedAssetTransaction.populated("orders")) {
        await linkedAssetTransaction.populate("orders");
      }

      const transactionIsAssetBuy =
        linkedAssetTransaction.portfolioTransactionCategory === "update" &&
        linkedAssetTransaction.orders[0].side === "Buy";
      const transactionIsPortfolioBuy = linkedAssetTransaction.portfolioTransactionCategory === "buy";
      if (transactionIsPortfolioBuy || transactionIsAssetBuy) {
        if (!linkedAssetTransaction.populated("cashback")) {
          await linkedAssetTransaction.populate("cashback");
        }

        if (linkedAssetTransaction.cashback) {
          cashbackCount++;
          await CashbackTransaction.findByIdAndUpdate(linkedAssetTransaction.cashback.id, {
            status: "Rejected"
          });
        }
      }
    }

    logger.info(`Rejected ${cashbackCount} cashbacks that were connected to direct debit`, {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationAddStatusToRewardsScriptRunner().run();
