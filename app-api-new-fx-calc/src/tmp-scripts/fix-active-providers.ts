import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { User } from "../models/User";
import { Mandate } from "../models/Mandate";
import ProviderService, { ProviderScopeEnum } from "../services/providerService";

export class FixActiveProvidersScriptRunner extends ScriptRunner {
  scriptName = "fix-active-providers";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Fix active providers with wrong format...", {
      module: `script:${this.scriptName}`
    });

    await User.updateMany(
      {
        "activeProviders.0.$pull": "gocardless"
      },
      {
        activeProviders: ProviderService.getProviders(entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK, [
          ProviderScopeEnum.BROKERAGE,
          ProviderScopeEnum.BANK_ACCOUNTS,
          ProviderScopeEnum.CARD_SUBSCRIPTION_PAYMENTS,
          ProviderScopeEnum.KYC
        ])
      }
    );
    await Mandate.updateMany(
      { "activeProviders.0.$pull": "gocardless" },
      {
        activeProviders: ProviderService.getProviders(entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK, [
          ProviderScopeEnum.DIRECT_DEBIT_MANDATES
        ])
      }
    );

    logger.info("Fixed active providers with wrong format...", {
      module: `script:${this.scriptName}`
    });
  }
}

new FixActiveProvidersScriptRunner().run();
