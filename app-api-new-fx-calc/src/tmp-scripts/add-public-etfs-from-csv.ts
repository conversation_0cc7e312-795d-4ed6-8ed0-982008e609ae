import * as fs from "fs";
import { writeFile } from "fs/promises";

// eslint-disable-next-line @typescript-eslint/no-var-requires
require("dotenv").config({ path: "dev.env" });

type ETFCSVHeaderType =
  | "Ticker"
  | "Exchange"
  | "Provider"
  | "Logo"
  | "Base Currency"
  | "Traded Currency"
  | "Income"
  | "Code"
  | "Provider Code";

const CSV_HEADER_TO_KEY_MAPPINGS: Record<ETFCSVHeaderType, string> = {
  Code: "keyName",
  Ticker: "formalTicker",
  Exchange: "formalExchange",
  Provider: "provider",
  Logo: "providerLogo",
  "Provider Code": "providerKey",
  "Base Currency": "baseCurrency",
  "Traded Currency": "tradedCurrency",
  Income: "income"
};

/**
 * When we add new **public** ETFs, we can run this script to retrieve the config that will be added in our public universe.
 */
async function printETFsFromCSV(): Promise<void> {
  const FILE_NAME = "etfs.csv";

  const csv = fs.readFileSync(`./src/tmp-scripts/${FILE_NAME}`, { encoding: "utf8" });
  const lines = csv.split("\n");
  const headers = lines[0].split(",");

  const result = [];
  for (let i = 1; i < lines.length; i++) {
    const config: any = {};
    const currentLine = lines[i].split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);

    console.log(`Processing line ${currentLine}`);

    for (let j = 0; j < headers.length; j++) {
      config[CSV_HEADER_TO_KEY_MAPPINGS[headers[j].trim() as ETFCSVHeaderType]] = currentLine[j]
        .trim()
        .replace(/"+/g, "");
    }

    config.category = "etf";

    result.push(config);
  }

  const items = JSON.stringify(
    Object.fromEntries(
      result.map((config) => {
        const { keyName, ...configWithoutKeyname } = config;
        return [keyName, configWithoutKeyname];
      })
    )
  );

  // The content of these two files go in the shared-configs project.
  await writeFile("assetConfig.json", items, "utf8");
  await writeFile("assets.json", JSON.stringify(result.map((config) => config.keyName)), "utf8");
}

(async () => {
  try {
    await printETFsFromCSV();
    console.log("success");
    process.exit(1);
  } catch (err) {
    console.log("error");
    console.log(err);
  }
})();
