import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { ContentEntry } from "../models/ContentEntry";

class ContentEntryAddPublishAtScriptRunner extends ScriptRunner {
  scriptName = "content-entry-add-publish-at";

  async processFn(): Promise<void> {
    logger.info("Migrating content entries to copy finimize publishedAt to publishAt...", {
      module: `script:${this.scriptName}`
    });

    // Update all content entries that have finimize publishedAt but no publishAt
    const updateResult = await ContentEntry.updateMany(
      {
        "providers.finimize.publishedAt": { $exists: true },
        publishAt: { $exists: false }
      },
      [
        {
          $set: {
            publishAt: "$providers.finimize.publishedAt"
          }
        }
      ]
    );

    logger.info(`Updated ${updateResult.modifiedCount} content entries`, {
      module: `script:${this.scriptName}`,
      data: {
        modifiedCount: updateResult.modifiedCount,
        matchedCount: updateResult.matchedCount
      }
    });
  }
}

new ContentEntryAddPublishAtScriptRunner().run();
