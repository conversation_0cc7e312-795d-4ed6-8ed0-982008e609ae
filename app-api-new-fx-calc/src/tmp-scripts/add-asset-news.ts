import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import InvestmentProductService from "../services/investmentProductService";
import AssetNewsService from "../services/assetNewsService";
import { captureException } from "@sentry/node";

class AssetNews extends ScriptRunner {
  scriptName = "add-asset-news";

  async processFn(): Promise<void> {
    logger.info("Adding 100 latest asset news for each investment product...", {
      module: `script:${this.scriptName}`
    });

    const investmentProducts = await InvestmentProductService.getInvestmentProducts({
      populateTicker: false,
      useCache: false,
      listedOnly: true
    });

    for (let index = 0; index < investmentProducts.length; index++) {
      const investmentProduct = investmentProducts[index];

      logger.info(`Fetching asset news for investment ${investmentProduct.commonId}...`, {
        module: `script:${this.scriptName}`
      });

      try {
        await AssetNewsService.retrieveAndStoreAssetNews(investmentProduct, 100);
      } catch (err) {
        captureException(err);
        logger.error(`Failed while retrieving and storing asset news for asset ${investmentProduct.commonId}`, {
          module: "AssetNewsService",
          method: "createAssetNews",
          data: {
            err: JSON.stringify(err)
          }
        });
      }
    }
    logger.info("✅ Added asset news to investment products!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AssetNews().run();
