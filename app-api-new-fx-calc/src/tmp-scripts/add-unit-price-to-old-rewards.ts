import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Reward } from "../models/Reward";

class AddUnitPriceToOldRewardsScriptRunner extends ScriptRunner {
  scriptName = "add-unit-price-to-old-rewards";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating rewards to have unit price...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    const numberOfDocs = await Reward.countDocuments({
      $and: [
        { "consideration.amount": { $exists: true } },
        { "consideration.amount": { $gt: 0 } },
        { quantity: { $exists: true } },
        { quantity: { $gt: 0 } }
      ],
      status: "Settled",
      unitPrice: { $exists: false }
    });

    logger.info(`Found ${numberOfDocs} rewards to update...`, {
      module: `script:${this.scriptName}`
    });

    if (!this.options.dryRun) {
      logger.info(`Going to add unit price field for ${numberOfDocs} rewards...`, {
        module: `script:${this.scriptName}`
      });

      await Reward.updateMany(
        {
          $and: [
            { "consideration.amount": { $exists: true } },
            { "consideration.amount": { $gt: 0 } },
            { quantity: { $exists: true } },
            { quantity: { $gt: 0 } }
          ],
          status: "Settled",
          unitPrice: { $exists: false }
        },
        [
          {
            $set: {
              unitPrice: {
                amount: { $round: [{ $divide: [{ $divide: ["$consideration.amount", "$quantity"] }, 100] }, 2] },
                currency: "GBP"
              }
            }
          }
        ]
      );
    } else {
      logger.info(`Not adding unit price field for ${numberOfDocs} rewards since we're in dry run...`, {
        module: `script:${this.scriptName}`
      });
    }

    logger.info("Finished migrating rewards!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddUnitPriceToOldRewardsScriptRunner().run();
