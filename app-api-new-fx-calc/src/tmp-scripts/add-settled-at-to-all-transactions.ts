import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import {
  AssetDividendTransaction,
  DepositCashTransaction,
  DividendTransaction,
  RevertRewardTransaction,
  WithdrawalCashTransaction
} from "../models/Transaction";

class AddSettledAtFieldRunner extends ScriptRunner {
  scriptName = "add-settled-at-to-all-transactions";

  async processFn(): Promise<void> {
    logger.info("Updating all settled transactions to have settled at...", {
      module: `script:${this.scriptName}`
    });

    await Promise.all([
      WithdrawalCashTransaction.updateMany(
        { "providers.wealthkernel.status": "Settled", settledAt: { $exists: false } },
        [
          {
            $set: {
              settledAt: "$createdAt"
            }
          }
        ]
      ),
      AssetDividendTransaction.updateMany(
        { "providers.wealthkernel.status": "Settled", settledAt: { $exists: false } },
        [
          {
            $set: {
              settledAt: "$createdAt"
            }
          }
        ]
      ),
      DividendTransaction.updateMany(
        { "providers.wealthkernel.status": "Settled", settledAt: { $exists: false } },
        [
          {
            $set: {
              settledAt: "$createdAt"
            }
          }
        ]
      ),
      DepositCashTransaction.updateMany({ status: "Settled", settledAt: { $exists: false } }, [
        {
          $set: {
            settledAt: "$createdAt"
          }
        }
      ]),
      RevertRewardTransaction.updateMany({ status: "Settled", settledAt: { $exists: false } }, [
        {
          $set: {
            settledAt: "$createdAt"
          }
        }
      ])
    ]);

    logger.info("✅ Finished updating all settled transactions to have settled at!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddSettledAtFieldRunner().run();
