import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Order } from "../models/Order";

class MigrationAddStatusToOrdersScriptRunner extends ScriptRunner {
  scriptName = "add-status-to-orders";

  async processFn(): Promise<void> {
    logger.info("Migrating orders to add status field...", {
      module: `script:${this.scriptName}`
    });

    const numberOfDocs = await Order.countDocuments({});

    logger.info(`Going to update status field for ${numberOfDocs} orders...`, {
      module: `script:${this.scriptName}`
    });

    await Order.updateMany(
      {}, // This empty filter will match all documents
      [
        {
          $set: {
            status: {
              $cond: {
                if: { $eq: ["$providers.wealthkernel.status", "Matched"] },
                then: "Matched",
                else: {
                  $cond: {
                    if: { $eq: ["$providers.wealthkernel.status", "Rejected"] },
                    then: "Rejected",
                    else: {
                      $cond: {
                        if: { $eq: ["$providers.wealthkernel.status", "Cancelled"] },
                        then: "Cancelled",
                        else: "Pending"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      ]
    );

    logger.info("Finished migrating orders to add status field!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationAddStatusToOrdersScriptRunner().run();
