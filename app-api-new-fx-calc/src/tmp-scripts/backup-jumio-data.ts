import ScriptRunner from "../jobs/services/scriptRunner";
import { JumioService } from "../external-services/jumioService";
import { User, UserDocument } from "../models/User";
import { KycOperationDocument } from "../models/KycOperation";
import axios from "axios";
import CloudflareService, { BucketsEnum, ContentTypeEnum } from "../external-services/cloudflareService";
import { Readable } from "stream";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";

class BackupJumioDataScriptRunner extends ScriptRunner {
  scriptName = "backup-jumio-data";

  async processFn(): Promise<void> {
    await User.find({ residencyCountry: { $exists: true } })
      .populate("kycOperation")
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(async (user: UserDocument) => {
        try {
          const kycOperation = user.kycOperation as KycOperationDocument;

          if (kycOperation?.isJourneyCompleted) {
            const { presignedUrl } = await JumioService.Instance.generateTransactionPDF(
              user.providers.jumio.id,
              kycOperation.providers.jumio.id
            );

            // Fetch PDF data from presigned URL
            const response = await axios.get(presignedUrl, {
              responseType: "stream"
            });

            // Upload the PDF to Cloudflare
            await CloudflareService.Instance.uploadObject(
              BucketsEnum.JUMIO_DATA,
              `${user.id}/data.pdf`,
              response.data as Readable,
              { contentType: ContentTypeEnum.APPLICATION_PDF }
            );
          }
        } catch (err) {
          logger.error(`Storing Jumio data for user ${user.id} failed`, {
            module: "BackupJumioDataScriptRunner",
            method: "processFn"
          });
          captureException(err);
        }
      });
  }
}

new BackupJumioDataScriptRunner().run();
