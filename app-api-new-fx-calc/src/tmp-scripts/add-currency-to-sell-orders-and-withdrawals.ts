import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Order } from "../models/Order";
import { WithdrawalCashTransaction } from "../models/Transaction";

class MigrationAddCurrencyScriptRunner extends ScriptRunner {
  scriptName = "add-currency-to-sell-orders-and-withdrawals.ts";

  async processFn(): Promise<void> {
    logger.info("Migrating orders to add consideration.currency field...", {
      module: `script:${this.scriptName}`
    });

    await Order.updateMany(
      { consideration: null },
      {
        consideration: {
          currency: "GBP"
        }
      }
    );
    await Order.updateMany(
      { "consideration.currency": { $exists: false } },
      {
        "consideration.currency": "GBP"
      }
    );

    logger.info("Finished migrating orders to add consideration.currency field!", {
      module: `script:${this.scriptName}`
    });

    logger.info("Migrating transactions to add consideration.currency field...", {
      module: `script:${this.scriptName}`
    });

    await <PERSON>drawalCashTransaction.updateMany(
      { "consideration.currency": { $exists: false } },
      {
        "consideration.currency": "GBP"
      }
    );

    logger.info("Finished migrating transactions to add consideration.currency field!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationAddCurrencyScriptRunner().run();
