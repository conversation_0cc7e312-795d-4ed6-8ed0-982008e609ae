import { DailySavingsProductTickerDocument } from "./../models/DailyTicker";
import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { DailyPortfolioSavingsTicker, DailySavingsProductTicker } from "../models/DailyTicker";
import DateUtil from "../utils/dateUtil";
import Decimal from "decimal.js";

const startDate = new Date("2024-06-01T00:00:00.000Z");
const endDate = new Date("2024-06-28T23:59:59.999Z");

class MigrateDailyAccrualsScriptRunner extends ScriptRunner {
  scriptName = "migrate-daily-accruals";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating daily accruals for June - applying correct fee formula...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will appy!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    const portfolioSavingsTickers = await DailyPortfolioSavingsTicker.find({
      date: { $gte: startDate, $lte: endDate }
    });
    const savingsProductTickersTickers = await DailySavingsProductTicker.find({
      date: { $gte: startDate, $lte: endDate }
    });

    const savingsProductTickerByDate = savingsProductTickersTickers.reduce(
      (acc, ticker) => {
        acc[DateUtil.formatDateToDDMONYYYY(ticker.date)] = ticker;

        return acc;
      },
      {} as Record<string, DailySavingsProductTickerDocument>
    );

    for (const portfolioSavingsTicker of portfolioSavingsTickers) {
      const date = DateUtil.formatDateToDDMONYYYY(portfolioSavingsTicker.date);
      const savingsProductTicker = savingsProductTickerByDate[date];

      if (!savingsProductTicker) {
        logger.error(
          `Could not find savings product ticker for date ${date} for portfolio savings ticker ${portfolioSavingsTicker.id}`,
          {
            module: `script:${this.scriptName}`
          }
        );

        continue;
      }

      const { holdingAmount, dailyAccrual, planFee } = portfolioSavingsTicker;
      const oneDayFee = new Decimal(planFee).div(365);
      const dailyDistributionFactorAfterWealthyhoodFee = new Decimal(savingsProductTicker.dailyDistributionFactor)
        .sub(oneDayFee)
        .toDP(9);

      const dailyAccrualWithNewFeeCalculation = Decimal.mul(
        dailyDistributionFactorAfterWealthyhoodFee,
        holdingAmount
      )
        .toDP(9)
        .toNumber();

      logger.info(
        `Would apply daily accrual of ${dailyAccrualWithNewFeeCalculation} to portfolio savings ticker ${portfolioSavingsTicker.id}`,
        {
          module: `script:${this.scriptName}`,
          data: {
            dailyAccrual,
            dailyAccrualWithNewFeeCalculation: dailyAccrualWithNewFeeCalculation,
            oneDayFee: oneDayFee.toNumber(),
            dailyDistributionFactorAfterWealthyhoodFee: dailyDistributionFactorAfterWealthyhoodFee.toNumber(),
            holdingAmount,
            dailyDistributionFactor: savingsProductTicker.dailyDistributionFactor
          }
        }
      );
      if (!this.options.dryRun) {
        portfolioSavingsTicker.dailyAccrual = dailyAccrualWithNewFeeCalculation;
        await portfolioSavingsTicker.save();
      }
    }

    logger.info("✅ Finished migrating daily accruals for June", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrateDailyAccrualsScriptRunner().run();
