import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { DailyPortfolioSavingsTicker, DailySavingsProductTicker } from "../models/DailyTicker";

const DATES_TO_DELETE = [
  new Date("2024-05-06T12:00:00.000+00:00"), // Early May bank holiday
  new Date("2024-05-05T12:00:00.000+00:00"), // Sunday
  new Date("2024-05-04T12:00:00.000+00:00"), // Saturday
  new Date("2024-04-27T12:00:00.000+00:00"), // Sunday
  new Date("2024-04-28T12:00:00.000+00:00"), // Saturday
  new Date("2024-04-21T12:00:00.000+00:00"), // Sunday
  new Date("2024-04-20T12:00:00.000+00:00") // Saturday
];

class DeleteSavingsTickersCreatedOnWeekendAndHolidaysScriptRunner extends ScriptRunner {
  scriptName = "delete-savings-tickers-created-on-weekend-holidays";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Deleting savings tickers created on weekends and UK bank holidays...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`
      });
    }

    const query = {
      date: { $in: DATES_TO_DELETE }
    };

    const [savingsProductTickerCount, portfolioSavingsTickerCount] = await Promise.all([
      DailySavingsProductTicker.countDocuments(query),
      DailyPortfolioSavingsTicker.countDocuments(query)
    ]);
    const totalDeleted = savingsProductTickerCount + portfolioSavingsTickerCount;
    logger.info(`Deleting ${totalDeleted} savings tickers`, {
      module: `script:${this.scriptName}`
    });

    if (!this.options.dryRun) {
      await Promise.all([
        DailySavingsProductTicker.deleteMany(query),
        DailyPortfolioSavingsTicker.deleteMany(query)
      ]);
    }

    logger.info("Finished deleting savings tickers created on weekends and UK bank...", {
      module: `script:${this.scriptName}`
    });
  }
}

new DeleteSavingsTickersCreatedOnWeekendAndHolidaysScriptRunner().run();
