import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { AssetTransaction } from "../models/Transaction";
import { ExecutionTypeEnum } from "../configs/executionWindowConfig";

class MigrationExecutionWindowScriptRunner extends ScriptRunner {
  scriptName = "migrate-execution-window";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating pending asset transactions that have execution window...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    const assetTransactions = await AssetTransaction.find({
      status: "Pending",
      "executionWindow.start": { $exists: true },
      "executionWindow.etfs.start": { $exists: false }
    });

    logger.info(`About to migrate execution window to ${assetTransactions.length} asset transactions`, {
      module: `script:${this.scriptName}`
    });

    for (const assetTransaction of assetTransactions) {
      if (this.options.dryRun) {
        logger.info(`Would update execution window for document with id ${assetTransaction.id} `, {
          module: `script:${this.scriptName}`,
          method: "processFn"
        });
      } else {
        const executionWindow = assetTransaction.executionWindow;

        await AssetTransaction.findByIdAndUpdate(assetTransaction.id, {
          executionWindow: {
            etfs: { ...executionWindow, executionType: ExecutionTypeEnum.MARKET_HOURS },
            stocks: { ...executionWindow, executionType: ExecutionTypeEnum.MARKET_HOURS }
          }
        });
        logger.info(`Updated execution window for ${assetTransaction.id}`, {
          module: `script:${this.scriptName}`
        });
      }
    }

    logger.info("Migrated pending transactions that have execution window...", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationExecutionWindowScriptRunner().run();
