import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { DepositCashTransaction, DepositMethodEnum } from "../models/Transaction";
import { ProviderEnum } from "../configs/providersConfig";

class MigrateAutomedDepositProviderStructureScriptRunner extends ScriptRunner {
  scriptName = "migrate-automated-deposit-provider-structure";

  async processFn(): Promise<void> {
    logger.info("Migrating active provider & deposit method for past automated deposits...", {
      module: `script:${this.scriptName}`
    });

    await DepositCashTransaction.updateMany(
      {
        linkedAutomation: { $exists: true }
      },
      {
        $addToSet: {
          "directDebit.activeProviders": ProviderEnum.WEALTHKERNEL
        },
        activeProviders: [],
        depositMethod: DepositMethodEnum.DIRECT_DEBIT
      }
    );

    logger.info("Migrated  active provider & deposit method for past automated deposits!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrateAutomedDepositProviderStructureScriptRunner().run();
