import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import DailySummarySnapshotCronService from "../jobs/services/dailySummarySnapshotCronService";

class RunSummaryCreationForFridayRunner extends ScriptRunner {
  scriptName = "run-summary-creation-for-friday";

  async processFn(): Promise<void> {
    logger.info("💼  Creating daily snapshots for converted users...", { module: `script:${this.scriptName}` });
    await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers(
      new Date("2025-04-23T22:00:00")
    );
    logger.info("✅ Created daily snapshots for converted users", { module: `script:${this.scriptName}` });

    logger.info("💼  Creating daily snapshots for users with cash/savings...", {
      module: `script:${this.scriptName}`
    });
    await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings(
      new Date("2025-04-23T22:00:00")
    );
    logger.info("✅ Created daily snapshots for users with cash/savings", { module: `script:${this.scriptName}` });
  }
}

new RunSummaryCreationForFridayRunner().run();
