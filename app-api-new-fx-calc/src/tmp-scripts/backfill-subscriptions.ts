import { User } from "../models/User";
import logger from "../external-services/loggerService";
import ScriptRunner from "../jobs/services/scriptRunner";
import { Subscription } from "../models/Subscription";

const USERS_WITHOUT_SUBSCRIPTION = [
  "667d84ddf2eb5d76978c4bff",
  "667d872bf2eb5d76979246bb",
  "667d84dbf2eb5d76978c492f",
  "667d8f34f2eb5d7697a331ec",
  "667d84dcf2eb5d76978c4b4d",
  "667d7a4ef2eb5d769770e6d9",
  "667d872af2eb5d76979242e8",
  "667d872bf2eb5d76979248b3",
  "667d84dcf2eb5d76978c4a3f"
];

class BackfillSubscriptions extends ScriptRunner {
  scriptName = "backfill-subscriptions";

  async processFn(): Promise<void> {
    logger.info("Backfilling subscriptions...", {
      module: `script:${this.scriptName}`
    });

    for (const userId of USERS_WITHOUT_SUBSCRIPTION) {
      try {
        const user = await User.findById(userId);

        if (!user) {
          logger.warn(`User ${userId} not found`, {
            module: `script:${this.scriptName}`
          });
          continue;
        }

        // Check if user already has a subscription
        const existingSubscription = await Subscription.findOne({ owner: userId });
        if (existingSubscription) {
          logger.info(`User ${userId} already has a subscription`, {
            module: `script:${this.scriptName}`
          });
          continue;
        }

        // Create free subscription
        await Subscription.create({
          owner: userId,
          active: true,
          category: "FeeBasedSubscription",
          price: "free_monthly"
        });

        logger.info(`Created free subscription for user ${userId}`, {
          module: `script:${this.scriptName}`
        });
      } catch (error) {
        logger.error(`Failed to create subscription for user ${userId}`, {
          module: `script:${this.scriptName}`
        });
      }
    }

    logger.info("✅ Backfilled subscriptions!", {
      module: `script:${this.scriptName}`
    });
  }
}

new BackfillSubscriptions().run();
