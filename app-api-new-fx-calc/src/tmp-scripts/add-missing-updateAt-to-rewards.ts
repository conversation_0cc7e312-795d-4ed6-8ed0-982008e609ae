import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Reward } from "../models/Reward";

class AddMissingUpdatedAtToRewardsScriptRunner extends ScriptRunner {
  scriptName = "add-missing-updateAt-to-rewards";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Adding missing updatedAt field to rewards...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    const rewards = await Reward.find({ updatedAt: { $exists: false } });

    logger.info(`About to add updatedAt field to ${rewards.length} rewards`, {
      module: `script:${this.scriptName}`
    });

    if (!this.options.dryRun) {
      for (const reward of rewards) {
        reward.updatedAt = reward.createdAt;
        await reward.save();
      }
    }

    logger.info("✅ Finished adding missing updatedAt field to rewards...", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddMissingUpdatedAtToRewardsScriptRunner().run();
