import { User, UserDocument } from "../models/User";
import {
  AssetTransactionDocument,
  Transaction,
  TransactionDocument,
  TransactionPopulationFieldsEnum
} from "../models/Transaction";
import {
  DailySummarySnapshot,
  IndividualSentimentScoreComponentEnum,
  TotalSentimentScoreComponentEnum
} from "../models/DailySummarySnapshot";
import DateUtil from "../utils/dateUtil";
import logger from "../external-services/loggerService";
import Decimal from "decimal.js";
import ScriptRunner from "../jobs/services/scriptRunner";
import { captureException } from "@sentry/node";
import {
  ExtendedAssetTransactionCategoryEnum,
  getExtendedPortfolioTransactionCategory
} from "../utils/transactionUtil";
import { DailyPortfolioSavingsTicker, DailyPortfolioTicker } from "../models/DailyTicker";
import { HoldingsType, PortfolioDocument, PortfolioPopulationFieldsEnum } from "../models/Portfolio";
import { ValuationType, WealthkernelService } from "../external-services/wealthkernelService";
import * as InvestmentUniverseUtil from "../utils/investmentUniverseUtil";
import DbUtil from "../utils/dbUtil";
import { OrderDocument } from "../models/Order";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import eodService, { AssetSentimentsType, ExchangeRates } from "../external-services/eodService";
import MathUtil from "../utils/mathUtil";
import { AssetSentimentScoresType, SentimentScoreType } from "../services/dailySummarySnapshotService";
import { PartialRecord } from "utils";
import PortfolioUtil from "../utils/portfolioUtil";
import InvestmentProductService from "../services/investmentProductService";
import { InvestmentProductsDictType } from "investmentProducts";
import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import * as CacheUtil from "../utils/cacheUtil";

const { AssetArrayConst } = investmentUniverseConfig;

const DB_SUMMARY_SNAPSHOT_BATCH_SIZE = 5;

const BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS: Record<IndividualSentimentScoreComponentEnum, number> = {
  [IndividualSentimentScoreComponentEnum.NEWS]: 0.3,
  [IndividualSentimentScoreComponentEnum.ANALYST]: 0.2,
  [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.5
};

const USERS_ALREADY_BACKFILLED = [
  "63f54222b69a488c860df853",
  "62817626f8b5dc004af406ce",
  "62cdbc56cc77cc0049f79890",
  "6532d33cc921b8610631267d",
  "652bd2b6ac7e34b83c351442",
  "6279bd1c58cbeb004af32221",
  "641b671b9c8a76bb9bfaff2d",
  "60abbed434d2dd003e14ac36",
  "635406393fa984004c5447fd",
  "65300963210d7db9f4386ff9",
  "62d6b1361ee7e0004985a8ac",
  "63b5ce526c59b6121d229c5a",
  "63652504cc4b12004b18674f",
  "629e351b56c9f70049ad596c",
  "6284a04905f0bb004a79dddc",
  "62fc28249ef8ee004b64413c",
  "63e8ad09218dd176dc70404d",
  "629cb4ff211b5c0049a8ad8b",
  "64b3f511515b6150efa7bdf1",
  "62ffd6834cb595004b7add7b",
  "63a32a58077104dfe17d096f",
  "63c27138df9ae32d31b50fe1",
  "63cf11c152942213ef25f046",
  "629e15a5c58d57004aebe251",
  "637e6176be0a7b004bbe7752",
  "63979f3b19b378004b07c275",
  "6287c5bb2cfdd4004a6edcba",
  "649ef938eb0b0791d0258835",
  "616455a02ebdf5004828a327",
  "6321d2a3ed2117004bc6b0a4",
  "63b9e87e9c8dbc3dda2f2e32",
  "647b3d38c6a88c981d72385f",
  "63bc8635b19a7c2599d1f8d1",
  "61f7ee379f0758004abad735",
  "631c4ec11d45af004b60d02d",
  "622d5268c8cac4004b13913d",
  "64f498ac04818c58efb4c0c3",
  "62810272b4d82e004a73a7fc",
  "6398f31e19b378004b08b757",
  "62882315928318004aa35d01",
  "62b9aff26807e60049bcc713",
  "62ab2d5e15f91100496c92ff",
  "64a0b5c6d3cf29d3d832817a",
  "6201825696f825004a153516",
  "629dc0aac58d57004adfeb46",
  "6306970e5d246c004b4d0d80",
  "6176d47a20c9570048ea7cc6",
  "629107bff5b05c0049494643",
  "641e00a25a418a529fffd6e8",
  "62868d499e7a9b004ab3fc54",
  "62d090bb8d93da00498e9334",
  "6318eb75f13813004bec0844",
  "635ed52e69de9c004b7c9e3c",
  "63c596790afb41bd3b4e8ad1",
  "66286154acddf5311683b8aa",
  "6389322e07f64a004c2a3331",
  "6349f7a08bc9d0004bf23c77",
  "6343356ad37336004b293ba9",
  "66b6257f6e1ae943e7785550",
  "63b4ed6c99548b3f12ddaf08",
  "62881a43928318004aa356b6",
  "62f80e3684b866004b91ddd4",
  "6426fc032bae678c9fcd779d",
  "651174ffd2eb8245f49df90c",
  "63db900748654847b935ea96",
  "64443d7036889f01cc08b309",
  "63e82c37218dd176dc7033a0",
  "63ffcdb9e65f80346ce83bb6",
  "640bfbaa5f0dfcbefab98f0f",
  "65ad2904c5118ba49e1a5eda",
  "6321fd68ed2117004bc6f158",
  "63bd5f253ef39fd1418af0f8",
  "64dd16c509cf9b1c7ca4c896",
  "62b645a51c4e1300494b12bc",
  "638ca84859d281004b6054ea",
  "640b4dd4dacbabab00eeb5c7",
  "628145819d77c3004a54c684",
  "646623ae1dfedbe692065ab3",
  "6373b38c9eccaf004b2a5f68",
  "657a11c9968bbb5d78c44a0e",
  "62dad9d0c9672e004a0dba4f",
  "623baab7ab888f004a0b8348",
  "62f7020284b866004b869980",
  "6281f971fa112f004a2b44b5",
  "64490abf2568c0312d24ffad",
  "63eca01dc16693a157917819",
  "648643966208e2c5615b9c32",
  "6445a218277d0d703ba7479e",
  "62815e5ef8b5dc004af3fa14",
  "634dc1cc65c1bb004bc361b5",
  "6462a15a142295263795b1ca",
  "62813e299d77c3004a54c0ea",
  "62959973c77e290049964bbc",
  "6206a96658b09c004ac00cc0",
  "639208fc605b27004b1b1cfa",
  "629cd279211b5c0049a9e9aa",
  "62a4f2150f6b62004adbf43e",
  "6424217d251b6e74fb59eb41",
  "63b728759c8dbc3dda2d9045",
  "6308a2ee798c57004b089d37",
  "63da4063c9b8463927c41bf6",
  "62ac332015f911004972a70d",
  "649c6835ce08f43bc31cb338",
  "6287e3b22cfdd4004a6f0e2c",
  "62d026788d93da00498a679b",
  "6501e0cc9c29238235458a6d",
  "63adc1b23bde17003a2d22e5",
  "63c1d2cadf9ae32d31b4dcb7",
  "629b978d9f24360049fba952",
  "6362b5b0d1c46b004b43d682",
  "62a30dba5949110049dec3f8",
  "633c24d8494a2e004bdd23de",
  "6383a43b197be1004c29a047",
  "63e22dfe7de8c3da56937175",
  "63320ca526bc05004b09d3d4",
  "640db61e2731960097516047",
  "6401c35c9c2fc924629a7a86",
  "63e3821384167f1094617f14",
  "6435a8a3ac26dd0f8de79865",
  "63219455ed2117004bc644d9",
  "63159f8c6e7c9b004befdb03",
  "6360031eaf44a4004ced0f5e",
  "641b45f69c8a76bb9bfadf6b",
  "621d12793d0d42004adb88e7",
  "62bee8fd103755004910470a",
  "628fd44cc168ba0049847919",
  "639cfd5f9e613f004bad9209",
  "63a850b13bde17003a2a41d9",
  "648a1e09bd59efe3f7f65856",
  "6429d9177178d7b656088143",
  "634865ebb1bcc7004cedf5a4",
  "63fa70472b5ffb01b43b4e68",
  "634d79249eb087004c43ec54",
  "630018c14cb595004b7debd3",
  "628128579d77c3004a54ab07",
  "6453cad8686ecf110055b3e3",
  "63d7ee4fbe5fe06f774021c1",
  "630cf124d12d51004bfcd5e3",
  "648a6659b98618230c881f32",
  "63e4222084167f109462203b",
  "647b2a406fdcd5c5bbed2e53",
  "647161925e167f0d1afae576",
  "627e37ebbb4b4e004a440470",
  "63a6f4f93bde17003a29e546",
  "646f7087e20eea986945a849",
  "647f884c8ecae893390b7650",
  "630e1483ebb7d8004b3277b9",
  "633c65949f6946004bd171dc",
  "63f73ff34e66c24d52b14f97",
  "6361a7c0f18b27004ba1c040",
  "63077836d32e39004bfadd38",
  "641d90c7168c760c92b1f288",
  "63daceb1e704008b86613050",
  "633ae170722234004b7680f6",
  "644e62bcfc4e77e44fe2f674",
  "65c010f16b4aeaa8ff5ac108",
  "62b59bd9d674840049fd6996",
  "644a4bec2568c0312d258c07",
  "6426ad82726880644bd0881e",
  "6352b2f6575b22004bf7e2d6",
  "6388f975c06f39004b07de39",
  "62b5ffa51c4e13004949584a",
  "64371ed46b4cfeccc9f11b81",
  "65c73f249d530aa6cc4551a9",
  "632a1de309e01d004b611aa0",
  "63e570e3f65640ffb3dbfef2",
  "634457ad2f7c04004b3818a8",
  "67a27987ee815a2e54576543",
  "6289ec7b50b204004afef792",
  "654108b7fbf0e7c7942a84aa",
  "62dea7d6616ad8004c80b391",
  "62a1da463187eb0049c57492",
  "6480e355b6594424bdeffa2f",
  "631c848d075586004becfcd6",
  "6302c716aced7c004b0f0e5a",
  "62bd80543d25c5004af1bdf9",
  "64b44105515b6150efa7d8a5",
  "63b34dbc53aff3f86b686d67",
  "62cececb0a08740049c3625f",
  "61ffa8f3377bca004b2cbfaf",
  "64bc5d6e943124da8e618647",
  "62e045570a8955004b183279",
  "648cd901bd43f6e21550ad39",
  "64482c1802675a693dcc4de0",
  "62b388d5c570320049b196e7",
  "6536dcaeefb814a53afd8ad9",
  "6294f35c04f7f3004ac4470f",
  "63776b60d6179d004bb20384",
  "62d09d82d87b950049e519c8",
  "6461e19ad9afaf01d71644cd",
  "628e8db3519c34004a160b10",
  "6457f6c3705921416efa35cf",
  "635d60901bf2ec004b4863ac",
  "62af2e8b15f9110049843570",
  "638a49ff59d281004b5f6eab",
  "63b5c8ce6c59b6121d2295c4",
  "62dd9abb2785f70049089d80",
  "64ef5058d6b1f8278227132a",
  "64f8ad29732600ef63f6f4f0",
  "6246082491abae004a36e97c",
  "646542c67d47e17fd276f5b7",
  "639e8789a00ffe004baf326c",
  "642b1417b03ff7d2c0f6d4fb",
  "644baab28a10193d06a09431",
  "6468c658b07555c2a609bf74",
  "643fc3f58a389c983f393a7f",
  "6287aed230bef8004a1e32aa",
  "63506040a30a0f004d4b4d25",
  "6335f67fad6b41004b23cf30",
  "64c2d76c152cc3adde3fcd40",
  "629df125c58d57004ae6ab78",
  "62e4fc3ab4f567004cc951bb",
  "628a9bce02d452004aedd3f0",
  "634eec067b4a32004bc8cbbb",
  "645e7f04e4fdadf66e143d26",
  "6495cf39b3149e81dba1567c",
  "6495ed425e0799ec2c7c85e5",
  "651280ba0929775f53a4e244",
  "6210e96a0cc574004b435293",
  "6287ac8830bef8004a1e2e0b",
  "636586e3130ae1004b97c017",
  "649c809eefc77a497398495e",
  "6630e3b9edcad0d5db70d92a",
  "63f7325d4e66c24d52b13d0a",
  "6384f51fe4538f004b46fa34",
  "638522b79667bc004bc45d3d",
  "63dce5e4adcf956353f6bbec",
  "669fbe2ab5286191e4289b1c",
  "62e8c820703ae0004bdc45ec",
  "61b562a53eb64d0048bae9fc",
  "63b4924b37bb074c81c23469",
  "648d6bae6feb770d6394bbac",
  "64de039a9b68ac21d3e300d5",
  "6520f8a90995a4d93f2071d3",
  "630d6045d12d51004b02bc35",
  "63617643f18b27004ba181e9",
  "646d2efa38aa6aefd80f9a2f",
  "62a8add8f708cd0049da7fe3",
  "61ae14eae16e0d0048c30870",
  "6328c2fcdb9ab3004ba2231a",
  "64ccc647ec45e1d8e8d37840",
  "64f9fe8b3870651e6c1ec6e2",
  "6312292c6091e2004b25303a",
  "64b451b1515b6150efa7e1f4",
  "64a163a9eb0b0791d0269701",
  "64b44b8f515b6150efa7df0f",
  "651c7c22eca1bd6c8b4e5b79",
  "63536acea30a0f004d4e90fe",
  "62928d28f5b05c00495f2f9f",
  "629b8bd30a4b510049aa85b9",
  "63d6af9f3f1ecbaa3b062533",
  "64f1065dee219696ae94eb65",
  "63d7c5a80164bfbf2482a2ae",
  "64105de64fb8f234628b2775",
  "6457fc903094dbc36ca07a17",
  "649422eca0a8dcfd35901900",
  "633079e89d2fd3004b350575",
  "62e449eeb4f567004cc364d5",
  "64559602705921416ef97b85",
  "62cdf888cc77cc0049f97f7a",
  "62c4345e38cdba004918ba29",
  "63c2d7f5df9ae32d31b54b53",
  "62bca3e93d25c5004aec03c1",
  "63942bec19b378004b060159",
  "6419da1e71db9c609cc737e2",
  "626ed32a398b2a004a922e41",
  "64653ed0da4203423777b1b2",
  "62d2975c28b9fc0049c8160a",
  "6509f4a05da4791344bf12dc",
  "64a1050dd3cf29d3d8328764",
  "62694bcfc5d2a4004ae4459b",
  "648a1350bd59efe3f7f64b66",
  "6494962e5ce8da4dccf12a99",
  "6287a67c30bef8004a1e2a9c",
  "62f378df2bdd33004ba2e2f3",
  "63ff6979e65f80346ce7d7e4",
  "63198cbaf13813004bec5ae8",
  "63566eb08aeef0004b7d08c9",
  "6317ab53f5fa83004b708f26",
  "6475f1a40eab6139474c6992",
  "6436fd9c6b4cfeccc9f10572",
  "628e9ae8519c34004a179395",
  "633ca9a39a3520004bb7d928",
  "641f6d1b8b0db5a27610e28a",
  "639e4c3ba00ffe004baf2a7b",
  "63f54109ce4b812002376624",
  "63f0b0bb5ddc7dfc693b1c21",
  "620134ab96f825004a152829",
  "6273b6b7d3d148004a7d99ec",
  "6283eed20d8d2c004a405121",
  "629a74f9bf3cc9004ad13326",
  "65689a8ae44eb6025464ede5",
  "64669efd13882dd592f64501",
  "62e524bab4f567004ccae0ff",
  "6492c23db949827b94a833b4",
  "61b7587ccb2b2e00480f152e",
  "629b49c1bf3cc9004adc7fc6",
  "62af41dd15f911004984b4e2",
  "62f51828e441c6004be18e0e",
  "66241afaed55a0e1bab9b356",
  "63e1215f36477e05113b8a79",
  "64ef458886a8da10563fc20a",
  "63f7b579f8064505718b4cdd",
  "62868f6f9e7a9b004ab3febf",
  "62961e5dc77e290049a73c8d",
  "6281723ff8b5dc004af4044e",
  "630355fee18b7b004b24d113",
  "627ab22ea51b18004ab874a4",
  "62a8a6aef708cd0049d9c504",
  "62822dfdfa112f004a2b72e0",
  "631487006e7c9b004bef3364",
  "62e4de59b4f567004cc847e8",
  "643e9cfe5e6a3a15f27c9613",
  "629f22ba56c9f70049c94e53",
  "62d1991f28b9fc0049c027c1",
  "64aa957f12c99485334989d7",
  "6387202a2e9709004cdbe453",
  "65ccefc11187904ededb6fe0",
  "63e184017de8c3da5692f26e",
  "656e0803c74bbb0d2230ba5a",
  "630acfaf08cbe0004b4efe39",
  "63283aea286e0f004b73a1b0",
  "63a5a1ef3bde17003a28c813",
  "62b9a407fc434b004982d3f2",
  "64203f085a418a529f006efb",
  "649d6dfe89fcf9a920d9257d",
  "649cb6efce08f43bc31d672c",
  "62880656928318004aa33be2",
  "63f71a014e66c24d52b11d62",
  "646553827d47e17fd2770637",
  "62cbfc86c8996a0049ed1eec",
  "63e16f677de8c3da5692e335",
  "62b0413a6f40c1004942fb30",
  "6501f3221f729a9ce483c3d4",
  "63ebe3c130493fe0357dcdb2",
  "6342c3b459b828004cbfe137",
  "64a1c0c8eb0b0791d026ca1a",
  "63ceb29152942213ef2576fd",
  "631b9049075586004bec7996",
  "6285d22d366045004aefff27",
  "634f06047b4a32004bc8e4e5",
  "628239a276279c004ae55032",
  "65cd29c8a0a74c7a98e7c239",
  "6336d2d986498e004bc9c533",
  "641486e22c68e51c65671140",
  "629891ef3b47dc004964c123",
  "62c213daef6dfa0049f2cb34",
  "62eac8c8a5ca4d004b736eb1",
  "63c086c3a865fdc013188b5d",
  "6643a8f2181d8fb2714f9720",
  "639215ca605b27004b1b3caf",
  "633ccc089f6946004bd1e98a",
  "63712e2285d8e9004b32644f",
  "637bda7eb33b6d004b654ac7",
  "63209c74075586004bf0fac7",
  "62ed119dd46581004bdcc0ae",
  "64847a545e7ab29d9dba7364",
  "636f8bf885d8e9004b315311",
  "6432bf3a30cc043915e3e05a",
  "6398f52619b378004b08b79c",
  "62d7b1f97091b40049298c0a",
  "6536a93cefb814a53afd67f9",
  "652591f7742449819db6e08b",
  "61fada865a5fae004a6eb9a9",
  "62877771740531004a5ae366",
  "62c30a8c196b570049cc5835",
  "64ae6cbd1b415bab2be898cb",
  "62f4d5f6c93654004b7a89e0",
  "63a32412e361a26095e2fa13",
  "655c3eb4cd49cfe1393f2bd4",
  "634d90e49eb087004c443927",
  "62fe8d22aced7c004bdf14ec",
  "62909e50c168ba00499d334b",
  "64349885f4b4f30685147460",
  "6519eef8ac4f043a320ecd20",
  "63a60cc8a70d98d233d2d886",
  "62faa43d9ef8ee004b53498a",
  "62fe60de4cb595004b6a9cbc",
  "655bd587bfa6a996d0024fe1",
  "63f69aea4e66c24d52b0ec17",
  "63911cc64c5c59004b69fc82",
  "62f8d1189ef8ee004b3e1bbf",
  "64c59a1ea0250ce3d7d2ab5e",
  "629631c3c77e290049a9d3cf",
  "62934e97f5b05c0049656bbd",
  "628acfe102d452004aedeea7",
  "62af10e215f911004983b601",
  "629dbed4c58d57004adf8e3e",
  "63f7ba154e66c24d52b1b4ae",
  "63a7f8b13bde17003a2a325d",
  "62c4157e186f4c00495f61b2",
  "6288b2404cf66b004a1cba1f",
  "64957639b3149e81dba0fff2",
  "640501fb020f43f50611fb0d",
  "62d56b9328b9fc0049de1084",
  "63b5fd6b19c3b7da09a16d0e",
  "6364514bf18b27004ba505eb",
  "6374705a893f44004c4aec36",
  "623ba001ab888f004a0b822d",
  "646bc27f221079c32e49de48",
  "6256e036236286004a32d4fb",
  "62a0bcbb1e0b8600494cf487",
  "65168ce0a6bb1a2d2877f980",
  "643db7736d98a4d05512165f",
  "65a17f27a24ce0f1396be7bb",
  "63d0280b99a11787ae31a0d7",
  "634975ccd8ea42004b2a9479",
  "63eaa7bb5074500ad146c5fb",
  "63f897492d84995315dc4a7a",
  "649c5241efc77a4973978bf8",
  "63cdbc70fcf17527256e7b17",
  "629d522ec58d57004ad0af20",
  "62cbea73c8996a0049ec79e4",
  "652bafd5ac7e34b83c350b6f",
  "643bcb8eee6da54df5e792d1",
  "65637563cfea0bfb3c475e17",
  "641e33e78b0db5a276108485",
  "630927bd5c4e69004ba7f401",
  "637e01ffbe0a7b004bbdd68e",
  "64de6f6e6c4249ead08c5cac",
  "63ce3148fcf17527256eaea1",
  "62c5d474688e490049bbca7f",
  "641e2fa48b0db5a2761081a9",
  "62b933707876400049c1c98a",
  "64c3df1fa0250ce3d7d21158",
  "63d94de0c9b8463927c33912",
  "638bdb5259d281004b600622",
  "642095958b0db5a276113a40",
  "6249c3b25cb997004a593533",
  "62af3e142a10f70049e2fa9f",
  "63e91c7b218dd176dc70700f",
  "64652c2ada42034237779b4f",
  "6435001130cc043915e49454",
  "63b5b8a66c59b6121d22889f",
  "62d3024928b9fc0049cb14e3",
  "6332b774a5ac8b004b3eec99",
  "62c46a7ec292160049ce0048",
  "62e446e2703ae0004bb06c8f",
  "6391c53abcdbeb004b884338",
  "63652adccc4b12004b1870db",
  "642a843a5314539563f38247",
  "649f38fbd3cf29d3d831d82a",
  "629cfb99211b5c0049ab6b98",
  "63c7a61e9aa331fa8a764764",
  "647eefe86cd028e28574703b",
  "62b767021c4e1300494e220a",
  "63e0e09b6a0a297a61b3f52c",
  "63284c42852ea6004b51dcf7",
  "64a335c6ab9f3c3901d6b976",
  "6536d647efb814a53afd8653",
  "62de2d082785f700490e2f5d",
  "642722f55314539563f24fe4",
  "646936d2b07555c2a609ec6f",
  "63ed255c6bb451016db36f3a",
  "628fe721c168ba004986d308",
  "62ed8c6bd46581004be13540",
  "625840f59d684b004a981fe0",
  "63d833cbe9fc800c3fb91afe",
  "637a023583b2d7004bf4b292",
  "62947610f5b05c0049792671",
  "631512616091e2004b271171",
  "6479a16828fcb61b88027cd6",
  "64dd43b409cf9b1c7ca4e246",
  "6528df58539bc552a3b24a9a",
  "641649dc3050698c99a1c964",
  "66cfa49b58b03edea3d162d9",
  "626d98319de6a8004b99c435",
  "64b53a82da49796346e78900",
  "64904356d2b624da3ae47887",
  "64af1546c4d9959d16756143",
  "63c45b685904aa390646eb67",
  "629bab53211b5c00499ec9e1",
  "62f4d3eec93654004b7a7605",
  "6396c36012e52d004bba7261",
  "63d97d9c0f65ea183fbe5375",
  "639b279e4136c7004bf9df08",
  "647857ac630416e11f204320",
  "648778037708f02304bc0a48",
  "62cb0524c8996a0049e59532",
  "633963c8722234004b7579cf",
  "634b1686d8ea42004b2bd0be",
  "6416561b3050698c99a1d188",
  "61fa83445a5fae004a6ea345",
  "63985381a1e839004cd12cf3",
  "63f61cb9b389bde31ab02847",
  "635159a1a30a0f004d4c2a9f",
  "628919663b1522004a2d8cba",
  "63edfb936bb451016db3ed7d",
  "64cb82083e7017ea8fd32361",
  "63b4721b37bb074c81c213ca",
  "6388c0602e9709004cddbdb4",
  "642b0fc2b03ff7d2c0f6ccfd",
  "62d671d3c9a95500497c6a39",
  "62826eb10b1398004a5f4756",
  "640a1ad6910f3b483c0a3992",
  "639df1a49e613f004badaaa7",
  "64388f20ee6da54df5e682fa",
  "6390683cabaaa3004c251a4f",
  "63d2ded0fdfd125238eb0679",
  "6465a2427d47e17fd2771700",
  "6493702b149b8f70a1d4beda",
  "6280d539b4d82e004a737885",
  "6369cba48359fc004c6145f5",
  "649c9102efc77a49739868c4",
  "6488509fe966693f130098eb",
  "62887f6d4cf66b004a1c8670",
  "646e02a8675c03218ff6ea99",
  "634faa287b4a32004bc925cd",
  "621fe4644f3e42004aaf9cad",
  "641c7dee0161464eace89a55",
  "6441b7c6cb6d3ef853841fe5",
  "6299d15f73a180004993f439",
  "62a9098f14862300498e8633",
  "6254062bf5dbd4004b8eeff9",
  "6324dea299a775004b22f0bf",
  "630e1015d12d51004b0ba5a8",
  "63509710a30a0f004d4b83a9",
  "667e14387a5186bbcab54b3f",
  "645e547fe4fdadf66e141171",
  "62a7585c0f6b62004af2b254",
  "63acc3073bde17003a2ca308",
  "636e3f2a85d8e9004b3068d5",
  "64ca6dd875039af0db6ef9bf",
  "63d6fa683f1ecbaa3b065beb",
  "629b97c69f24360049fbaa82",
  "62da8ae5228c35004a18d492",
  "62e415221c9bdd004c12c691",
  "63ea8d155074500ad146b251",
  "63ad8d983bde17003a2cf8b0",
  "640ce5b827319600975145f7",
  "6532786edb2c55165106e401",
  "63d8ef8cdd285f55d1a942a6",
  "648433706208e2c5615b008f",
  "640bf330dacbabab00eeef27",
  "63c078c6a52fb8a3b5c3c76a",
  "625718c31e8a13004a2afb8f",
  "62818a98f8b5dc004af40aa8",
  "6287f2c92cfdd4004a6f207b",
  "644fdb87fc4e77e44fe39644",
  "63d8fc7ce0ed3b1a07e93e49",
  "62dd5a9e2785f70049063aac",
  "63064376d3d6e1004b07e23c",
  "64ef8e8af345686d1df7ee48",
  "632210bb1e6809004b7f1c8d",
  "62a8894a1d07810049a816ea",
  "63015caa4cb595004b8c21ea",
  "6417d3393050698c99a21872",
  "62be2b14486b65004994106d",
  "62963e84c77e290049ab8380",
  "63e264d77de8c3da5693c748",
  "6377d31965d523004b2457a7",
  "63429c60d37336004b28d8bc",
  "628ff587c168ba0049888d41",
  "627a86deb29a8e004b6db37f",
  "61ef1e5f9f0758004ab98fa8",
  "651c125bc6e9eb8b164fd154",
  "63ebd57b30493fe0357dc4df",
  "6377b50d65d523004b2421ef",
  "6399b15a19b378004b090580",
  "667361453606b3b8a90ca80d",
  "649c4eacce08f43bc31c534f",
  "63918bed1cffd6004b73c169",
  "63f360815ddc7dfc693bfcb1",
  "628e12c403c7d7004a64b623",
  "623de28ef553bb004a823b36",
  "63d463451f2cc41fd8721b0e",
  "647da7a96fdcd5c5bbede829",
  "651c9d03eca1bd6c8b4e6e70",
  "64513024fc4e77e44fe44340",
  "626f378c398b2a004a9237be",
  "6494aada5ce8da4dccf13b05",
  "62d38a0972c9e00049e3b26d",
  "621a3e0f3d0d42004adb2444",
  "619808a4a70b770048ece9d2",
  "630b97c55c4e69004bc537b1",
  "646bd13f221079c32e49e95d",
  "64ca77e1153f67b83986a9d9",
  "649c871dce08f43bc31d2a09",
  "62f8c38a9ef8ee004b3d5e92",
  "6406831ab2be15399f0fe7e7",
  "6282b3ed8f7d91004a7f3dfd",
  "62a1e1aa32b0be00498bd91f",
  "63dea8d16a0a297a61b2de44",
  "62ffc763aced7c004beed152",
  "61e5b410e4a16b004a354509",
  "64de78596c4249ead08c62a9",
  "641ad81771db9c609cc7982d",
  "63a971ba3bde17003a2a8551",
  "6281f356fa112f004a2b4243",
  "6281593bf8b5dc004af3f614",
  "6560a2372d07ccf8e731b07c",
  "6363e1e7d1c46b004b450dcd",
  "636b8dd60387fc004c02cb6a",
  "6320d6ba1d45af004b65ad05",
  "64f3159104818c58efb455c1",
  "624ac2f89771d5004a4994eb",
  "640c894adacbabab00ef1892",
  "6429e7707178d7b656088d46",
  "6429068f5314539563f2e654",
  "6280d4f2b4d82e004a737870",
  "630b37f208cbe0004b53cbf3",
  "628137de9d77c3004a54ba72",
  "6283ce7d8823eb004a17bace",
  "64726113649b6432fe07968e",
  "641726272c68e51c6567d0dc",
  "64008e99b42ac2202974e773",
  "631b95f2075586004bec7c52",
  "649b5733c22aa28cdbac2f05",
  "64b501b0515b6150efa8279d",
  "62882225928318004aa35c86",
  "6457f5f33094dbc36ca075a4",
  "626ebed9398b2a004a922cea",
  "629b092cbf3cc9004ad9ce54",
  "63925053d8efc1004b7cffeb",
  "63b9f32a9c8dbc3dda2f3656",
  "62cdb5a7b96622004980841e",
  "62eb01535fd326004b2152d9",
  "6400e896997289fcd5899982",
  "63640d35f18b27004ba4a98f",
  "63e8f025f65640ffb3dd9569",
  "63d96895c9b8463927c3641b",
  "633440843ad47c004bd74cd6",
  "643dbd6fee6da54df5e84e5c",
  "629e291cc58d57004aeee612",
  "6322f3631e6809004b7f942b",
  "63cea14a52942213ef2568fd",
  "642ac76aacaa934b5b468f24",
  "62888d104cf66b004a1c92a8",
  "641b1d717730e3b2bfc47ac6",
  "62e6c572703ae0004bc8d439",
  "62a2dcd65949110049dc4d90",
  "62618ec13b865a004aa8a854",
  "63524daf575b22004bf74e95",
  "6280b9cba07bfa004a6413cb",
  "6287cd5c2cfdd4004a6eee58",
  "6431406d30cc043915e38f98",
  "648d5b52bd43f6e21550c2c5",
  "628810cf928318004aa345fa",
  "63dad7f3fd000d43405a184e",
  "6206669b58b09c004abff4db",
  "62923103f5b05c00495be41c",
  "64f238df04818c58efb4029c",
  "635402cd3fa984004c5443ac",
  "628c98ed82b90c004ada4c33",
  "638631de394b10004c6987a2",
  "633b073785b236004be52d1c",
  "6280d5c0b4d82e004a737944",
  "629bb18a211b5c00499f0975",
  "64712b2e649b6432fe0742cc",
  "6327333799a775004b244b24",
  "634ebbba7b4a32004bc8766f",
  "636a95a5cbebab004c4be82f",
  "63967d0f19b378004b06e6ec",
  "65984f198a82ed0de4e0cb0b",
  "64a29729d3cf29d3d83336d4",
  "652ef39419fc684bf58eadb6",
  "63e9116cf65640ffb3dda347",
  "628e094b03c7d7004a64ac9e",
  "62b4d9cbd674840049fba344",
  "629c8f7d211b5c0049a720d3",
  "64ef3cdc8fe77532d87725f5",
  "627c2f36688160004a374be9",
  "629c01c4211b5c0049a20ada",
  "6222fea020dddc004aed963e",
  "62758a47ecca72004a221b93",
  "6298922a3b47dc004964c207",
  "65afc6b557d4e651c6fe6c94",
  "6355b63728de6a004b0804dd",
  "637fd8dcbe0a7b004bbf991c",
  "628ee954519c34004a20a911",
  "64d386b12a6c35b14f2547e0",
  "645f820c5ab0dcee3aa84ea5",
  "643843feee6da54df5e6459d",
  "64ca774575039af0db6f2329",
  "641ded148b0db5a276105366",
  "63a36a8e19dd48663b15cdb6",
  "63f52e48ce4b8120023759cc",
  "63cf1165fcf17527256fd2ca",
  "64629f6e2383223fe5af34f8",
  "64cfa032ec45e1d8e8d4a450",
  "6284dd1805f0bb004a7a098e",
  "63014b314cb595004b8b44ad",
  "62a28a0e32b0be00499780c1",
  "629001cac168ba004989cf78",
  "6496c6af5e0799ec2c7cc346",
  "629ca9f7211b5c0049a8403c",
  "62dcda03c9672e004a206687",
  "6294bee1f5b05c004982a690",
  "63d5f0e73f1ecbaa3b05dc47",
  "635e2e341bf2ec004b48d3a0",
  "62d03d99d87b950049e20cb2",
  "645324f3e16f2b4e4e83b517",
  "64595309a23962cc1b29f499",
  "645baf73a23962cc1b2b8a66",
  "64a0bfafeb0b0791d0266336",
  "63892798c06f39004b081a22",
  "6378b66783b2d7004bf4213d",
  "62aa408fd3f3560049f0ce44",
  "648cfa986feb770d6394a7b4",
  "65245c2e0647bc8bea386695",
  "65b69229da9f8052f28566db",
  "633f244fd37336004b26f092",
  "642aac4c7178d7b656092a9d",
  "6536cae7efb814a53afd7ecf",
  "6453894ee16f2b4e4e83ebef",
  "61a476dde505e50048b47c78",
  "62862eeed0bf84004a5bea20",
  "6300a1734cb595004b837f26",
  "620c1eddf5b8af004a24d096",
  "6363a426d1c46b004b4483f8",
  "62c2de65ef6dfa0049f8a3c8",
  "65044e8ded8b81621efaa330",
  "639a74d4eccb42004bf904c9",
  "65ce59661187904ededcf6cd",
  "6377f3e565d523004b248ce7",
  "646a3998b07555c2a60a27c1",
  "63320b9af3349a004b1c122c",
  "62f5305ce441c6004be2ba9c",
  "63db4ed6e704008b86618861",
  "65157bb85f16914c13f9b4c6",
  "62a9f164a992dd0049cbd8e9",
  "6695971245e15ae38f5f50a3",
  "6283efa60d8d2c004a4052cb",
  "64808f0a2b1b94341c82cd1a",
  "63c7f4249aa331fa8a76a38d",
  "630ac2035c4e69004bbb1337",
  "63fc92e72b5ffb01b43c4602",
  "64593fbe920c70f93e7d2210",
  "628e5f4a519c34004a100822",
  "62e64356b4f567004cd51a7f",
  "62c57b171a21be00493b80d4",
  "64a33586ab9f3c3901d6b913",
  "62bccd82486b65004989cb25",
  "6674613b5b714d85629f3996",
  "63dbdc2948654847b936956d",
  "6283199f75fcf8004a95d9be",
  "630913f808cbe0004b3c2220",
  "62d085208d93da00498e234b",
  "6384cb9cbd3bc9004c9a39dd",
  "647b830e6fdcd5c5bbed5380",
  "634efd887b4a32004bc8dd1d",
  "64aaa8e612c99485334993f1",
  "629e63b956c9f70049b46e6c",
  "645f4b0ee4fdadf66e148bd1",
  "63333045cf69a6004cd9a0e0",
  "62809ec8a07bfa004a63f929",
  "62d690541625280049f13299",
  "647ce1896fdcd5c5bbedab1a",
  "63628e26f18b27004ba2b4c1",
  "62a77d6d4846e5004937c4c5",
  "64f4547004818c58efb4b0c0",
  "634e84d2684cc1004badbfe5",
  "63b21d353bde17003a2ebb48",
  "62822c2efa112f004a2b7196",
  "63b3404553aff3f86b68682f",
  "62b5aec01c4e13004948bd42",
  "62a243065949110049d01ab7",
  "643f085a8a389c983f390d62",
  "64a8f116933996d4ff0e2185",
  "62915228f5b05c0049528885",
  "64ae3faf1b415bab2be888cd",
  "64ca7e93394d741bea2c0170",
  "63778bd0d6179d004bb23793",
  "663f5ea6e6315e6ba0da5537",
  "61f02b119f0758004ab9b7ef",
  "62762ce6ecca72004a223107",
  "630894e98c305c004bcdec13",
  "6299bc0c73a180004990f2d7",
  "629b96e89f24360049fb9c60",
  "62fd41a755539c004b3ffa50",
  "6358155305572d004c3ae24f",
  "63921931d8efc1004b7cc08f",
  "6364635ef18b27004ba51b23",
  "63f288a8e9c63adf7716d11d",
  "644bb0e08a10193d06a0a078",
  "6429bc4e5314539563f31e9a",
  "63263bbe99a775004b23c858",
  "629f2dd356c9f70049cad88f",
  "642d49b49e1cb48e9ef6b624",
  "62b76b1b1c4e1300494e24c8",
  "63b9e04b9c8dbc3dda2f29eb",
  "65076f75ed8b81621efb572f",
  "6350030d7b4a32004bc98388",
  "6265b28e4f727a004ac52df0",
  "630de9a0ebb7d8004b30338a",
  "638b1c4259d281004b5fb7e8",
  "648e08246feb770d63951b06",
  "639fc9df9e613f004bae1e37",
  "629a966cbf3cc9004ad50cca",
  "64674c57e9992c0e2422b1f9",
  "63bdf35a3ef39fd1418ba860",
  "63f4f324d56c88a458008e47",
  "635ab3001bf2ec004b45f60e",
  "627bc0fe0cebad004a6e197c",
  "6475363a0eab6139474c1582",
  "65283bc071134fc00410c11f",
  "64a4771d6b60aff5dbaa2609",
  "633488782c0d75004b6ecbfc",
  "6294e61ef5b05c004987ba4f",
  "6385e351394b10004c694c2a",
  "628125fa9d77c3004a54a6ff",
  "6279988358cbeb004af31e29",
  "62db30942785f70049f38bc4",
  "62a1c9d03187eb0049c4539c",
  "6476625505605162f312d014",
  "6377ffa065d523004b249a38",
  "648ca6f86feb770d63948423",
  "64a5afec9c9a848ca7a71ba0",
  "62cc022fc8996a0049ed3e51",
  "63056774d3d6e1004bfc688c",
  "628571b5366045004aeff29f",
  "63b489af99548b3f12dd8768",
  "651e93fcc0786079ac825111",
  "63fc81c39c5cc055079f078f",
  "63cd838052942213ef248177",
  "6364d928f18b27004ba55b1b",
  "65145f9ebdc10ddcde781bec",
  "630dedf2d12d51004b0a4d58",
  "635b6ad81bf2ec004b468db1",
  "63944d1119b378004b061512",
  "62fe9fdbaced7c004be01031",
  "63ff91dba8ee1db11d937ef4",
  "6304065ce18b7b004b2d9224",
  "63eb9b255074500ad147590b",
  "641c56be9b17cd833a376d64",
  "62e2ed0a703ae0004ba28a97",
  "63d49b0957398a43e81c1894",
  "648eac486feb770d63953deb",
  "63d7710e3f1ecbaa3b0684c7",
  "6290bc25f5b05c00493f5e12",
  "637217b185d8e9004b3314ab",
  "63ab9a0b3bde17003a2b8887",
  "62e248fc703ae0004b9c7617",
  "62e524e9b4f567004ccae123",
  "66f9c9f97b5ec77699953398",
  "63dfbfd0a114166d6d876cb7",
  "64e4034cbb34d7511c0c24db",
  "63b1f2fa4c1eff196c7b917b",
  "6470f5455e167f0d1afabedd",
  "62c2cc4cef6dfa0049f7c529",
  "624ddc92855bf1004a858fce",
  "62b46b5f9418b400494f5a99",
  "62a86d69db598e0049c63d82",
  "6349ea8d8bc9d0004bf2380a",
  "629a024d69a22c00491d2bb9",
  "635167c3a30a0f004d4c5b37",
  "636ceec5f1a26a004c0ea464",
  "651437e5c01262742ab5047a",
  "63e367a84ff748ae87f57ba2",
  "6353fc2b3fa984004c543eb7",
  "6279889258cbeb004af316b7",
  "64baeaf3943124da8e6111f3",
  "650326aaed8b81621ef9f7f2",
  "643931c7ee6da54df5e6cc4a",
  "64044695020f43f50611c917",
  "644aefb9716f000391fd0d04",
  "6452996b686ecf110055089c",
  "635b164d1bf2ec004b465f27",
  "65d882c5683d63410e90dc5b",
  "62876d54740531004a5acd23",
  "62e046160a8955004b1841be",
  "6349762ed8ea42004b2a94e5",
  "62e5baa4703ae0004bbeaf90",
  "64208a9c8b0db5a276113650",
  "62f3f642c93654004b712cff",
  "630b2e7508cbe0004b5345f7",
  "635b084f69de9c004b798c7b",
  "641b58b39c8a76bb9bfaf4ef",
  "63a4b8b33f7b74716d71bfcd",
  "62d1936328b9fc0049bff8e6",
  "6429e9007178d7b656088e40",
  "646a31a9b07555c2a60a253f",
  "62b457909418b400494eee1e",
  "646ddac4675c03218ff6c476",
  "62879bee30bef8004a1e1b2b",
  "6411a5b1fa2fc202672521b2",
  "62968f9b5b006400497ae03d",
  "651c559faca9419bfe655931",
  "634956db4bad4b004bb39100",
  "636aab2acbebab004c4c1969",
  "62a1ae7f3187eb0049c2af76",
  "63755bb8d6179d004bb08aea",
  "629a655dbf3cc9004ad0a081",
  "628fcca9c168ba0049837400",
  "64bb1e13943124da8e6125a1",
  "63d9f263c9b8463927c3d092",
  "62a65aa84846e5004927f16b",
  "63ede02b1b5b1529a3d53898",
  "63745e4b893f44004c4adfce",
  "643e9bee8a389c983f38af32",
  "61a91390ff4b130049b2a1d7",
  "6393322f38e142004d8b98a9",
  "63e2576584167f109460866c",
  "647c8f2f6fdcd5c5bbed9348",
  "645a337ba23962cc1b2a835b",
  "65188ca70082d22b619da453",
  "618cfcfea39df50048a58968",
  "62a877d01d07810049a63e48",
  "6528490c71134fc00410dfdc",
  "619638147cd35f0048a012f3",
  "628dff68c53091004a2660d2",
  "64692ab0c0a92ee066d648a9",
  "656b16168cdf67954a3a64f9",
  "65889943d1478c9eb487192d",
  "645ebeb4e4fdadf66e1464e1",
  "64f71d30527a8be2030706dc",
  "63de25aa6a0a297a61b28362",
  "62e027e257806f004bd45cb0",
  "6398ed8419b378004b08ab38",
  "62882c8e928318004aa3630c",
  "62812bd59d77c3004a54ae81",
  "629388fbf5b05c0049676da0",
  "63f5364cb69a488c860ded65",
  "6280e2a2b4d82e004a738a63",
  "63051986d3d6e1004bf9515d",
  "64089e30b2be15399f1174d3",
  "632f972ae20362004bff523d",
  "6287fad82cfdd4004a6f292e",
  "63b6156319c3b7da09a17ef8",
  "62900622c168ba00498a636d",
  "62c993686671240049bc41e4",
  "63d5131257398a43e81c4459",
  "64f78c99e5363a7dfee31520",
  "627a37d28efa1a004a432d66",
  "6525c4a56ddd37e5ab4749e2",
  "64f3640904818c58efb47da0",
  "6280fbc9b4d82e004a739fb4",
  "64f09eb75177ebff191c2996",
  "62ec21505fd326004b2c18a4",
  "62bb66bb4d948600496a57b8",
  "63b57d54f1f31fd32a087253",
  "627c2a3b688160004a3749d1",
  "6415b6ad3050698c99a198e6",
  "624605c291abae004a36e897",
  "6339f0f4722234004b75d84b",
  "61c99f10d974de004b1592b8",
  "6248bcd03a7c88004a7d4a98",
  "628170d8f8b5dc004af402cf",
  "6363e762f18b27004ba458b5",
  "652e686629674b6b9b7485ac",
  "64c1804f6e3e937b07bdca93",
  "6470bf835e167f0d1afa9175",
  "64f4eb15fe432dae1583f3d3",
  "63bdc3433ef39fd1418b75bc",
  "64a01a11eb0b0791d0261020",
  "61a5251ae505e50048b48bf6",
  "63dbb3f048654847b936342a",
  "62a628474846e5004926cd54",
  "638c6c5059d281004b6033b8",
  "62a1e0193187eb0049c5def4",
  "62c6d3776530ed00499a1964",
  "630e1613d12d51004b0be0b3",
  "642a4f5e5314539563f36615",
  "62d9eed840da0b004a86deea",
  "632b227e82b9f1004b8f6df2",
  "64050eda020f43f50611ffca",
  "643e0656ee6da54df5e863f0",
  "64fb315534da932eae977a7d",
  "62c97c1e6671240049bb7ce8",
  "61c7a4e6d974de004b155941",
  "628227d2fa112f004a2b6e55",
  "649f0ddfeb0b0791d0259b01",
  "63caadf4fcf17527256d6641",
  "62bac4ba9047e100497aceb2",
  "635aa92969de9c004b79088c",
  "638520dce4538f004b47408c",
  "62ac208415f9110049724cf1",
  "6287b7d630bef8004a1e4112",
  "62de94732eb09a004bdc9ff9",
  "62a883b3f708cd0049d7f53f",
  "649738705e0799ec2c7cf48a",
  "63dc509ebdb027f35e95de0f",
  "620cf7a55f65a5004a9cf05d",
  "64cf8542ec45e1d8e8d49abc",
  "63eff6975ddc7dfc693af6ea",
  "63306908e20362004bffbcea",
  "6434ca2c30cc043915e49106",
  "631b05d0861101004b2edb3d",
  "64a46fe36b60aff5dbaa1a10",
  "63cc507552942213ef242621",
  "62951f21c77e29004986eccd",
  "62d181c8fa1bbd004986018f",
  "636b84eba4a834004bcd6dd4",
  "649559b82741cc5b1e1bf5bd",
  "641def4b5a418a529fffc1b1",
  "642c6d5feb7c4dfbb46a5f7e",
  "63c95e53361ffb66a514a59d",
  "63aaef574c1eff196c782d34",
  "64dcf6f109cf9b1c7ca4ba27",
  "62912557f5b05c00494d5061",
  "629406bff5b05c00496ba2b8",
  "61af4bdce16e0d0048c35bc8",
  "6280ffd7b4d82e004a73a57a",
  "62fac48b9ef8ee004b55101b",
  "64103ea34fb8f234628b0c82",
  "647f4e1f6cd028e28574de49",
  "649c87a3ce08f43bc31d2b2e",
  "64a81de551aefb32a023178b",
  "623ba81cab888f004a0b8344",
  "634476022f7c04004b383a94",
  "649d33b4ce08f43bc31d946e",
  "6458b66e3094dbc36ca0a633",
  "64de24e63a261927fc552c61",
  "63a61a67a70d98d233d2e57f",
  "631e037d1d45af004b61e760",
  "6286bda59e7a9b004ab424e1",
  "63b6a02019c3b7da09a1ab00",
  "63778de9d6179d004bb23c73",
  "632200481e6809004b7eecf5",
  "62736855d3d148004a7d90dd",
  "628d5a00a79c82004a18878e",
  "629c2dc1211b5c0049a37538",
  "62ff55bd4cb595004b7479b0",
  "6284cf3a05f0bb004a7a0002",
  "63b68fab19c3b7da09a19886",
  "6349412f4bad4b004bb36ca5",
  "64726e23649b6432fe079bf7",
  "63e1a6c37de8c3da5692fe95",
  "62d2a23a72c9e00049dcc522",
  "668d397bd70639a7bd2abff4",
  "62960ff3c77e290049a52f09",
  "6445679e277d0d703ba73045",
  "651b47acb6a95311c4e09539",
  "63978db119b378004b07b9f2",
  "648cc4386feb770d63948fe0",
  "6425ea7d726880644bd03fe2",
  "62f17004d46581004b0096b6",
  "65170dd2b24e74e99f66de2b",
  "663a3b488a4c7d98138112bd",
  "63a1141cbd4eba4156c5d36b",
  "6378d1ce83b2d7004bf43fc2",
  "62ebe915a5ca4d004b7d6867",
  "63dbe18448654847b9369b33",
  "61c4cbb57b0553004ad4a750",
  "63dbd96f48654847b9368e47",
  "646bf2d7cae270ffd0af6a8a",
  "648817707708f02304bc3976",
  "6613f29327f8146110437b77",
  "65a9aad189742120c3d515ed",
  "63ddf3ad6a0a297a61b26b11",
  "628bca6dcf90c0004a45dddb",
  "64ca71f875039af0db6f092f",
  "630de129d12d51004b099586",
  "62d863411afaa1004abb39f6",
  "65294d1cac7e34b83c346481",
  "62877c32740531004a5af8f8",
  "629b6ffabf3cc9004ade0828",
  "647d0fea6fdcd5c5bbedc029",
  "639367c519b378004b05a746",
  "648619856208e2c5615b9108",
  "62e432eb703ae0004baf9d08",
  "642a165b7178d7b65608b214",
  "638fc353891bfa004b8cba88",
  "64c3f6a1a0250ce3d7d22b51",
  "63f39f8e89def3d9121010f6",
  "63d547841f2cc41fd87288a9",
  "630e74a5d12d51004b10b0fe",
  "642883ce5314539563f2cf76",
  "63776cf0d6179d004bb2082b",
  "62da7b7e228c35004a18451e",
  "6290bab4f5b05c00493f2e51",
  "625b35f13cf5a8004ad5b7db",
  "62dd342d2785f7004905033e",
  "63c0ca79a52fb8a3b5c40d3f",
  "639f47ad9e613f004badef91",
  "6523f9710647bc8bea382d50",
  "63f168e8e9c63adf77168471",
  "643105a1f4b4f3068513712c",
  "62d029b58d93da00498a7d59",
  "64275b6c5314539563f26eda",
  "636a53fa00c2a5004ca1a876",
  "62b4968533dfd9004937fe31",
  "649cd287ce08f43bc31d72fb",
  "62f34a6f2bdd33004ba0f293",
  "646fd6c0d58947086e39fd93",
  "62b5ad4a1c4e13004948ba92",
  "62d68cf31625280049f130dd",
  "6478752c69beed4589d8a812",
  "629a17f758593a0049cdb88c",
  "634481be2f7c04004b3856df",
  "63efe62c5ddc7dfc693aefb4",
  "6280ac82a07bfa004a64090a",
  "63276c21286e0f004b731ca9",
  "6429de697178d7b656088319",
  "64ec8b936698ce5f3902f50d",
  "64669f0febfe5314836137e9",
  "62cd922bb9662200497f4e8b",
  "62b1b0b17b5b090049c30bfa",
  "62c8479bbff3ab00496d0233",
  "62e574f9703ae0004bbbc802",
  "645d4de13b0bcd777d8e21e5",
  "628d1846907dc6004a7a943b",
  "6281eadffa112f004a2b3ea8",
  "6474bde576d19600d8567f56",
  "6450299bdc098e8b83adf0a7",
  "641b21f99cbd3754a69954c8",
  "628fd48ac168ba0049847b07",
  "62b0e4500776b2004948caf6",
  "646281562383223fe5af0abd",
  "631e38e3075586004bee15ae",
  "64242553ac04aa9321107b40",
  "649c9689ce08f43bc31d40e0",
  "62dfabb8616ad8004c8a2809",
  "6280c922b4d82e004a7372a7",
  "64b3f28b07ad4a55d184a507",
  "651c1bafb6a95311c4e107fb",
  "64064471b2be15399f0fc1fe",
  "625bfae83cf5a8004ad5c2cc",
  "628124689d77c3004a54a48a",
  "631ded661d45af004b61d331",
  "62cff9a10a08740049ca4d66",
  "62a3ba000f6b62004ad3b7ab",
  "62af8f1d2a10f70049e50539",
  "617019af4691ad00499ddcf3",
  "62877562740531004a5ade9e",
  "63cfe190560527497683c424",
  "6497288bb3149e81dba1e168",
  "6462588b14229526379555ba",
  "63a1823ebd4eba4156c5f2b7",
  "63cf110c52942213ef25efb1",
  "639e69d19e613f004badc627",
  "6351b293a30a0f004d4cebb3",
  "62d0a3a78d93da00498efdad",
  "62d042e9d87b950049e23b0b",
  "633733de722234004b748fd8",
  "62fcd86984b866004bc63690",
  "64d98f0cad268f13db6b7e39",
  "6525aa551be4a5802dc13e00",
  "63362a63ad6b41004b24104f",
  "62811e7a9d77c3004a5498e8",
  "632efd8fe20362004bfed5d3",
  "63ea1d445074500ad146367f",
  "64839e365e7ab29d9dba21b0",
  "6468b341c0a92ee066d613f0",
  "63a9f1ed4c1eff196c77a8f6",
  "65ae977b0bebee9ff4da8dd4",
  "63f27513e9c63adf7716cc85",
  "643ee3975e6a3a15f27cd88d",
  "637133628141f5004c4103a8",
  "6399101319b378004b08c38a",
  "63a8cd163bde17003a2a614c",
  "645773903094dbc36ca04c7f",
  "62bd99cd3d25c5004af26311",
  "64d0fcdb2a6c35b14f23ddb9",
  "64625cea2383223fe5aee264",
  "62eb73585fd326004b254b9b",
  "6363c5d7d1c46b004b44c2df",
  "648644f45e7ab29d9dbae781",
  "63f5648ece4b81200237741a",
  "621bc31d3d0d42004adb47b2",
  "637e12a3be0a7b004bbdec9e",
  "63a2172f40bb2fef9d0d33dd",
  "666d324566e3925bf9c8990d",
  "64a159b5d3cf29d3d832acdc",
  "638fcf02891bfa004b8cc29f",
  "64fe09c4bdfa14f9c27c2bc0",
  "6517fe74b24e74e99f673521",
  "63ef33fe16fc3b0df0c29c1d",
  "62a1cc363187eb0049c4abad",
  "62d1c3a728b9fc0049c18621",
  "648d70acbd43f6e21550d194",
  "635d4c641bf2ec004b483da7",
  "62a32dfe32b0be0049a3a956",
  "616f25784691ad00499dd85d",
  "61f7dba19f0758004abad195",
  "656e518be5e49bbd21b5aa76",
  "6298e04f73a1800049741bb4",
  "65d295c08dfbdb5df44495a7",
  "6509bbcb5da4791344bef68f",
  "631c8677075586004bed0150",
  "6327751a286e0f004b731eed",
  "658446a415bff0f159be42ff",
  "64471de203ec71fc122ec2d1",
  "6494bbe42cd6cfd92f814106",
  "63bc8c8eb19a7c2599d1fd1e",
  "64d4095b2a6c35b14f25c217",
  "63c5b484dadd6783ff250a71",
  "64c27f6e7485ee04f0ececc6",
  "628110a39d77c3004a54837d",
  "62a0b8b1145da20049ba2c54",
  "64660fe11dfedbe692062974",
  "6356aabae3801e004b107aea",
  "63c968560819a01a8f100e96",
  "61e72c5078d330004aea5d3d",
  "62c713a7a05e260049dcbfc0",
  "65d93cc4683d63410e917db7",
  "64c825f228d955f9767626c4",
  "636ab75800c2a5004ca26d18",
  "637fdf7c10ffff004b68d332",
  "63a09374bd4eba4156c597ad",
  "6439658aee6da54df5e6fbab",
  "60901dd468d3be003ecacd64",
  "6335828b162274004b8425bc",
  "64302c7130cc043915e33fff",
  "63ac7fd63bde17003a2c56d3",
  "6451085adc098e8b83ae51fd",
  "64ca92ae153f67b83986edc7",
  "63accadd4c1eff196c797fd4",
  "63baf22e9c8dbc3dda2fc491",
  "632b663682b9f1004b90860a",
  "63f3b91b89def3d912104435",
  "63b61c066c59b6121d22d2c1",
  "6245b134c1017f004a193d28",
  "63a38b8b99de17c875706143",
  "6229f56c6343ea004a4ee4ce",
  "6342d70859b828004cbfec93",
  "62de694f2aa390004c786d52",
  "6280ebb0b4d82e004a73927f",
  "62e4397cb4f567004cc26079",
  "62ff73b64cb595004b76899f",
  "6458167e705921416efa4160",
  "635d060a69de9c004b7b17ba",
  "628e82b4519c34004a14b76a",
  "667b3aa3ac5c541e5b9aeb3d",
  "643c3155ee6da54df5e7b140",
  "62c2c8fe196b570049cac193",
  "62878edf30bef8004a1e00d1",
  "63b08eed3bde17003a2e54b4",
  "6461e55a44988549f3f70b08",
  "62a87e521d07810049a724f0",
  "6527393b45e8ab60062c04d1",
  "646cc8e138aa6aefd80f3dfd",
  "629546f9c77e2900498c0806",
  "62d030a48d93da00498ab371",
  "644323d236889f01cc0877eb",
  "62daf0882785f70049f1773d",
  "63efe9b65ddc7dfc693af13b",
  "62f3d6d4183c98004b5ff1bc",
  "649af07de056e0f42cc84258",
  "64296d265314539563f306d5",
  "6458875e3094dbc36ca08fa6",
  "64d327bdec45e1d8e8d65a15",
  "62fb42a584b866004bb51d5b",
  "652314480995a4d93f21229e",
  "6484b3ef5e7ab29d9dba85d7",
  "63e540e9218dd176dc6e8001",
  "62b58eaa33dfd9004939b820",
  "63e257947de8c3da5693be4e",
  "61a81e8f9651490049c5e776",
  "631e4754075586004bee4f52",
  "62c9a5ab6671240049bcca70",
  "633dc793ebc4c4004cfd17d5",
  "62af41482a10f70049e2ff8a",
  "6280fe0db4d82e004a73a30d",
  "65034228ed8b81621efa112c",
  "642315f52f5429c10e0192f8",
  "62b9e24c6807e60049bcf534",
  "62eaed84a5ca4d004b750bc4",
  "635e8b551bf2ec004b49312f",
  "635b1a481bf2ec004b466602",
  "63d6fbb01f2cc41fd8733f90",
  "666d4dc34e95d0611449fbb9",
  "649c477dce08f43bc31c362f",
  "629e466d56c9f70049b01667",
  "649c797eefc77a4973982e9c",
  "64e29bc9216a28a008de598f",
  "62a99ee9250fb2004a6133a0",
  "65a16aa2a24ce0f1396ba4bb",
  "6359255a0f322a004cc76ec5",
  "647a6ce76fdcd5c5bbed0704",
  "630ca7ca5c4e69004bd14b60",
  "62803509a07bfa004a63e430",
  "643823d8ee6da54df5e620de",
  "6482648981461d34e44c9c3c",
  "6451a186686ecf1100546d54",
  "63ced15e52942213ef25b9a0",
  "62821a09fa112f004a2b5fe0",
  "629b8ed50a4b510049aaa935",
  "63851172e4538f004b4730e5",
  "635ffc1a78f6e3004c258426",
  "6271ae982a0294004a02619d",
  "628e6a3c519c34004a119fae",
  "6197e129a70b770048ecd0ea",
  "641d4ef0168c760c92b1c017",
  "638a07dd640454004bdfb880",
  "6638c47d7cb5942c774627f8",
  "6321f6081e6809004b7ecccc",
  "61e068d85b295e004b14e0bd",
  "640603ca15e0ef0022942066",
  "63496aea8bc9d0004bf18953",
  "627ae8eea51b18004ab88702",
  "6318d1fff13813004bebb614",
  "63c9a77629d30af632f25564",
  "6377a8d083b2d7004bf340a1",
  "647c9df6c6a88c981d729b5f",
  "63927896d8efc1004b7d1ef5",
  "62dd04652785f70049035e46",
  "63600ed39c83fc004ce3741e",
  "652f15b3e0ad5b0b8ff2943e",
  "63ce872a52942213ef254d11",
  "64dcb6e2b0cfd45a221f7eee",
  "633f7468d37336004b272820",
  "657e2a5b051d2a18719395c8",
  "6442588c701f1f1d55a77d47",
  "640258c2020f43f50611382e",
  "628fffc6c168ba0049898486",
  "648dcb28bd43f6e21551100a",
  "6465371dda4203423777a82a",
  "6257b78c1e8a13004a2b182e",
  "6280cc4bb4d82e004a737538",
  "652c362137a80b80d9767095",
  "63b5c78c19c3b7da09a1459e",
  "63f6ebbdf8064505718aa8c1",
  "63627b17d1c46b004b4348be",
  "64595f8fa23962cc1b2a00b7",
  "651dc646ca70b91c337ef9c5",
  "62810f429d77c3004a54822b",
  "63e63713218dd176dc6f104f",
  "627fc79cba5a12004a3b7335",
  "633c50669a3520004bb76b89",
  "628bccd4cf90c0004a45df55",
  "647547e705605162f312014e",
  "628100d3b4d82e004a73a60d",
  "62c8373e6671240049b29047",
  "62ffecccaced7c004bf0a231",
  "637a097765d523004b257be0",
  "64542938686ecf1100560195",
  "6429e5a27178d7b656088743",
  "64f86675f89d024442fdfb17",
  "650b066c3110fb0522fd5518",
  "62ead57aa5ca4d004b73e404",
  "61f2905a9f0758004aba069f",
  "6245d47a675d5e004b1b583b",
  "63b4703399548b3f12dd6921",
  "642f01949e1cb48e9ef7d441",
  "6257361d1e8a13004a2b06eb",
  "647dfc8ac6a88c981d732d83",
  "625868856fbe06004aaeb0ec",
  "62ee1da3d46581004be6586e",
  "65a83d2d0f906b14b2a5192c",
  "64f5a56a336878573d6676b5",
  "6294e686f5b05c004987bc70",
  "6390df6a4c5c59004b69cba3",
  "63a57c22acd16a4f91dda89f",
  "63b4a48e99548b3f12dd9834",
  "64aee87726f468ae31a8f6f0",
  "626d2c659de6a8004b99bb22",
  "62877f56740531004a5b0166",
  "62a784764846e500493808d0",
  "62a449fe0f6b62004ad85d51",
  "64ab08ec933996d4ff0ee8a8",
  "6509b92a5da4791344bef34f",
  "63a0a9a3bd4eba4156c5adb6",
  "65a0438d099cfb23c242f44e",
  "620d8d815f65a5004a9d0a93",
  "6285558dcef116004a19c93f",
  "64d3a9542a6c35b14f256f81",
  "632fb950e20362004bff57ce",
  "63f3853d84b516ec464f957e",
  "61e6ecd068c124004b5e506b",
  "63495d192edfc5004b9e704f",
  "637f2fa3be0a7b004bbed684",
  "62e50a89b4f567004cc9d9fd",
  "6392205cd8efc1004b7cc684",
  "646112bae4fdadf66e151bcc",
  "64f213d104818c58efb3cfea",
  "636cc4e263ce2b004c1c76ee",
  "626e92f7398b2a004a9228f0",
  "62ffe96caced7c004bf05e3d",
  "649d35c7efc77a497398d78d",
  "62cc190aa991840049154d9a",
  "62f8b33f9ef8ee004b3c9fa5",
  "636c2abe114750004bc002e0",
  "62a2252e32b0be004990b94a",
  "643728336b4cfeccc9f11f74",
  "62856ad5366045004aefe3f7",
  "63643745f18b27004ba4ea42",
  "6280f645b4d82e004a739a53",
  "61afe4ac0c753c00480a5bbb",
  "630b86a908cbe0004b56cfa2",
  "65c1348330c0bb3b085f4bd8",
  "63080174387b89004b27d055",
  "6362abdfd1c46b004b43b757",
  "62e6ca9e703ae0004bc90583",
  "646f3ad5e20eea9869457e80",
  "62dfe2c7bbc301004be3c0bb",
  "63dc1c34bdb027f35e95b365",
  "640914d8b2be15399f11de0a",
  "62d5c42628b9fc0049e0cf7f",
  "64ad1426e1be534b84c99030",
  "622dd8d9c8cac4004b1395c5",
  "64ab1a6612c994853349c515",
  "63dc0d10bdb027f35e959e61",
  "634c6ccad8ea42004b2c837f",
  "628fd8aac168ba004984ee67",
  "63e42b4b84167f1094622476",
  "641376163050698c99a0c2fc",
  "6445ac5636889f01cc090331",
  "63dad194fd000d43405a0889",
  "61c6089fd974de004b154418",
  "6255545122baf3004b88b832",
  "631471ae6091e2004b267b00",
  "64b454a3515b6150efa7e46a",
  "6479ad6828fcb61b88028259",
  "649466e8f6cbc5dfe216c1e1",
  "6281176f9d77c3004a54906a",
  "63acb9553bde17003a2c9de2",
  "62aa07f0d3f3560049ed4805",
  "6306367fd3d6e1004b071359",
  "6682d8674d7a61e0b4459791",
  "641d6d64f652599b766414e6",
  "6473c48e5e167f0d1afb7acf",
  "64efb3d7f345686d1df81353",
  "652904c8ed5769d88d3982d3",
  "634c12b68bc9d0004bf35064",
  "630677e15d246c004b4b7e8b",
  "63cb261a52942213ef23d43b",
  "62821d6afa112f004a2b646e",
  "651e6b8da30179babed9e854",
  "62a263625949110049d272d0",
  "6488e9d5e966693f130115f1",
  "6488de4fe966693f13010e24",
  "64b01e991c02d45dcf3d7b4e",
  "64006986e65f80346ce8bdd8",
  "63ef71240d8b65eeb3f11292",
  "62b9911cfc434b00498284f8",
  "640f9ee081ffb000ac872060",
  "63c34f81df9ae32d31b578c3",
  "631c5f861d45af004b60e03a",
  "61efe3129f0758004ab9ac55",
  "6388bdd22e9709004cddb706",
  "6408e919b2be15399f11c850",
  "6501be85b7eb819601aba966",
  "63a333466404370a54f45506",
  "6444d37f277d0d703ba70c50",
  "62928f18f5b05c00495f43f4",
  "62eaf88c5fd326004b21010d",
  "63890c33c06f39004b07ff9c",
  "63b21f173bde17003a2ebc17",
  "628fcc89c168ba0049837232",
  "631103766091e2004b23ff7f",
  "6299068f73a180004979c25c",
  "62d654d628b9fc0049e5c306",
  "664e52b2598bab8be0efee02",
  "64ce5a6d2a6c35b14f22e652",
  "628770b4740531004a5ad36a",
  "6528516271134fc00410ea20",
  "62eab249a5ca4d004b7283b9",
  "628a90cc02d452004aedccf6",
  "641b10477730e3b2bfc44dc4",
  "637e4bc2be0a7b004bbe441f",
  "652846d7539bc552a3b21107",
  "6463eb2c2383223fe5b0437b",
  "642af73aacaa934b5b46cb83",
  "63fce25f2b5ffb01b43c9e54",
  "645fb4465ab0dcee3aa86ce8",
  "6529399fda64fda58844b801",
  "61a781cc9651490049c5d805",
  "62de66a12eb09a004bdb0c66",
  "63d6cae01f2cc41fd8731c42",
  "632f8ef8e20362004bff50c7",
  "64c4270528d955f97674d5bf",
  "64f736c1eb2ebd205ea6b26a",
  "6518aa48b24e74e99f6776f5",
  "650b2e9b3110fb0522fd6d86",
  "628d872aa79c82004a188ec1",
  "63b9d76a567febd3fe4db3d7",
  "62954e45c77e2900498cf427",
  "63e81526f65640ffb3dd6176",
  "66d84d2b5736e8dbe9877e73",
  "641204182c68e51c6565d5d2",
  "6477926e30882a85b524a788",
  "63400e00d37336004b2788f7",
  "631b4e4b1d45af004b602a60",
  "639f9a35a00ffe004baf7e13",
  "6314d0476091e2004b26da55",
  "64cbaf5771e7da0b9596f268",
  "628e8280519c34004a14a9a1",
  "626cfc9e9de6a8004b99b797",
  "6429e91d5314539563f33776",
  "6287c4a42cfdd4004a6ed9bb",
  "629b6ce6bf3cc9004addf495",
  "62c3898fabfb3a0049b10d54",
  "6463948714229526379654e9",
  "6429f91e7178d7b656089b3e",
  "649c52adefc77a4973978d75",
  "62c579331a21be00493b8019",
  "61d0b79eaff234004a724b3c",
  "629e2630c58d57004aee242b",
  "636b6d8b00c2a5004ca3121e",
  "64a5ddbd9c9a848ca7a73ae1",
  "6373f2ef893f44004c4a96a5",
  "6403541e020f43f506119892",
  "629bb7ee211b5c00499f4b6a",
  "6431a623f4b4f3068513a1f3",
  "627c057b688160004a373c81",
  "62700f24978cfe004aa28e26",
  "62ebafc1a5ca4d004b7ba657",
  "636cd4038feb83004baab0a8",
  "6290bb9ff5b05c00493f3a5a",
  "63487818b1bcc7004cedfede",
  "6358f6b09c3da5004b2a0635",
  "63916fb84c5c59004b6a146b",
  "63a054a6940b7ea38b8767a8",
  "63cebd5e52942213ef258425",
  "631b332d3ec078004bdebcbe",
  "6473f094649b6432fe07f5fb",
  "63c5cd440afb41bd3b4ebb8b",
  "6328954309e01d004b5ed7b2",
  "6515375d5a2a491d030393a8",
  "62bee10c2b10d0004a7a974d",
  "647c9a2cc6a88c981d72983a",
  "6391e480bcdbeb004b8866df",
  "64989ccab3149e81dba24118",
  "64b5406eda49796346e795fb",
  "629de548c58d57004ae504fc",
  "62949399f5b05c00497cfbb3",
  "64ef8c985177ebff191b6e9b",
  "63779bcfd6179d004bb26ac4",
  "642c10519e1cb48e9ef5abe9",
  "665b4ebee8b60172020dd14a",
  "6280bd8ba07bfa004a64166a",
  "62e3fd351c9bdd004c11d9c4",
  "62b5be0d7876400049bb25c4",
  "636ad47bcbebab004c4c648e",
  "64a1c37feb0b0791d026cbf0",
  "6385d4b8e4538f004b478cf1",
  "63dd8c166a0a297a61b24cfd",
  "6377bf3283b2d7004bf36ad4",
  "62a8737df708cd0049d6f15d",
  "63566cf3ea3770004b531ec4",
  "629c987a211b5c0049a784af",
  "643ff29b5e6a3a15f27d3ace",
  "64679ce6c0a92ee066d5a3be",
  "6281029eb4d82e004a73a828",
  "624182c83f1132004a3bc56c",
  "6299f45c69a22c00491c7d7d",
  "613f3b760b27bf00475c1670",
  "62daaa3b228c35004a19cbab",
  "63e4148c84167f10946216f0",
  "628f5e121714a800493b5f90",
  "635028f7b21dbd004ba0bebc",
  "63f2122e5ddc7dfc693b777f",
  "63dd79fea114166d6d864c92",
  "63779730d6179d004bb25c09",
  "628d2846907dc6004a7a9cbb",
  "645a8c0d920c70f93e7e0d4a",
  "630674b8d3d6e1004b0a74e4",
  "64a7f6812d2132f5728c2788",
  "62f949de84b866004b9f6ef5",
  "62ad88d52a10f70049d904dd",
  "6356816032b60e004b792f92",
  "63e2ad8984167f109460fe88",
  "6286bf659e7a9b004ab426b2",
  "644cdd21fc4e77e44fe284ab",
  "6295279fc77e29004987ed27",
  "6318ddb6cce39d004b57b59d",
  "630e06edd12d51004b0b446c",
  "63499b9d8bc9d0004bf1fe68",
  "636029439c83fc004ce3b86e",
  "6363d113f18b27004ba42444",
  "647a26a36fdcd5c5bbecdcd8",
  "63ba83068d7f46fac4612b52",
  "63e5626f218dd176dc6e993a",
  "646be4d9221079c32e49fd27",
  "62d4082672c9e00049e7800b",
  "636d89fac51245004cf98fed",
  "637fca0810ffff004b68c709",
  "637d54b9be0a7b004bbd8200",
  "60c86740862e22004702712a",
  "63b5020537bb074c81c25cb1",
  "6432e4bcf4b4f3068513e08f",
  "62a8d11814862300498b40c1",
  "637f3aa7be0a7b004bbee58d",
  "644e52d2dc098e8b83ad24a0",
  "6469fe68c0a92ee066d66d2b",
  "61edecacd2bbcb004a4cacf4",
  "628fe5c5c168ba00498697ea",
  "636d7a550b6aba004b481cdb",
  "6341a831d37336004b286f51",
  "66dc2aafd94730ac5c8e04d8",
  "6286a71c9e7a9b004ab40bb1",
  "63ceeaf9fcf17527256fad15",
  "62af42182a10f70049e30a7a",
  "64b3a74007ad4a55d1849422",
  "621c025c3d0d42004adb5408",
  "6388b12cb5a7a4004c27bdd6",
  "636a3ae98359fc004c619e27",
  "63ab64c73bde17003a2b7a69",
  "63d7393b1f2cc41fd87359ff",
  "62929e16f5b05c00495fc05e",
  "62b59e7633dfd9004939c4a9",
  "646cde4127b05007e36d07f6",
  "63bdbe8e563fc4caa93a499c",
  "63ac98f63bde17003a2c7dcc",
  "62c287f5196b570049c90cda",
  "64ea0bcae9598cda09f912c0",
  "6519bf33b24e74e99f67cf8c",
  "640b7123dacbabab00eec949",
  "6290e1e1f5b05c00494420fd",
  "62e11890d36297004b6ce991",
  "632526ae286e0f004b71adbc",
  "63a884e33bde17003a2a5064",
  "631c474a1d45af004b60cad7",
  "62879b6330bef8004a1e18c6",
  "64af1ccf26f468ae31a91585",
  "61ad3fa7e16e0d0048c30197",
  "64f7750ce5363a7dfee3051c",
  "62805928a07bfa004a63ed38",
  "63d312e4e77ea3648f7e1c37",
  "628901d63b1522004a2d7739",
  "62d6ba7c1ee7e0004985ef31",
  "62f8283b84b866004b933789",
  "64bac0ad943124da8e60dd23",
  "64045dff020f43f50611ce68",
  "64ef94c6f345686d1df7f6c7",
  "62c575211a21be00493b52f0",
  "628cbaba82b90c004ada671c",
  "63dbfbf548654847b936da2e",
  "6336bb1b722234004b73dcff",
  "629909fb73a18000497a2064",
  "63ea83ae5074500ad146a818",
  "6514609dbdc10ddcde781cea",
  "62c5f1e0ff3c9d0049f505da",
  "62fef5b84cb595004b70823f",
  "6356c2bce3801e004b10c7fd",
  "63cdb1ea52942213ef249eb3",
  "64d0c8adec45e1d8e8d50a40",
  "6494246aa0a8dcfd35901a76",
  "637b7c82b33b6d004b64a7df",
  "6279778258cbeb004af30e94",
  "62946913f5b05c0049778210",
  "63c571b2df9ae32d31b69448",
  "62acbd9715f9110049764663",
  "62dd7616c9672e004a261a12",
  "631ddf011d45af004b61cbe4",
  "641b31390f8e0c52fcf68dcd",
  "632385b799a775004b214618",
  "6412f83c2c68e51c65664511",
  "62cb514cc8996a0049e78686",
  "64765bce9a69799b195a5f5b",
  "62a1dfc73187eb0049c5d5b1",
  "63e2af2a84167f109460ff0e",
  "61c632bbd974de004b154713",
  "62aa3d7fd3f3560049f0885f",
  "63d639bc1f2cc41fd872d68d",
  "6288fb3b3b1522004a2d75bb",
  "63baf2268d7f46fac4617917",
  "64ce02e7ec45e1d8e8d41d21",
  "63b3520253aff3f86b687056",
  "63da777ae704008b86609f22",
  "61ace3e8e16e0d0048c2f595",
  "62dd3574c9672e004a23828f",
  "63863372394b10004c698a06",
  "63efa4e3e9c63adf7715e429",
  "640a4686b2be15399f127cbf",
  "63a645f73bde17003a29af6d",
  "638887682e9709004cdd0ec5",
  "649c7a18ce08f43bc31d0215",
  "63c06efca52fb8a3b5c3b9ad",
  "637fa88cbe0a7b004bbf4f52",
  "63a728284c1eff196c7707da",
  "645821aa3094dbc36ca0844d",
  "6392a315605b27004b1bb21d",
  "65291be2da64fda588449ea0",
  "640cebe127319600975147f9",
  "653fa52f323a26ce97256e3e",
  "6287df482cfdd4004a6f08b0",
  "63357c2aad6b41004b22f5b5",
  "64280d905314539563f29edb",
  "62af1a3215f911004983c780",
  "633ad97b722234004b767bb9",
  "636aa60500c2a5004ca24084",
  "619406cde393d900487bafef",
  "62be212b3d25c5004af640a7",
  "63186662f13813004beb2676",
  "6347f3f6b1bcc7004ced578b",
  "63fac30d9c5cc055079e5740",
  "645d247f3b0bcd777d8dfb96",
  "636cf263f3eee1004bfd4b10",
  "66534ebadca83cbf21714eb8",
  "6280bf17a07bfa004a6418b7",
  "632a2faf09e01d004b613ba5",
  "64cfc29dec45e1d8e8d4b638",
  "64caab37153f67b839873b68",
  "64f4e35804818c58efb4d42c",
  "6286dea49e7a9b004ab43566",
  "63f37cd9009708ae9eb82f42",
  "635e29e41bf2ec004b48d0a9",
  "642091668b0db5a276113948",
  "633f093b59b828004cbdb2eb",
  "6299f6cd69a22c00491ca078",
  "62d2471c72c9e00049d9b314",
  "642a63897178d7b65608c3be",
  "6297ca603b47dc00494affc3",
  "63eaa1534eb1d1e6a0737d7b",
  "619b5e50a70b770048ed315b",
  "628e8dff519c34004a1623fe",
  "62fdde76dee809004c507a00",
  "642311ad9297c660278d672e",
  "63e52a67f65640ffb3dbb914",
  "62a45cfa0f6b62004ad901d9",
  "62d54e0d72c9e00049f23529",
  "643e88868a389c983f38a0f1",
  "638bda9459d281004b600423",
  "62a863fcdb598e0049c56876",
  "6372d23186f1aa004b74e09f",
  "64174b433050698c99a1faa1",
  "62811e0a9d77c3004a549858",
  "639ee1c39e613f004badd187",
  "632316c9b27042004bdb5421",
  "628c8cf182b90c004ada3f04",
  "6363b9d2d1c46b004b44b00c",
  "63a341696404370a54f474c7",
  "62a7685a4846e500493669b8",
  "63a7b4fd4c1eff196c77230c",
  "63a3586899de17c875702fd7",
  "63dfb150a114166d6d8765b5",
  "63b889349c8dbc3dda2e8cd9",
  "63917c3f4c5c59004b6a150f",
  "63506680a30a0f004d4b5dbb",
  "63a0747f94e0acdc815ba8e0",
  "648678325e7ab29d9dbaf3cd",
  "6287716e740531004a5ad551",
  "62eafa67a5ca4d004b75bc16",
  "64015aae997289fcd589d3d4",
  "647cf8096fdcd5c5bbedb630",
  "6298e04f73a1800049741bb6",
  "635d53be69de9c004b7b864a",
  "63d94b210f65ea183fbe0c66",
  "64db6893ed5b9ef9d313bee8",
  "642dbe6ceb7c4dfbb46b53da",
  "62d80432a7f81e0049555766",
  "6323a26e99a775004b2167d8",
  "636e386f8141f5004c3eff80",
  "62f2c5e52bdd33004b9b215f",
  "61d26518aff234004a727955",
  "647f27688ecae893390b251d",
  "62f29ee22bdd33004b99c30c",
  "63e68ebdf65640ffb3dccac6",
  "63a3282e127dcb970448ded5",
  "6329682109e01d004b5f4ad0",
  "651172cb494c9d61670a60ef",
  "64d14c3d2a6c35b14f245c08",
  "635a97bb3632a2004bd072b8",
  "62878b2030bef8004a1dfa80",
  "63ea6de85074500ad1468081",
  "635e70b669de9c004b7c3cb9",
  "62c978cbbff3ab004976a639",
  "62feb19baced7c004be179fd",
  "6281123d9d77c3004a5484f7",
  "63fba2449c5cc055079e91f4",
  "6324df8c99a775004b22f2f2",
  "6289f65c50b204004afefb0f",
  "629b4786bf3cc9004adc5ba9",
  "62861c4ad0bf84004a5be144",
  "62744279369230004afa8ec3",
  "634be6ea8bc9d0004bf31a12",
  "62797b0a58cbeb004af30f31",
  "64ab33e1933996d4ff0ef496",
  "646239af2d228d858b55b339",
  "64c25dfb152cc3adde3f81ac",
  "6373427786f1aa004b752eb1",
  "665f268da10a8b17dad1aba4",
  "615f33463ec4a30048cabb9a",
  "63411b30d37336004b283a5b",
  "629e48bd56c9f70049b06b2a",
  "63b5a61d6c59b6121d226413",
  "628b7d9d81ea43004bc96065",
  "62f92e0e84b866004b9e22b2",
  "630cf05bebb7d8004b25ac73",
  "62a4f9510f6b62004adc1303",
  "6369aa16cbebab004c4ac262",
  "6395091819b378004b065b4a",
  "63500da830fb13004b257467",
  "641d0629168c760c92b1b4d0",
  "65afc56957d4e651c6fe6b72",
  "645b75f6a23962cc1b2b6474",
  "637c17de1c56b7004bb20adb",
  "637fffe410ffff004b68edfc",
  "64025096020f43f5061133c7",
  "634688e1f51648004cf521ee",
  "63cec2ef52942213ef259917",
  "63fa76f22b5ffb01b43b5287",
  "630e6d3fd12d51004b102e34",
  "62ead2845fd326004b1f59c2",
  "63779795d6179d004bb25c53",
  "65df294a59bdce82ab1f467f",
  "62fe2f0d4cb595004b6890b4",
  "6442cfc536889f01cc085fa0",
  "643fc4535e6a3a15f27d1e76",
  "63937b1c19b378004b05bb4b",
  "641dcd0f8b0db5a276103640",
  "6287c6de2cfdd4004a6ee14a",
  "62eb86aba5ca4d004b7a41ef",
  "642a7f877178d7b65608d704",
  "64997901ed33f04f6f7dd762",
  "63527e4da30a0f004d4d8011",
  "631453af6e7c9b004bef03ad",
  "619ccd2670898e0048687a0b",
  "63ba10c0567febd3fe4dddcf",
  "62beddaa2b10d0004a7a73ea",
  "650c0a14263aec6283eacc4f",
  "64d52ba296f5cd4a123fe8bd",
  "63496260e48ca9004bf53bdc",
  "63c502e0a54c9b18e96fbc01",
  "62e1ab3dd36297004b7276b9",
  "6286a7399e7a9b004ab40bc9",
  "6294d3d0f5b05c00498548bb",
  "63ef853ce9c63adf7715cd14",
  "634063abd37336004b27d0ce",
  "63487680f27224004b14a16a",
  "63af7ed64c1eff196c7ade8b",
  "65636772cfea0bfb3c475537",
  "63d725343f1ecbaa3b066a5a",
  "62f8e34284b866004b9b7299",
  "64c4d79fa0250ce3d7d276ca",
  "62b077e9f53bc4004950982b",
  "63fb60d49c5cc055079e7011",
  "633f36bad37336004b2707f6",
  "641b57b39c8a76bb9bfaf342",
  "61d94fb069a759004a80a1b4",
  "619804bba70b770048ece87a",
  "631e404d1d45af004b623ea4",
  "6393619812e52d004bb92767",
  "650c3c463110fb0522fdc27c",
  "6465997e7d47e17fd277159c",
  "63eeb27816fc3b0df0c26a33",
  "6596ddb80efbdd0b08bb19b4",
  "648cc043bd43f6e215509f3c",
  "642c3eadeb7c4dfbb46a210c",
  "63c50ee2a54c9b18e96fc2f3",
  "62eacd6da5ca4d004b73b732",
  "6301b0c2aced7c004b03a0cd",
  "63d7b85a4e39c43a80126e11",
  "63032cb74cb595004ba179ff",
  "64207a118b0db5a2761130f3",
  "64654c1fda4203423777c508",
  "623b4fcfab888f004a0b733c",
  "6287f36f2cfdd4004a6f20b6",
  "631515106091e2004b271468",
  "65131d262472250b292cf336",
  "63dc832048654847b9375df7",
  "635d60561bf2ec004b486311",
  "63f65909862dbf4467f1bd56",
  "6280b7f8a07bfa004a641113",
  "635aa4a969de9c004b79012a",
  "6288a8354cf66b004a1cb2cb",
  "64936b63c42945f67c930461",
  "62a257065949110049d1808c",
  "65251f78949adf7bdc0ae176",
  "63e962df218dd176dc709887",
  "640e76e7273196009751af85",
  "64b9ab9cbc573a5cdb830d9f",
  "64a5e9da9c9a848ca7a743d0",
  "642b1369acaa934b5b4701f8",
  "64410e524afe85f36b6030d2",
  "62c2ccecef6dfa0049f7d55e",
  "63a6a7693bde17003a29ba4a",
  "63dc35cf48654847b937251f",
  "62880381928318004aa33a05",
  "634958a52edfc5004b9e5ce6",
  "6477b9e76712588cfb4b3ba7",
  "65abea5cc5118ba49e19ff7f",
  "64ebab59e9598cda09f96893",
  "62a0e2f43187eb0049b51c50",
  "652d1a20f8bf8db6c719c884",
  "63b5b07519c3b7da09a124df",
  "62b466f39418b400494f3fe6",
  "6239bfecbd562d004a67726d",
  "6468f458c0a92ee066d62c48",
  "6425d350f5774c37ae4760d9",
  "621a1a1d3d0d42004adb1e61",
  "64b1706707ad4a55d183fad1",
  "62878f8230bef8004a1e01ae",
  "63a26b1eed3f89a7527d6cd3",
  "65637ef72d07ccf8e733417d",
  "65194accb24e74e99f6798e3",
  "628fe504c168ba00498691ea",
  "62ab549515f91100496d54cf",
  "62b8a0511c4e1300494e84a6",
  "6394ba6319b378004b06421a",
  "63c27c4ddf9ae32d31b52cd3",
  "62b9fe966807e60049bdb5d5",
  "64300782eb7c4dfbb46c52eb",
  "642eeca49e1cb48e9ef7bf31",
  "64ae930e8015bd4737d9cf59",
  "621df8d03d0d42004adbaed4",
  "6373cc89893f44004c4a4d47",
  "6326eee7286e0f004b729d41",
  "651dd0ba45184d5cd1ec6c22",
  "629636cbc77e290049aa7ce5",
  "61d0b3c6aff234004a724979",
  "630ddb89ebb7d8004b2f6dc2",
  "63db06aae704008b866167a1",
  "628e133f03c7d7004a64b66e",
  "64ea11664518086a73c74dd6",
  "6360dca2af44a4004cede323",
  "62c98294bff3ab004976d6aa",
  "635180cc575b22004bf6ba74",
  "62882b45928318004aa36170",
  "629694a55b006400497b7839",
  "62e50dde703ae0004bb7c962",
  "629bcbbb211b5c0049a00888",
  "63ea6b0f4eb1d1e6a07337f6",
  "6353223b575b22004bf89005",
  "632ef4aee20362004bfec7c1",
  "61b955835ecee90048749212",
  "63509afb575b22004bf59b82",
  "6145b959f9fab600473d9f9c",
  "6280ad4ca07bfa004a640ab3",
  "629bad76211b5c00499ee1e7",
  "6381cf49bf4577004bc5907d",
  "62d05b6a8d93da00498c78d8",
  "632361c999a775004b210977",
  "6345f3bef51648004cf4c746",
  "63c09afda52fb8a3b5c3e242",
  "64825d3481461d34e44c9518",
  "63a3619b19dd48663b15c82e",
  "65200a5e0995a4d93f1ffe48",
  "645c12c019778803b1b3eb75",
  "636d82af0b6aba004b48279b",
  "6496a8a5b3149e81dba19bf3",
  "660145c2b8c1adba48f76122",
  "6405cac915e0ef002293e4a9",
  "645563493094dbc36c9fb89a",
  "62820977fa112f004a2b4ee1",
  "64b6d98d515b6150efa972b9",
  "6385e66eb11eb3004bc9b444",
  "6390b3194c5c59004b69a9af",
  "6494b41f5ce8da4dccf1418b",
  "626a931bc3d20a004af71078",
  "63da7ebde704008b8660a60d",
  "6294b0f1f5b05c004980e357",
  "64d38926ec45e1d8e8d6a559",
  "64f33e9efe432dae15838160",
  "6606e05891d9173f001f976e",
  "6203ba5058b09c004abf910e",
  "62811f5c9d77c3004a549b39",
  "63b338d915cee34fa5985b5f",
  "63026e2e4cb595004b990d33",
  "616d7f34ebe6a80049d03ca4",
  "6481de934f64f7c918fee3fb",
  "63ddb1cea114166d6d8679a1",
  "6356b73505572d004c394db1",
  "64a9d16112c99485334971c6",
  "6280ef2fb4d82e004a73958c",
  "641b51f828a82a8c84ed3f9f",
  "6327967099a775004b24b011",
  "6295fd95c77e290049a310b0",
  "62c6bf6dff3c9d0049fb9801",
  "650f35c3d2eb8245f49d6760",
  "622cf085c8cac4004b13892c",
  "64eb3b834518086a73c7800f",
  "63867a302e9709004cdba027",
  "63d7e2f8e794926f767d7098",
  "63910cd84c5c59004b69eef5",
  "61c9faf9276a59004a8c52e6",
  "6467ae98b07555c2a6094875",
  "632737a6286e0f004b72edc1",
  "6405d54115e0ef002293ee64",
  "6356e25905572d004c39d6ab",
  "63ba16d19c8dbc3dda2f4fcd",
  "62dd430dc9672e004a240379",
  "62cd48b717d374004943f61a",
  "63d53ab61f2cc41fd87283a3",
  "62ff5ada4cb595004b751179",
  "62ea9e8a5fd326004b1d535c",
  "646bbcd4221079c32e49d636",
  "637bcf811c56b7004bb1c5a2",
  "629c7ee8211b5c0049a66193",
  "634906deb1bcc7004cee44c0",
  "628539e8cef116004a19b4e6",
  "652568cc949adf7bdc0b1c6e",
  "61a24a40871d690048b31da9",
  "625730a91e8a13004a2b0505",
  "647658e19a69799b195a5c41",
  "63d3e6c71f2cc41fd871a590",
  "6394760619b378004b06264c",
  "6372c4e0d0e8a4004bebb338",
  "64f0f226ee219696ae94e2d7",
  "63358498ad6b41004b230b96",
  "62d8e77e1afaa1004ac05c90",
  "62de74df2aa390004c78cd12",
  "63b9be94567febd3fe4da1d0",
  "63121ed26091e2004b2528ae",
  "63e2c32b84167f10946111f1",
  "6256d09401a7b5004ac4fbd9",
  "6420c9305a418a529f00a674",
  "6356f47805572d004c39fb3a",
  "65319edd2e681a302f23ea34",
  "651d2f74477fb2f500a8ade6",
  "62be4a58486b650049950ea5",
  "6253128097ab13004a55d85e",
  "627a2a9b8efa1a004a4328fb",
  "633016bf9d2fd3004b34a9e0",
  "62b767397876400049c07837",
  "61ffff96377bca004b2ccc6d",
  "6373f86cdb65c2004b275e26",
  "640db17d2731960097515e3d",
  "637c3e93b33b6d004b658c97",
  "636a0147cbebab004c4ae3ee",
  "63fe23912b5ffb01b43db25f",
  "650239b39c2923823545acf0",
  "651c64b0aca9419bfe6565ba",
  "6329bf81db9ab3004ba2ecbb",
  "65f9d984991f8333dcce7693",
  "64f3042404818c58efb4405a",
  "631e535e075586004bee5f89",
  "64487e4202675a693dcc6b4c",
  "63cc515052942213ef2429c9",
  "634121d759b828004cbf28d3",
  "6139bfad9105c2004886ddd5",
  "628a56dd02d452004aedbd0b",
  "63cf1a6152942213ef25f6f6",
  "6247674852b323004a4ff3c3",
  "629624b1c77e290049a7ecea",
  "62af87e82a10f70049e4c198",
  "62d145346b4763004960facc",
  "62923a75f5b05c00495c3b25",
  "63d114e1f49e884bde635fbc",
  "62a8869e1d07810049a7d58b",
  "630e75cdebb7d8004b3758b0",
  "64cad59f394d741bea2ca2df",
  "65106057d2eb8245f49d914f",
  "63e01bf1a114166d6d87a65b",
  "6357173fe3801e004b1160d6",
  "6408fbb9910f3b483c0997a3",
  "641733f93050698c99a1f2be",
  "6330a3619d2fd3004b35234c",
  "64545c5a686ecf1100561493",
  "64b871fc9c80d5cddfc8fd34",
  "61c86814d974de004b1578c4",
  "64d8b726ad268f13db6b51e9",
  "65a06396099cfb23c24332c1",
  "62e9edc5a5ca4d004b6a8bc2",
  "63a60cfd3bde17003a299090",
  "635d2ade1bf2ec004b481bf9",
  "64a7268ec991b3450cf3cd53",
  "64691df6c0a92ee066d637a6",
  "6463789514229526379637e1",
  "62a9219af87c5600497c7efd",
  "63d7ec56915797b3a8a0aaf6",
  "635f99d569de9c004b7ce82e",
  "640decea16dabd973d714889",
  "64ca653375039af0db6ed84e",
  "62ddaf43c9672e004a27c5c0",
  "61c0f097bf3697004bc41d05",
  "639b49f3e40cb9004ca429a5",
  "635a51809c3da5004b2baa0e",
  "63d592ae1f2cc41fd872aa19",
  "63d645f91f2cc41fd872da75",
  "62aa724bd3f3560049f469e1",
  "62929886f5b05c00495f955d",
  "628b5d5781ea43004bc94c3a",
  "62becbfe10375500490fa01a",
  "6273edc6d3d148004a7dab6f",
  "64ef529ed6b1f82782271748",
  "64492ed9687f1d96e48e03e6",
  "63f8a323f0b57f8efbe1fd7f",
  "638a14670d6970004be882ac",
  "65281ea571134fc00410ae3d",
  "6485d55d5e7ab29d9dbac635",
  "64dcd037b0cfd45a221f9545",
  "6294d8f3f5b05c004985e757",
  "62a9c389e4b3780049432ad6",
  "62ae18ca2a10f70049dc60aa",
  "638521479667bc004bc45ae0",
  "632dbc4ab4d69e004b585495",
  "65aac405d37dc355b5c12736",
  "62b23de1599a3400494a1eaa",
  "637e6f1210ffff004b67bdc9",
  "65393beafcb3af286d9c6ad8",
  "62acd0532a10f70049d47237",
  "659b0a878a82ed0de4e1e4b7",
  "6356fe1905572d004c3a07de",
  "634bc689d8ea42004b2bfacf",
  "63418a5a59b828004cbf4d7b",
  "6347da77b1bcc7004ced3939",
  "62de55d12785f700490fc42d",
  "613f589c593b6e00478c2148",
  "647b70ca6fdcd5c5bbed4a4a",
  "63911c631cffd6004b73a5ac",
  "640c8b70dacbabab00ef19a3",
  "61917312d2ad0f0048680e46",
  "62c360a2abfb3a0049afedac",
  "6290ed00f5b05c0049458c84",
  "63a95fe33bde17003a2a76c9",
  "646211d4b843a834c9e038e2",
  "61b10e43cd9c9d0048086f0f",
  "62f50ca60adb71004ba20684",
  "6296825a5b00640049792208",
  "62a86538c697e00049bce6af",
  "63d02a48090c1a20d0cf984c",
  "6469230eb07555c2a609dfd0",
  "60c724ff07d4650045e53649",
  "63238d96286e0f004b6fcac1",
  "641700992c68e51c6567ca19",
  "6461eac6d9afaf01d71649e4",
  "6279c07558cbeb004af32454",
  "6424b193ac04aa9321112087",
  "634415d7d37336004b29c370",
  "6368c679cc4b12004b1b60b5",
  "636fa3c18141f5004c400600",
  "646e74fd675c03218ff730da",
  "644ad6893a0e9ffd5c05b877",
  "6287bf1530bef8004a1e537f",
  "62eac96ba5ca4d004b73809f",
  "633f14ac59b828004cbdc60d",
  "6294ace9f5b05c00498044e2",
  "632ca5d34fa535004b1cc427",
  "63ad48663bde17003a2cd1bb",
  "63a5a82aa70d98d233d221fc",
  "645bdce619778803b1b3c658",
  "62faa56d9ef8ee004b534ffa",
  "634fe65130fb13004b255a0b",
  "6391be6cb17566004b2bd756",
  "64785c885fd1caf810136332",
  "62f3aac721a236004ba96c17",
  "63d418b557398a43e81bda3b",
  "634eeb897b4a32004bc8c996",
  "646b4410b07555c2a60a8255",
  "63cdaf0252942213ef249d70",
  "636288b3f18b27004ba2ab01",
  "630cd5a3d12d51004bfb8f4f",
  "634eeb8f7b4a32004bc8ca48",
  "63b6f242d5a73616eb74bb43",
  "62a757610f6b62004af2b0ef",
  "64eb2ce4e9598cda09f94383",
  "6398b0a219b378004b08690a",
  "6349691d8bc9d0004bf186a3",
  "6481cc8781461d34e44c330d",
  "642e901d9e1cb48e9ef77015",
  "6207bed158b09c004ac0191c",
  "64ef5d6a86a8da1056401ba3",
  "6324c803286e0f004b715384",
  "610bcb9d8fc188004707a471",
  "6280ea11b4d82e004a738f10",
  "66e5b5b3c656db18d7bed89e",
  "62c6ba88688e490049c23990",
  "62273a035d91c5004af59819",
  "62e568eab4f567004ccd6960",
  "647265625e167f0d1afb2982",
  "64efa5705177ebff191b87d0",
  "629b425abf3cc9004adc2200",
  "640d08d816dabd973d711f33",
  "6294e387f5b05c0049873480",
  "642b0039b03ff7d2c0f69f46",
  "637549c1f6bf63004b604a95",
  "65292b44ed5769d88d39ab66",
  "642a011a7178d7b65608a0a6",
  "62ded8606d3382004b41252c",
  "635c29651bf2ec004b4758e8",
  "629141daf5b05c004950cf1d",
  "62e11295d36297004b6cb74e",
  "62a247435949110049d03d95",
  "63f9ed939c5cc055079e0ba3",
  "63f0b627e9c63adf77164b7f",
  "6421eb2802ed03a97f55b5d2",
  "63d833c8e9fc800c3fb91af0",
  "642fe5a49e1cb48e9ef828b2",
  "645e6502e4fdadf66e141789",
  "629608abc77e290049a44e11",
  "62bc52104d94860049706f5e",
  "62a218d632b0be00498f2200",
  "64e9ba7ae9598cda09f8f114",
  "64da58b3ad268f13db6bdd12",
  "62a876881d07810049a5f2ae",
  "62c56be6c292160049d5c858",
  "6294a541f5b05c00497f3ac4",
  "64a30c15ab9f3c3901d675c9",
  "640001e8a8ee1db11d93d110",
  "62814b2df8b5dc004af3ee28",
  "63d958150f65ea183fbe2f3a",
  "62888fbf4cf66b004a1c96f3",
  "6192c22a1eca4900493a0e4b",
  "628271910b1398004a5f49fa",
  "64c7e5a428d955f9767601df",
  "635699e54c7c1d004b5f159a",
  "62b089ad44f0e000498ddccb",
  "63631332d1c46b004b443291",
  "649df67329e06608afc03d42",
  "6291128df5b05c00494aecff",
  "6426b54ff5774c37ae47e486",
  "628fdba6c168ba004985548a",
  "660844a8e63cf926952ec91f",
  "62b217a7599a340049492027",
  "63f270ef5ddc7dfc693b977b",
  "634d8ce365c1bb004bc2f34b",
  "6287bee530bef8004a1e52e6",
  "62ff67eb4cb595004b75eebf",
  "6373c2f92839bd004b54200b",
  "63495f13e48ca9004bf52f27",
  "643809579a84a5d87ecc44eb",
  "63554f7e3fa984004c54f5d9",
  "63e952f6f65640ffb3ddc5ad",
  "65c3d9bdf86b6aed7c6aea9b",
  "62323f6de93c56004beb0e11",
  "62932424f5b05c004963fe42",
  "654caf5a484c09668f9c661f",
  "6342af8159b828004cbfd355",
  "629c6da8211b5c0049a5a69a",
  "63ef36bc5f8737036fc131f8",
  "63da979be704008b8660d7b9",
  "64d3fd9c2a6c35b14f25b44b",
  "636cf8f6f1a26a004c0eb2ee",
  "6385ec39394b10004c695546",
  "62470b3952b323004a4fe4a8",
  "62b5df5c1c4e13004949064f",
  "62ac3f512a10f70049d04a1f",
  "6364021df18b27004ba49db3",
  "62db1a0d2785f70049f2c562",
  "63c14a81b9752b1023056e88",
  "64f325a6fe432dae15837a79",
  "646189835ab0dcee3aa8f405",
  "61d9c04e69a759004a80ad44",
  "63bbfc407814a38746a21c28",
  "63d83925dd285f55d1a8e587",
  "6386faeeb5a7a4004c25f24d",
  "62bc3940b812320049acc019",
  "62876f27740531004a5ad179",
  "629e0b85c58d57004aeaad72",
  "6485b0a85e7ab29d9dbab654",
  "6374c60e893f44004c4b3e41",
  "63d7cc6110658195356f5be3",
  "64653ef2da4203423777b1f9",
  "63e18f2d84167f10945fe87c",
  "649405f7149b8f70a1d4eb09",
  "63681372130ae1004b998f2c",
  "64d7d1a0e18acaebbacc5113",
  "62864926d0bf84004a5bf732",
  "65930c6082d645fa727b8519",
  "64b10d6fef2ad29abf804d38",
  "652585806af95d689be36225",
  "6281346e9d77c3004a54b67f",
  "630a9c9b5c4e69004bb95873",
  "64f107e26554a9db6c5a2ef3",
  "62fe151355539c004b49fe6c",
  "62d966d140da0b004a81794d",
  "63237d06286e0f004b6fb716",
  "637e16ba10ffff004b672ced",
  "639e49459e613f004badc24d",
  "651df0c945184d5cd1ec8e2f",
  "63472213f51648004cf613aa",
  "6341a927d37336004b287140",
  "6380949fbe0a7b004bbfdcf4",
  "631f7335075586004befffcb",
  "6412f73c2c68e51c656644fe",
  "63ffb249e65f80346ce81f1b",
  "62afac9e15f9110049873d7d",
  "62ebaaafa5ca4d004b7b5d01",
  "630337d24cb595004ba22fc4",
  "653109d70368ef10292de6ce",
  "63a89f1f4c1eff196c774d1c",
  "62dc7ad0c9672e004a1d5d3b",
  "62e988fe5fd326004b11aa2a",
  "63f37f013515f88a9d58a1e0",
  "630d067ed12d51004bfda549",
  "62e45fe4b4f567004cc47972",
  "627bb1720cebad004a6e14f7",
  "63eeb58d16fc3b0df0c26b5f",
  "648dad48bd43f6e21550f9f6",
  "65c48f37f86b6aed7c6b559d",
  "64a09beceb0b0791d0265846",
  "625379f897ab13004a55e6d7",
  "63cdc36252942213ef24b6f5",
  "6283fd150d8d2c004a4065c1",
  "64a06f4aeb0b0791d02646aa",
  "65142830ee0f66d9c3678411",
  "636d23ff0b6aba004b477def",
  "6516b17d75159f15e938068c",
  "6235b5074396a2004afa3682",
  "65109c8d494c9d61670a1143",
  "6212c70a0cc574004b438464",
  "649acf3b621b9766a9f5c995",
  "62921ccff5b05c00495b39c0",
  "64dc0a2fed5b9ef9d3140e7e",
  "6521357f0647bc8bea373ccc",
  "6287e45b2cfdd4004a6f0f42",
  "63a325a39a4fefce46f8aaf7",
  "646c8f08221079c32e4a48c3",
  "62eaa1cea5ca4d004b71ddb3",
  "64f90787732600ef63f71aeb",
  "644ad259716f000391fcf446",
  "6429eedc5314539563f33b04",
  "62ebd88aa5ca4d004b7cccef",
  "63cdb38252942213ef24a05d",
  "63b345eb53aff3f86b6869bb",
  "62b5f9d91c4e130049494c1c",
  "6140c97423a2df004796d9af",
  "64596cfea23962cc1b2a0b80",
  "63a461c1078212cf4e3a358d",
  "64bb10be943124da8e61228e",
  "61446a3829a39f0047032b6c",
  "65b7b08b42be2138bbe6be91",
  "62bc5643b812320049ad8c06",
  "63d4277b1f2cc41fd871f60e",
  "64fce0c73d52d401addee252",
  "629a3b20bf3cc9004aceaf3a",
  "62f9344a84b866004b9e6933",
  "6362f0bbd1c46b004b44199c",
  "635429f83fa984004c5462f5",
  "64a57bc9f248fc833f925a5a",
  "63f0e6585ddc7dfc693b2e54",
  "648b589fa2ed9dca5136e410",
  "636114199c83fc004ce48ae7",
  "635988690f322a004cc81f7f",
  "61bb2cdd7cbb99004834302c",
  "63527c28a30a0f004d4d7871",
  "636a409c8359fc004c61abf6",
  "640e440516dabd973d717696",
  "63e3ba884ff748ae87f5e0e2",
  "629049bec168ba0049929c97",
  "63e8bf47218dd176dc70487b",
  "6357af1805572d004c3a6514",
  "63cede36fcf17527256fa248",
  "661ed4322fb00c8decb1a4f1",
  "64bceb40943124da8e619c13",
  "64ca5cf54ca0f1cd019d956b",
  "64a0a058eb0b0791d0265ae8",
  "6402688b020f43f506114680",
  "63d7b9c10164bfbf24828fb3",
  "6290fc56f5b05c0049478742",
  "6498a247b3149e81dba242d4",
  "62c2d7c9ef6dfa0049f82f0c",
  "63c45b45df9ae32d31b5d8ac",
  "629caae4211b5c0049a84cae",
  "6349682fd8ea42004b2a7297",
  "64763b819a69799b195a440d",
  "64ca8793394d741bea2c1ddf",
  "648ceb2bbd43f6e21550b2a8",
  "6320d99c1d45af004b65b5a9",
  "61f1d4a09f0758004ab9f2dd",
  "65945ebe448bc4215dc3eaa0",
  "6245f8b3675d5e004b1b6319",
  "6446961e03ec71fc122e63a2",
  "63b72f3a9c8dbc3dda2d931e",
  "6377b77183b2d7004bf359c0",
  "640c90f55f0dfcbefab9b5e8",
  "628923cc3b1522004a2d9412",
  "63623e5ff18b27004ba225ab",
  "63893ef8c06f39004b08279c",
  "63baa9399c8dbc3dda2f7704",
  "6505a9a3c0d8e41f4427efcf",
  "6426e0b2726880644bd0d396",
  "65200ec30647bc8bea36ba0d",
  "62991f0473a18000497d05bd",
  "644af76f3a0e9ffd5c05d38f",
  "6419f27c115bf388cf601e8b",
  "65ca632d996be2469fbe40ca",
  "62b0ba20366e14004907b05e",
  "639e3707a00ffe004baf25af",
  "6287fcd42cfdd4004a6f2aa5",
  "643d85506d98a4d05511f8fc",
  "62ffe84aaced7c004bf04584",
  "6356e70405572d004c39dd93",
  "643a69d46d98a4d055112ac1",
  "643eb4ff8a389c983f38c0db",
  "62815a4af8b5dc004af3f6f7",
  "67a520ee0c53554de5f256c0",
  "6493c33f149b8f70a1d4d73b",
  "64045a0315e0ef0022935293",
  "63bf0850df71db94a5320cc8",
  "62a1014a3187eb0049b6ad08",
  "640846c9910f3b483c08b438",
  "66a55a8f3fb38acd14efa04d",
  "65a9449c89742120c3d45b13",
  "6386617cb5a7a4004c25bd7b",
  "646c99c6221079c32e4a54fd",
  "636234cfd1c46b004b42c38a",
  "62af466915f911004984d71c",
  "6284f3022e5247004ad7c506",
  "63f3d82f89def3d9121060da",
  "6425a96df5774c37ae473a07",
  "630bcfea08cbe0004b5a3300",
  "64f9c1513870651e6c1e8948",
  "6313341b6e7c9b004bee683b",
  "62dee0cf6d3382004b4185db",
  "628fefcec168ba004987b029",
  "62a819c70f6b62004afe52b4",
  "638c900a59d281004b604315",
  "6303368e4cb595004ba216e8",
  "6476509605605162f312bfac",
  "63c02cb9a52fb8a3b5c36ce4",
  "64148ed82c68e51c65671a2c",
  "64cb6fb1153f67b8398788a7",
  "640c9d905f0dfcbefab9c1ef",
  "645acc7aa23962cc1b2b15c0",
  "62a8edc314862300498cf250",
  "62804254a07bfa004a63e9fa",
  "63405f87d37336004b27cec8",
  "637fdd4310ffff004b68d041",
  "63e948a2f65640ffb3ddbf07",
  "642f11df9e1cb48e9ef7e105",
  "63cffaa0090c1a20d0cf46ab",
  "6166229676ee830048e0f09a",
  "62dad1c0c9672e004a0d4d1b",
  "62937132f5b05c00496696aa",
  "63d34510e77ea3648f7e29ac",
  "63c659c0dadd6783ff253e7a",
  "6372ab3486f1aa004b74a8de",
  "62d7075c808b87004997c159",
  "6488ea457708f02304bcfd68",
  "60abcf6134d2dd003e14ae04",
  "648075bb2b1b94341c82bbd8",
  "62f98e629ef8ee004b46b3b4",
  "64ef40d6d6b1f8278226cd08",
  "63cef4a4fcf17527256fb2b8",
  "60ab78969a6b39003e0cfb10",
  "629249b6f5b05c00495ccfdf",
  "62caef1c6671240049c5f7d4",
  "633c93a89f6946004bd1a844",
  "633d2ec49a3520004bb818c7",
  "62fd714b55539c004b42853f",
  "643fca258a389c983f394021",
  "62c92b336671240049b9760f",
  "64ef741e5177ebff191b4390",
  "624b25f43079fc004a2237cc",
  "6287405a740531004a5ab838",
  "646c6c81221079c32e4a31a4",
  "64870f0736fc48d7710e972b",
  "631b14953ec078004bde8299",
  "628780f9740531004a5b03ee",
  "6300bc90aced7c004bf9d1b6",
  "636d268e0b6aba004b478587",
  "64f4b800fe432dae1583e196",
  "6282b41a8f7d91004a7f3e3d",
  "65dbea800cfbdf2ceab18edb",
  "638bde5b59d281004b6009a9",
  "62dd4236c9672e004a2402ec",
  "628106c8b4d82e004a73ad5e",
  "632b0b04db9ab3004ba5a2ba",
  "63d70fdd3f1ecbaa3b066396",
  "63930983d8efc1004b7d8966",
  "638d2caa8b4338004b92309f",
  "63b83c01567febd3fe4cbd23",
  "638b5e0759d281004b5fd0f8",
  "6284bcdb05f0bb004a79f5ad",
  "62d25bb872c9e00049da4e9e",
  "6377bd0683b2d7004bf36146",
  "62d3ed8328b9fc0049d1ee28",
  "638ba45f8b4338004b918f1e",
  "651d901f45184d5cd1ec2251",
  "647e50f56fdcd5c5bbee6881",
  "6478a324bb2334b0005b1a3a",
  "62ea9a60a5ca4d004b718746",
  "6464b4ac56092c08b76f8609",
  "64cf81f0ec45e1d8e8d49966",
  "63e6520bf65640ffb3dc9897",
  "638760862e9709004cdc39bf",
  "642ab82e7178d7b6560937c1",
  "6429d5e85314539563f32cf5",
  "62a659a74846e5004927e497",
  "6283f7230d8d2c004a40609a",
  "63a5dc3d3bde17003a2916fc",
  "62fcf3ac9ef8ee004b6bbdc7",
  "648085873d5f3450ecba9fc5",
  "62af25b115f911004983df1d",
  "63616cced1c46b004b42227b",
  "651d442e45184d5cd1eba4a5",
  "642382c26a0fe8df5f5ff828",
  "6378d69583b2d7004bf44159",
  "652d86559a59d38b12041903",
  "627ab9cea51b18004ab877b3",
  "637dfda810ffff004b670a46",
  "63d6b8481f2cc41fd87313ee",
  "62c30341ef6dfa0049f9e290",
  "63208569075586004bf0dcb1",
  "644af6143a0e9ffd5c05d1ed",
  "6344891c17d3d5004ca140db",
  "651e4c9f45184d5cd1eca282",
  "64c816c2a0250ce3d7d3852f",
  "6371621a85d8e9004b329458",
  "63cfb98c52942213ef265268",
  "6287747d740531004a5adc3e",
  "6669cdab072925e8c729ab52",
  "65e951376a4069769a601489",
  "642644eef5774c37ae479060",
  "63779f4ad6179d004bb26fd9",
  "62b1be0679b8f700495e79e5",
  "6429f4367178d7b6560897b1",
  "63e77f2a218dd176dc6fe35f",
  "64d109baec45e1d8e8d53b91",
  "636a9146cbebab004c4bd7cb",
  "641cc12b168c760c92b197c5",
  "63f9108277d837fbfb0cc561",
  "62f927949ef8ee004b41eb93",
  "6446ff3503ec71fc122ebcbb",
  "6280ba41a07bfa004a641405",
  "642a2b855314539563f36143",
  "64bd6a9e1eeb1bdb61493ec3",
  "65c02e473f6fade7d517dff8",
  "6438777aee6da54df5e67662",
  "63fed6d39c5cc05507a112e4",
  "623c2e153dc5ee004b1e4fff",
  "63767043f6bf63004b614ccb",
  "61b36c913eb64d0048bad098",
  "6497407bb3149e81dba1ed23",
  "646e3f8f675c03218ff7099d",
  "6424345f251b6e74fb5a111f",
  "62878848740531004a5b0f6e",
  "646c804e221079c32e4a3c04",
  "61ccd16bbe4ce3004a68bc10",
  "6481e0c081461d34e44c44a2",
  "64a0616ad3cf29d3d8325dfd",
  "645aa929a23962cc1b2afd9f",
  "640a4dae910f3b483c0a5586",
  "629a0a0058593a0049cd2d25",
  "648f3b376feb770d63956c56",
  "62a08e1c30702400493b9ddf",
  "64cad1fc153f67b8398759d7",
  "624aec8dc29cca004a1052a6",
  "64c9726f3a867fb6a1d9752a",
  "628e370603c7d7004a64d72a",
  "628a9da902d452004aedd80f",
  "628f63311714a800493c2662",
  "6280fca0b4d82e004a73a05c",
  "62764d3decca72004a223d08",
  "6349621ae48ca9004bf53ac5",
  "6253232097ab13004a55db3e",
  "6307f4c8387b89004b277176",
  "63611fa7af44a4004cee5a8a",
  "638612e4394b10004c6972e8",
  "636e0dcec51245004cf9bcf7",
  "637e2d5fbe0a7b004bbe198f",
  "62c9edb3bff3ab004979f2f1",
  "63bf3782be96f2327030d219",
  "64673214c3bf0f5f128a1620",
  "6481ae5a81461d34e44c20e1",
  "63e3acc784167f109461b6c8",
  "64615491e4fdadf66e153334",
  "64a6cf17152d5a46c1961fff",
  "65e99998219e2e1592221f55",
  "62e51270b4f567004cca4ea0",
  "618d2072a39df50048a59926",
  "62a8c4baf87c560049759968",
  "62cf35518958d600498168d8",
  "61c72019d974de004b154ee2",
  "641e181d8b0db5a276107751",
  "6424368dac04aa932110a10d",
  "64b6fb59da49796346e896a6",
  "63303368e20362004bff85bf",
  "628132569d77c3004a54b547",
  "63dad7c6e704008b86614310",
  "62b5992733dfd9004939c09e",
  "6378c8cd65d523004b2500dc",
  "63d4f31957398a43e81c29d5",
  "640f15fa4fb8f234628a582d",
  "63682eb5cc4b12004b1b16f0",
  "62b0a5e50776b200494766f0",
  "628e9a81519c34004a17883c",
  "6415283e3050698c99a17f48",
  "62810211b4d82e004a73a7af",
  "634ac3828bc9d0004bf29cd5",
  "63a9c92f4c1eff196c779f9d",
  "648d388abd43f6e21550bdbf",
  "62b22788d3116b0049718281",
  "63650609ab1d3e004b9f4be9",
  "6527122845e8ab60062bfa20",
  "64653a33da4203423777aac5",
  "6211af220cc574004b436a72",
  "64b1728807ad4a55d183fc7e",
  "6303865ee18b7b004b271296",
  "652e602229674b6b9b747e9c",
  "62a9f40fa992dd0049cbe603",
  "64b8f6e74e5b32f156c30161",
  "63e8abbe218dd176dc703f49",
  "65ae1476c5118ba49e1ab1f5",
  "62af7f7e15f9110049862ea9",
  "62b5d6d21c4e13004948e103",
  "62cebc14b96622004988073a",
  "64673f89de596f3acf311f60",
  "62704f05978cfe004aa29ca0",
  "63287b0bdb9ab3004ba1c77d",
  "64b67cf4515b6150efa90afd",
  "6367bd4d130ae1004b99336c",
  "6283bbbb08f5fd004a06b4b2",
  "61a6579b4a47ab004975c5f1",
  "647787ce30882a85b5249f6d",
  "628e324c03c7d7004a64d252",
  "646b7abb221079c32e497f2a",
  "6280f2d4b4d82e004a739760",
  "651977610082d22b619ddeda",
  "63225b1fed2117004bc76dd0",
  "635d3a6f69de9c004b7b63af",
  "642ad861b03ff7d2c0f67216",
  "63a9c0a23bde17003a2aa288",
  "629f2a9456c9f70049ca626e",
  "650aed8b67e4f186f9761e3c",
  "63d6ef203f1ecbaa3b065a2f",
  "6291e682f5b05c0049596501",
  "62d57e2b28b9fc0049de7f96",
  "6445a11b277d0d703ba7475d",
  "651d22d83151b4ce9c2a44bd",
  "64a1d2e2d3cf29d3d832e821",
  "641d9ecf83bc247c7ff5d91b",
  "633c8fd49f6946004bd19f9a",
  "62949fecf5b05c00497ec3a5",
  "6284e32105f0bb004a7a118f",
  "63f8c9cc77d837fbfb0c8321",
  "617d41ff4af4030049438f27",
  "6390f1ac1cffd6004b738477",
  "63b93b689c8dbc3dda2ee52e",
  "63ecc774c16693a157919885",
  "6280afeea07bfa004a640da1",
  "6469b528b07555c2a609fdd8",
  "6453df6c686ecf110055d035",
  "6378b43b65d523004b24e47e",
  "62d0ccb58d93da0049901dab",
  "61b227953eb64d0048baa211",
  "63de2387a114166d6d86abc2",
  "641e09ef5a418a529fffdd1e",
  "65e61720ca176e0a5463e73d",
  "643803236b4cfeccc9f1a32f",
  "648204294f64f7c918fefc8d",
  "6493a847149b8f70a1d4c98e",
  "6245dfed675d5e004b1b5ca4",
  "651c11e6c6e9eb8b164fd111",
  "63e4da42549d199306f7319d",
  "64f07799f345686d1df87954",
  "62d00a258958d60049876c62",
  "652bd651ac7e34b83c3516e1",
  "61cdbeb7be4ce3004a68e71e",
  "63348e0f2c0d75004b6ed8b6",
  "64cbe88cec45e1d8e8d31f15",
  "62be1beb3d25c5004af620be",
  "63d54b5757398a43e81c7707",
  "628778d1740531004a5ae7e8",
  "63455f7f17d3d5004ca1d6f5",
  "642a09485314539563f34f1d",
  "62a4e6880f6b62004adba4d9",
  "63c04a89a865fdc013184941",
  "639f84359e613f004bae0ae8",
  "646bda82cae270ffd0af54c7",
  "6298dcee73a180004973ca6a",
  "6141da4447cc8d0048ab732c",
  "6524b2f40995a4d93f21dfca",
  "62e86565b4f567004ce9121a",
  "6331cd6bf3349a004b1b7154",
  "63d4658f57398a43e81c0901",
  "64615c29e4fdadf66e153a74",
  "638e1238476cc4004b25dfb3",
  "6266f3f2e74e15004a8e8ffc",
  "641e2bdf8b0db5a27610804f",
  "62d840901afaa1004aba345a",
  "6458ed289901c3301702a282",
  "6523ba1d0995a4d93f214e15",
  "627a2669b89853004ac601f8",
  "64ed046e26fb7faf52ac265f",
  "629e25b4c58d57004aedf595",
  "63359e02162274004b8466b9",
  "6430df4830cc043915e37bb7",
  "6280e973b4d82e004a738e41",
  "631476466091e2004b26805a",
  "6280db26b4d82e004a7383c4",
  "643dc9d16d98a4d055121e34",
  "649de82e418ece57577db845",
  "628fd141c168ba004983e25a",
  "630dd8d3d12d51004b09232d",
  "64aeeec026f468ae31a8ff3a",
  "6328c09309e01d004b5f0bc5",
  "641375eb2c68e51c6566a777",
  "6329ae4409e01d004b5fb08b",
  "6445815d36889f01cc08f059",
  "62878098740531004a5b0272",
  "63b7ee3e9c8dbc3dda2de329",
  "6521359f0995a4d93f208a2e",
  "6248d0e83a7c88004a7d4c2f",
  "628777d5740531004a5ae434",
  "627d6469033825004a022d31",
  "63ca462e9175659653560f64",
  "67220b40310abaf088f39000",
  "62e139f005ed45004b220a71",
  "64423d9a701f1f1d55a7721c",
  "62e3ef341c9bdd004c113e07",
  "627fe610ba5a12004a3b7624",
  "6520282f0647bc8bea36d041",
  "62d70052808b8700499781a8",
  "653cbe3cea336ab50bc40bdf",
  "635fb8d969de9c004b7d19d5",
  "62dac7522785f70049ef4c83",
  "6314c8296e7c9b004bef6e9a",
  "62d0ae98d87b950049e5b7e6",
  "6280c7aab4d82e004a7370aa",
  "642a01cb5314539563f3491c",
  "65aeb31d0bebee9ff4dacf99",
  "6418d74fc71cfd369a474b01",
  "6524381e0647bc8bea38564b",
  "626046ae80e916004a8eebc3",
  "626cee5e9de6a8004b99b3a6",
  "63c778f79aa331fa8a763a02",
  "646fc343edf335b77715c726",
  "651da7c045184d5cd1ec3f11",
  "64a738ef152d5a46c196a570",
  "628ebe38519c34004a1bc726",
  "622381f320dddc004aed9de3",
  "640a51b1910f3b483c0a5a85",
  "633e6f61c32cbb004ba8ea61",
  "62b7734a1c4e1300494e2b85",
  "625ea19f1a8902004a79095f",
  "6350893ea30a0f004d4b82ac",
  "63d9a6240f65ea183fbe72a5",
  "63daf25be704008b86615c96",
  "63ce4ff552942213ef24f173",
  "639c5d1bbe5446004c545165",
  "658e883cb76f3cbfb0efa318",
  "64a60276f248fc833f932647",
  "644c4783fc4e77e44fe269ec",
  "63fbdcbc2b5ffb01b43bd989",
  "62de9e3b2eb09a004bdd25c4",
  "650b352667e4f186f9765d54",
  "652e04199a59d38b12043d05",
  "63f09998e9c63adf7716443f",
  "62dd4eaac9672e004a24a162",
  "63dce4de92e73cab1f7017e9",
  "63b89b4b567febd3fe4d17a6",
  "639ef2c19e613f004badd393",
  "6307bf8b387b89004b24aa54",
  "62af54f615f9110049852e1b",
  "63c798ae9aa331fa8a763dd7",
  "649c5fd2efc77a497397b4ac",
  "6391f3a8605b27004b1ae839",
  "629bb8c6211b5c00499f57be",
  "63652069130ae1004b970b84",
  "63e644b5f65640ffb3dc7c6d",
  "63fccd399c5cc055079f6dfa",
  "63874f052e9709004cdc2ba3",
  "6453d81c686ecf110055c738",
  "653175a1313b9d53b037bf96",
  "6342f83159b828004cc002d0",
  "62dd38fbc9672e004a23979d",
  "62c2cb97196b570049cad340",
  "628e161003c7d7004a64bc58",
  "651f21e9d2ac9ac7dd143005",
  "634f036130fb13004b24d4a0",
  "61ea9b341054d6004b129511",
  "636a382bcbebab004c4b29cb",
  "61ad20dee16e0d0048c2f9ed",
  "63715d6685d8e9004b328701",
  "63a320fce361a26095e2efe3",
  "6384fa7d9667bc004bc41ee4",
  "6480dd6e292c5bab944b3179",
  "6279924b58cbeb004af31a7a",
  "634940717da695004be95e60",
  "663e9d8ca066615caca07372",
  "6501b528912fdf8544a58f96",
  "628c964682b90c004ada4993",
  "64691f24b07555c2a609da3b",
  "62a9db875ededa00495a303f",
  "6287b1ce30bef8004a1e36d8",
  "621386060cc574004b4395ea",
  "6288924a4cf66b004a1c99b5",
  "62e4523eb4f567004cc3f186",
  "642d529eeb7c4dfbb46aeaba",
  "628cdfd173803e004a8ae83a",
  "64fb182d3870651e6c1f5031",
  "62877393740531004a5ada3e",
  "63e5217df65640ffb3dbadc2",
  "642b0df8b03ff7d2c0f6c8b6",
  "629c93d0211b5c0049a747a5",
  "629b71d3bf3cc9004ade23df",
  "644148be4afe85f36b605671",
  "62cca869a9918400491a09f4",
  "62dc7cd8c9672e004a1d772c",
  "628687e19e7a9b004ab3fbdf",
  "62ff4d3daced7c004be8c247",
  "63bb0c399c8dbc3dda2fdac1",
  "630fe7e424a58a004bd1a315",
  "63567c0832b60e004b792740",
  "63f3c2f589def3d9121050dc",
  "641df3408b0db5a276105a37",
  "643a75b7ee6da54df5e75c20",
  "651493ea5a2a491d03036f51",
  "651473445a2a491d03036028",
  "62853c35cef116004a19b8a9",
  "62f8d2769ef8ee004b3e32a6",
  "62a8b1d3f708cd0049db1914",
  "652ecba4e0ad5b0b8ff266a8",
  "634aceffd8ea42004b2b9aa7",
  "62cf40a98958d60049818eee",
  "62c446fa38cdba0049195be9",
  "62f182af71c5cb004b5533d6",
  "63ba94cc8d7f46fac46130ca",
  "64dbedaeb0cfd45a221f064e",
  "649d58b752ee106600500817",
  "62934e3af5b05c0049656361",
  "636019699c83fc004ce39a2a",
  "637a589a83b2d7004bf4f78e",
  "63a9ab984c1eff196c779126",
  "63e55d3ef65640ffb3dbee08",
  "6400627ee65f80346ce89f36",
  "62955986c77e2900498e613b",
  "62939b4ff5b05c00496803aa",
  "6351ac37a30a0f004d4cda48",
  "627f6780ba5a12004a3b6238",
  "6432ca6f30cc043915e3e1c1",
  "62b06594f53bc40049508c12",
  "638cc51d8b4338004b920845",
  "64270efa5314539563f21734",
  "6342adf859b828004cbfce60",
  "62fab6ff84b866004baf24d2",
  "662a8e268872ca499c3aad6d",
  "63a2220540bb2fef9d0d36f9",
  "63d9999b0f65ea183fbe7019",
  "63d489941f2cc41fd8722915",
  "64efa1d7f345686d1df7fc64",
  "6257f7c69d684b004a980c80",
  "635d0c401bf2ec004b47ed1e",
  "62869ad59e7a9b004ab404ef",
  "641f00f35a418a529f001cf4",
  "63e3cc4e4ff748ae87f5f0b1",
  "64d51df496f5cd4a123fe292",
  "62a8ebfc14862300498cdaf4",
  "62c991cbbff3ab004977334a",
  "62fc0f7684b866004bbf3062",
  "6466720febfe5314836100d6",
  "63eb709966091fd77d9ad034",
  "642a86777178d7b65608e4c8",
  "64a1ae73eb0b0791d026beb1",
  "62923696f5b05c00495c1dbd",
  "6457c0bd3094dbc36ca06525",
  "627fa099ba5a12004a3b6b6e",
  "622a522d6343ea004a4ef463",
  "6183fde18b103b004919761b",
  "63a029bfa00ffe004baf9388",
  "64933e3d149b8f70a1d4a7b3",
  "625826939d684b004a9816f7",
  "62c56c8bc292160049d5cc43",
  "63a6010d3bde17003a297a79",
  "6245e8f9675d5e004b1b5f58",
  "62e2edb6703ae0004ba29efd",
  "63658801cc4b12004b19170d",
  "62c2c04b196b570049ca7cbc",
  "62665dc84f727a004ac53708",
  "6416510c3050698c99a1cf08",
  "62f3ae4b21a236004ba99aa9",
  "642d84aeeb7c4dfbb46b0d1e",
  "6282cfaa75fcf8004a95c4f3",
  "645d5a993b0bcd777d8e2fe3",
  "64defdb8453d56c4ebec6d94",
  "62810c749d77c3004a54814d",
  "62f2a00e2bdd33004b99c472",
  "653e135a6f0005b171bce6bc",
  "63fbedf02b5ffb01b43bdcd6",
  "63d180ab22a0bbbb867665c0",
  "64970bfd5e0799ec2c7cdeac",
  "62f16dbbd46581004b00684d",
  "6474ab7d61c3cb7713c1b21f",
  "625868e36fbe06004aaeb18c",
  "62c3f72d6e19670049b7c6ff",
  "651f09a2d2ac9ac7dd141b8b",
  "629555e2c77e2900498dd078",
  "62a9d8215ededa004959a7a5",
  "63d177d122a0bbbb86765af6",
  "63d972f5c9b8463927c36cf9",
  "6387486d2e9709004cdc1517",
  "63a5ff10a70d98d233d2b211",
  "6380978d10ffff004b691985",
  "651d29d1c9ab47da056f0c27",
  "6425eeb8726880644bd04514",
  "6313f50e6e7c9b004beedc1d",
  "6245c6ed675d5e004b1b50e1",
  "62969faf5b006400497d02c1",
  "64dca582ed5b9ef9d314350f",
  "616589698d4b590048ff7f4c",
  "62d7ba19d4c3ef004961e845",
  "6301dd9b4cb595004b91f95d",
  "6286d6539e7a9b004ab43287",
  "642013635a418a529f00604a",
  "63ca6a09675aa042375989a9",
  "642d3634eb7c4dfbb46ad4a0",
  "62f28c2afec6b7004c8c6358",
  "62a2595e5949110049d19e07",
  "645116dbdc098e8b83ae60b4",
  "633a3498722234004b7606ae",
  "628898344cf66b004a1ca1fb",
  "629b90e50a4b510049aab4e2",
  "64ce7cd2ec45e1d8e8d445b6",
  "641eea6a5a418a529f001022",
  "62901a6fc168ba00498ce415",
  "635cfd7169de9c004b7b11d7",
  "63e1640784167f10945fb9ba",
  "6310e2e56e7c9b004bec672b",
  "6472783f5e167f0d1afb3143",
  "62f633ba358778004b95a74f",
  "61be0cb3406cf0004a4f1279",
  "64104fee81ffb000ac8768c9",
  "6384fbb69667bc004bc42251",
  "645bdb9a19778803b1b3c48e",
  "64eb5f264518086a73c78d2b",
  "61f9dfa15a5fae004a6e8c10",
  "6488ec07e966693f13011755",
  "641029e84fb8f234628b0191",
  "661e823e8796bb0604c330ed",
  "6394747e12e52d004bb9ac7f",
  "63be29823ef39fd1418bcdfb",
  "62d06303d87b950049e32dc4",
  "63550b2c28de6a004b079326",
  "6197fb34a70b770048ece57c",
  "63df8694a114166d6d8753fb",
  "645d0aa80d2538bc437f6c7e",
  "648a29bfb98618230c88060d",
  "629b6de1bf3cc9004addf53c",
  "628390bba4b713004a49ad0b",
  "62a2484d5949110049d041bb",
  "64b68558da49796346e82ce0",
  "671ab222a3ccd7bd39b8bd40",
  "6283a4e008f5fd004a069d82",
  "636caca5114750004bc04e94",
  "632e10f9e20362004bfe319e",
  "67311783b6931e8bf46bfec8",
  "647f6c5c6cd028e28574f543",
  "62a31d285949110049df633c",
  "67a4e9fd63c0994a293fd725",
  "6296620d5b0064004974d4c1",
  "64296d325314539563f306f3",
  "63f672f1862dbf4467f1cf03",
  "60abe50134d2dd003e14b1a1",
  "6351a921a30a0f004d4cd9cc",
  "644432e9277d0d703ba6f2aa",
  "64cb79c8153f67b8398792d4",
  "632713d199a775004b242f8c",
  "64896877e966693f13013174",
  "620699d658b09c004ac00663",
  "6287f5642cfdd4004a6f244e",
  "6336040fad6b41004b23ec82",
  "632445b099a775004b220275",
  "64d37d652a6c35b14f253939",
  "6287938030bef8004a1e0a05",
  "64034ac8020f43f506118cfb",
  "62dad74ac9672e004a0d983c",
  "62b162480776b200494b632a",
  "62de5be32eb09a004bdac2ed",
  "6377a2fa65d523004b240c11",
  "62c35094abfb3a0049af32a9",
  "620d4b115f65a5004a9cfc20",
  "6347f910b1bcc7004ced65cd",
  "63a743ab4c1eff196c770fec",
  "62a4b1ed0f6b62004ada85f1",
  "62f9a11a84b866004ba3618b",
  "644988f82568c0312d253f49",
  "636ddea6c51245004cf9ac56",
  "6362af99d1c46b004b43cb86",
  "64cb6a5e153f67b83987848a",
  "6294e7f8f5b05c004987ce12",
  "638248cfbf4577004bc5bf50",
  "62b616d21c4e13004949cab8",
  "65e1ec588cde5c71f48056a1",
  "63628a72d1c46b004b436281",
  "6345e35ba6dfd1004b42b711",
  "6356f15205572d004c39f37e",
  "62f8bf1f84b866004b9a2b03",
  "63accf744c1eff196c798306",
  "63501ba030fb13004b258b40",
  "62b00d0f2a10f70049e88fe7",
  "63e4c52b549d199306f71a58",
  "62bdfc2f3d25c5004af51948",
  "639b96afbe5446004c542290",
  "647bd9b9c6a88c981d7275fc",
  "651b2ea2c6e9eb8b164f5e8a",
  "6466265a2078a374bb41c9d5",
  "62a21c4832b0be00498f8eab",
  "632d61e33b1820004bd0bbc7",
  "64091a1d910f3b483c09adc0",
  "6559382f2085a5a7ba773cc3",
  "63e21f817de8c3da56935442",
  "629a9a2dbf3cc9004ad59484",
  "64499c3c687f1d96e48e3a5c",
  "649c8b1ece08f43bc31d33de",
  "6339c70986498e004bcb6eca",
  "64b6dc1e515b6150efa973c9",
  "63c850d260d21baa2294b5ac",
  "61e046e05b295e004b14da5f",
  "6321e3431e6809004b7eb823",
  "636c02b163ce2b004c1bd16b",
  "62c6bf6e688e490049c26a2c",
  "6510a295494c9d61670a124d",
  "642d4f519e1cb48e9ef6bf44",
  "641cd2bef652599b7663f126",
  "62a2946332b0be0049987479",
  "62854b0acef116004a19c191",
  "6380ae6cbe0a7b004bbff278",
  "64c01aed6e3e937b07bcfe04",
  "651d7617ca70b91c337ea18c",
  "63599ed30f322a004cc846cc",
  "62a679e64846e500492853ee",
  "64ca9dd1153f67b8398721f4",
  "63d88977e9fc800c3fb941b9",
  "62888a674cf66b004a1c8e3f",
  "6272690a65792f004a95ac5d",
  "62bad879e5b25f0049819efc",
  "62925302f5b05c00495d26db",
  "63865384b5a7a4004c25b4b8",
  "63e3824c4ff748ae87f5938e",
  "6579eeb5ed7162285b6d89c7",
  "6341d72ed37336004b28913f",
  "62a3234632b0be0049a2e997",
  "617d49574af4030049439013",
  "633cdfc89a3520004bb80ae6",
  "64c78fb328d955f97675c8e1",
  "62e46247b4f567004cc49e48",
  "63d02e9b090c1a20d0cf9d32",
  "62be08a3486b650049932c8b",
  "63add3a93bde17003a2d37de",
  "6281350a9d77c3004a54b6f5",
  "62d04a8c8d93da00498bcc24",
  "640c9262dacbabab00ef1c0b",
  "645ed38d5ab0dcee3aa81e4a",
  "646f6db5e20eea986945a51e",
  "636a44718359fc004c61b39c",
  "6341ee7e59b828004cbf8662",
  "63c32e79df9ae32d31b56dee",
  "650ff802494c9d616709ec25",
  "64515aa8686ecf11005450d7",
  "6377d30e83b2d7004bf38b8f",
  "639fb500a00ffe004baf8343",
  "64e7c72e216a28a008e09376",
  "651f14cad2ac9ac7dd1423bb",
  "62926f96f5b05c00495e22cb",
  "628733c9740531004a5ab3fb",
  "62a254cb32b0be004993b7cb",
  "632b625c82b9f1004b9080b5",
  "641b237234dff3ae05f06c38",
  "64021832020f43f50610fec6",
  "64905fe5c0b36cab673d7b2f",
  "62889d7f4cf66b004a1ca701",
  "644b8cba716f000391fd5d97",
  "62cd465d17d374004943ecb4",
  "6466407713882dd592f5d384",
  "628114e39d77c3004a548ba4",
  "62c74ca5a05e260049dd1b5a",
  "62890d493b1522004a2d81d2",
  "65e0e234d16f68546208b533",
  "6466003444045ae375b8d45b",
  "6391f7e4605b27004b1af08d",
  "654a58d0a5d02487ebc7e0cc",
  "649f22d3eb0b0791d025a5a2",
  "62e0804d0a8955004b1a7b2b",
  "6516e03abfef1082d3827f5e",
  "62e7c8ac703ae0004bd2eb29",
  "62a7085d4846e50049301ac9",
  "62ed7504d46581004be01dcb",
  "65143151ee0f66d9c3679b91",
  "649d98abf149100eb1f3bea0",
  "63ce79edfcf17527256f13f3",
  "64ef462fd6b1f8278226db29",
  "64c8b86c28d955f976764642",
  "64588f28705921416efa51a1",
  "6429d0b45314539563f32786",
  "62cb4950a9918400490f7aa1",
  "6398378ea1e839004cd11035",
  "62eabfb8a5ca4d004b7301ad",
  "631b70a1075586004bec5466",
  "645ed6e85ab0dcee3aa81f25",
  "61f7ef6a9f0758004abad7b9",
  "649d2f35ce08f43bc31d8af5",
  "6432dd2a30cc043915e3ec51",
  "669d307ce04a4f0dcbcc3cd4",
  "648b3f1fa2ed9dca5136c7f1",
  "6352c081a30a0f004d4dd9b9",
  "62ddc364c9672e004a286dde",
  "636abbee00c2a5004ca27674",
  "627ebf31bb4b4e004a442242",
  "6377cc4283b2d7004bf380f0",
  "6425f2af726880644bd0473d",
  "62541da9f5dbd4004b8ef65b",
  "635ae9c91bf2ec004b464116",
  "629233dcf5b05c00495c065d",
  "63c07e57a865fdc0131884a5",
  "62d014760a08740049cb4ce7",
  "62c2e0f0ef6dfa0049f8b5e5",
  "62af615515f9110049857c7a",
  "628fcbbdc168ba00498331eb",
  "628f60d91714a800493bc624",
  "62803d23a07bfa004a63e63d",
  "629903ea73a18000497972f1",
  "645f925fe4fdadf66e14a851",
  "6288b86e4cf66b004a1cbd89",
  "64d3ea51ec45e1d8e8d704dd",
  "6328797a09e01d004b5eb64a",
  "64f9c5ec72201639e7f3a860",
  "6245c08e675d5e004b1b4f30",
  "63be17af563fc4caa93a8aad",
  "63615353d1c46b004b41e00b",
  "63c565f6a54c9b18e9702d6e",
  "636232f6f18b27004ba20195",
  "638716892e9709004cdbdda9",
  "63b9ff379c8dbc3dda2f45f6",
  "65ba720d26c0a63857238316",
  "647b901bc6a88c981d725d3c",
  "6156fdb101d5cd00488dffb3",
  "62b1b83f79b8f700495e5718",
  "63808021be0a7b004bbfd73a",
  "632ae96d09e01d004b621405",
  "6378112683b2d7004bf3de5a",
  "63343e17cf69a6004cda58e9",
  "635918d90f322a004cc756af",
  "64899876e966693f13014a99",
  "629819d53b47dc004955227d",
  "651cc43daca9419bfe659d09",
  "627ff613ba5a12004a3b795d",
  "64dc8128b0cfd45a221f22ef",
  "628fe4adc168ba00498686d1",
  "639f6c35a00ffe004baf6cf2",
  "62f13b71d46581004bfec015",
  "62a0dee532b0be00497973e7",
  "63a34f1499de17c8757024c0",
  "62c473969086080049d7f898",
  "659d367398e97d55b577ffd6",
  "629132e6f5b05c00494ed3f7",
  "64a16df2d3cf29d3d832bb85",
  "631b3cc3861101004b2f4f46",
  "623bd102ab888f004a0b86a9",
  "6288c8a54cf66b004a1cc63e",
  "62e5adcab4f567004ccff6c8",
  "628fe37ac168ba00498645d7",
  "64f08e31f345686d1df890ef",
  "65304bdb4f62f8aaf49dd589",
  "62809e82a07bfa004a63f8d1",
  "6492ffdf11e6bad37538583d",
  "63b199163bde17003a2e84eb",
  "6447b98f065f09e4f5b216d5",
  "63761cf5f6bf63004b60cf86",
  "62f421dfc93654004b7372e3",
  "63a3320d077104dfe17d1bfa",
  "634429e117d3d5004ca095c6",
  "62ee7ab271c5cb004b390cf1",
  "63dbb863bdb027f35e94e72c",
  "6501c704b7eb819601abaf1c",
  "64a70d37c991b3450cf3b414",
  "63a466b7078212cf4e3a3bc6",
  "6292b1f9f5b05c0049606c6c",
  "62bb862bb812320049a71ff1",
  "628f3c07519c34004a2a6f6e",
  "61eca897d2bbcb004a4c96c2",
  "630b6bc808cbe0004b55d866",
  "6350669d575b22004bf572a3",
  "61ab7101e16e0d0048c2cf09",
  "6531ae4e313b9d53b037e2ea",
  "65fc5e1d8bacccf78fc401cf",
  "646286632383223fe5af1016",
  "62e56e57703ae0004bbb95d4",
  "66164acdcd82d8a03a69ba97",
  "629a82d8bf3cc9004ad25399",
  "6320d2741d45af004b659fc3",
  "64f0490ff345686d1df84298",
  "64025d5415e0ef002292c573",
  "649f3561d3cf29d3d831d527",
  "6288a31b4cf66b004a1cabf1",
  "644a56b9687f1d96e48e8cff",
  "663b473879ad289445ac62d7",
  "62cd82dfb9662200497eaca7",
  "664f775af23becef49198632",
  "639108714c5c59004b69ea8c",
  "66d615d664c058deaf6852aa",
  "6579fc1e968bbb5d78c41566",
  "64ef7791f345686d1df7c6f4",
  "6282bc3b8f7d91004a7f4742",
  "62966e4d5b006400497672c9",
  "63e8825a218dd176dc703a58",
  "665ec617c71752aa97ac5ca3",
  "6459629fa23962cc1b2a04aa",
  "6536df0fefb814a53afd8ead",
  "62880d10928318004aa34140",
  "63682639cc4b12004b1b0b79",
  "6329dfc2db9ab3004ba337b6",
  "62a0e42d3187eb0049b51e43",
  "632b0330db9ab3004ba59094",
  "650ffaf1494c9d616709ed10",
  "65d644906e2e41ac15c1ce6b",
  "625767f51e8a13004a2b1350",
  "63638de2f18b27004ba3ab41",
  "632230101e6809004b7f3566",
  "63ae24ce3bde17003a2d5cde",
  "64093d98910f3b483c09b519",
  "6409db8cb2be15399f1222d8",
  "624f169074bddc004a83da5a",
  "63bf3249df71db94a5323c64",
  "62815ebdf8b5dc004af3fa2e",
  "633ded19ebc4c4004cfd3ceb",
  "649c7b2cefc77a49739831c3",
  "649c6885ce08f43bc31cb49f",
  "639e194d9e613f004badb089",
  "62fcfc589ef8ee004b6be667",
  "64a5f8d0f248fc833f931d20",
  "647d5a99c6a88c981d72cd60",
  "649c6dafce08f43bc31cd88a",
  "66a1130a67ff180b4d66cfa9",
  "6280fccfb4d82e004a73a08c",
  "62d313b628b9fc0049cba771",
  "620fc0e30cc574004b431f1f",
  "629d1b13c58d57004acd2aa8",
  "62dfd81fbbc301004be30fcf",
  "633012d49d2fd3004b34a150",
  "66cc8223a0c8f80b0391d82e",
  "61bf76ae406cf0004a4f219a",
  "641d8136168c760c92b1e955",
  "642af368acaa934b5b46bdfd",
  "638c9d138b4338004b91e93a",
  "64a1a90eeb0b0791d026bcb8",
  "6377ddb983b2d7004bf3a5e3",
  "6317ac90b0c92e004b08e549",
  "6385d98be4538f004b479182",
  "63511ab3a30a0f004d4bd7de",
  "630cc829ebb7d8004b23bf0e",
  "6256d83c236286004a32d0de",
  "63aed9a93bde17003a2d9985",
  "65bbb099ef9bfd7d03c189a3",
  "6398e4eca1e839004cd1c544",
  "64cc4ff32a6c35b14f21ff2f",
  "641208943050698c999ff1ff",
  "624c41c0e89b42004a789f25",
  "6424b50aac04aa9321112409",
  "62b26876599a3400494b09e2",
  "654ec2ba9a01f6fe976c7288",
  "63a4528ef8856a3ca1f2f88e",
  "634bed3e8bc9d0004bf3220a",
  "6358e05e99a430004cc51623",
  "635c0bde69de9c004b7a761c",
  "64a2da58eb0b0791d027731c",
  "634544852f7c04004b38c556",
  "63ac48273bde17003a2bf02e",
  "62c3617fabfb3a0049afedf8",
  "63da671ce704008b86608dfb",
  "62807082a07bfa004a63f032",
  "64689537b07555c2a609a67a",
  "66e177ffceceef220ca2c9fd",
  "6280d8bcb4d82e004a737e3f",
  "6303f885e18b7b004b2cd6bd",
  "635d43ac1bf2ec004b4834e6",
  "62e2e5fd703ae0004ba22baf",
  "62fdf1f955539c004b4836d2",
  "6345481f17d3d5004ca1a891",
  "6431bf0630cc043915e3bd61",
  "65034a09ed8b81621efa15c5",
  "648643ed5e7ab29d9dbae6a9",
  "628fc47bc168ba0049824ae9",
  "61dd86815b295e004b145ce5",
  "628fcc70c168ba004983713c",
  "6356dd54e3801e004b111a18",
  "629c8515211b5c0049a6a011",
  "63ef3bfb5f8737036fc1389b",
  "6302ea084cb595004b9e7551",
  "629cea51211b5c0049aab17c",
  "64ca794c394d741bea2bf227",
  "634df9bc6a7970004c74c564",
  "6676e33ff9fcb34ec4e39336",
  "63a8db924c1eff196c77590c",
  "6245c927675d5e004b1b51fa",
  "646df4cee20eea986944e756",
  "627eaec4bb4b4e004a441a5d",
  "6490a674c0b36cab673dc2f0",
  "6452b498686ecf1100551221",
  "621a31d23d0d42004adb211a",
  "620ef5105f65a5004a9d3923",
  "6535387bc921b8610631d189",
  "635c92791bf2ec004b47b43e",
  "6281125d9d77c3004a54851d",
  "63d7a85e1f2cc41fd873a940",
  "64d3bb70ec45e1d8e8d6e219",
  "64c5a38f28d955f9767542f3",
  "6376645df6bf63004b612165",
  "647edde06fdcd5c5bbee940d",
  "63ac7ae04c1eff196c7920f2",
  "6296b97e5b00640049803427",
  "651c48cdeca1bd6c8b4e3c93",
  "63864957b5a7a4004c25a5ab",
  "62883108928318004aa36723",
  "63dd14b398d8beee46da7615",
  "629e6fd256c9f70049b620e6",
  "63daf40ce704008b86615e52",
  "621e8d953d0d42004adbcb3c",
  "6378084365d523004b24a05c",
  "63cc4e8352942213ef24235f",
  "6399f4fd1d9614004b159ef7",
  "63ffbe55a8ee1db11d93a3be",
  "663bf6bede69d4c22e11aff4",
  "64b82b4c9c80d5cddfc8dac1",
  "6364db28a2e57f004c561ea6",
  "628fcf70c168ba004983ba06",
  "63a1ccefed3f89a7527d2a31",
  "636906dce45342004b10e743",
  "628e276503c7d7004a64ca5e",
  "631d9f16075586004bed8a41",
  "6499cebbed33f04f6f7e13d2",
  "63a5a049a70d98d233d21783",
  "64d13d8b2a6c35b14f244ea9",
  "628c0b6d326d23004aceb11b",
  "62a66cf64846e50049282f15",
  "6413b7643050698c99a0dd75",
  "65143437688530c627812545",
  "63ab74f44c1eff196c786e27",
  "628e63ea519c34004a10e1fe",
  "640df0ef27319600975176fb",
  "64920a40c0b36cab673e6c57",
  "64d0ee8f2a6c35b14f23d36a",
  "6351745c575b22004bf68e0f",
  "628f9914c168ba00497bb5a1",
  "6474660e76d19600d8564504",
  "6618e42f618984cf024b029e",
  "6411f25f2c68e51c6565c3db",
  "63a04321a6fe91f13bf1b58d",
  "62a8d379f87c5600497641a3",
  "63052ba8d3d6e1004bf9dbad",
  "63e8b06f218dd176dc7042f1",
  "640f29004fb8f234628a6dc9",
  "629b534dbf3cc9004adce1f5",
  "640a5bd7b2be15399f128c3c",
  "62de673b2eb09a004bdb0e7f",
  "62b5f17d7876400049bb8cf7",
  "636e8d698141f5004c3f6199",
  "63ab7a8b3bde17003a2b7f85",
  "64f391c1fe432dae1583aecc",
  "62d86d15cf6ba50049448592",
  "62af48392a10f70049e32788",
  "63de8df66a0a297a61b2d4d5",
  "62348d47d07ddc004aaa391a",
  "6634054670108fb63867087b",
  "62094b8458b09c004ac0361e",
  "63e25b4284167f10946088de",
  "648d4c8c6feb770d6394b0ad",
  "65198a730082d22b619de424",
  "64ef573b86a8da10564006b2",
  "646c9f4acae270ffd0afc410",
  "642a72367178d7b65608c661",
  "63a83fc44c1eff196c77314b",
  "62f1c743d46581004b041a8d",
  "646cae0c27b05007e36cdd43",
  "62b8602d1c4e1300494e72da",
  "6389ad8cd8cebc004ba604f5",
  "648041518ecae893390bc1f1",
  "6280b7aba07bfa004a6410b9",
  "65c2ba606e99d65850f79e72",
  "639fa95aa00ffe004baf8214",
  "635e6edc1bf2ec004b490949",
  "642a20325314539563f35ed6",
  "642a7adb7178d7b65608cd4e",
  "62d0a5658d93da00498f1145",
  "63cdabe152942213ef2497ef",
  "63e3ed264ff748ae87f60fc9",
  "64a7e6809cfcf71bbb778c97",
  "62cd1f03c8996a0049f5f136",
  "637bdfa2b33b6d004b655495",
  "624a410a5cb997004a59444c",
  "62965d7d5b006400497470bd",
  "62ab78b92a10f70049cb69b8",
  "666211824aff8419974d9bb4",
  "62b23e36599a3400494a1eba",
  "628ea3a2519c34004a18b4f1",
  "6477c6796712588cfb4b42cc",
  "60c769c8f3234b0045ed7815",
  "6288b3444cf66b004a1cbb55",
  "63c122b1cc251661e521cd24",
  "64f32351fe432dae158377df",
  "6283d5048823eb004a17c4a8",
  "62d02ea18d93da00498aae74",
  "636801a2130ae1004b99751f",
  "6475d70805605162f31230f1",
  "640a8b33b2be15399f129574",
  "64467c9103ec71fc122e51cd",
  "62f777a284b866004b8b5640",
  "61cb07ec276a59004a8c64ee",
  "614cb8fa9e990f00475b2d48",
  "64384dc36d98a4d055101dc2",
  "63ca78eacf8762c445837cfb",
  "63cbd099fcf17527256dcd6a",
  "63d48f0d57398a43e81c1744",
  "62876b8a740531004a5acba4",
  "64f5783004818c58efb4f34d",
  "62bd83103d25c5004af1caf9",
  "64c2d816152cc3adde3fcdd2",
  "6287999c30bef8004a1e16c4",
  "63715f528141f5004c4125a5",
  "6336d1bd722234004b740318",
  "634dd64c65c1bb004bc373af",
  "6356ef8fe3801e004b113743",
  "63778f77f6bf63004b621113",
  "63edf4bd1b5b1529a3d55a86",
  "64627c3b1422952637957e32",
  "63345d91cf69a6004cda750b",
  "6367af91cc4b12004b1a852b",
  "62803f89a07bfa004a63e7c0",
  "6287c40c2cfdd4004a6ed54a",
  "648af5d0a2ed9dca51368692",
  "628f4dde1714a80049398ceb",
  "62967a8d5b006400497830ab",
  "62715562f0c2ef004a126021",
  "62adedc015f91100497cd7f7",
  "63bb75be9c8dbc3dda301739",
  "643867e2ee6da54df5e66f2d",
  "62f01cfad46581004bf5f576",
  "62eab6555fd326004b1e469d",
  "633c75fb9f6946004bd18a0e",
  "6594478d448bc4215dc37bfb",
  "61daaead69a759004a80c8b4",
  "666e275a676211195e7d54ce",
  "6377f9cf65d523004b2494b9",
  "6521a73a0995a4d93f20c460",
  "61b36b543eb64d0048bad00a",
  "629dcf13c58d57004ae20ad2",
  "63ab502f3bde17003a2b6db0",
  "64ff50283d52d401addf730f",
  "631e656b1d45af004b6283a5",
  "6495dbe95e0799ec2c7c7d60",
  "62daf4acc9672e004a0ef526",
  "62f938489ef8ee004b42c5db",
  "6406169415e0ef0022943e08",
  "629f238956c9f70049c95b89",
  "63d43dcd1f2cc41fd8720515",
  "637fab4710ffff004b689096",
  "6423348f2f5429c10e01ba00",
  "641cad82168c760c92b18807",
  "6293d781f5b05c00496a0c5e",
  "634480cd17d3d5004ca1241e",
  "630cb927d12d51004bf9b423",
  "64738c0c5e167f0d1afb6bdd",
  "64f24d0604818c58efb40d73",
  "649b1808c22aa28cdbac0b39",
  "615cb8423522f90048151b95",
  "63cee1f852942213ef25ca61",
  "62ee874ad46581004bea1698",
  "65119fefe6a50891447a1d82",
  "6411faae2c68e51c6565ce8d",
  "6429fd795314539563f34699",
  "63658bbb130ae1004b97c8b6",
  "624a4e575cb997004a59456f",
  "64a445f06b60aff5dba9f433",
  "6298b243c97e4500490d514e",
  "631475ca6e7c9b004bef2278",
  "64fc13063d52d401addebd1b",
  "647b10966fdcd5c5bbed28dd",
  "62ab7ec815f91100496ec886",
  "6284dba505f0bb004a7a0919",
  "62e17f7905ed45004b2474d2",
  "63c1992a5904aa390645d497",
  "642a1bb65314539563f35d35",
  "639a8f1e1d9614004b15e3df",
  "641f19c25a418a529f0028bc",
  "63643095f18b27004ba4dc12",
  "651d803945184d5cd1ec0d57",
  "62c9bbd0bff3ab004978321e",
  "634eebdd30fb13004b24bca3",
  "63f553f4b69a488c860dff6a",
  "649d64fd2d0c34f96bc572ae",
  "62e6b208703ae0004bc7fd23",
  "648e343e6feb770d63952aa7",
  "62a44057b3113b004936dced",
  "649c8473efc77a4973985119",
  "629c8194211b5c0049a6858e",
  "628fe400c168ba0049866e1d",
  "63d42ae01f2cc41fd871f90b",
  "63751666f6bf63004b60052a",
  "63ce6a7afcf17527256ef5cb",
  "63e67100218dd176dc6f673e",
  "62e504fd703ae0004bb7880b",
  "628e40b403c7d7004a64e455",
  "6281d533fa112f004a2b3bc4",
  "6404c94515e0ef0022937371",
  "6533cecedb2c55165107af21",
  "62c84cabbff3ab00496d27aa",
  "6313c2016e7c9b004beebfbc",
  "63d43ab157398a43e81bec4e",
  "63db7451bb43da7cd33698a7",
  "63a321819a4fefce46f8a2d3",
  "62e11571d36297004b6ccb65",
  "642a44465314539563f36484",
  "633c56499a3520004bb76e3a",
  "62d405b372c9e00049e76a11",
  "620238b296f825004a154d46",
  "63518e19a30a0f004d4cc202",
  "63514344a30a0f004d4c0468",
  "637a5f0665d523004b25c2bc",
  "642b1811b03ff7d2c0f6d9f8",
  "64825f2b4f64f7c918ff483c",
  "6658be11b700e873d8baa854",
  "6287d9192cfdd4004a6f0094",
  "63be244b3ef39fd1418bbe20",
  "62bd60f53d25c5004af0e176",
  "637a3ecf83b2d7004bf4e3a1",
  "649c703fefc77a4973981551",
  "62ab1ab02a10f70049c8e412",
  "631118536091e2004b241683",
  "628de06ec53091004a2646ee",
  "628f1f0f519c34004a2710d2",
  "62a2239d32b0be0049907f94",
  "63a5cdcd3bde17003a290129",
  "63fe93439c5cc05507a0fa62",
  "65e2363c4622aae6c8a3fd43",
  "641304f12c68e51c65664f0a",
  "63e54654218dd176dc6e8756",
  "632201451e6809004b7ef24e",
  "630f2c77d12d51004b191c98",
  "63f613bb1afdcaabe443225a",
  "651d718aca70b91c337e9995",
  "63c0782aa52fb8a3b5c3c757",
  "66e1d68f57658c3954f4b36a",
  "6519bee50082d22b619e03b7",
  "633a0c2f722234004b760124",
  "628a9f0e02d452004aedda61",
  "62e5911d703ae0004bbcc825",
  "63a962614c1eff196c776f40",
  "65ae7206901610a5e0bf5918",
  "64234ad7e8c1a14c799c4c33",
  "6474d89f05605162f311d7bb",
  "64b54aa9515b6150efa878a9",
  "645ac30f920c70f93e7e3ae7",
  "63a980754c1eff196c777efb",
  "62f011f771c5cb004b474ae7",
  "6324b075286e0f004b713cdb",
  "631c741d075586004becf1a6",
  "646b2361c0a92ee066d6d2ea",
  "6292cef0f5b05c00496157e0",
  "64e281f0216a28a008de5477",
  "6256a72f01a7b5004ac4f1e5",
  "6407b629910f3b483c089207",
  "64458ec5277d0d703ba73f60",
  "630e447cd12d51004b0e616a",
  "627c1719688160004a3743d5",
  "6420d1ce5a418a529f00a7c6",
  "63ac700d3bde17003a2c19e2",
  "647ca067c6a88c981d729c3c",
  "63860f61394b10004c696f99",
  "62d13b88fa1bbd00498378e5",
  "6295f4c4c77e290049a1d5d4",
  "650b07b13110fb0522fd57c9",
  "64b044111c02d45dcf3d9737",
  "64b10e19a5320b1d48ba5e54",
  "64b3c72e515b6150efa7b122",
  "623a306ebd562d004a678174",
  "62aed3b22a10f70049e07941",
  "62e402651c9bdd004c121ee9",
  "624d586b8d91d4004aa3f03a",
  "64318ed430cc043915e3a3b2",
  "651d6d1945184d5cd1ebe924",
  "62de5b002aa390004c779bad",
  "63a81c193bde17003a2a3743",
  "615f370b3ec4a30048cabd38",
  "63da442fe704008b86602fce",
  "6372bd5ad0e8a4004bebafd3",
  "63b80577567febd3fe4c765e",
  "62811ccf9d77c3004a549763",
  "62ade0ca15f91100497c912a",
  "6280f301b4d82e004a739786",
  "63049461dbf509004b0fa4ac",
  "63710a0585d8e9004b324605",
  "642a73755314539563f36c03",
  "643fc33a8a389c983f3939e7",
  "648d9d94bd43f6e21550e4f3",
  "64ea0fdde9598cda09f91581",
  "6424d0f7ac04aa9321113221",
  "63a97c304c1eff196c777d1b",
  "645d3d380d2538bc437f8e2c",
  "64119a12fa2fc20267251a28",
  "629bb4c3211b5c00499f2818",
  "632ef8ebe20362004bfecf09",
  "641706052c68e51c6567cb72",
  "6446da0003ec71fc122eaf86",
  "629cc2e9211b5c0049a933c7",
  "63d97a42c9b8463927c373aa",
  "62d7a06ac00974004904eb9f",
  "637e5be410ffff004b679f00",
  "642066f65a418a529f007e51",
  "649c73c7ce08f43bc31cf34a",
  "6585d41a86a424bad2cdff3f",
  "6505ded5c0d8e41f4427fade",
  "62ed934ad46581004be18dfb",
  "63699190cbebab004c4aa514",
  "629c8e78211b5c0049a7152d",
  "61f7c94d9f0758004abac86f",
  "642b02e9b03ff7d2c0f6a151",
  "64174e1e2c68e51c6567ddc6",
  "641b25390f8e0c52fcf66d31",
  "6352a6a4575b22004bf7c4ea",
  "651dd80eca70b91c337f0d05",
  "6353102f575b22004bf87c06",
  "6453014be16f2b4e4e83b128",
  "629dd6a2c58d57004ae307a9",
  "628e31a903c7d7004a64d14a",
  "637257168141f5004c420ea9",
  "63a05232a6fe91f13bf1be45",
  "62dd8f7bc9672e004a26caa9",
  "62ded5f3616ad8004c82a9aa",
  "64dbe641ed5b9ef9d313fe6f",
  "636e64ed8141f5004c3f26a6",
  "63a942593bde17003a2a6d12",
  "6280d71cb4d82e004a737bef",
  "62a22fff32b0be0049916352",
  "62e93307a5ca4d004b63a732",
  "622e0cecc8cac4004b139ac0",
  "6331bb3126bc05004b090419",
  "6383b7b1f6b013004b3e8258",
  "62aeefd215f911004982d950",
  "659a4f7e6baa75eea8fd9342",
  "6532c4dbdb2c5516510737a1",
  "6358447c05572d004c3b1948",
  "629cb039211b5c0049a88724",
  "62809c9ea07bfa004a63f807",
  "61d08ee5aff234004a724051",
  "6540d5c3a99415f40d6e7c0d",
  "63e2d3f084167f10946117d3",
  "645c13fc19778803b1b3ecca",
  "62b1df2ad3116b00496f66ab",
  "62228c3f20dddc004aed8f49",
  "624bf8cae89b42004a7893ae",
  "64309a4630cc043915e371f7",
  "6298f86473a180004977ef57",
  "62de3d852785f700490ecbde",
  "62e8cd63b4f567004ced6941",
  "63c404615904aa390646afc4",
  "6327649e99a775004b2484a5",
  "6345d02ba6dfd1004b429f73",
  "63741ce5db65c2004b278650",
  "64669f0513882dd592f6450e",
  "6398c153a1e839004cd1a65c",
  "63779853d6179d004bb25e26",
  "629dc08ec58d57004adfd9ad",
  "6296315fc77e290049a9d35c",
  "649ad95ef26c778eae45d59a",
  "6521b0c30647bc8bea37773d",
  "6280b006a07bfa004a640da9",
  "62a2787632b0be00499613d7",
  "63ab23d94c1eff196c784dca",
  "647c8191c6a88c981d728f52",
  "64c7aea8a0250ce3d7d342fa",
  "64a2defbd3cf29d3d83389af",
  "64d53fd0e18acaebbacb9cc0",
  "6437c6b49a84a5d87ecbf80c",
  "64565569705921416ef9b588",
  "652da5719a59d38b12042e2b",
  "62dd8d88c9672e004a26b521",
  "6388fa0507f64a004c29ebe2",
  "629d3854c58d57004ace2679",
  "653021a0e4d7bf3b243c380c",
  "652a4f02ac7e34b83c34c465",
  "64a01d23eb0b0791d0261086",
  "63aad35e4c1eff196c781905",
  "64ae5c441b415bab2be88e68",
  "6370d2ae8141f5004c40a0b6",
  "61cf2696aff234004a7222f1",
  "629202fef5b05c00495a5a41",
  "62fa901984b866004bad8f4d",
  "639f5e4ea00ffe004baf6936",
  "62c35454abfb3a0049af5468",
  "629b7435bf3cc9004ade3d00",
  "62cd7340cc77cc0049f51897",
  "63eaca475074500ad146d8ff",
  "642d74a3eb7c4dfbb46b0528",
  "637b58e3b33b6d004b6484b9",
  "6408adae910f3b483c0954dd",
  "65284839539bc552a3b211e3",
  "62b7156e7876400049c024f4",
  "63b59426017429a6de6f8c24",
  "657b7ae8b400fb909b9594ab",
  "64b0612ea5320b1d48ba2e1b",
  "6307e5fdd32e39004bfff463",
  "62fe9f8aaced7c004be00f79",
  "639f90f69e613f004bae0e33",
  "6482b09b4f64f7c918ff63fa",
  "64a3f826fb419e35ee4d5009",
  "63bc60bbb19a7c2599d1c2f7",
  "65cfcef6ae6b6d1d421bc60c",
  "6287d1b32cfdd4004a6efab8",
  "63cee2d052942213ef25cb03",
  "63505c1a575b22004bf55a1f",
  "6358de8305572d004c3b81df",
  "642bfac89e1cb48e9ef597a0",
  "643bbd35ee6da54df5e7906c",
  "63a04124940b7ea38b8758f8",
  "62909dd1c168ba00499d28c4",
  "64621622b843a834c9e04010",
  "62a614f54846e50049266b7f",
  "6421a4fa4195a80a049438d4",
  "646bd858cae270ffd0af5297",
  "637a2c8165d523004b259ef7",
  "64fd06b5bdfa14f9c27c10fb",
  "636becfd63ce2b004c1ba7b0",
  "62a2ccdd5949110049db0ba5",
  "631f2ece075586004bef187f",
  "65cf228e304b9c9c6be19947",
  "6256e161236286004a32d5fc",
  "628ea2de519c34004a187782",
  "6460121f5ab0dcee3aa895f5",
  "629e56b956c9f70049b2649c",
  "6356b68505572d004c3948ef",
  "6295c7f8c77e2900499c32ae",
  "629b2e9ebf3cc9004adb519f",
  "63b64ed66c59b6121d22d9d4",
  "649d467defc77a497398e814",
  "644988fc2568c0312d253f5b",
  "63a7174e4c1eff196c770384",
  "651905c2b24e74e99f6783e1",
  "64a46c09500bcd38779c2155",
  "642ac7eab03ff7d2c0f65821",
  "646fa6ffd58947086e39de20",
  "6356824332b60e004b79329d",
  "62928642f5b05c00495ef346",
  "6284ae4d05f0bb004a79ec72",
  "64666ed513882dd592f61200",
  "62a2de2f5949110049dc4ea2",
  "625718141e8a13004a2afab5",
  "641b2b1a34dff3ae05f08e2b",
  "65217b920995a4d93f20b66d",
  "62a121733187eb0049b91109",
  "63b96188567febd3fe4d7ad6",
  "62e82818703ae0004bd6d61e",
  "64c3fcd428d955f97674c0d0",
  "6280d99fb4d82e004a737fbd",
  "641273c12c68e51c65660343",
  "628fdf73c168ba004985abf5",
  "664e222e598bab8be0ee5c45",
  "645d43fa3b0bcd777d8e1b07",
  "6460ff5de4fdadf66e151767",
  "63924981605b27004b1b71ed",
  "633cae749a3520004bb7db6a",
  "62b0eb87366e14004909573c",
  "62180c053d0d42004adadb7e",
  "63324506f3349a004b1c321a",
  "62dd50a6c9672e004a24a3de",
  "6245cb7b675d5e004b1b533a",
  "63aa1b164c1eff196c77d7dc",
  "62909727c168ba00499c3f84",
  "635a6adb0f322a004cc8d95a",
  "62d074b58d93da00498d7161",
  "62e389d5703ae0004ba88a0e",
  "6337f42486498e004bca927c",
  "63e1d4ea7de8c3da5693178b",
  "641b225a34c53a4e97ff8a61",
  "63b362b953aff3f86b6881e0",
  "63c41ff15904aa390646cf5c",
  "641239643050698c99a00cdc",
  "64a70d30152d5a46c1967c21",
  "6287972330bef8004a1e0ec7",
  "62838899a4b713004a499ee6",
  "62e3db401c9bdd004c10ab9a",
  "62fb4b719ef8ee004b5a87d4",
  "650aee2867e4f186f9761ef2",
  "62e429a7703ae0004baf1825",
  "63ab7e563bde17003a2b81f9",
  "6341aaced37336004b2873b9",
  "633c3ff69a3520004bb75991",
  "633f02dd59b828004cbda3bb",
  "62d069248d93da00498d0ba3",
  "6458ff11ae166fbe2fc7f3c3",
  "62eab6fd5fd326004b1e4a31",
  "6373dd10db65c2004b27319e",
  "63bf4508be96f2327030dab0",
  "6293c37cf5b05c0049695f97",
  "640e297016dabd973d7168b8",
  "63146fdd6e7c9b004bef19ee",
  "63e7f948218dd176dc7014a5",
  "6428cd3f5314539563f2e21d",
  "6347ffbdf27224004b1413db",
  "63431d75d37336004b292a71",
  "61ce6a98aff234004a7206ea",
  "6532e76fdb2c551651077f48",
  "6320b31d1d45af004b656500",
  "642a94ad5314539563f394f1",
  "63b3f4b2897c8f4a8130f02f",
  "645e955a5ab0dcee3aa80172",
  "629a46c9bf3cc9004acf60c5",
  "62878b7e30bef8004a1dfb65",
  "62811e269d77c3004a549865",
  "629b60b5bf3cc9004add71a2",
  "63f25d265ddc7dfc693b919f",
  "64518675686ecf110054617f",
  "642a03885314539563f34a56",
  "638b77b759d281004b5fdbd1",
  "645f3a8d5ab0dcee3aa83284",
  "63cfa6c4fcf1752725701244",
  "6262f65b1b7f9e004a939afe",
  "6310fd8f6e7c9b004bec812b",
  "629fd4dbdb130c004953c86e",
  "62ea2b0da5ca4d004b6ce644",
  "62823c18e3d214004a23f7c4",
  "6428ce7d7178d7b656083662",
  "62808b60a07bfa004a63f358",
  "649d620b2d0c34f96bc56414",
  "63ac91a74c1eff196c795070",
  "62c57f671a21be00493b8f20",
  "63ac65563bde17003a2c03fc",
  "6328584fdb9ab3004ba18366",
  "651732d8b24e74e99f66f331",
  "62ff6389aced7c004be99b3c",
  "62b9abd0fc434b004982e120",
  "63d1af5522a0bbbb8676a849",
  "654775bb767d26656f3691bb",
  "62c5e6c9688e490049bc6ce2",
  "63b4a70c99548b3f12dd9c17",
  "64596bd0920c70f93e7d42fd",
  "62d2afb772c9e00049dd220e",
  "62e76a9fb4f567004cdf84e4",
  "63452b9417d3d5004ca174d0",
  "63779ab1d6179d004bb26714",
  "6745facedba27c9f2033d188",
  "6453c0abe16f2b4e4e842229",
  "62d74a48808b8700499a0eca",
  "63d526c957398a43e81c6195",
  "64ea0dbae9598cda09f91440",
  "641dc4585a418a529fff9308",
  "62d2be0528b9fc0049c9343e",
  "63504e20575b22004bf52ed6",
  "630fce2424a58a004bd193b4",
  "64abb965933996d4ff0f11f8",
  "64996b1bed33f04f6f7dc1da",
  "6441d0f6cb6d3ef853842ae0",
  "62b7804d7876400049c09d39",
  "64689f0dc0a92ee066d60a64",
  "633e1e07ebc4c4004cfd692a",
  "63b4000737bb074c81c187dc",
  "63ba723b567febd3fe4dff01",
  "6287ba5330bef8004a1e498c",
  "63e24d137de8c3da5693aff1",
  "645f53475ab0dcee3aa83f16",
  "64da46efe18acaebbaccfd1d",
  "61e828cb78d330004aea88b0",
  "623cff1b3dc5ee004b1e6385",
  "63036d1ce18b7b004b25ff76",
  "6363a186f18b27004ba3c2c0",
  "62795f9158cbeb004af3094c",
  "629e42af56c9f70049afb8a1",
  "645fc9675ab0dcee3aa87d9e",
  "649a3a75ed33f04f6f7e524c",
  "649c99d5efc77a497398880e",
  "6314f85a6e7c9b004befa9b3",
  "63e53f34218dd176dc6e7a43",
  "6470d3d0649b6432fe070521",
  "62a23c1e32b0be0049922865",
  "6504b3a3ed8b81621efae4b6",
  "6335894c162274004b84333e",
  "63f62710b389bde31ab0337d",
  "6343405b59b828004cc032a6",
  "62b7aea07876400049c0a5d8",
  "62d9870d40da0b004a82ac9b",
  "63c43da95904aa390646d7fd",
  "62878647740531004a5b0db4",
  "6319f654055a89004b0c319b",
  "652d3555d4b09d1365dfecbc",
  "653ffb42e3544ed42573ddd4",
  "630e7429ebb7d8004b3734d7",
  "63c3b9f85904aa3906469e53",
  "643083f330cc043915e36c6a",
  "65228daa0995a4d93f20e9cd",
  "6377da5065d523004b246ece",
  "64a47483500bcd38779c2a53",
  "62c3622f6e19670049b371ee",
  "629b486abf3cc9004adc6a76",
  "62cfd32a8958d6004985b024",
  "63fc24a79c5cc055079ed0e1",
  "636d03b2c51245004cf88050",
  "63a3647699de17c875703c31",
  "622cd73ec8cac4004b13852d",
  "641ddf7e8b0db5a276103dae",
  "631cfd75075586004bed4c24",
  "629cbaf5211b5c0049a8e7ff",
  "62ed8d47d46581004be159f3",
  "64a6dfa1c991b3450cf3772d",
  "63cd6b9cfcf17527256e5074",
  "62a0f20a32b0be00497a776c",
  "648321605e7ab29d9db9cb78",
  "64ca7b4c153f67b83986b538",
  "6444a45c36889f01cc08c37a",
  "64807d932b1b94341c82c360",
  "62fab6e09ef8ee004b545ac2",
  "648901287708f02304bd03cf",
  "64101d7a4fb8f234628af9a3",
  "6350e843575b22004bf5ad97",
  "62f6992d9ef8ee004b26dffe",
  "63e162ce7de8c3da5692b9f9",
  "6280058dba5a12004a3b7f16",
  "63381fbc722234004b751391",
  "6356d674e3801e004b10fb20",
  "6259f456bd376c004a5710a3",
  "6373b2619eccaf004b2a5a6e",
  "63e2b55584167f109461052f",
  "651beae9c6e9eb8b164fb015",
  "651dac72ca70b91c337ee21b",
  "62a090f930702400493bf5db",
  "63bd5a173ef39fd1418aea3d",
  "644d23eefc4e77e44fe2a71b",
  "6205a61c58b09c004abfdabd",
  "619775ccc9fbcd00493206fb",
  "61a29f3f871d690048b324a1",
  "634959f3e48ca9004bf5219c",
  "62e6c817703ae0004bc8e99e",
  "62998afc73a18000498ab751",
  "628125619d77c3004a54a550",
  "62d0f8128d93da0049913fec",
  "63f171055ddc7dfc693b5b1c",
  "649af3bce056e0f42cc84622",
  "62825d430b1398004a5f39bb",
  "63517af0575b22004bf6ae9b",
  "63258ceb99a775004b2344d8",
  "633de87ec32cbb004ba8a99f",
  "63bbcd2e8d7f46fac461ff00",
  "62d52a4472c9e00049f0f66c",
  "65356873c921b8610631dcd4",
  "6531b1bf2e681a302f23f730",
  "62e8ec45b4f567004cee6c4e",
  "627a4d3c8efa1a004a433508",
  "62a7a7f34846e5004939dbf4",
  "630ce1eed12d51004bfc1fd1",
  "6388565fb5a7a4004c26ff43",
  "62ec853ba5ca4d004b82df27",
  "631cf6ff1d45af004b615519",
  "6486459c6208e2c5615b9da2",
  "6490afb9d2b624da3ae4d3c5",
  "63dd68c6a114166d6d862d36",
  "6392695bd8efc1004b7d0ede",
  "6197dc5ca70b770048eccc57",
  "63f2a2345ddc7dfc693badd3",
  "628e7feb519c34004a142ba8",
  "63da8fe7e704008b8660cde8",
  "63c2d98a5904aa39064669e6",
  "64d8f0e2ad268f13db6b61b7",
  "63d52ee057398a43e81c6a80",
  "63fbb05d9c5cc055079e98a0",
  "61aba2afe16e0d0048c2e5da",
  "62877c68740531004a5af937",
  "64537b22e16f2b4e4e83e225",
  "6340886d59b828004cbedd38",
  "652519c9949adf7bdc0ad2e3",
  "646074a55ab0dcee3aa8a5be",
  "628122739d77c3004a54a205",
  "629515e0c77e29004985b8ab",
  "6378c1f383b2d7004bf43038",
  "62d7f40f2c55c0004915483a",
  "635174c5a30a0f004d4c7522",
  "6377ce1483b2d7004bf3840a",
  "6367bf6c130ae1004b993965",
  "63f7c32d4e66c24d52b1bbd8",
  "6261c5a03b865a004aa8adcc",
  "6289af0850b204004afeedae",
  "64177dcc3050698c99a20a87",
  "646bd9c1221079c32e49efae",
  "6448282402675a693dcc4be2",
  "63e81ba4218dd176dc7024d3",
  "63fa39ac2b5ffb01b43b3c7e",
  "6286c6c09e7a9b004ab42eaa",
  "629b961e9f24360049fb9b49",
  "6418dd1ec0a642bb2b38d05c",
  "635567233fa984004c55082c",
  "6363d19af18b27004ba424f7",
  "63c713a39aa331fa8a761d65",
  "62e45c91b4f567004cc44c6c",
  "6288f4cb3b1522004a2d6e1b",
  "62c34871abfb3a0049aeeb3c",
  "63a36ffc99de17c8757047c9",
  "6283344d75fcf8004a95df34",
  "6245ff4f675d5e004b1b656c",
  "62a208973187eb0049c9eddc",
  "623da8aa3dc5ee004b1e6a1c",
  "640dfc5916dabd973d714ed0",
  "64f81d67eb2ebd205ea7274c",
  "62eaf268a5ca4d004b75511c",
  "652d97d99a59d38b120427db",
  "62903c98c168ba004990f5d5",
  "62d4053272c9e00049e764b2",
  "629c7502211b5c0049a60338",
  "6395af6319b378004b067cca",
  "651729ef0082d22b619d2753",
  "6276b568bb772e004a2b5e81",
  "618a8cd2dd8b540049659d17",
  "637eaf5f10ffff004b67f35f",
  "630e6c9debb7d8004b36b368",
  "6469f214c0a92ee066d66814",
  "645ebb2f5ab0dcee3aa812e5",
  "64309f7f30cc043915e3744f",
  "64d74535e18acaebbacc2d3c",
  "62b5f39f1c4e130049494221",
  "6507ffde86b7398611e6ecdb",
  "62ff6d5baced7c004bea1e08",
  "648e98736feb770d63953734",
  "642a1ca27178d7b65608b70e",
  "615ee3153ec4a30048caacbc",
  "641b541528a82a8c84ed40e3",
  "642b4ec0acaa934b5b473772",
  "63cecd5cfcf17527256f9200",
  "633589fb162274004b843398",
  "62ab1b4b15f91100496c0c4e",
  "61cbb09b276a59004a8c73f6",
  "6280aa4ca07bfa004a64072f",
  "6488e689e966693f1301141e",
  "63dad626fd000d43405a0dc8",
  "6405fcdb15e0ef002294123e",
  "636a18c48359fc004c616e17",
  "62ab3d7a15f91100496cdd57",
  "639f6b75a00ffe004baf6c35",
  "64a70b24c991b3450cf3b0d8",
  "649ec382eb0b0791d025361b",
  "652574e36af95d689be34d39",
  "62471e9252b323004a4fe9f7",
  "634c3dc78bc9d0004bf374d8",
  "628f54561714a800493a5670",
  "649c62a3ce08f43bc31c9648",
  "6463b8751422952637968051",
  "62e02b540a8955004b176997",
  "63dedaf0a114166d6d871b3f",
  "63ee93775f8737036fc0eca8",
  "62880e59928318004aa34358",
  "62ff83124cb595004b77229d",
  "63c07f2da865fdc01318850c",
  "65d743268307055ebbea93ed",
  "646155555ab0dcee3aa8e68f",
  "62af78ac2a10f70049e46425",
  "63c71dfec9fe947c61dd661c",
  "63ff7a6fe65f80346ce7ecd4",
  "6515314d5a2a491d03038fda",
  "63f07a1ce9c63adf77164001",
  "628bcd54cf90c0004a45dfd7",
  "63ab0c6b4c1eff196c783c52",
  "64bda017943124da8e61e38f",
  "628cf06573803e004a8aefb1",
  "64594d2d920c70f93e7d2783",
  "60e7241a49ba53004770f0de",
  "640b69e15f0dfcbefab9603c",
  "63777ad5f6bf63004b61f23c",
  "646bdc65221079c32e49f24d",
  "652152840995a4d93f209fcd",
  "628117319d77c3004a548fe1",
  "6418a509c71cfd369a473a2b",
  "6356bc3205572d004c39699b",
  "637d578abe0a7b004bbd8487",
  "62f7f5139ef8ee004b35ff16",
  "63a8566b4c1eff196c7735b6",
  "649fdf09eb0b0791d025f0ee",
  "6469daf8c0a92ee066d664a4",
  "6291f37df5b05c004959c9f5",
  "659be1a68a82ed0de4e2a7f5",
  "631aa3e53ec078004bde4cad",
  "659ff57d71ba4d0347e296d9",
  "6364f34e0c0ec5004b50c641",
  "640a8f6a910f3b483c0a6e59",
  "6277d998102ccc004a16db54",
  "667bf96f239e1e891093c2d1",
  "6489cf6f4f2b2a7c5b5d56b3",
  "641ab997115bf388cf6057b6",
  "63c4fff7a54c9b18e96fba6b",
  "649df08d29e06608afc03989",
  "62e1ffb305ed45004b292db6",
  "6367bbf3130ae1004b993189",
  "643822a26d98a4d0550fed16",
  "63346399cf69a6004cda7c8c",
  "63703d9485d8e9004b31e59a",
  "646534617d47e17fd276e1fe",
  "646bdc51221079c32e49f237",
  "63a306ffc2db910d0ea00e3b",
  "62955487c77e2900498d97a5",
  "63d6dee91f2cc41fd873269b",
  "642736275314539563f25aed",
  "64935b30c42945f67c92fe29",
  "628bc53bcf90c0004a45d68f",
  "628040cca07bfa004a63e83e",
  "62f4de60c93654004b7ae3ba",
  "630d08ffebb7d8004b26f468",
  "64bd5ff5943124da8e61c97f",
  "63814e12bf4577004bc5700d",
  "648df171bd43f6e2155124db",
  "65b4d1db69b70e365d64f178",
  "62d3650772c9e00049e2b612",
  "62125a0d0cc574004b4377aa",
  "63a16c0897f9259c786d637e",
  "6520037c0647bc8bea36adf1",
  "62ab9d2f15f91100496f7e9d",
  "63518868575b22004bf6d0eb",
  "649890025e0799ec2c7d4296",
  "628814eb928318004aa34ea2",
  "629152b8f5b05c0049528a27",
  "6280d7d1b4d82e004a737d3c",
  "64ca768e75039af0db6f1ff2",
  "62cedf9c8958d6004980c9be",
  "635a47ff0f322a004cc8a415",
  "62821f97fa112f004a2b6784",
  "6291a874f5b05c00495703cd",
  "63c5e9070afb41bd3b4ec571",
  "6280b64ca07bfa004a641052",
  "63a63e473bde17003a29ab98",
  "62572b601e8a13004a2b02cd",
  "64246438251b6e74fb5a4a7e",
  "651d5e8aca70b91c337e6c45",
  "62820988fa112f004a2b4efe",
  "6494a5b65ce8da4dccf13850",
  "644c3a4cdc098e8b83ac9bc4",
  "6499cd40ed33f04f6f7e11e8",
  "6402b9de15e0ef002292e427",
  "64c82394a0250ce3d7d38dfe",
  "64301329532d5e72ac792363",
  "63a1ce8740bb2fef9d0d09ef",
  "63a324449a4fefce46f8a9e4",
  "651d2b15c9ab47da056f1607",
  "63d2b80c30211bca53d84ba7",
  "6418560e3050698c99a26c0b",
  "63ceba0bfcf17527256f5f38",
  "6421c9c04195a80a04945a2d",
  "63209979075586004bf0f7bd",
  "635cf1e269de9c004b7b0981",
  "630a289908cbe0004b47edd9",
  "63f4c42e89def3d91210d7d0",
  "635533963fa984004c54dda4",
  "635b773369de9c004b79d6aa",
  "641f6f8c5a418a529f004206",
  "6445abf236889f01cc0902cc",
  "62b50dcad674840049fbb576",
  "6291952ef5b05c0049566349",
  "641cd2cef652599b7663f13a",
  "625990c3bd376c004a5700af",
  "66ef5b51e6821ced741742cb",
  "636abfde00c2a5004ca281cd",
  "628052d6a07bfa004a63ed30",
  "63a11e6d97f9259c786d531a",
  "642e72d99e1cb48e9ef765f2",
  "63cec8c552942213ef25a7e4",
  "62c460479086080049d739d4",
  "6306443ad3d6e1004b07e3ec",
  "63dd64c86a0a297a61b20fa9",
  "63440351d37336004b29b5a3",
  "63a49623ef87bf84dee5b826",
  "630de448d12d51004b09b138",
  "64caa725153f67b83987397e",
  "642857c05314539563f2bce8",
  "649cb517efc77a497398a21b",
  "63c0731da865fdc01318796e",
  "63c415a0df9ae32d31b5a6a1",
  "624ca15629d9d3004a60e1d0",
  "62b809a07876400049c0af56",
  "635582173fa984004c551cb5",
  "6299cd9f73a1800049936037",
  "62f77a4984b866004b8b745d",
  "631ba0d71d45af004b6088d7",
  "641044d081ffb000ac8763c7",
  "64202a995a418a529f0065be",
  "65239da50647bc8bea37dfcc",
  "647bc0e1c6a88c981d726ee2",
  "63cead8752942213ef2575bb",
  "643da917ee6da54df5e843c9",
  "6466197b1dfedbe692063350",
  "64501151fc4e77e44fe3acdc",
  "651e4b68ca70b91c337f3f1b",
  "62e043580a8955004b180948",
  "62f3d6ae183c98004b5ff1a9",
  "63a3553c19dd48663b15bae3",
  "6432ec9730cc043915e3f7ce",
  "6398caa3a1e839004cd1aa38",
  "65453f40a1dc2543466216d7",
  "6430386430cc043915e345be",
  "64d528f196f5cd4a123fe735",
  "635c28b51bf2ec004b475639",
  "64ea0592e9598cda09f91174",
  "63b0d54f4c1eff196c7b39b7",
  "647f0b806cd028e2857499c7",
  "63d72a271f2cc41fd87350ae",
  "63b70bbbb4ae428ffbe3fb67",
  "63016820aced7c004b00d8c9",
  "63df46ffa114166d6d8748a1",
  "63c53b1fdf9ae32d31b64aed",
  "634da6c29eb087004c44676e",
  "636bb5de114750004bbf4600",
  "6400ddcc997289fcd5898e5e",
  "6488bf067708f02304bcda34",
  "637c9566b33b6d004b65b7d4",
  "656dcbede3b439f93861cf70",
  "6383691ebf4577004bc623a0",
  "64905e1cc0b36cab673d7ab5",
  "62b80cd27876400049c0b0a5",
  "62d5fbd328b9fc0049e318f0",
  "63a9e1c64c1eff196c77a4ba",
  "62c980ebbff3ab004976d220",
  "6470a78e86d27b79db8c2ec5",
  "62be1789486b650049937024",
  "63ff25509c5cc05507a14bf9",
  "631b19e33ec078004bde8ea1",
  "63011e55aced7c004bfda94f",
  "64b2985607ad4a55d184549c",
  "63a20071ed3f89a7527d5418",
  "635e504c1bf2ec004b48f22d",
  "63fc7d6b2b5ffb01b43c328c"
];
const NON_CONVERTED_USERS_WITH_DEPOSITS = [
  "6492e8fbdf6122d366cc2366",
  "664f384ef645f03ed6e5800a",
  "667d7a4ef2eb5d769770e6d9",
  "667d84dbf2eb5d76978c492f",
  "667d84dcf2eb5d76978c4a3f",
  "667d872af2eb5d76979242e8",
  "667d84dcf2eb5d76978c4b4d",
  "667d84ddf2eb5d76978c4bff",
  "667d872bf2eb5d76979246bb",
  "667d872bf2eb5d76979248b3",
  "667d8f34f2eb5d7697a331ec",
  "64197439c71cfd369a477759",
  "63b88c7d9c8dbc3dda2e8fed",
  "63b88c7d9c8dbc3dda2e8fed",
  "63b88c7d9c8dbc3dda2e8fed",
  "63b88c7d9c8dbc3dda2e8fed",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "667d7a4ef2eb5d769770e6d9",
  "667d84dbf2eb5d76978c492f",
  "667d84dcf2eb5d76978c4a3f",
  "667d872af2eb5d76979242e8",
  "667d84dcf2eb5d76978c4b4d",
  "667d84ddf2eb5d76978c4bff",
  "667d872bf2eb5d76979246bb",
  "667d872bf2eb5d76979248b3",
  "667d8f34f2eb5d7697a331ec",
  "64197439c71cfd369a477759",
  "63b88c7d9c8dbc3dda2e8fed",
  "63b88c7d9c8dbc3dda2e8fed",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "63b88c7d9c8dbc3dda2e8fed",
  "63b88c7d9c8dbc3dda2e8fed",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "667d84dbf2eb5d76978c492f",
  "667d84dcf2eb5d76978c4a3f",
  "667d872af2eb5d76979242e8",
  "667d84dcf2eb5d76978c4b4d",
  "667d84ddf2eb5d76978c4bff",
  "667d872bf2eb5d76979246bb",
  "667d872bf2eb5d76979248b3",
  "667d8f34f2eb5d7697a331ec",
  "64197439c71cfd369a477759",
  "66e14a4965d92e77164991ac",
  "66e14d4ebd9c09c827ae669f",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "66548bf50e0e6ca66a2bd7e1",
  "63b88c7d9c8dbc3dda2e8fed",
  "667d7a4ef2eb5d769770e6d9",
  "667d84dbf2eb5d76978c492f",
  "667d84dcf2eb5d76978c4a3f",
  "667d872af2eb5d76979242e8",
  "667d84dcf2eb5d76978c4b4d",
  "667d84ddf2eb5d76978c4bff",
  "667d872bf2eb5d76979246bb",
  "667d872bf2eb5d76979248b3",
  "667d8f34f2eb5d7697a331ec",
  "64197439c71cfd369a477759",
  "66548bf50e0e6ca66a2bd7e1",
  "63b88c7d9c8dbc3dda2e8fed",
  "66548bf50e0e6ca66a2bd7e1",
  "667d84dbf2eb5d76978c492f",
  "667d84dcf2eb5d76978c4a3f",
  "667d872af2eb5d76979242e8",
  "667d84dcf2eb5d76978c4b4d",
  "667d84ddf2eb5d76978c4bff",
  "667d872bf2eb5d76979246bb",
  "667d872bf2eb5d76979248b3",
  "667d8f34f2eb5d7697a331ec",
  "64197439c71cfd369a477759",
  "6761baf20ec266217ab13355",
  "6761baf20ec266217ab13355",
  "6761b4120ec266217ab127c6",
  "60f13f0f2bc170004880d977",
  "615e078d3ec4a30048ca9d45",
  "617bf5a04af4030049437c34",
  "617bdde100e50400481e92ab",
  "6147c5976bd9b80047d37ae6",
  "61941e24e393d900487bb1b4",
  "619724e0c9fbcd004931f1da",
  "619d81689d2004004878902d",
  "6196fbf1c9fbcd004931f09a",
  "61a8a8cf9651490049c5ed1a",
  "61a95f61ff4b130049b2a7de",
  "61b33a423eb64d0048bac594",
  "61b61bc83eb64d0048baef06",
  "61bd36de406cf0004a4efe63",
  "61c768d3d974de004b1553b8",
  "61d1dd11aff234004a726a67",
  "61d5fc6e9b0b3a004ada635e",
  "61d8878b69a759004a8089f7",
  "61f2fae69f0758004aba3dd5",
  "61f890419f0758004abaf7d5",
  "62000867377bca004b2cce1b",
  "6200115f377bca004b2cd2f7",
  "6200365c377bca004b2cdb6d",
  "61ae4a53e16e0d0048c30c16",
  "614d663b9e990f00475b2ebc",
  "6210c9600cc574004b434661",
  "61e8182b78d330004aea8647",
  "621aae4e3d0d42004adb2e4b",
  "621e02823d0d42004adbb05b",
  "61fff888377bca004b2cc8b7",
  "622f2b02c8cac4004b13ab7d",
  "62333961986acc004a7a71b3",
  "6236ec714396a2004afa40b8",
  "625814a19d684b004a9814a1",
  "626683fc4f727a004ac53b87",
  "6242f5ae3f1132004a3bd9f9",
  "6282db0875fcf8004a95ccfd",
  "62836fa4a4b713004a498c1c",
  "6283ba3408f5fd004a06b218",
  "62961463c77e290049a5ce4c",
  "629a858abf3cc9004ad2e52a",
  "629a094f58593a0049cd1fca",
  "629fd387db130c0049538de2",
  "629fde99db130c0049553473",
  "62ae688415f91100497f8daa",
  "62aefe5915f91100498330f6",
  "62b9a2bcfc434b004982d394",
  "62ac5f5815f91100497410b1",
  "62c2cd09196b570049cad51d",
  "621e02823d0d42004adbb05b",
  "62d0194b0a08740049cb8c07",
  "62d4b99028b9fc0049d86d20",
  "62c3127cef6dfa0049fa6132",
  "62deda03616ad8004c82c486",
  "62eaa82f5fd326004b1da941",
  "62eaddcfa5ca4d004b741e2f",
  "62ea85f15fd326004b1c42fe",
  "63075d8fd32e39004bf9690e",
  "62921a06f5b05c00495b1d2e",
  "63120bc56e7c9b004bed9fa1",
  "63146e256091e2004b2672c4",
  "6329d46309e01d004b60034d",
  "632fe778e20362004bff6199",
  "633207a326bc05004b09c9fb",
  "63371e8c86498e004bca2713",
  "634da14465c1bb004bc32914",
  "63244c3399a775004b2213ea",
  "637e1e8cbe0a7b004bbe011d",
  "639f3287a00ffe004baf4be0",
  "63b297033bde17003a2ed71e",
  "63b297033bde17003a2ed71e",
  "63b297033bde17003a2ed71e",
  "63d04ff699a11787ae31bd7d",
  "63cfaa70fcf175272570169d",
  "63dcc4f848654847b9378a89",
  "6391fbcbd8efc1004b7c8065",
  "63f0c5d3e9c63adf771652ee",
  "63f10453e9c63adf771667ec",
  "63f11029e9c63adf77166bf5",
  "63f49af089def3d91210b28e",
  "63fac8479c5cc055079e57c7",
  "63fecbfd2b5ffb01b43e37d5",
  "63ff5fdee65f80346ce7d123",
  "63ff5fdee65f80346ce7d123",
  "63ff5fdee65f80346ce7d123",
  "63c6bb23d80cfe92bc2d52c9",
  "63c6bb23d80cfe92bc2d52c9",
  "63d8de66e0ed3b1a07e90d4e",
  "640663d9910f3b483c078877",
  "6400c18484a527d37b86b5db",
  "6413b0022c68e51c6566bd4f",
  "6415e4533050698c99a1af39",
  "641c30f26533768afa497562",
  "641f2b948b0db5a27610d45c",
  "642048295a418a529f007259",
  "63f365a8e9c63adf77173ad9",
  "642700535314539563f20913",
  "642795527178d7b65607cf28",
  "6429db1b7178d7b6560881c8",
  "642b863db03ff7d2c0f71a78",
  "642a73e85314539563f36c7c",
  "6432cde2f4b4f3068513d4a0",
  "6432cde2f4b4f3068513d4a0",
  "642d31e6eb7c4dfbb46ad1d6",
  "64457ef936889f01cc08eefe",
  "644e409cdc098e8b83ad1cb6",
  "644e409cdc098e8b83ad1cb6",
  "644e409cdc098e8b83ad1cb6",
  "6451a7bfe16f2b4e4e82e368",
  "64528558e16f2b4e4e836ec2",
  "645532bc3094dbc36c9f8f64",
  "6456bfcd3094dbc36ca03386",
  "645959b3920c70f93e7d2eb0",
  "6460b7295ab0dcee3aa8b729",
  "6465dfabda4203423777f2ad",
  "6466170944045ae375b8e7ec",
  "646a1518c0a92ee066d679a9",
  "6474a2f076d19600d8567092",
  "6470e5385e167f0d1afaaab2",
  "6477f48e6712588cfb4b50aa",
  "6478c80728fcb61b8802096c",
  "6478e67428fcb61b88022533",
  "6478e67428fcb61b88022533",
  "647a0d1b6fdcd5c5bbecc4e4",
  "647f9ca78ecae893390b8b49",
  "64836def5e7ab29d9dba036b",
  "648f1e03bd43f6e215516c8d",
  "64915890d2b624da3ae5025c",
  "6491d150c0b36cab673e4226",
  "648a0eb3b98618230c87ef87",
  "648e10a8bd43f6e21551308d",
  "649d41f6efc77a497398de51",
  "6468ad2cb07555c2a609b4d9",
  "6468ad2cb07555c2a609b4d9",
  "6468ad2cb07555c2a609b4d9",
  "64b06221a5320b1d48ba2e4b",
  "64b563f3515b6150efa88d12",
  "64ba25fcbc573a5cdb8328fa",
  "64ab03d912c994853349bd7f",
  "64ca31424ca0f1cd019d4665",
  "64e9bc78e9598cda09f8f317",
  "64fb1acb72201639e7f46eb5",
  "652135580995a4d93f2089b3",
  "65098ac06433ac237ff65639",
  "652c920dac7e34b83c3554ca",
  "65318815313b9d53b037cbe0",
  "6532b795c921b8610630e715",
  "6536ea81efb814a53afd9444",
  "65283cf6539bc552a3b1f6d5",
  "653caae4ea336ab50bc405c2",
  "653cfa9aea336ab50bc42961",
  "653cfa9aea336ab50bc42961",
  "653ed3737ce2a2508f9d6ff3",
  "654813027527e212eac4ccbb",
  "654684ab05f22e391b58fe73",
  "65500f189a01f6fe976ce6ad",
  "655184ea9a01f6fe976d56e2",
  "654eb9c01fb118011b6df693",
  "654eb9c01fb118011b6df693",
  "65592f7de2abf1a48e9ed44c",
  "651910af0082d22b619db9a8",
  "6557f499d2df5bc63c835c37",
  "6584c95eb564b4a354e74e44",
  "658d5d77b76f3cbfb0ee6e1f",
  "65945101448bc4215dc3aa0c",
  "65945101448bc4215dc3aa0c",
  "659154399387623a98ba8895",
  "653c34a0ea336ab50bc3fa69",
  "6598416c8a82ed0de4e0be43",
  "65a0276d9292c61cf652f9d0",
  "65a029fd9292c61cf65304db",
  "65a0ed2b099cfb23c2438725",
  "642795527178d7b65607cf28",
  "6359531d9c3da5004b2ab972",
  "65a9496589742120c3d460bf",
  "65ad43edc5118ba49e1a6881",
  "65b6e97eda9f8052f2859489",
  "6605a254824e17627385f391",
  "6605c636380f6738da364214",
  "6606f8cc1677e395ab81ad20",
  "6606f8cc1677e395ab81ad20",
  "660a6aa460a26b0960db58cb",
  "6605c636380f6738da364214",
  "660bacaf1cfc8af967ec3dab",
  "660bacaf1cfc8af967ec3dab",
  "6638612f7bb8e48007cae333",
  "6638612f7bb8e48007cae333",
  "66670d77963d9d0ef2f2f2ba",
  "66641e626e1889c03e1389c2",
  "668df3eea5350b4dbb64121c",
  "6568f47d659f98cea02e4ca6",
  "62ebb0e7a5ca4d004b7ba8c1",
  "62ebb0e7a5ca4d004b7ba8c1",
  "67389c3eebf6b0cd0e3c78c1",
  "6762f0ef1c00f2e5d6dbccfc",
  "6762f0ef1c00f2e5d6dbccfc",
  "676d5c7d92ed9045ad9eaf32",
  "677098f7c3c593eb318a0c86",
  "677098f7c3c593eb318a0c86",
  "66548bf50e0e6ca66a2bd7e1",
  "677a7e12efc243c9a652f5d9",
  "679fa34c1d932b2b0647e9d2"
];
const NON_CONVERTED_USERS_WITH_SAVINGS = [
  "66548bf50e0e6ca66a2bd7e1",
  "677098f7c3c593eb318a0c86",
  "63b88c7d9c8dbc3dda2e8fed",
  "6638612f7bb8e48007cae333"
];

class BackfillSummariesRunner extends ScriptRunner {
  scriptName = "backfill-summaries";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Backfilling summaries...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };
    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will appy!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    // We retrieve several things that are going to be re-used throughout the script:
    // 1. Investment products
    // 2. FX rates (current)
    // 3. Historical prices for all assets
    // 4. Sentiment scores for all assets
    const [investmentProductsDict, fxRates, lastYearHistoricalPrices, lastYearStockSentiments] = await Promise.all(
      [
        InvestmentProductService.getInvestmentProductsDict("commonId", false, {
          listedOnly: true
        }),
        CacheUtil.getCachedDataWithFallback<ExchangeRates>(
          "fxRates",
          async (): Promise<ExchangeRates> => eodService.getLatestFXRates()
        ),
        BackfillSummariesRunner._getLastYearHistoricalPrices(),
        BackfillSummariesRunner._getAllStockSentiments()
      ]
    );

    const users = await User.find({
      id: { $nin: USERS_ALREADY_BACKFILLED },
      $or: [
        { portfolioConversionStatus: "completed" },
        { id: { $in: NON_CONVERTED_USERS_WITH_SAVINGS } },
        { id: { $in: NON_CONVERTED_USERS_WITH_DEPOSITS } }
      ]
    }).populate("portfolios");

    for (let i = 0; i < users.length; i += DB_SUMMARY_SNAPSHOT_BATCH_SIZE) {
      const usersToBackfill = users.slice(i, i + DB_SUMMARY_SNAPSHOT_BATCH_SIZE);

      await Promise.all(
        usersToBackfill.map((user) =>
          BackfillSummariesRunner._backfillUserDailyCashSummaries(
            user,
            investmentProductsDict,
            lastYearHistoricalPrices,
            fxRates,
            lastYearStockSentiments,
            this.options.dryRun
          )
        )
      );
    }
  }

  private static async _backfillUserDailyCashSummaries(
    user: UserDocument,
    investmentProductsDict: InvestmentProductsDictType,
    allHistoricalPrices: Record<investmentUniverseConfig.AssetType, { date: string; close: number }[]>,
    fxRates: ExchangeRates,
    sentiments: Record<investmentUniverseConfig.AssetType, AssetSentimentsType>,
    dryRun: boolean
  ): Promise<void> {
    try {
      if (USERS_ALREADY_BACKFILLED.includes(user.id) || user.isDeleted) {
        return;
      }

      const portfolio = user.portfolios[0] as PortfolioDocument;
      const transactions = await Transaction.find({
        owner: user.id,
        $or: [
          { status: "Settled" },
          { "providers.wealthkernel.status": "Settled" },
          { "deposit.providers.wealthkernel.status": "Settled" }
        ],
        category: {
          $in: [
            "DepositCashTransaction",
            "WithdrawalCashTransaction",
            "DividendTransaction",
            "WealthyhoodDividendTransaction",
            "CashbackTransaction",
            "AssetTransaction"
          ]
        }
      }).sort("settledAt");

      if (transactions.length === 0 && !NON_CONVERTED_USERS_WITH_SAVINGS.includes(user.id)) {
        return;
      }

      const transactionsByDate = this._groupTransactionsByDate(transactions);

      const transactionDates = Object.keys(transactionsByDate).sort(
        (a, b) => new Date(a).getTime() - new Date(b).getTime()
      );
      if (transactionDates.length === 0) return;

      const startDate = new Date(transactionDates[0]);
      if (DateUtil.isToday(startDate)) {
        return;
      }

      const endDate = new Date();

      const [cashValueByDate, valuationsByDate] = await Promise.all([
        BackfillSummariesRunner._getCashValueByDate(
          transactionsByDate,
          DateUtil.getAllDatesBetweenTwoDates(startDate, endDate)
        ),
        BackfillSummariesRunner._getValuationsByDate(portfolio.providers.wealthkernel.id, startDate, endDate)
      ]);

      const datesToCreateSummariesFor = DateUtil.getWeekDaysBetween(
        DateUtil.getDateOfYearsAgo(new Date(), 1),
        new Date(),
        {
          includingStart: true,
          includingEnd: false
        }
      );

      const BATCH_SIZE = 20;

      for (let i = 0; i < datesToCreateSummariesFor.length; i += BATCH_SIZE) {
        const batchDates = datesToCreateSummariesFor.slice(i, i + BATCH_SIZE);

        const dbOperations = await Promise.all(
          batchDates.map(async (date) => {
            const dateString = DateUtil.getYearAndMonthAndDay(date);
            try {
              return await BackfillSummariesRunner._createDailySummary(
                user,
                date,
                cashValueByDate[dateString],
                valuationsByDate[dateString],
                investmentProductsDict,
                allHistoricalPrices,
                fxRates,
                sentiments,
                dryRun
              );
            } catch (err) {
              captureException(err);
              logger.error(`Could not create summary for user ${user.id}, date ${dateString}`, {
                module: "BackfillSummariesRunner",
                data: { err }
              });
            }
          })
        );

        const bulkOps = dbOperations.filter((operation) => !!operation);

        // Execute bulk operation for current batch if there are operations
        if (!dryRun && bulkOps.length > 0) {
          await DailySummarySnapshot.bulkWrite(bulkOps);
        }
      }
    } catch (err) {
      captureException(err);
      logger.error(`Could not create summaries for user ${user.id}`, {
        module: "BackfillSummariesRunner"
      });
    }
  }

  private static async _getCashValueByDate(
    transactionsByDate: Record<string, TransactionDocument[]>,
    allDates: Date[]
  ): Promise<Record<string, number>> {
    let currentCash = 0;
    const cashByDate: Record<string, number> = {};

    for (const date of allDates) {
      const dateKey = DateUtil.getYearAndMonthAndDay(date);
      const dayTransactions = transactionsByDate[dateKey] || [];

      if (dayTransactions.length > 0) {
        currentCash = await BackfillSummariesRunner._updateCashBasedOnDaysTransactions(
          currentCash,
          dayTransactions
        );
      }

      cashByDate[dateKey] = Decimal.div(currentCash, 100).toNumber();
    }

    return cashByDate;
  }

  private static _groupTransactionsByDate(
    transactions: TransactionDocument[]
  ): Record<string, TransactionDocument[]> {
    const transactionsByDate: Record<string, TransactionDocument[]> = {};

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];
      if (!transaction.settledAt) continue;

      const dateKey = DateUtil.getYearAndMonthAndDay(transaction.settledAt);
      if (!transactionsByDate[dateKey]) {
        transactionsByDate[dateKey] = [];
      }
      transactionsByDate[dateKey].push(transaction);
    }

    return transactionsByDate;
  }

  /**
   * @param startingCash
   * @param transactions
   * @private
   */
  private static async _updateCashBasedOnDaysTransactions(
    startingCash: number,
    transactions: TransactionDocument[]
  ): Promise<number> {
    let currentCash = startingCash;

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];

      if (transaction.category === "AssetTransaction") {
        const assetTransaction = transaction as AssetTransactionDocument;

        // If the asset transaction is a buy pending gift, it does not affect cash.
        if (assetTransaction.pendingGift) {
          continue;
        }

        await DbUtil.populateIfNotAlreadyPopulated(assetTransaction, TransactionPopulationFieldsEnum.ORDERS);
        const orders = assetTransaction.orders as OrderDocument[];

        // We separate asset transactions based on their extended transaction category (portfolio buy/sell,
        // asset buy/sell) - if the transaction is an old portfolio update transaction we ignore it for simplicity.
        const transactionType = getExtendedPortfolioTransactionCategory(assetTransaction);

        if (transactionType === ExtendedAssetTransactionCategoryEnum.PORTFOLIO_BUY) {
          const amount = new Decimal(
            assetTransaction.originalInvestmentAmount ?? assetTransaction.consideration.amount
          );

          currentCash = new Decimal(currentCash).minus(amount).toNumber();
        } else if (transactionType === ExtendedAssetTransactionCategoryEnum.ASSET_BUY) {
          const amount = new Decimal(orders?.[0].consideration.originalAmount ?? orders?.[0].consideration.amount);

          currentCash = new Decimal(currentCash).minus(amount).toNumber();
        } else if (transactionType === ExtendedAssetTransactionCategoryEnum.PORTFOLIO_SELL) {
          const amount = new Decimal(assetTransaction.consideration.amount);

          currentCash = new Decimal(currentCash).plus(amount).toNumber();
        } else if (transactionType === ExtendedAssetTransactionCategoryEnum.ASSET_SELL) {
          const amount = new Decimal(orders?.[0].consideration.amount);

          currentCash = new Decimal(currentCash).plus(amount).toNumber();
        }
      } else if (
        [
          "DepositCashTransaction",
          "DividendTransaction",
          "WealthyhoodDividendTransaction",
          "CashbackTransaction"
        ].includes(transaction.category)
      ) {
        const amount = new Decimal(transaction.consideration.amount);

        currentCash = new Decimal(currentCash).plus(amount).toNumber();
      } else if (transaction.category === "WithdrawalCashTransaction") {
        const amount = new Decimal(transaction.consideration.amount);

        currentCash = new Decimal(currentCash).minus(amount).toNumber();
      }
    }

    return currentCash;
  }

  private static async _createDailySummary(
    user: UserDocument,
    date: Date,
    cashValue: number,
    valuation: ValuationType,
    investmentProductsDict: InvestmentProductsDictType,
    allHistoricalPrices: Record<investmentUniverseConfig.AssetType, { date: string; close: number }[]>,
    fxRates: ExchangeRates,
    sentiments: Record<investmentUniverseConfig.AssetType, AssetSentimentsType>,
    dryRun: boolean
  ): Promise<{ insertOne: { document: any } } | null> {
    const { start, end } = DateUtil.getStartAndEndOfDay(date);
    const portfolio = user.portfolios[0] as PortfolioDocument;

    const existingEntry = await DailySummarySnapshot.findOne({
      "metadata.owner": user.id,
      date: {
        $gte: start,
        $lt: end
      }
    });

    if (existingEntry) {
      return null;
    }

    // Get the portfolio savings & holdings value for this date
    const [savingsTicker, portfolioTicker] = await Promise.all([
      DailyPortfolioSavingsTicker.findOne({
        portfolio: portfolio.id,
        date: {
          $gte: start,
          $lt: end
        }
      }),
      DailyPortfolioTicker.findOne({
        portfolio: portfolio.id,
        date: {
          $gte: start,
          $lt: end
        }
      })
    ]);

    const flooredCashValue = Decimal.max(cashValue ?? 0, 0).toNumber();
    const savingsValue = savingsTicker ? Decimal.div(savingsTicker.holdingAmount, 100).toNumber() : 0;
    const holdingsValue = portfolioTicker ? portfolioTicker.getPrice(user.currency) : 0;
    const totalValue = new Decimal(flooredCashValue || 0)
      .plus(savingsValue || 0)
      .plus(holdingsValue || 0)
      .toNumber();

    const holdings = valuation?.holdings || [];

    portfolio.holdings = holdings
      .filter((holding) => holding.isin !== "IE00B3L10356")
      .map((holding) => {
        const assetCommonId = InvestmentUniverseUtil.getAssetIdFromIsin(holding.isin);
        const investmentProduct = investmentProductsDict[assetCommonId];

        return {
          assetCommonId,
          quantity: holding.quantity,
          asset: {
            ...investmentProduct
          }
        } as HoldingsType;
      });

    const assets = portfolio.holdings.map((holding) => {
      return {
        assetId: holding.assetCommonId,
        quantity: holding.quantity,
        dailyReturnPercentage: BackfillSummariesRunner._calculateDailyReturnPercentage(
          holding.assetCommonId,
          date,
          allHistoricalPrices
        )
      };
    });

    const sentimentScore = await BackfillSummariesRunner._calculatePortfolioSentimentScore(
      portfolio,
      date,
      allHistoricalPrices,
      fxRates,
      sentiments
    );

    if (dryRun) {
      logger.info(`Would have created summary for user ${user.id}, date ${date}...`, {
        module: "BackfillSummariesRunner",
        method: "_createDailySummary",
        data: {
          cash: cashValue || 0,
          holdings: holdingsValue || 0,
          savings: savingsValue || 0,
          total: totalValue || 0,
          sentimentScore: JSON.stringify(sentimentScore),
          assets: JSON.stringify(assets)
        }
      });

      if (cashValue < 0 || holdingsValue < 0 || savingsValue < 0) {
        throw new Error(
          `Would have create summary with negative value, holdings: ${holdingsValue}, savings: ${savingsValue}, cash: ${cashValue}, user ${user.id}, ${date}!`
        );
      }
      return null;
    }

    return {
      insertOne: {
        document: {
          isBackfilled: true,
          metadata: { owner: user.id },
          date,
          ...(sentimentScore && { sentimentScore }), // To avoid sentiment score being persisted when it is null.
          portfolio: {
            cash: {
              value: {
                currency: user.currency,
                amount: flooredCashValue || 0
              }
            },
            savings: {
              value: {
                currency: user.currency,
                amount: savingsValue || 0
              }
            },
            holdings: {
              value: {
                currency: user.currency,
                amount: holdingsValue || 0
              },
              assets
            },
            total: {
              value: {
                currency: user.currency,
                amount: totalValue
              }
            }
          }
        }
      }
    };
  }

  private static async _getValuationsByDate(portfolioId: string, startDate: Date, endDate: Date) {
    const valuations = await WealthkernelService.UKInstance.retrieveAllValuations({
      portfolioId: portfolioId,
      startDate: WealthkernelService.formatDate(startDate),
      endDate: WealthkernelService.formatDate(endDate)
    });

    return valuations.reduce(
      (acc, valuation) => {
        const dateKey = DateUtil.getYearAndMonthAndDay(new Date(valuation.date));
        acc[dateKey] = valuation;
        return acc;
      },
      {} as Record<string, ValuationType>
    );
  }

  private static async _calculateNewsSentimentScore(
    asset: InvestmentProductDocument,
    date: Date,
    allSentiments: AssetSentimentsType
  ): Promise<number> {
    try {
      const sentiments = allSentiments.filter(({ date: eodDate }) => DateUtil.isPastDate(new Date(eodDate), date));

      if (sentiments && sentiments.length > 0) {
        const totalCount = sentiments
          .map(({ count }) => count)
          .reduce((sum, value) => sum.plus(value), new Decimal(0))
          .toNumber();

        // Get the weighted average of the scores by "count" and then normalize it to be between [0, 1] by doing (X+1)/2
        const score = sentiments
          .reduce(
            (sum, { normalized, count }) => Decimal.div(count, totalCount).mul(normalized).add(sum),
            new Decimal(0)
          )
          .add(1)
          .div(2)
          .toNumber();

        return MathUtil.adjustBayesian(totalCount, score, 20);
      }
    } catch (err) {
      captureException(err);
      logger.error(`Failed to calculate news sentiment score for ${asset.commonId}`, {
        module: "BackfillSummariesRunner",
        method: "_calculateNewsSentimentScore"
      });
    }
  }

  private static async _calculatePortfolioSentimentScore(
    portfolio: PortfolioDocument,
    date: Date,
    allHistoricalPrices: Record<investmentUniverseConfig.AssetType, { date: string; close: number }[]>,
    fxRates: ExchangeRates,
    sentiments: Record<investmentUniverseConfig.AssetType, AssetSentimentsType>
  ): Promise<SentimentScoreType> {
    if (portfolio.holdings?.length > 0) {
      await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
      const user = portfolio.owner as UserDocument;

      const assetSentimentScores = await BackfillSummariesRunner._calculatePortfolioAssetSentimentScores(
        portfolio,
        date,
        allHistoricalPrices,
        fxRates,
        sentiments
      );

      if (!assetSentimentScores || assetSentimentScores.length === 0) {
        return null;
      }

      const individualPortfolioSentimentScores: Record<IndividualSentimentScoreComponentEnum, number> = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: null,
        [IndividualSentimentScoreComponentEnum.NEWS]: null,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: null
      };

      // For each individual sentiment score for the portfolio, we calculate it based on the relevant asset sentiment scores and their percentage allocation in the portfolio.
      Object.keys(individualPortfolioSentimentScores).forEach((key) => {
        const relevantScores = assetSentimentScores.filter(
          (assetScore) => !!assetScore?.score?.[key as IndividualSentimentScoreComponentEnum]
        );

        if (relevantScores.length > 0) {
          const totalAllocation = relevantScores.reduce(
            (sum, { allocation }) => sum.add(allocation),
            new Decimal(0)
          );

          individualPortfolioSentimentScores[key as IndividualSentimentScoreComponentEnum] = relevantScores
            .reduce((sum, { allocation, score }) => {
              // Normalize the allocation relative to total allocation of assets with scores
              const normalizedAllocation = Decimal.div(allocation, totalAllocation);
              return Decimal.mul(normalizedAllocation, score[key as IndividualSentimentScoreComponentEnum]).add(
                sum
              );
            }, new Decimal(0))
            .toNumber();
        }
      });

      const totalScore = BackfillSummariesRunner._calculatePortfolioTotalSentimentScore(
        individualPortfolioSentimentScores,
        user.isPriceMomentumSentimentEnabled
      );

      return Object.fromEntries(
        Object.entries({
          [TotalSentimentScoreComponentEnum.TOTAL]: totalScore,
          [IndividualSentimentScoreComponentEnum.ANALYST]:
            individualPortfolioSentimentScores[IndividualSentimentScoreComponentEnum.ANALYST],
          [IndividualSentimentScoreComponentEnum.NEWS]:
            individualPortfolioSentimentScores[IndividualSentimentScoreComponentEnum.NEWS],
          [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: user.isPriceMomentumSentimentEnabled
            ? individualPortfolioSentimentScores[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
            : null
        }).filter(([, value]) => value !== null)
      ) as SentimentScoreType;
    }
  }

  /**
   * Calculates, for each asset in the portfolio, its sentiment score and the percentage allocation of it in the portfolio.
   * @param portfolio
   * @param date
   * @param allHistoricalPrices
   * @param fxRates
   * @param sentiments
   * @private
   */
  private static async _calculatePortfolioAssetSentimentScores(
    portfolio: PortfolioDocument,
    date: Date,
    allHistoricalPrices: Record<investmentUniverseConfig.AssetType, { date: string; close: number }[]>,
    fxRates: ExchangeRates,
    sentiments: Record<investmentUniverseConfig.AssetType, AssetSentimentsType>
  ): Promise<
    {
      score: PartialRecord<IndividualSentimentScoreComponentEnum, number> &
        Record<TotalSentimentScoreComponentEnum, number>;
      allocation: number;
    }[]
  > {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);

    const assetAllocation = PortfolioUtil.mapHoldingsToAllocationFormat(
      portfolio.currency,
      portfolio.holdings.map((holding) => {
        return {
          assetCommonId: holding.assetCommonId,
          quantity: holding.quantity,
          asset: {
            ...holding.asset,
            currentTicker: {
              ...holding.asset.currentTicker,
              getPrice: (currency: currenciesConfig.MainCurrencyType) => {
                const historicalPrices = allHistoricalPrices[holding.assetCommonId];
                const dateStr = DateUtil.getYearAndMonthAndDay(date);

                const currentElement =
                  historicalPrices.find(({ date: eodDate }) => eodDate === dateStr) ??
                  historicalPrices
                    .filter(({ date: eodDate }) => DateUtil.isPastDate(new Date(eodDate), date))
                    .at(-1);

                if (!currentElement) {
                  return 0;
                }

                return Decimal.mul(fxRates[holding.asset.tradedCurrency][currency], currentElement.close)
                  .toDecimalPlaces(2)
                  .toNumber();
              }
            }
          }
        } as HoldingsType;
      })
    ).assets;

    return (
      await Promise.all(
        portfolio.holdings.map(async (holding) => {
          let assetSentimentScore: SentimentScoreType;

          if (holding.asset.isStock) {
            assetSentimentScore = BackfillSummariesRunner._calculateAssetSentimentScore(holding.asset, {
              [IndividualSentimentScoreComponentEnum.NEWS]:
                await BackfillSummariesRunner._calculateNewsSentimentScore(
                  holding.asset,
                  date,
                  sentiments[holding.assetCommonId]
                )
            });
          }

          return {
            allocation: Decimal.div(assetAllocation[holding.assetCommonId], 100).toNumber(),
            score: assetSentimentScore
          };
        })
      )
    ).filter(({ score }) => !!score);
  }

  private static _calculateAssetSentimentScore(
    asset: InvestmentProductDocument,
    sentimentScores: AssetSentimentScoresType
  ): SentimentScoreType {
    if (asset.isETF || Object.values(sentimentScores)?.filter((value) => !!value).length === 0) {
      return null;
    }

    const scoresForCalculation = Object.fromEntries(
      Object.entries(sentimentScores).filter(
        ([key]) => key !== IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM
      )
    ) as AssetSentimentScoresType;

    const totalScore = BackfillSummariesRunner._getWeightedAverageTotalSentimentScore(scoresForCalculation);

    return {
      [TotalSentimentScoreComponentEnum.TOTAL]: totalScore,
      ...scoresForCalculation
    };
  }

  private static _getWeightedAverageTotalSentimentScore(
    inputs: PartialRecord<IndividualSentimentScoreComponentEnum, number>
  ): number {
    const availableInputs = Object.entries(inputs).filter(([, value]) => !!value) as [
      keyof IndividualSentimentScoreComponentEnum,
      number
    ][];

    if (availableInputs.length === 0) {
      throw new Error("Cannot calculate sentiment score when no individual sentiment scores are calculated.");
    }

    const totalWeight = availableInputs.reduce(
      (sum, [key]) =>
        Decimal.add(sum, BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS[key as IndividualSentimentScoreComponentEnum]),
      new Decimal(0)
    );

    return availableInputs
      .reduce((sum, [key, value]) => {
        const adjustedWeight = Decimal.div(
          BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS[key as IndividualSentimentScoreComponentEnum],
          totalWeight
        );
        return Decimal.mul(value, adjustedWeight).add(sum);
      }, new Decimal(0))
      .toNumber();
  }

  private static _calculatePortfolioTotalSentimentScore(
    individualPortfolioSentimentScores: Record<IndividualSentimentScoreComponentEnum, number>,
    isPriceMomentumSentimentEnabled: boolean
  ) {
    const totalWeight = Object.entries(BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS)
      .filter(
        ([key]) =>
          !!individualPortfolioSentimentScores[key as IndividualSentimentScoreComponentEnum] &&
          (key !== IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM || isPriceMomentumSentimentEnabled)
      )
      .reduce((sum, [, weight]) => Decimal.add(sum, weight), new Decimal(0));

    return Object.entries(BASE_TOTAL_SENTIMENT_SCORE_WEIGHTS)
      .filter(
        ([key]) =>
          !!individualPortfolioSentimentScores[key as IndividualSentimentScoreComponentEnum] &&
          (key !== IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM || isPriceMomentumSentimentEnabled)
      )
      .reduce((sum, [key, weight]) => {
        const adjustedWeight = Decimal.div(weight, totalWeight);
        return Decimal.mul(
          individualPortfolioSentimentScores[key as IndividualSentimentScoreComponentEnum],
          adjustedWeight
        ).add(sum);
      }, new Decimal(0))
      .toNumber();
  }

  private static async _getLastYearHistoricalPrices(): Promise<
    Record<investmentUniverseConfig.AssetType, { date: string; close: number }[]>
  > {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const lastYearHistoricalPrices: Record<investmentUniverseConfig.AssetType, { date: string; close: number }[]> =
      {};
    for (const asset of AssetArrayConst) {
      lastYearHistoricalPrices[asset] = (
        await CacheUtil.getCachedDataWithFallback<{ date: string; close: number }[]>(
          `eod:historical:${asset}`,
          async () => {
            const today = new Date(Date.now());
            const historicalPriceAll = await eodService.getHistoricalPrices(asset, {
              from: DateUtil.getYearAndMonthAndDay(DateUtil.getDateOfYearsAgo(today, 10)),
              period: "d"
            });
            return historicalPriceAll;
          }
        )
      ).filter(({ date }) =>
        DateUtil.isSameOrFutureDate(new Date(date), DateUtil.getDateOfDaysAgo(new Date(Date.now()), 400))
      );
    }

    return lastYearHistoricalPrices;
  }

  private static async _getAllStockSentiments(): Promise<
    Record<investmentUniverseConfig.AssetType, AssetSentimentsType>
  > {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const lastYearSentiments: Record<investmentUniverseConfig.AssetType, AssetSentimentsType> = {};
    for (const asset of AssetArrayConst) {
      lastYearSentiments[asset] = await eodService.getAssetSentiments(asset, new Date("2024-01-01"), new Date());
    }

    return lastYearSentiments;
  }

  private static _calculateDailyReturnPercentage(
    asset: investmentUniverseConfig.AssetType,
    date: Date,
    allHistoricalPrices: Record<investmentUniverseConfig.AssetType, { date: string; close: number }[]>
  ): number {
    const historicalPrices = allHistoricalPrices[asset];
    const dateStr = DateUtil.getYearAndMonthAndDay(date);

    // Get current day's price
    const currentElement =
      historicalPrices.find(({ date: eodDate }) => eodDate === dateStr) ??
      historicalPrices.filter(({ date: eodDate }) => DateUtil.isPastDate(new Date(eodDate), date)).at(-1);
    const currentPrice = currentElement?.close;

    if (!currentElement) {
      return null;
    }

    // Get previous day's price
    const previousElement =
      historicalPrices[historicalPrices.findIndex(({ date: eodDate }) => currentElement.date === eodDate) - 1];
    const previousPrice = previousElement?.close;

    if (!previousElement) {
      return null;
    }

    return PortfolioUtil.getReturns({
      startValue: new Decimal(previousPrice),
      endValue: new Decimal(currentPrice)
    }).toNumber();
  }
}

new BackfillSummariesRunner().run();
