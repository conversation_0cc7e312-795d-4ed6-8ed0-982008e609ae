import <PERSON><PERSON>tRunner from "../jobs/services/scriptRunner";
import { ContentfulContentTypeEnum } from "../configs/contentfulConfig";
import ContentfulRetrievalService from "../external-services/contentfulRetrievalService";
import ContentEntryService from "../services/contentEntryService";
import logger from "../external-services/loggerService";

class ImportLearningGuidesRunner extends ScriptRunner {
  scriptName = "import-learning-guides";

  async processFn(): Promise<void> {
    logger.info("Starting to import learning guides from Contentful...", {
      module: `script:${this.scriptName}`
    });

    // Get all learning guides from Contentful
    const entries = await ContentfulRetrievalService.LearnHubInstance.getEntries(
      ContentfulContentTypeEnum.LEARNING_GUIDE,
      { limit: 20 }
    );

    logger.info(`Found ${entries.items.length} entries in Contentful`, {
      module: `script:${this.scriptName}`
    });

    for (const entry of entries.items) {
      if (entry.sys.contentType.sys.id !== "learningGuide") {
        continue;
      }

      const fields = entry.fields as any;
      try {
        await ContentEntryService.createLearningGuideContentEntry({
          title: fields.title["en-US"] as string,
          contentfulConfig: {
            id: entry.sys.id,
            spaceId: entry.sys.space.sys.id,
            environmentId: entry.sys.environment.sys.id
          }
        });

        logger.info(`Successfully imported guide: ${fields.title["en-US"]}`, {
          module: `script:${this.scriptName}`,
          data: { entryId: entry.sys.id }
        });
      } catch (err) {
        logger.error(`Failed to import guide: ${fields.title["en-US"]}`, {
          module: `script:${this.scriptName}`,
          data: {
            entryId: entry.sys.id,
            errorMessage: err instanceof Error ? err.message : String(err)
          }
        });
      }
    }

    logger.info("✅ Finished importing learning guides!", {
      module: `script:${this.scriptName}`
    });
  }
}

new ImportLearningGuidesRunner().run();
