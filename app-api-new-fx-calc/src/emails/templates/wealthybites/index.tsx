import * as React from "react";
import { Html, Head, Body, Container, Section, Preview } from "@react-email/components";
import type { WealthybitesEmailBody } from "types/wealthybites";

// Import Components
import { Header } from "./_components/Header";
import { InvestmentSnapshot } from "./_components/InvestmentSnapshot";
import { PortfolioMovers } from "./_components/PortfolioMovers";
import { MarketRoundup } from "./_components/MarketRoundup";
import { TopNews } from "./_components/TopNews";
import { LearnInsights } from "./_components/LearnInsights";
import { ReferralSection } from "./_components/ReferralSection";
import { Footer } from "./_components/Footer";
import { LearningGuide } from "./_components/LearningGuide";

export const WealthybitesEmail: React.FC<WealthybitesEmailBody> = ({
  firstName,
  userLocale,
  userCurrency,
  userEmail,
  investmentSnapshot,
  topPortfolioMovers,
  marketRoundup,
  topMarketIndexes,
  topNews,
  analystInsights,
  learningGuide
}) => {
  return (
    <Html lang="en">
      <Head>
        <title>Wealthybites - Your Daily Financial Update</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <style type="text/css">{`
          @font-face {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 300;
            src: url('https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hjp-Ek-_RwuA.woff2') format('woff2');
          }
          @font-face {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 400;
            src: url('https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuOKfAZ9hjp-Ek-_RwuA.woff2') format('woff2');
          }
          @font-face {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 500;
            src: url('https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fAZ9hjp-Ek-_RwuA.woff2') format('woff2');
          }
          @font-face {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 600;
            src: url('https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hjp-Ek-_RwuA.woff2') format('woff2');
          }
          @font-face {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 700;
            src: url('https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYAZ9hjp-Ek-_RwuA.woff2') format('woff2');
          }
          @media screen and (max-width: 480px) {
            .market-column {
              display: block !important;
              width: 100% !important;
              padding-left: 0px !important;
              padding-right: 0px !important;
            }
          }
        `}</style>
      </Head>
      <Preview>
        Wealthybites - Your financial summary for{" "}
        {new Date().toLocaleDateString("en-US", {
          month: "long",
          day: "numeric"
        })}
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          {/* Header Card */}
          <Section style={styles.cardSection}>
            <Container
              style={{
                ...styles.card,
                borderRadius: "8px 8px 8px 8px",
                borderBottomLeftRadius: 8,
                borderBottomRightRadius: 8
              }}
            >
              <Header firstName={firstName} />
            </Container>
          </Section>
          {/* Blue background spacer */}

          {/* Investment Snapshot + Portfolio Movers Card */}
          {investmentSnapshot && (
            <Section style={styles.cardSection}>
              <Container style={styles.card}>
                <InvestmentSnapshot
                  investmentsValue={investmentSnapshot.investmentsValue}
                  weeklyReturns={investmentSnapshot.weeklyReturns}
                  upByValue={investmentSnapshot.upByValue}
                  cashValue={investmentSnapshot.cashValue}
                  savingsValue={investmentSnapshot.savingsValue}
                  savingsDailyInterest={investmentSnapshot.savingsDailyInterest}
                  totalValue={investmentSnapshot.totalValue}
                  userCurrency={userCurrency}
                  userLocale={userLocale}
                />
                {/* PortfolioMovers component handles undefined winners/losers with default empty arrays internally */}
                <PortfolioMovers
                  winners={topPortfolioMovers?.winners}
                  losers={topPortfolioMovers?.losers}
                  userLocale={userLocale}
                />
              </Container>
            </Section>
          )}

          {/* Market Roundup Card */}
          <Section style={styles.cardSection}>
            <Container style={styles.card}>
              <MarketRoundup
                marketRoundupText={marketRoundup}
                topMarketIndexes={topMarketIndexes}
                userLocale={userLocale}
              />
            </Container>
          </Section>

          {/* Top News Card */}
          <Section style={styles.cardSection}>
            <Container style={styles.card}>
              <TopNews newsItems={topNews} />
            </Container>
          </Section>

          {/* Learn Insights Card */}
          <Section style={styles.cardSection}>
            <Container style={styles.card}>
              <LearnInsights insights={analystInsights.insights} quickTakes={analystInsights.quickTakes} />
            </Container>
          </Section>

          {/* Learning Guide Card */}
          <Section style={styles.cardSection}>
            <Container style={styles.card}>
              <LearningGuide
                title={learningGuide.title}
                url={learningGuide.url}
                chapters={learningGuide.chapters}
              />
            </Container>
          </Section>

          {/* Referral Section Card */}
          <Section style={styles.cardSection}>
            <Container style={styles.card}>
              <ReferralSection firstName={firstName} />
            </Container>
          </Section>

          {/* Footer */}
          <Footer userEmail={userEmail} />
        </Container>
      </Body>
    </Html>
  );
};

export const styles = {
  body: {
    backgroundColor: "#F1F3FD",
    fontFamily: "Inter, Tahoma, sans-serif",
    margin: "0",
    padding: "0",
    fontSize: "14px",
    fontWeight: 400,
    lineHeight: "21px",
    color: "#333333"
  },
  container: {
    width: "100%",
    maxWidth: "600px",
    margin: "0 auto",
    padding: "30px 0 48px",
    backgroundColor: "#F1F3FD",
    borderRadius: "8px"
  },
  cardSection: {
    padding: "0 16px",
    marginBottom: "15px"
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: "8px",
    width: "100%",
    maxWidth: "568px"
  },
  unifiedCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: "8px",
    width: "100%",
    maxWidth: "568px",
    overflow: "hidden",
    padding: 0
  }
};
