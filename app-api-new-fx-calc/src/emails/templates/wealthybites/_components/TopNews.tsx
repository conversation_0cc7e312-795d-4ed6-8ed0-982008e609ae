import * as React from "react";
import { Section, Text, Container, Img, Row, Column } from "@react-email/components";
import SectionHeading from "./SectionHeading";

interface NewsItem {
  imageUrl: string;
  companyName: string;
  title: string;
  content: string;
}

interface TopNewsProps {
  newsItems: NewsItem[];
}

export const TopNews: React.FC<TopNewsProps> = ({ newsItems }) => (
  <Container>
    <SectionHeading label="WHAT YOU NEED TO KNOW" title="Top news of the week" paddingTop="32px" />
    <Section style={styles.newsCardWrapper}>
      {newsItems.map((item, idx) => (
        <React.Fragment key={idx}>
          <Row style={styles.newsRow}>
            <Column style={styles.imageCol}>
              <Img src={item.imageUrl} alt={item.title} style={styles.newsImage} />
            </Column>
            <Column style={styles.contentCol}>
              <Text style={styles.newsCompany}>{item.companyName}</Text>
              <Text style={styles.newsTitle}>{item.title}</Text>
            </Column>
          </Row>
          <Row style={styles.summaryRow}>
            <Column style={styles.summaryCol}>
              <Text style={styles.newsSummary}>{item.content}</Text>
            </Column>
          </Row>
        </React.Fragment>
      ))}
    </Section>
  </Container>
);

const styles = {
  newsCardWrapper: {
    background: "#fff",
    width: "100%",
    margin: "0 auto",
    borderRadius: 8,
    overflow: "hidden" as const,
    boxSizing: "border-box" as const,
    padding: 0,
    paddingBottom: 12
  },
  newsRow: {
    paddingTop: 24,
    paddingLeft: 0,
    paddingRight: 0,
    paddingBottom: 0
  },
  imageCol: {
    width: 56,
    paddingLeft: 16,
    paddingRight: 0
  },
  contentCol: {
    paddingLeft: 7,
    paddingRight: 8
  },
  newsImage: {
    width: 48,
    height: 48,
    borderRadius: 8,
    objectFit: "contain" as const,
    display: "block",
    background: "#f4f4f4",
    border: "none",
    maxWidth: "initial"
  },
  newsCompany: {
    color: "#536ae3",
    fontSize: 12,
    fontWeight: 500,
    textTransform: "uppercase" as const,
    margin: 0,
    padding: 0,
    lineHeight: "14px",
    paddingBottom: "4px"
  },
  newsTitle: {
    fontSize: 14,
    fontWeight: 600,
    margin: 0,
    padding: 0,
    lineHeight: "16px",
    marginTop: 2
  },
  summaryRow: {
    paddingTop: 12,
    paddingBottom: 12
  },
  summaryCol: {
    paddingLeft: 16,
    paddingRight: 16
  },
  newsSummary: {
    fontSize: 14,
    fontWeight: 400,
    color: "#333333",
    lineHeight: "21px",
    margin: 0,
    padding: 0,
    textAlign: "left" as const
  }
};

export default TopNews;
