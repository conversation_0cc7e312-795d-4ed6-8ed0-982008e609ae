import * as React from "react";
import { Section, Text, Container, Link, Row, Column } from "@react-email/components";
import SectionHeading from "./SectionHeading";

interface LearnInsightsItem {
  emoji: string;
  title: string;
  url: string;
}

interface LearnInsightsProps {
  insights: LearnInsightsItem[];
  quickTakes: LearnInsightsItem[];
}

export const LearnInsights: React.FC<LearnInsightsProps> = ({ insights, quickTakes }) => (
  <Container>
    <SectionHeading label="IN CASE YOU MISSED IT" title="Analyst Insights" />

    <Section style={styles.insightsContainer}>
      {insights.map((item, index) => (
        <Row key={index} style={styles.insightsRow}>
          <Column style={styles.emojiCol}>
            <Text style={styles.emoji}>{item.emoji}</Text>
          </Column>
          <Column style={styles.linkCol}>
            <Link href={item.url} style={styles.insightLink}>
              {item.title}
            </Link>
          </Column>
        </Row>
      ))}
    </Section>

    <SectionHeading title="Quick Takes" paddingTop="16px" />
    <Section style={styles.insightsContainer}>
      {quickTakes.map((item, index) => (
        <Row key={`quick-${index}`} style={styles.insightsRow}>
          <Column style={styles.emojiCol}>
            <Text style={styles.emoji}>{item.emoji}</Text>
          </Column>
          <Column style={styles.linkCol}>
            <Link href={item.url} style={styles.insightLink}>
              {item.title}
            </Link>
          </Column>
        </Row>
      ))}
    </Section>
  </Container>
);

const styles = {
  insightsContainer: {
    paddingTop: "12px",
    paddingLeft: "16px",
    paddingRight: "16px",
    paddingBottom: "16px"
  },
  insightsRow: {
    marginBottom: "16px"
  },
  emojiCol: {
    width: "20px",
    paddingRight: "8px"
  },
  linkCol: {
    width: "auto"
  },
  emoji: {
    lineHeight: "20px",
    margin: "0",
    padding: "0"
  },
  insightLink: {
    textAlign: "left" as const,
    lineHeight: "20px",
    textDecorationLine: "underline",
    color: "#536AE3"
  }
};

export default LearnInsights;
