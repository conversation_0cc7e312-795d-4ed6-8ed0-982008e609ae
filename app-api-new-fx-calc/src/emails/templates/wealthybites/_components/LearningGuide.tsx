import * as React from "react";
import { Section, Container, Button, Row, Column } from "@react-email/components";
import SectionHeading from "./SectionHeading";
import type { LearningGuideData } from "types/wealthybites";

export const LearningGuide: React.FC<LearningGuideData> = ({ title, url, chapters }) => (
  <Container>
    <SectionHeading label="Learning guide of the week" title={title} />
    <Section style={styles.contentSection}>
      <Row style={styles.row}>
        <Column style={styles.leftCol}>
          <ol style={styles.topicList}>
            {chapters.map((chapter, idx) => (
              <li key={idx} style={styles.topicItem}>
                {chapter}
              </li>
            ))}
          </ol>
          <div style={styles.ctaWrapper}>
            <Button href={url} style={styles.ctaButton}>
              Start learning
            </Button>
          </div>
        </Column>
      </Row>
    </Section>
  </Container>
);

const styles = {
  contentSection: {
    paddingTop: "0px",
    paddingLeft: "16px",
    paddingRight: "16px",
    paddingBottom: "16px"
  },
  row: {
    paddingTop: 0,
    paddingBottom: 0,
    paddingLeft: 0,
    paddingRight: 0
  },
  leftCol: {
    width: "60%",
    verticalAlign: "top" as const,
    paddingLeft: 0,
    paddingRight: 0
  },
  rightCol: {
    width: "40%",
    verticalAlign: "top" as const,
    paddingLeft: 0,
    paddingRight: 0,
    textAlign: "right" as const
  },
  topicList: {
    margin: 0,
    paddingRight: 0,
    paddingLeft: 18,
    paddingTop: 8,
    paddingBottom: 4,
    marginBottom: "16px"
  },
  topicItem: {
    marginBottom: "6px",
    lineHeight: "20px"
  },
  ctaWrapper: {
    marginTop: "10px",
    paddingBottom: "16px"
  },
  ctaButton: {
    borderRadius: "60px",
    backgroundColor: "#000001",
    color: "#FFFFFF",
    fontWeight: 600,
    width: "138px",
    textAlign: "center" as const,
    boxShadow: "none",
    fontFamily: "inherit",
    whiteSpace: "nowrap",
    paddingTop: "12px",
    paddingBottom: "12px",
    cursor: "pointer",
    letterSpacing: 0
  }
};

export default LearningGuide;
