import * as React from "react";
import { Section, Text, Container, Link, Row, Column, Img } from "@react-email/components";

interface FooterProps {
  userEmail: string;
}

export const Footer: React.FC<FooterProps> = ({ userEmail }) => (
  <Container style={styles.outerContainer}>
    {/* Logo and Social Row */}
    <Section style={styles.logoSocialSection}>
      {/* Logo Row */}
      <Row style={styles.logoRow}>
        <Column style={styles.logoCol}>
          <Link href="https://wealthyhood.com" style={styles.iconLink}>
            <Img
              src="https://email-assets.wealthyhood.cloud/wealthyhood.png"
              alt="Wealthyhood logo"
              width={164}
              style={styles.logo}
            />
          </Link>
        </Column>
      </Row>
      {/* Social Icons Row */}
      <Row style={styles.socialIconsRow}>
        <Column style={styles.socialIconsCol}>
          <Link href="https://www.linkedin.com/company/wealthyhood-invest" style={styles.iconLink}>
            <Img
              src="https://email-assets.wealthyhood.cloud/icon-linkedin.png"
              alt="LinkedIn"
              width={20}
              height={20}
              style={styles.iconImg}
            />
          </Link>
          <Link href="https://www.instagram.com/wealthyhood" style={styles.iconLink}>
            <Img
              src="https://email-assets.wealthyhood.cloud/icon-instagram.png"
              alt="Instagram"
              width={20}
              height={20}
              style={styles.iconImg}
            />
          </Link>
          <Link href="https://www.tiktok.com/@wealthyhood.eu" style={styles.iconLink}>
            <Img
              src="https://email-assets.wealthyhood.cloud/icon-tiktok.png"
              alt="Tiktok"
              width={20}
              height={20}
              style={styles.iconImg}
            />
          </Link>
        </Column>
      </Row>
    </Section>
    {/* Horizontal Line */}
    <Section style={styles.dividerSection}>
      <Row>
        <Column>
          <div style={styles.divider} />
        </Column>
      </Row>
    </Section>
    {/* Disclaimer Block */}
    <Section style={styles.disclaimerSectionOuter}>
      <Row>
        <Column style={styles.disclaimerCol}>
          <Text style={styles.disclaimerText}>
            When you invest, your capital is at risk. Wealthyhood does not render investment, financial, legal, tax
            or accounting advice. Tax treatment depends on individual circumstances and is subject to change. Past
            performance is not a reliable indicator of future performance. You should consider your own personal
            circumstances when making investment decisions and, if necessary, seek qualified advice.
          </Text>
          <Text style={styles.disclaimerText}>
            Wealthyhood Europe AEPEY is authorised and regulated to operate as an investment firm by the Hellenic
            Capital Markets Commission (HCMC) under activity licence number 3/1014.
          </Text>
          <Text style={styles.disclaimerText}>
            Wealthyhood (Wealthyhood Ltd, FCA Register: 933675) is an appointed representative of RiskSave
            Technologies Ltd, which is authorised and regulated by the Financial Conduct Authority (FRN 775330).
          </Text>
        </Column>
      </Row>
    </Section>
    {/* Recipient Info and Links */}
    <Section style={styles.recipientsLinksSection}>
      <Text style={styles.recipientText}>This email was sent to {userEmail}.</Text>
      <Row style={styles.linksRow}>
        <Column style={styles.linkColPad}>
          <Link href="https://wealthyhood.onelink.me/TwZO/notificationsettings" style={styles.link}>
            Update subscription preferences
          </Link>
        </Column>
      </Row>
      <Text style={styles.recipientText}>9 Kingsland Road, London, E2 8DD, United Kingdom</Text>
    </Section>
  </Container>
);

const styles = {
  outerContainer: {
    backgroundColor: "#f1f3fd",
    padding: 0,
    width: "100%",
    fontFamily: "Inter, Tahoma, sans-serif"
  },
  logoSocialSection: {
    paddingTop: 66,
    paddingBottom: 56,
    textAlign: "center" as const
  },
  logoRow: {
    width: "100%",
    textAlign: "center" as const,
    marginBottom: 24
  },
  logoCol: {
    width: "100%",
    textAlign: "center" as const
  },
  logo: {
    display: "block",
    margin: "0 auto",
    border: "none",
    objectFit: "contain" as const,
    width: 164,
    maxWidth: "100%"
  },
  socialIconsRow: {
    width: "100%",
    textAlign: "center" as const
  },
  socialIconsCol: {
    width: "100%",
    textAlign: "center" as const
  },
  iconLink: {
    display: "inline-block",
    margin: "0 8px"
  },
  iconImg: {
    width: 20,
    height: 20,
    objectFit: "contain" as const
  },
  dividerSection: {
    paddingTop: 0,
    paddingBottom: 0,
    paddingLeft: 24,
    paddingRight: 24
  },
  divider: {
    height: 1,
    width: "100%",
    backgroundColor: "#cfd0d4",
    margin: "0 auto"
  },
  disclaimerSectionOuter: {
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 24,
    paddingRight: 24
  },
  disclaimerCol: {
    width: "100%",
    maxWidth: 552,
    margin: "0 auto"
  },
  disclaimerText: {
    fontSize: 12,
    fontWeight: 300,
    color: "#909090",
    textAlign: "left" as const,
    lineHeight: "18px",
    margin: 0,
    padding: 0,
    paddingBottom: 16,
    marginBottom: 16
  },
  recipientsLinksSection: {
    paddingTop: 16,
    textAlign: "center" as const,
    paddingBottom: 32
  },
  recipientText: {
    fontSize: 10,
    fontWeight: 300,
    color: "#909090",
    textAlign: "center" as const,
    lineHeight: "15px",
    margin: 0,
    padding: 0,
    marginBottom: 4
  },
  linksRow: {
    textAlign: "center" as const,
    margin: "8px 0"
  },
  linkCol: {
    paddingRight: 4,
    textAlign: "right" as const,
    width: "50%"
  },
  linkColPad: {
    paddingLeft: 4,
    textAlign: "center" as const,
    width: "50%"
  },
  link: {
    fontSize: 10,
    fontWeight: 300,
    color: "#909090",
    textDecoration: "underline"
  }
};

export default Footer;
