import * as React from "react";
import { Section, Text, Container, Row, Column } from "@react-email/components";
import SectionHeading from "./SectionHeading";
import type { MarketIndexItem } from "types/wealthybites";
import { formatPercentage } from "../../../../utils/formatterUtil";
import { localeConfig } from "@wealthyhood/shared-configs";

interface MarketRoundupProps {
  marketRoundupText: string;
  topMarketIndexes: MarketIndexItem[];
  userLocale: localeConfig.LocaleType;
}

export const MarketRoundup: React.FC<MarketRoundupProps> = ({
  marketRoundupText,
  topMarketIndexes,
  userLocale
}) => (
  <Container>
    <SectionHeading label="NEWS digest" title="Market Roundup" />

    <Section style={styles.summaryContainer}>
      <Text style={styles.summaryText}>{marketRoundupText}</Text>
    </Section>

    <Section style={styles.marketsContainer}>
      <Text style={styles.marketsHeading}>Global markets this week:</Text>
    </Section>

    <Section style={styles.marketsListContainer}>
      <Row style={styles.row}>
        <Column style={styles.column} className="market-column">
          {topMarketIndexes.slice(0, Math.ceil(topMarketIndexes.length / 2)).map((market, index) => (
            <Text key={index} style={styles.marketItem}>
              {market.name}{" "}
              <span style={market.returns >= 0 ? styles.greenText : styles.redText}>
                {formatPercentage(market.returns, userLocale, 2, 2, "always")}
              </span>
              {index < Math.ceil(topMarketIndexes.length / 2) - 1 && <br />}
            </Text>
          ))}
        </Column>
        <Column style={styles.column} className="market-column">
          {topMarketIndexes.slice(Math.ceil(topMarketIndexes.length / 2)).map((market, index) => (
            <Text key={index} style={styles.marketItem}>
              {market.name}{" "}
              <span style={market.returns >= 0 ? styles.greenText : styles.redText}>
                {formatPercentage(market.returns, userLocale, 2, 2, "always")}
              </span>
              {index < topMarketIndexes.slice(Math.ceil(topMarketIndexes.length / 2)).length - 1 && <br />}
            </Text>
          ))}
        </Column>
      </Row>
    </Section>
  </Container>
);

const styles = {
  summaryContainer: {
    backgroundColor: "#FFFFFF",
    padding: "14px 16px"
  },
  summaryText: {
    color: "#333333",
    margin: "0",
    lineHeight: "21px",
    fontSize: "14px",
    textAlign: "left" as const
  },
  marketsContainer: {
    backgroundColor: "#FFFFFF",
    paddingTop: "14px",
    paddingLeft: "16px",
    paddingRight: "31px",
    height: "43px"
  },
  marketsHeading: {
    color: "#333333",
    margin: "0",
    lineHeight: "21px",
    fontSize: "14px",
    fontWeight: "700",
    textAlign: "left" as const
  },
  marketsListContainer: {
    backgroundColor: "#FFFFFF",
    padding: "14px 16px 32px 16px",
    borderRadius: "8px"
  },
  row: {
    width: "100%",
    display: "table",
    tableLayout: "fixed" as const
  },
  column: {
    width: "50%",
    display: "table-cell",
    verticalAlign: "top" as const,
    paddingRight: "11px",
    paddingLeft: "11px"
  },
  marketItem: {
    color: "#333333",
    margin: "0",
    lineHeight: "21px",
    fontSize: "14px",
    textAlign: "left" as const
  },
  greenText: {
    color: "#23846A"
  },
  redText: {
    color: "#D63C3C"
  }
};

export default MarketRoundup;
