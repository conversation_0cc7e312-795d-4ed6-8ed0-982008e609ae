import * as React from "react";
import { Section, Text, Container, Row, Column } from "@react-email/components";
import SectionHeading from "./SectionHeading";
import type { InvestmentSnapshotData } from "types/wealthybites";
import CurrencyUtil from "../../../../utils/currencyUtil";
import { formatPercentage } from "../../../../utils/formatterUtil";
import { currenciesConfig, localeConfig } from "@wealthyhood/shared-configs";

interface InvestmentSnapshotProps extends InvestmentSnapshotData {
  userCurrency: currenciesConfig.MainCurrencyType;
  userLocale: localeConfig.LocaleType;
}

export const InvestmentSnapshot: React.FC<InvestmentSnapshotProps> = ({
  investmentsValue,
  weeklyReturns,
  upByValue,
  cashValue,
  savingsValue,
  savingsDailyInterest,
  totalValue,
  userCurrency,
  userLocale
}) => {
  const direction = upByValue >= 0 ? "up" : "down";
  const upByChangeFormatted = CurrencyUtil.formatCurrency(upByValue, userCurrency, userLocale, true, "always");
  const weeklyInvestmentReturnsFormatted = formatPercentage(weeklyReturns, userLocale, 2, 2, "never");

  return (
    <Container>
      <SectionHeading label="WEEKLY SUMMARY" title="Your investment snapshot" />

      <Section style={styles.snapshotContainer}>
        <Row>
          <Column>
            <Text style={styles.snapshotText}>
              Your investments are {direction} by{" "}
              <span style={styles.boldText}>{weeklyInvestmentReturnsFormatted} this week.</span> Here's an overview
              of your Wealthyhood account:
            </Text>
          </Column>
        </Row>
        <Row style={styles.dataRow}>
          <Column>
            <Text style={styles.lineItem}>
              📈 Investments: {CurrencyUtil.formatCurrency(investmentsValue, userCurrency, userLocale)}{" "}
              <span style={upByValue >= 0 ? styles.greenText : styles.redText}>({upByChangeFormatted})</span>
            </Text>
          </Column>
        </Row>
        <Row>
          <Column>
            <Text style={styles.lineItem}>
              💸 Cash: {CurrencyUtil.formatCurrency(cashValue, userCurrency, userLocale)}
            </Text>
          </Column>
        </Row>
        {savingsValue != null && savingsDailyInterest != null && (
          <Row>
            <Column>
              <Text style={styles.lineItem}>
                💰 Savings: {CurrencyUtil.formatCurrency(savingsValue, userCurrency, userLocale)}{" "}
                {savingsDailyInterest > 0 && (
                  <>
                    (earning {CurrencyUtil.formatCurrency(savingsDailyInterest, userCurrency, userLocale)} interest
                    per day)
                  </>
                )}
              </Text>
            </Column>
          </Row>
        )}
        <Row style={styles.totalRow}>
          <Column>
            <Text style={styles.totalText}>
              Total account value: {CurrencyUtil.formatCurrency(totalValue, userCurrency, userLocale)}
            </Text>
          </Column>
        </Row>
      </Section>
    </Container>
  );
};

const styles = {
  snapshotContainer: {
    backgroundColor: "#FFFFFF",
    padding: "14px 16px 24px",
    borderRadius: "0"
  },
  snapshotText: {
    fontFamily: "Poppins, Tahoma, sans-serif",
    fontSize: "14px",
    fontWeight: "400",
    textAlign: "left" as const,
    lineHeight: "21px",
    color: "#333333",
    margin: "0",
    padding: "23px 23px 23px 23px",
    border: "1px solid #DCE2FD",
    borderRadius: "16px 16px 0 0",
    borderBottom: "0"
  },
  lineItem: {
    fontFamily: "Poppins, Tahoma, sans-serif",
    fontSize: "14px",
    fontWeight: "400",
    textAlign: "left" as const,
    lineHeight: "21px",
    color: "#333333",
    margin: "0",
    padding: "1px 23px",
    borderLeft: "1px solid #DCE2FD",
    borderRight: "1px solid #DCE2FD",
    backgroundColor: "#FFFFFF"
  },
  dataRow: {
    marginTop: "0"
  },
  totalRow: {
    marginTop: 0
  },
  totalText: {
    fontFamily: "Poppins, Tahoma, sans-serif",
    fontSize: "14px",
    fontWeight: "400",
    textAlign: "left" as const,
    lineHeight: "21px",
    color: "#333333",
    margin: "0",
    padding: "20px 23px 23px 23px",
    borderLeft: "1px solid #DCE2FD",
    borderRight: "1px solid #DCE2FD",
    borderBottom: "1px solid #DCE2FD",
    borderRadius: "0 0 16px 16px",
    backgroundColor: "#FFFFFF"
  },
  boldText: {
    fontWeight: "700"
  },
  greenText: {
    color: "#23846A"
  },
  redText: {
    color: "#D63C3C"
  }
};

export default InvestmentSnapshot;
