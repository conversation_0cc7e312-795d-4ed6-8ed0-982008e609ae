const { MongoMemoryReplSet } = require("mongodb-memory-server");
const { mkdtemp } = require("fs/promises");
const { join } = require("path");

// Load dev env variables
require("dotenv").config({ path: "dev.env" });

const MONGO_VERSION = "7.0.11";

module.exports = async () => {
  if (process.env.DATABASE_IN_MEMORY_ENABLED === "true") {
    const replicaSet = await MongoMemoryReplSet.create({
      instanceOpts: [
        {
          dbPath: await mkdtemp(join("/dev/shm", "action-"))
        }
      ],
      binary: {
        version: MONGO_VERSION
      },
      replSet: { count: 1, storageEngine: "wiredTiger" }
    });

    // eslint-disable-next-line no-undef
    globalThis.__MONGOD__ = replicaSet;

    const uri = replicaSet.getUri();
    process.env.IN_MEMORY_DATABASE_URL = uri.slice(0, uri.lastIndexOf("/"));
  }
};
