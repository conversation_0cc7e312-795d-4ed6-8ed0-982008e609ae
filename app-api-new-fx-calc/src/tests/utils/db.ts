import mongoose from "mongoose";

async function connectDb(moduleName: string): Promise<void> {
  let url: string;
  if (process.env.DATABASE_IN_MEMORY_ENABLED === "true") {
    url = `${process.env.IN_MEMORY_DATABASE_URL}/${moduleName}`;
  } else {
    url = `${process.env.DATABASE_URL}-${moduleName}`;
  }

  _initListeners();

  await mongoose
    .set("strictQuery", false)
    .connect(url)
    .catch((err) => {
      console.error(
        `🙅 🚫 🙅 🚫 🙅 🚫 🙅 🚫 mongoose exception handling on in-memory database connect → ${err.message}`
      );
    });
}

async function clearDb(): Promise<void> {
  await Promise.all(Object.values(mongoose.connection.collections).map((collection) => collection.deleteMany({})));
}

async function closeDb(): Promise<void> {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
}

function _initListeners(): void {
  mongoose.connection.on("error", (err) => {
    console.error(`🙅 🚫 🙅 🚫 🙅 🚫 🙅 🚫 mongodb connection error → ${err.message}`);
  });

  mongoose.connection.on("connected", function () {
    console.log(`✅ ✅ ✅ → mongodb is connected for ${mongoose.connection.name}`);
  });

  mongoose.connection.on("disconnected", function () {
    console.warn("❌ ❌ ❌ → mongodb disconnected");
  });
}

export { connectDb, clearDb, closeDb };
