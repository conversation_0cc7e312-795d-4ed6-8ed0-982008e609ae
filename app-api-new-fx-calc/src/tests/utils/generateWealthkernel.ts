import { faker } from "@faker-js/faker";
import { countriesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import {
  AccountType,
  AddressType,
  BalanceType,
  BankAccountType,
  BonusType,
  CurrencyEnum,
  DepositType,
  FillType,
  OrderType,
  PartyType,
  PortfolioType,
  PortfolioWrapperTypeEnum,
  TransactionStatusArray,
  TransactionType,
  TransactionTypeArray,
  ValuationType,
  WithdrawalRetrievalResponseType
} from "../../external-services/wealthkernelService";

const { AssetArrayConst, ASSET_CONFIG } = investmentUniverseConfig;

function buildWealthkernelTransactionResponse(overrides: Partial<TransactionType>): TransactionType {
  return {
    id: faker.string.uuid(),
    portfolioId: faker.string.uuid(),
    isin: faker.helpers.arrayElement(Object.values(ASSET_CONFIG).map(({ isin }) => isin)),
    type: faker.helpers.arrayElement(TransactionTypeArray),
    status: faker.helpers.arrayElement(TransactionStatusArray),
    quantity: faker.number.int({ min: 1, max: 10 }),
    consideration: {
      currency: CurrencyEnum.GBP,
      amount: faker.number.int({ min: 1, max: 20 })
    },
    charges: {
      currency: CurrencyEnum.GBP,
      amount: faker.number.int({ min: 0, max: 1 })
    },
    date: new Date(faker.date.between({ from: "2022-01-01", to: "2022-01-05" })).toDateString(),
    timestamp: new Date(faker.date.between({ from: "2022-01-01", to: "2022-01-05" })).toDateString(),
    settledOn: new Date(faker.date.between({ from: "2022-01-01", to: "2022-01-05" })).toDateString(),
    updatedAt: new Date(faker.date.between({ from: "2022-01-01", to: "2022-01-05" })).toDateString(),
    ...overrides
  };
}

function buildWealthkernelAddressResponse(overrides: Partial<AddressType> = {}): AddressType {
  return {
    id: faker.string.uuid(),
    partyId: faker.string.uuid(),
    line1: faker.location.streetAddress(),
    city: faker.location.city(),
    countryCode: faker.helpers.arrayElement(countriesConfig.countryCodesArray),
    postalCode: "EC1R3AL",
    ...overrides
  };
}

function buildWealthkernelBonusResponse(overrides: Partial<BonusType> = {}): BonusType {
  return {
    id: faker.string.uuid(),
    status: "Settled",
    sourcePortfolio: faker.string.uuid(),
    destinationPortfolio: faker.string.uuid(),
    consideration: {
      currency: CurrencyEnum.GBP,
      amount: faker.number.int()
    },
    clientReference: faker.string.uuid(),
    ...overrides
  };
}

function buildWealthkernelBankAccountResponse(overrides: Partial<BankAccountType> = {}): BankAccountType {
  return {
    id: faker.string.uuid(),
    status: "Active",
    activatedAt: new Date(),
    deactivatedAt: new Date(),
    partyId: faker.string.uuid(),
    name: "Best account ever",
    accountNumber: "********",
    sortCode: "00-00-00",
    currency: CurrencyEnum.GBP,
    countryCode: "GB",
    ...overrides
  };
}

function buildFillType(overrides: Partial<FillType> = {}): FillType {
  return {
    transactionId: faker.string.uuid(),
    price: {
      currency: CurrencyEnum.GBP,
      amount: faker.number.int()
    },
    consideration: {
      currency: CurrencyEnum.GBP,
      amount: faker.number.int()
    },
    quantity: faker.number.int(),
    status: "Matched",
    filledAt: new Date(),
    settlementDate: new Date(),
    exchangeRate: faker.number.float({ min: 1, max: 1.4, multipleOf: 0.01 }),
    ...overrides
  };
}

function buildWealthkernelPartyResponse(overrides: any = {}): PartyType {
  return {
    createdAt: faker.date.anytime().toString(),
    id: faker.string.uuid(),
    kycPassed: false,
    status: "Created",
    forename: faker.person.firstName(),
    surname: faker.person.lastName(),
    emailAddress: faker.internet.email(),
    dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" }),
    taxResidencies: [],
    citizenships: [],
    address: null,
    bankAccount: null,
    ...overrides
  };
}

function buildWealthkernelOrderResponse(overrides: any = {}, filltypeOverrides: any = {}): OrderType {
  return {
    portfolioId: faker.string.uuid(),
    isin: faker.helpers.arrayElement(Object.values(ASSET_CONFIG).map(({ isin }) => isin)),
    settlementCurrency: CurrencyEnum.GBP,
    side: "Buy",
    clientReference: faker.string.sample(),
    quantity: faker.number.int(),
    fills: [buildFillType(filltypeOverrides)],
    reason: faker.string.sample(),
    receivedAt: faker.date.anytime(),
    id: faker.string.uuid(),
    status: "Matched",
    ...overrides
  };
}

function buildWealthkernelValuationResponse(overrides: any = {}): ValuationType {
  const portfolioValue = faker.number.int({ min: 10, max: 1000 });
  const cashValue = faker.number.int({ min: 1, max: portfolioValue });
  const holdingValue = portfolioValue - cashValue;
  const holdingPrice = faker.number.int({ min: 1, max: 50 });
  const assetId = faker.helpers.arrayElement(AssetArrayConst);

  return {
    portfolioId: faker.string.uuid(),
    date: faker.date.anytime(),
    value: {
      currency: "GBP",
      amount: portfolioValue
    },
    cash: [
      {
        currency: "GBP",
        amount: { currency: "GBP", amount: cashValue },
        value: { currency: "GBP", amount: cashValue },
        fxRate: 1.0
      }
    ],
    holdings:
      holdingValue > 0
        ? [
            {
              isin: ASSET_CONFIG[assetId].isin,
              quantity: holdingValue / holdingPrice,
              price: { currency: "GBP", amount: holdingPrice },
              value: { currency: "GBP", amount: holdingValue },
              fxRate: 1.0
            }
          ]
        : [],
    changedAt: faker.date.anytime(),
    ...overrides
  };
}

function buildWealthkernelCashBalanceResponse(overrides: any = {}): BalanceType {
  const cashValue = faker.number.int({ min: 1, max: 1000 });

  return {
    value: {
      currency: "GBP",
      amount: cashValue
    },
    ...overrides
  };
}

/**
 * Returns a fake response from the /withdrawal/{withdrawalId} GET request to Wealthkernel to retrieve a withdrawal.
 * For further information on the response definition, check out:
 *
 * https://docs.wealthkernel.com/docs/api/branches/2019-07-16/b3A6ODk0NTI5NQ-retrieve-a-withdrawal
 *
 * An response example:
 * {
 *   "type": "SpecifiedAmount",
 *   "portfolioId": "prt-32q2deogu225ps",
 *   "bankAccountId": "bac-32q2dek3j263q4",
 *   "consideration": {
 *     "currency": "GBP",
 *     "amount": 100
 *   },
 *   "reference": "32Q2DEOGU225PS",
 *   "status": "Settled",
 *   "requestedAt": "2019-10-08T13:55:16.4237925Z",
 *   "id": "wth-32qm6qal5225qe"
 * }
 *
 * @param overrides an object to override the response's properties
 * @returns a fake response from the /withdrawal/{withdrawalId} GET request to Wealthkernel to retrieve a withdrawal.
 */
function buildWealthkernelRetrieveWithdrawalResponse(overrides: any = {}): WithdrawalRetrievalResponseType {
  return {
    type: "SpecifiedAmount",
    portfolio: faker.string.uuid(),
    bankAccountId: faker.string.uuid(),
    consideration: {
      currency: CurrencyEnum.GBP,
      amount: faker.number.int({ min: 0, max: 100 })
    },
    reference: faker.string.uuid(),
    status: "Settled",
    requestedAt: "2019-10-08T13:55:16.4237925Z",
    id: faker.string.uuid(),
    ...overrides
  };
}

function buildWealthkernelAccountResponse(overrides: any = {}): AccountType {
  return {
    type: PortfolioWrapperTypeEnum.GIA,
    name: "Some account",
    productId: faker.string.uuid(),
    owner: faker.string.uuid(),
    status: "Active",
    addedAt: new Date("04 Dec 1995 00:12:00 GMT"),
    id: faker.string.uuid(),
    ...overrides
  };
}

function buildWealthkernelPortfoliosResponse(overrides: any = {}): PortfolioType {
  return {
    accountId: faker.string.uuid(),
    clientReference: faker.string.sample(),
    name: faker.string.sample(),
    currency: CurrencyEnum.GBP,
    mandate: {
      type: "ExecutionOnlyMandate"
    },
    createdAt: faker.date.anytime(),
    id: faker.string.uuid(),
    status: "Active",
    ...overrides
  };
}

function buildWealthkernelDepositResponse(overrides: Partial<BonusType> = {}): DepositType {
  return {
    id: faker.string.uuid(),
    portfolioId: faker.string.uuid(),
    accountId: faker.string.uuid(),
    consideration: {
      currency: CurrencyEnum.GBP,
      amount: faker.number.int()
    },
    reference: faker.string.uuid(),
    status: "Settled",
    createdAt: new Date().toString(),
    ...overrides
  };
}

export {
  buildWealthkernelTransactionResponse,
  buildWealthkernelAddressResponse,
  buildFillType,
  buildWealthkernelPartyResponse,
  buildWealthkernelOrderResponse,
  buildWealthkernelValuationResponse,
  buildWealthkernelRetrieveWithdrawalResponse,
  buildWealthkernelBankAccountResponse,
  buildWealthkernelAccountResponse,
  buildWealthkernelPortfoliosResponse,
  buildWealthkernelBonusResponse,
  buildWealthkernelDepositResponse,
  buildWealthkernelCashBalanceResponse
};
