import {
  EodETFFundamentalsResponseType,
  EodStockFundamentalsResponseType,
  HistoricalDataType,
  HistoricalVolumeDataType,
  IntraDayDataType,
  RealTimeDataType
} from "../../external-services/eodService";

function buildIntraDayDataResponse(overrides: IntraDayDataType[] = []): any {
  return overrides.length > 0
    ? overrides
    : [
        {
          timestamp: 1715607300,
          gmtoffset: 0,
          datetime: "2024-05-13 13:35:00",
          open: 184.904998,
          high: 185.020004,
          low: 184.619995,
          close: 184.929992,
          volume: 1238246
        }
      ];
}

function buildRealTimeDataResponse(overrides: Partial<RealTimeDataType> = {}): any {
  return {
    code: "AAPL.US",
    timestamp: 1717013640,
    gmtoffset: 0,
    open: 189.61,
    high: 192.247,
    low: 189.51,
    close: 190.29,
    volume: 52536626,
    previousClose: 189.99,
    change: 0.3,
    change_p: 0.1579,
    ...overrides
  };
}

function buildHistoricalDataResponse(overrides: HistoricalDataType[] = []): any {
  return overrides.length > 0
    ? overrides
    : [
        {
          date: "2024-05-29",
          open: 78.6975,
          high: 78.891,
          low: 77.9734,
          close: 78.7575,
          adjusted_close: 78.7575,
          volume: 165661
        }
      ];
}

function buildHistoricalVolumeDataResponse(
  overrides: HistoricalVolumeDataType[] = []
): HistoricalVolumeDataType[] {
  return overrides.length > 0
    ? overrides
    : [
        { date: "2024-07-22", volume: 43912461 },
        { date: "2024-07-23", volume: 31950580 },
        { date: "2024-07-24", volume: 49203121 },
        { date: "2024-07-25", volume: 60578527 },
        { date: "2024-07-26", volume: 49435832 },
        { date: "2024-07-29", volume: 47447191 },
        { date: "2024-07-30", volume: 68801711 },
        { date: "2024-07-31", volume: 114306797 },
        { date: "2024-08-01", volume: 94731930 },
        { date: "2024-08-02", volume: 78562000 },
        { date: "2024-08-05", volume: 76089359 },
        { date: "2024-08-06", volume: 64455609 },
        { date: "2024-08-07", volume: 54676488 },
        { date: "2024-08-08", volume: 49037449 },
        { date: "2024-08-09", volume: 40582992 },
        { date: "2024-08-12", volume: 39724340 },
        { date: "2024-08-13", volume: 42218020 },
        { date: "2024-08-14", volume: 35506434 },
        { date: "2024-08-15", volume: 43582391 },
        { date: "2024-08-16", volume: 31123930 },
        { date: "2024-08-19", volume: 60766854 }
      ];
}

function buildStockFundamentalsResponse(
  overrides?: Partial<EodStockFundamentalsResponseType>
): EodStockFundamentalsResponseType {
  return {
    General: {
      Exchange: "NASDAQ",
      GicIndustry: "Software",
      WebURL: "https://www.microsoft.com",
      FullTimeEmployees: 22000,
      ISIN: "US5949181045",
      AddressData: {
        City: "Redmond",
        State: "WA",
        Country: "United States"
      },
      Description:
        "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
      Officers: {
        0: {
          Name: "Mr. Satya  Nadella",
          Title: "Chairman & CEO"
        }
      },
      LogoURL: "/img/logos/US/MSFT.png"
    },
    Highlights: {
      DividendYield: 0.0079,
      EarningsShare: 10.3,
      PERatio: 36.6437,
      MarketCapitalization: 1252000000000, // 1.252 trillion
      WallStreetTargetPrice: 251.12
    },
    Valuation: {
      ForwardPE: 32.432
    },
    Technicals: {
      Beta: 0.885
    },
    AnalystRatings: {
      StrongBuy: 50,
      Buy: 22,
      Hold: 11,
      Sell: 5,
      StrongSell: 3
    },
    ...overrides
  };
}

function buildEtfFundamentalsResponse(
  overrides?: Partial<EodETFFundamentalsResponseType>
): EodETFFundamentalsResponseType {
  return {
    General: {
      Name: "Vanguard S&P 500 UCITS ETF",
      Exchange: "LSE",
      CurrencyCode: "GBP",
      Description: "NA"
    },
    Technicals: { "52WeekHigh": 100, "52WeekLow": 75.0987, "50DayMA": 90.4594, "200DayMA": 85.544 },
    ETF_Data: {
      ISIN: "IE00B4KBBD01",
      Company_Name: "Vanguard",
      ETF_URL: "",
      Company_URL: "http:\\/\\/global.vanguard.com",
      TotalAssets: "45250510000.00",
      Dividend_Paying_Frequency: "Quarterly",
      Performance: {
        "1y_Volatility": "10.50",
        "3y_Volatility": "0.00",
        "3y_ExpReturn": "0.00",
        "3y_SharpRatio": "0.00",
        Returns_YTD: "-5.12",
        Returns_1Y: "10.09",
        Returns_3Y: "11.15",
        Returns_5Y: "18.92",
        Returns_10Y: "12.18"
      },
      Valuations_Growth: {
        Valuations_Rates_Portfolio: {
          "Price/Sales": "15.54954",
          "Price/Book": "15.54954",
          "Price/Cash Flow": "15.54954",
          "Price/Prospective Earnings": "15.54954",
          "Dividend-Yield Factor": "1.61438"
        },
        Growth_Rates_Portfolio: {
          "Long-Term Projected Earnings Growth": "15.54954",
          "Historical Earnings Growth": "15.54954",
          "Sales Growth": "15.54954",
          "Cash-Flow Growth": "15.54954",
          "Book-Value Growth": "15.54954"
        }
      },
      Ongoing_Charge: "0.6500",
      Holdings_Count: 10,
      Top_10_Holdings: {
        "FSLR.US": {
          Code: "FSLR",
          Exchange: "US",
          Name: "First Solar Inc",
          "Assets_%": 7.64461
        },
        "ENPH.US": {
          Code: "ENPH",
          Exchange: "US",
          Name: "Enphase Energy Inc",
          "Assets_%": 7.51445
        },
        "VWS.CO": {
          Code: "VWS",
          Exchange: "CO",
          Name: "Vestas Wind Systems A/S",
          "Assets_%": 5.73413
        },
        "ORSTED.CO": {
          Code: "ORSTED",
          Exchange: "CO",
          Name: "Orsted A/S",
          "Assets_%": 5.03518
        },
        "600900.SHG": {
          Code: "600900",
          Exchange: "SHG",
          Name: "China Yangtze Power Co Ltd",
          "Assets_%": 4.41761
        },
        "NXT.US": {
          Code: "NXT",
          Exchange: "US",
          Name: "Nextracker Inc. Class A Common Stock",
          "Assets_%": 3.66898
        },
        "9502.TSE": {
          Code: "9502",
          Exchange: "TSE",
          Name: "Chubu Electric Power Co. Inc.",
          "Assets_%": 3.16037
        },
        "EDP.LS": {
          Code: "EDP",
          Exchange: "LS",
          Name: "EDP - Energias de Portugal S.A.",
          "Assets_%": 3.04983
        },
        "SUZLON.BSE": {
          Code: "SUZLON",
          Exchange: "BSE",
          Name: "SUZLON ENERGY LTD.",
          "Assets_%": 2.76903
        },
        "SEDG.US": {
          Code: "SEDG",
          Exchange: "US",
          Name: "SolarEdge Technologies Inc",
          "Assets_%": 2.50654
        }
      },
      World_Regions: {
        "North America": {
          "Equity_%": "44.803"
        },
        "United Kingdom": {
          "Equity_%": "0.35679"
        },
        "Europe Developed": {
          "Equity_%": "21.68"
        },
        "Europe Emerging": {
          "Equity_%": "0.322"
        },
        "Africa/Middle East": {
          "Equity_%": "0.487"
        },
        Japan: {
          "Equity_%": "3.157"
        },
        Australasia: {
          "Equity_%": "0.59"
        },
        "Asia Developed": {
          "Equity_%": "4.501"
        },
        "Asia Emerging": {
          "Equity_%": "18.891"
        },
        "Latin America": {
          "Equity_%": "5.213"
        }
      },
      Sector_Weights: {
        "Basic Materials": {
          "Equity_%": "3.29213"
        },
        "Consumer Cyclicals": {
          "Equity_%": "0"
        },
        "Financial Services": {
          "Equity_%": "0"
        },
        "Real Estate": {
          "Equity_%": "0.09898"
        },
        "Communication Services": {
          "Equity_%": "0"
        },
        Energy: {
          "Equity_%": "0"
        },
        Industrials: {
          "Equity_%": "17.25509"
        },
        Technology: {
          "Equity_%": "37.22878"
        },
        "Consumer Defensive": {
          "Equity_%": "0"
        },
        Healthcare: {
          "Equity_%": "0"
        },
        Utilities: {
          "Equity_%": "42.12502"
        }
      }
    },
    ...overrides
  };
}

export {
  buildIntraDayDataResponse,
  buildRealTimeDataResponse,
  buildHistoricalDataResponse,
  buildStockFundamentalsResponse,
  buildEtfFundamentalsResponse,
  buildHistoricalVolumeDataResponse
};
