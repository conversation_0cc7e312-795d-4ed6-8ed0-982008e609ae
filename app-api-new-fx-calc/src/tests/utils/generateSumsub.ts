import { faker } from "@faker-js/faker";
import { ApplicantType } from "../../external-services/sumsubService";

function buildApplicant(overrides?: Partial<ApplicantType>): ApplicantType {
  return {
    id: faker.string.uuid(),
    info: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      dob: "1996-11-24",
      nationality: "GBR"
    },
    review: {
      reviewId: faker.string.uuid(),
      reviewStatus: "completed",
      reviewResult: {
        reviewAnswer: "GREEN"
      }
    },
    ...overrides
  };
}

export { buildApplicant };
