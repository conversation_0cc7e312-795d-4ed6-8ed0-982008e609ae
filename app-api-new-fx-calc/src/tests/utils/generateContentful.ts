import { Entry, EntryCollection, EntryFields, EntrySkeletonType } from "contentful";
import {
  AnalystInsightType,
  FaqCategoryType,
  GlossaryItemType,
  NewsItemType
} from "../../services/wealthyhubService";
import { faker } from "@faker-js/faker";

function buildContentfulAnalystInsightsResponse(
  entry: Partial<AnalystInsightType & { bannerImagePath: string }>[]
): EntryCollection<EntrySkeletonType> {
  return {
    total: entry.length,
    skip: 0,
    limit: 100,
    items: entry.map((entry) => {
      return {
        metadata: { tags: [] },
        sys: {
          space: { sys: { type: "Link", linkType: "Space", id: "haw0z39aqcwg" } },
          id: faker.string.uuid(),
          type: "Entry",
          createdAt: entry.createdAt.toISOString() as EntryFields.Date,
          updatedAt: "2023-10-05T09:55:32.429Z",
          environment: { sys: { id: "sandbox", type: "Link", linkType: "Environment" } },
          revision: 1,
          contentType: { sys: { type: "Link", linkType: "ContentType", id: "analystInsights" } },
          locale: "en-US"
        },
        fields: {
          title: entry.title,
          tags: ["Banks", "US", "Finance", "Economy", "Global"],
          content: {
            data: {},
            content: [
              {
                data: {},
                content: [{ data: {}, marks: [], value: "TLDR;", nodeType: "text" }],
                nodeType: "heading-2"
              },
              {
                data: {},
                content: [
                  {
                    data: {},
                    marks: [],
                    value:
                      "Lorem ipsum dolor sit amet consectetur. Tincidunt integer aliquam sit diam iaculis viverra. Integer velit id turpis pretium ultrices id. ",
                    nodeType: "text"
                  },
                  { data: {}, marks: [{ type: "bold" }], value: "Viverra", nodeType: "text" },
                  {
                    data: {},
                    marks: [],
                    value:
                      " aliquam ultrices in accumsan. Arcu libero in in elementum condimentum sodales. Ut netus ullamcorper diam pulvinar id mi. ",
                    nodeType: "text"
                  }
                ],
                nodeType: "paragraph"
              },
              {
                data: {
                  target: {
                    metadata: { tags: [] },
                    sys: {
                      space: { sys: { type: "Link", linkType: "Space", id: "haw0z39aqcwg" } },
                      id: "4MGPbV6pBjtu4YLQnTy2kF",
                      type: "Asset",
                      createdAt: "2023-10-05T09:52:52.636Z",
                      updatedAt: "2023-10-05T09:52:52.636Z",
                      environment: { sys: { id: "sandbox", type: "Link", linkType: "Environment" } },
                      revision: 1,
                      locale: "en-US"
                    },
                    fields: {
                      title: "compoundgrowth",
                      description: "",
                      file: {
                        url: "//images.ctfassets.net/haw0z39aqcwg/4MGPbV6pBjtu4YLQnTy2kF/543640ed315128ff10cff950bc154fee/compoundgrowth.png",
                        details: { size: 8402, image: { width: 590, height: 328 } },
                        fileName: "compoundgrowth.png",
                        contentType: "image/png"
                      }
                    }
                  }
                },
                content: [],
                nodeType: "embedded-asset-block"
              },
              {
                data: {},
                content: [{ data: {}, marks: [], value: "Title 1", nodeType: "text" }],
                nodeType: "heading-3"
              },
              {
                data: {},
                content: [
                  { data: {}, marks: [], value: "Lorem ipsum dolor sit amet ", nodeType: "text" },
                  { data: {}, marks: [{ type: "bold" }], value: "consectetur", nodeType: "text" },
                  {
                    data: {},
                    marks: [],
                    value:
                      ". Tincidunt integer aliquam sit diam iaculis viverra. Integer velit id turpis pretium ultrices id. Viverra aliquam ultrices in accumsan. Arcu libero in in elementum condimentum sodales. Ut netus ullamcorper diam pulvinar id mi. ",
                    nodeType: "text"
                  }
                ],
                nodeType: "paragraph"
              }
            ],
            nodeType: "document"
          },
          fullImage: {
            metadata: { tags: [] },
            sys: {
              space: { sys: { type: "Link", linkType: "Space", id: "haw0z39aqcwg" } },
              id: "3rypnZJKsnZotyz5VJ2nz7",
              type: "Asset",
              createdAt: "2023-10-05T09:53:52.996Z",
              updatedAt: "2023-10-05T09:53:52.996Z",
              environment: { sys: { id: "sandbox", type: "Link", linkType: "Environment" } },
              revision: 1,
              locale: "en-US"
            },
            fields: {
              title: "Featured image @4x",
              description: "",
              file: {
                url: "//images.ctfassets.net/haw0z39aqcwg/3rypnZJKsnZotyz5VJ2nz7/3ee42fe7a3ff96d7249354de013dbd99/Featured_image__4x.png",
                details: { size: 730119, image: { width: 1080, height: 1920 } },
                fileName: "Featured image @4x.png",
                contentType: "image/png"
              }
            }
          },
          previewImage: {
            metadata: { tags: [] },
            sys: {
              space: { sys: { type: "Link", linkType: "Space", id: "haw0z39aqcwg" } },
              id: "656wXvOtrJKHwaDCpiJc25",
              type: "Asset",
              createdAt: "2023-10-05T09:54:07.326Z",
              updatedAt: "2023-10-05T09:54:07.326Z",
              environment: { sys: { id: "sandbox", type: "Link", linkType: "Environment" } },
              revision: 1,
              locale: "en-US"
            },
            fields: {
              title: "Thumbnail image @4x",
              description: "",
              file: {
                url: "//images.ctfassets.net/haw0z39aqcwg/656wXvOtrJKHwaDCpiJc25/0263688ce59d8ee2ed8ea67587520dc4/Thumbnail_image__4x.png",
                details: { size: 136931, image: { width: 288, height: 512 } },
                fileName: "Thumbnail image @4x.png",
                contentType: "image/png"
              }
            }
          },
          bannerImage: {
            metadata: { tags: [] },
            sys: {
              space: { sys: { type: "Link", linkType: "Space", id: "haw0z39aqcwg" } },
              id: "656wXvOtrJKHwaDCpiJc25",
              type: "Asset",
              createdAt: "2023-10-05T09:54:07.326Z",
              updatedAt: "2023-10-05T09:54:07.326Z",
              environment: { sys: { id: "sandbox", type: "Link", linkType: "Environment" } },
              revision: 1,
              locale: "en-US"
            },
            fields: {
              title: "Thumbnail image @4x",
              description: "",
              file: {
                url: `//images.ctfassets.net/${
                  entry.bannerImagePath ??
                  "haw0z39aqcwg/656wXvOtrJKHwaDCpiJc25/0263688ce59d8ee2ed8ea67587520dc4/Thumbnail_image__4x.png"
                }`,
                details: { size: 136931, image: { width: 288, height: 512 } },
                fileName: "Thumbnail image @4x.png",
                contentType: "image/png"
              }
            }
          },
          readingTime: "4 mins"
        }
      };
    })
  };
}
// The arguments are just three fields that can configure the contentful response and are not related
// to the actual contentful response type.
function buildContentfulNewsResponse(
  entries: Partial<{ title: string; createdAt: Date; content: string }>[]
): EntryCollection<EntrySkeletonType> {
  return {
    total: entries.length,
    skip: 0,
    limit: 100,
    items: entries.map((entry) => {
      return {
        metadata: {
          tags: []
        },
        sys: {
          space: {
            sys: {
              type: "Link",
              linkType: "Space",
              id: "haw0z39aqcwg"
            }
          },
          id: faker.string.uuid(),
          type: "Entry",
          createdAt: (entry.createdAt
            ? entry.createdAt.toISOString()
            : new Date().toISOString) as EntryFields.Date,
          updatedAt: "2023-10-10T12:11:12.240Z",
          environment: {
            sys: {
              id: "sandbox",
              type: "Link",
              linkType: "Environment"
            }
          },
          revision: 5,
          contentType: {
            sys: {
              type: "Link",
              linkType: "ContentType",
              id: "newsWealthyhub"
            }
          },
          locale: "en-US"
        },
        fields: {
          title: entry.title,
          tags: ["Tech", "Global", "Facebook", "Meta"],
          content: {
            nodeType: "document",
            data: {},
            content: [
              {
                nodeType: "paragraph",
                data: {},
                content: [
                  {
                    nodeType: "text",
                    value: entry.content ?? "This is the actual content of the page 😭",
                    marks: [],
                    data: {}
                  }
                ]
              }
            ]
          },
          fullImage: {
            metadata: {
              tags: []
            },
            sys: {
              space: {
                sys: {
                  type: "Link",
                  linkType: "Space",
                  id: "haw0z39aqcwg"
                }
              },
              id: "3rypnZJKsnZotyz5VJ2nz7",
              type: "Asset",
              createdAt: "2023-10-05T09:53:52.996Z",
              updatedAt: "2023-10-05T09:53:52.996Z",
              environment: {
                sys: {
                  id: "sandbox",
                  type: "Link",
                  linkType: "Environment"
                }
              },
              revision: 1,
              locale: "en-US"
            },
            fields: {
              title: "Featured image @4x",
              description: "",
              file: {
                url: "//images.ctfassets.net/haw0z39aqcwg/3rypnZJKsnZotyz5VJ2nz7/3ee42fe7a3ff96d7249354de013dbd99/Featured_image__4x.png",
                details: {
                  size: 730119,
                  image: {
                    width: 1080,
                    height: 1920
                  }
                },
                fileName: "Featured image @4x.png",
                contentType: "image/png"
              }
            }
          },
          readingTime: "1 min",
          previewTitleMain: "Meta to reorganise teams and reduce headcount",
          previewTitleSecondary: "for the first time ever.",
          previewLabel: "Bloomberg",
          previewLabelImage: {
            metadata: {
              tags: []
            },
            sys: {
              space: {
                sys: {
                  type: "Link",
                  linkType: "Space",
                  id: "haw0z39aqcwg"
                }
              },
              id: "1EZIyNHO7K9YQpdAcP1K83",
              type: "Asset",
              createdAt: "2023-10-06T13:24:04.691Z",
              updatedAt: "2023-10-06T13:24:04.691Z",
              environment: {
                sys: {
                  id: "sandbox",
                  type: "Link",
                  linkType: "Environment"
                }
              },
              revision: 1,
              locale: "en-US"
            },
            fields: {
              title: "bloomberg icon",
              description: "",
              file: {
                url: "//images.ctfassets.net/haw0z39aqcwg/1EZIyNHO7K9YQpdAcP1K83/3cd21fa0f3f96b7735899cccdd2a56da/bloomberg_icon.png",
                details: {
                  size: 660,
                  image: {
                    width: 45,
                    height: 45
                  }
                },
                fileName: "bloomberg_icon.png",
                contentType: "image/png"
              }
            }
          },
          previewImage: {
            metadata: {
              tags: []
            },
            sys: {
              space: {
                sys: {
                  type: "Link",
                  linkType: "Space",
                  id: "haw0z39aqcwg"
                }
              },
              id: "656wXvOtrJKHwaDCpiJc25",
              type: "Asset",
              createdAt: "2023-10-05T09:54:07.326Z",
              updatedAt: "2023-10-05T09:54:07.326Z",
              environment: {
                sys: {
                  id: "sandbox",
                  type: "Link",
                  linkType: "Environment"
                }
              },
              revision: 1,
              locale: "en-US"
            },
            fields: {
              title: "Thumbnail image @4x",
              description: "",
              file: {
                url: "//images.ctfassets.net/haw0z39aqcwg/656wXvOtrJKHwaDCpiJc25/0263688ce59d8ee2ed8ea67587520dc4/Thumbnail_image__4x.png",
                details: {
                  size: 136931,
                  image: {
                    width: 288,
                    height: 512
                  }
                },
                fileName: "Thumbnail image @4x.png",
                contentType: "image/png"
              }
            }
          },
          storyImage: {
            metadata: {
              tags: []
            },
            sys: {
              space: {
                sys: {
                  type: "Link",
                  linkType: "Space",
                  id: "haw0z39aqcwg"
                }
              },
              id: "656wXvOtrJKHwaDCpiJc25",
              type: "Asset",
              createdAt: "2023-10-05T09:54:07.326Z",
              updatedAt: "2023-10-05T09:54:07.326Z",
              environment: {
                sys: {
                  id: "sandbox",
                  type: "Link",
                  linkType: "Environment"
                }
              },
              revision: 1,
              locale: "en-US"
            },
            fields: {
              title: "Thumbnail image @4x",
              description: "",
              file: {
                url: "//images.ctfassets.net/haw0z39aqcwg/656wXvOtrJKHwaDCpiJc25/0263688ce59d8ee2ed8ea67587520dc4/Story_image__4x.png",
                details: {
                  size: 136931,
                  image: {
                    width: 288,
                    height: 512
                  }
                },
                fileName: "Thumbnail image @4x.png",
                contentType: "image/png"
              }
            }
          }
        }
      };
    })
  };
}

function buildContentfulLearningGuidesResponse(): EntryCollection<EntrySkeletonType> {
  return {
    total: 1,
    skip: 0,
    limit: 1,
    items: [
      {
        metadata: {
          tags: [],
          concepts: []
        },
        sys: {
          space: {
            sys: {
              type: "Link",
              linkType: "Space",
              id: "rhepdc9g3bcy"
            }
          },
          id: "7aBYU7lUMaEMlfQRYUW0Kc",
          type: "Entry",
          createdAt: "2024-11-19T18:12:36.321Z",
          updatedAt: "2024-11-20T16:05:47.850Z",
          environment: {
            sys: {
              id: "master",
              type: "Link",
              linkType: "Environment"
            }
          },
          revision: 6,
          contentType: {
            sys: {
              type: "Link",
              linkType: "ContentType",
              id: "learningGuide"
            }
          },
          locale: "en-US"
        },
        fields: {
          title: "Investing 101",
          description:
            "Losing sleep wondering how the stock market works? We’ll walk you through why people are so keen on investing, the kind of investor you might want to be, and of course, how to make your very first investment.",
          backgroundColor: "#F5F2FC",
          guideIcon: {
            sys: {
              type: "Link",
              linkType: "Asset",
              id: "1dD41YXNTn7cu60c06vYUJ"
            },
            fields: {
              file: {
                url: faker.internet.url().replace("https:", "")
              }
            }
          },
          mobileCoverImage: {
            sys: {
              type: "Link",
              linkType: "Asset",
              id: "62HFNiYhY2ggqvabYWj6bm"
            },
            fields: {
              file: {
                url: faker.internet.url().replace("https:", "")
              }
            }
          },
          webCoverImage: {
            sys: {
              type: "Link",
              linkType: "Asset",
              id: "62HFNiYhY2ggqvabYWj6bm"
            },
            fields: {
              file: {
                url: faker.internet.url().replace("https:", "")
              }
            }
          },
          chapters: [
            {
              sys: {
                type: "Link",
                linkType: "Entry",
                id: "YentS7igm0Ya5FhWXE6Su"
              }
            }
          ],
          slug: "investing-101",
          order: 1
        }
      }
    ],
    includes: {
      Entry: [
        {
          metadata: {
            tags: [],
            concepts: []
          },
          sys: {
            space: {
              sys: {
                type: "Link",
                linkType: "Space",
                id: "rhepdc9g3bcy"
              }
            },
            id: "YentS7igm0Ya5FhWXE6Su",
            type: "Entry",
            createdAt: "2024-11-19T18:35:30.653Z",
            updatedAt: "2024-11-20T10:55:44.896Z",
            environment: {
              sys: {
                id: "master",
                type: "Link",
                linkType: "Environment"
              }
            },
            publishedVersion: 7,
            revision: 3,
            contentType: {
              sys: {
                type: "Link",
                linkType: "ContentType",
                id: "learningGuideChapter"
              }
            },
            locale: "en-US"
          },
          fields: {
            title: "Why even invest?",
            body: {
              data: {},
              content: [
                {
                  data: {},
                  content: [
                    {
                      data: {},
                      marks: [],
                      value: "Hey there! 👋🏼",
                      nodeType: "text"
                    }
                  ],
                  nodeType: "paragraph"
                },
                {
                  data: {},
                  content: [
                    {
                      data: {},
                      marks: [],
                      value:
                        "Every investor has to start somewhere, and here’s your somewhere. We’ll walk you through why people are so keen on investing, what kind of investor you might want to be, and, of course, how to actually make your very first investment.",
                      nodeType: "text"
                    }
                  ],
                  nodeType: "paragraph"
                }
              ],
              nodeType: "document"
            },
            slug: "why-even-invest"
          }
        }
      ],
      Asset: [
        {
          metadata: {
            tags: [],
            concepts: []
          },
          sys: {
            space: {
              sys: {
                type: "Link",
                linkType: "Space",
                id: "rhepdc9g3bcy"
              }
            },
            id: "1dD41YXNTn7cu60c06vYUJ",
            type: "Asset",
            createdAt: "2024-11-19T18:10:30.942Z",
            updatedAt: "2024-11-20T16:07:06.694Z",
            environment: {
              sys: {
                id: "master",
                type: "Link",
                linkType: "Environment"
              }
            },
            publishedVersion: 8,
            revision: 2,
            locale: "en-US"
          },
          fields: {
            title: "investing-101",
            description: "",
            file: {
              url: "//images.ctfassets.net/rhepdc9g3bcy/1dD41YXNTn7cu60c06vYUJ/ef7d35b39774052eb8163f5f8cd47f56/investing_101.png",
              details: {
                size: 325082,
                image: {
                  width: 1640,
                  height: 1640
                }
              },
              fileName: "investing 101.png",
              contentType: "image/png"
            }
          }
        },
        {
          metadata: {
            tags: [],
            concepts: []
          },
          sys: {
            space: {
              sys: {
                type: "Link",
                linkType: "Space",
                id: "rhepdc9g3bcy"
              }
            },
            id: "3rdi2p0ePk89yx0y8KxpXW",
            type: "Asset",
            createdAt: "2024-11-20T13:09:27.417Z",
            updatedAt: "2024-11-20T13:09:27.417Z",
            environment: {
              sys: {
                id: "master",
                type: "Link",
                linkType: "Environment"
              }
            },
            publishedVersion: 4,
            revision: 1,
            locale: "en-US"
          },
          fields: {
            title: "Compound Growth",
            description: "",
            file: {
              url: "//images.ctfassets.net/rhepdc9g3bcy/3rdi2p0ePk89yx0y8KxpXW/1b741c5e7de8bdf0b2bf9a225666bcce/compound-growth-vs-simple-growth.webp",
              details: {
                size: 7924,
                image: {
                  width: 590,
                  height: 328
                }
              },
              fileName: "compound-growth-vs-simple-growth.webp",
              contentType: "image/webp"
            }
          }
        },
        {
          metadata: {
            tags: [],
            concepts: []
          },
          sys: {
            space: {
              sys: {
                type: "Link",
                linkType: "Space",
                id: "rhepdc9g3bcy"
              }
            },
            id: "62HFNiYhY2ggqvabYWj6bm",
            type: "Asset",
            createdAt: "2024-11-19T18:11:16.358Z",
            updatedAt: "2024-11-20T16:05:40.378Z",
            environment: {
              sys: {
                id: "master",
                type: "Link",
                linkType: "Environment"
              }
            },
            publishedVersion: 8,
            revision: 2,
            locale: "en-US"
          },
          fields: {
            title: "investing-101-cover",
            description: "",
            file: {
              url: "//images.ctfassets.net/rhepdc9g3bcy/62HFNiYhY2ggqvabYWj6bm/a95ecc8238a0f2ccc8f1c9bab94476d8/investing_104.jpg",
              details: {
                size: 150318,
                image: {
                  width: 1918,
                  height: 918
                }
              },
              fileName: "investing 104.jpg",
              contentType: "image/jpeg"
            }
          }
        }
      ]
    }
  };
}

function buildContentfulLearningGuideChapterResponse(): EntryCollection<EntrySkeletonType> {
  return {
    total: 1,
    skip: 0,
    limit: 1,
    items: [
      {
        metadata: {
          tags: [],
          concepts: []
        },
        sys: {
          space: {
            sys: {
              type: "Link",
              linkType: "Space",
              id: "rhepdc9g3bcy"
            }
          },
          id: "7aBYU7lUMaEMlfQRYUW0Kc",
          type: "Entry",
          createdAt: "2024-11-19T18:12:36.321Z",
          updatedAt: "2024-11-20T16:05:47.850Z",
          environment: {
            sys: {
              id: "master",
              type: "Link",
              linkType: "Environment"
            }
          },
          revision: 6,
          contentType: {
            sys: {
              type: "Link",
              linkType: "ContentType",
              id: "learningGuide"
            }
          },
          locale: "en-US"
        },
        fields: {
          title: "Why even invest?",
          body: {
            data: {},
            content: [
              {
                data: {},
                content: [
                  {
                    data: {},
                    marks: [],
                    value: "Hey there! 👋🏼",
                    nodeType: "text"
                  }
                ],
                nodeType: "paragraph"
              },
              {
                data: {},
                content: [
                  {
                    data: {},
                    marks: [],
                    value:
                      "Every investor has to start somewhere, and here’s your somewhere. We’ll walk you through why people are so keen on investing, what kind of investor you might want to be, and, of course, how to actually make your very first investment.",
                    nodeType: "text"
                  }
                ],
                nodeType: "paragraph"
              }
            ],
            nodeType: "document"
          },
          slug: "why-even-invest"
        }
      }
    ]
  };
}

// The arguments are just three fields that can configure the contentful response and are not related
// to the actual contentful response type.
function buildContentfulGlossaryResponse(
  entries: Partial<{ title: string; createdAt: Date; definition: string }>[]
): EntryCollection<EntrySkeletonType> {
  return {
    total: entries.length,
    skip: 0,
    limit: 100,
    items: entries.map((entry) => {
      return {
        metadata: { tags: [] },
        sys: {
          space: { sys: { type: "Link", linkType: "Space", id: "haw0z39aqcwg" } },
          id: faker.string.uuid(),
          type: "Entry",
          createdAt: (entry.createdAt
            ? entry.createdAt.toISOString()
            : new Date().toISOString()) as EntryFields.Date,
          updatedAt: "2023-10-05T10:02:18.086Z",
          environment: { sys: { id: "sandbox", type: "Link", linkType: "Environment" } },
          revision: 2,
          contentType: { sys: { type: "Link", linkType: "ContentType", id: "glossary" } },
          locale: "en-US"
        },
        fields: {
          title: entry.title,
          definition: {
            nodeType: "document",
            data: {},
            content: [
              {
                nodeType: "paragraph",
                data: {},
                content: [{ nodeType: "text", value: entry.definition || "", marks: [], data: {} }]
              }
            ]
          }
        }
      };
    })
  };
}

function buildContentfulFaqCategoryResponse(
  entries: Partial<FaqCategoryType>[]
): EntryCollection<EntrySkeletonType> {
  return {
    total: entries.length,
    skip: 0,
    limit: 100,
    items: entries.map((entry) => {
      return {
        metadata: { tags: [] },
        sys: {
          space: { sys: { type: "Link", linkType: "Space", id: "haw0z39aqcwg" } },
          id: faker.string.uuid(),
          type: "Entry",
          createdAt: new Date().toISOString() as EntryFields.Date,
          updatedAt: "2023-10-05T10:02:18.086Z",
          environment: { sys: { id: "sandbox", type: "Link", linkType: "Environment" } },
          revision: 2,
          contentType: { sys: { type: "Link", linkType: "ContentType", id: "glossary" } },
          locale: "en-US"
        },
        fields: {
          wealthyhoodId: entry.key ?? "ABOUT_WEALTHYHOOD",
          isHelpCentreCategory: entry.isHelpCentreCategory ?? true,
          title: entry.title ?? "About Wealthyhood",
          subtitle: entry.subtitle ?? "Read more about us and how we are regulated.",
          locale: {
            metadata: { tags: [], concepts: [] },
            sys: {
              space: {
                sys: {
                  type: "Link",
                  linkType: "Space",
                  id: "haw0z39aqcwg"
                }
              },
              id: "iskRhu5vFDQcZp1LzheUk",
              type: "Entry",
              createdAt: "2024-09-27T10:10:55.834Z",
              updatedAt: "2024-09-27T10:10:55.834Z",
              environment: {
                sys: {
                  id: "sandbox",
                  type: "Link",
                  linkType: "Environment"
                }
              },
              revision: 1,
              contentType: {
                sys: {
                  type: "Link",
                  linkType: "ContentType",
                  id: "locale"
                }
              },
              locale: "en-US"
            },
            fields: { code: entry.locale ?? "uk" }
          },
          order: entry.order ?? 1,
          faqs: entry?.faqs?.map((faq) => _buildContefulFaqItemResponse(faq.question, faq.answer)) ?? [
            _buildContefulFaqItemResponse()
          ]
        }
      };
    })
  };
}
function _buildContefulFaqItemResponse(question?: string, answer?: string): any {
  return {
    metadata: { tags: [], concepts: [] },
    sys: {
      space: {
        sys: {
          type: "Link",
          linkType: "Space",
          id: "haw0z39aqcwg"
        }
      },
      id: "1Fw18sIO8sslik3DuzGqdC",
      type: "Entry",
      createdAt: "2024-09-27T10:09:35.133Z",
      updatedAt: "2024-09-27T10:09:35.133Z",
      environment: {
        sys: {
          id: "sandbox",
          type: "Link",
          linkType: "Environment"
        }
      },
      revision: 1,
      contentType: {
        sys: {
          type: "Link",
          linkType: "ContentType",
          id: "faqSchema"
        }
      },
      locale: "en-US"
    },
    fields: {
      question: question ?? "What is Wealthyhood?",
      answer:
        answer ??
        "Wealthyhood is a wealth-building app helping younger investors learn, save, invest and build wealth privately. We are committed to making investing easier, more accessible and transparent for our generation by delivering the best investing app you could ask for!\n\nThe Wealthyhood experience revolves around:\n\n- __A. Learning:__ Our Learning Hub includes daily news, analyst insights, learning guides, a glossary, a daily newsletter with bit-sized summaries, and real-time past and future simulation to guide you on every step.\n- __B. Saving:__ Our Savings Vaults are high-yield accounts to help you earn market beating interest on your cash.\n- __C. Investing:__ We offer a 360 investing experience for beginners, busy professionals, and more experienced investors with:\n  - • DIY investing in global stocks & ETFs from €1 with zero commissions and fractional shares.\n  - • Our portfolio builder to help you get your fully personalised, well-diversified portfolio in minutes, depending on your preferences and risk appetite.\n  - • Our “Robo-advisor” mode with pre-made portfolios ready-to-invest.\n- __D. Automation:__ Set it and forget it. Our Autopilot helps you set up repeating monthly investments, savings and automated rebalancing to automate your wealth-building journey.\n\nWe are on a mission to help you get started investing, become a better investor day by day and build wealth under your own terms!"
    }
  };
}

function buildContentfulContentEntriesResponse(
  entries: Partial<AnalystInsightType & { bannerImagePath: string }>[]
): EntryCollection<EntrySkeletonType> {
  return {
    total: entries.length,
    skip: 0,
    limit: 100,
    items: entries.map((entry) => {
      return buildContentfulContentEntryResponse(entry);
    })
  };
}

function buildContentfulContentEntryResponse(
  entry: Partial<AnalystInsightType & { bannerImagePath?: string }>
): Entry<EntrySkeletonType> {
  return {
    metadata: { tags: [], concepts: [] },
    sys: {
      space: {
        sys: { type: "Link", linkType: "Space", id: "ba559p6m2vly" }
      },
      id: entry.id ?? "21b0ioCfWVAPAMlXnnuzoO",
      type: "Entry",
      createdAt: "2024-10-11T13:38:25.830Z",
      updatedAt: "2024-10-11T13:38:25.830Z",
      environment: {
        sys: { id: "sandbox", type: "Link", linkType: "Environment" }
      },
      revision: 1,
      contentType: {
        sys: {
          type: "Link",
          linkType: "ContentType",
          id: "contentEntry"
        }
      },
      locale: "en-US"
    },
    fields: {
      title: entry.title ?? "“Lost Decades” Happen (More Often Than You Think)",
      subtitle: "So it’s worth considering how well your portfolio can deal with one.",
      summary: "So it’s worth considering how well your portfolio can deal with one.",
      slug: "lost-decades-happen-more-often-than-you-think",
      content:
        'Test .\n\n\n!["Lost decades" happen more often than you think - and often start with high valuations](https://finimize-img.imgix.net/https%3A%2F%2Fchivas-assets.s3-eu-west-1.amazonaws.com%2Fstatic%2Fimages%2Flost_decades.original.png?ixlib=python-3.1.2&s=f575134d91c9e6425ae4fd91d797302a)\n\nBottom line: don’t ignore the risk of another lost decade. If you’re looking for a portfolio with attractive returns and a lower chance of hitting a really rough patch, check out our Easy Rider Portfolio.\n\n',
      publishedAt: entry.createdAt?.toISOString() ?? "2024-10-11T13:38:25.830Z",
      notificationTitle: "⌛ Lost Decades Happen",
      notificationBody:
        "They’re more common than you think. So it’s worth considering how well your portfolio can handle one.",
      readingTime: "2 minutes",
      contentType: entry.analystInsightType ?? "QUICK_TAKE",
      category: entry.contentType ?? "ANALYST_INSIGHTS",
      headerImage:
        "https://finimize-img.imgix.net/https%3A%2F%2Fchivas-assets.s3-eu-west-1.amazonaws.com%2Fstatic%2Fimages%2FQuicktake_111024.original.jpg?ixlib=python-3.1.2&s=65b310682bf7faca5c15b712c0ad481d",
      bannerImage:
        entry.bannerImageURL ??
        "https://finimize-img.imgix.net/https%3A%2F%2Fchivas-assets.s3-eu-west-1.amazonaws.com%2Fstatic%2Fimages%2FQuicktake_111024.original.jpg?ixlib=python-3.1.2&s=65b310682bf7faca5c15b712c0ad481d"
    }
  };
}

function buildContentfulContentEntryCreationResponse() {
  return {
    id: faker.string.uuid(),
    spaceId: faker.string.uuid(),
    environmentId: faker.string.uuid()
  };
}

export {
  buildContentfulAnalystInsightsResponse,
  buildContentfulNewsResponse,
  buildContentfulGlossaryResponse,
  buildContentfulFaqCategoryResponse,
  buildContentfulContentEntryCreationResponse,
  buildContentfulContentEntriesResponse,
  buildContentfulContentEntryResponse,
  buildContentfulLearningGuidesResponse,
  buildContentfulLearningGuideChapterResponse
};
