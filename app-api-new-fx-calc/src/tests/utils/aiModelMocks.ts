import { MockLanguageModelV1 } from "ai/test";

// Mock for MarketSummaryFormatter
export const marketSummaryModelMock = new MockLanguageModelV1({
  defaultObjectGenerationMode: "json",
  doGenerate: async () => ({
    rawCall: { rawPrompt: null, rawSettings: {} },
    finishReason: "stop",
    usage: { promptTokens: 10, completionTokens: 20 },
    text: JSON.stringify({
      overview: "GM sales up, Intel new CEO, Palantir faces pressure.",
      sections: [
        {
          companyName: "General Motors",
          companyTicker: "GM",
          title: "Auto Industry Q1 Performance",
          content:
            "GM leads auto sector with 17% increase in U.S. Q1 sales, driven by strong pickup and SUV demand."
        },
        {
          companyName: "Intel Corporation",
          companyTicker: "INTC",
          title: "Intel Welcomes New CEO",
          content: "Intel appoints <PERSON><PERSON><PERSON><PERSON><PERSON> as new CEO following <PERSON>'s departure."
        },
        {
          companyName: "Palantir Technologies",
          companyTicker: "PLTR",
          title: "Palantir Stock Faces Pressure",
          content: "Palantir experiences share price decline."
        }
      ]
    })
  })
});

// Mock for TickerFinder
export const tickerFinderModelMock = new MockLanguageModelV1({
  defaultObjectGenerationMode: "json",
  doGenerate: async (options) => {
    // Extract company name from prompt
    const content = options.prompt?.[0]?.content || "";
    const companyName = typeof content === "string" ? content : "";

    // Return appropriate ticker based on common companies
    let ticker = "";
    if (/general motors/i.test(companyName)) {
      ticker = "GM";
    } else if (/intel/i.test(companyName)) {
      ticker = "INTC";
    } else if (/palantir/i.test(companyName)) {
      ticker = "PLTR";
    } else if (/microsoft/i.test(companyName)) {
      ticker = "MSFT";
    } else if (/apple/i.test(companyName)) {
      ticker = "AAPL";
    }

    return {
      rawCall: { rawPrompt: null, rawSettings: {} },
      finishReason: "stop",
      usage: { promptTokens: 10, completionTokens: 20 },
      text: JSON.stringify(ticker)
    };
  }
});

// Mock for AssetIdResolver
export const assetIdResolverModelMock = new MockLanguageModelV1({
  defaultObjectGenerationMode: "json",
  doGenerate: async (options) => {
    const content = options.prompt?.[0]?.content;
    const input = typeof content === "string" ? JSON.parse(content) : {};
    const assetId =
      {
        PLTR: "equities_palantir_technologies",
        INTC: "equities_intel",
        GM: "equities_general_motors"
      }[input.companyTicker] || "equities_general_motors";

    return {
      rawCall: { rawPrompt: null, rawSettings: {} },
      finishReason: "stop",
      usage: { promptTokens: 10, completionTokens: 20 },
      text: JSON.stringify({ assetId })
    };
  }
});

// Mock for SectionTagger
export const sectionTaggerModelMock = new MockLanguageModelV1({
  defaultObjectGenerationMode: "json",
  doGenerate: async () => ({
    rawCall: { rawPrompt: null, rawSettings: {} },
    finishReason: "stop",
    usage: { promptTokens: 10, completionTokens: 20 },
    text: JSON.stringify({
      tag: "Market Trend"
    })
  })
});

// Mock for LearnNewsCleaner
export const learnNewsCleanerModelMock = new MockLanguageModelV1({
  defaultObjectGenerationMode: "json",
  doGenerate: async () => ({
    rawCall: { rawPrompt: null, rawSettings: {} },
    finishReason: "stop",
    usage: { promptTokens: 10, completionTokens: 20 },
    text: JSON.stringify({
      htmlContent: "<p>Main content</p>"
    })
  })
});
