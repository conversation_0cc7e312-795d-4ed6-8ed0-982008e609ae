import { GoogleAdsMetadataType, PlatformType, TrackingSourceType } from "../models/Participant";
import { UserDocument } from "../models/User";
import { AmlScreeningResultEnum } from "../configs/riskAssessmentConfig";

export type BankOriginType = "modal" | "my_account" | "truelayer_data" | "bank_transfer";

export type UserData = {
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  nationalities?: string[];
  taxResidency: {
    countryCode: string;
    proofType: string;
    value: string;
  };
  isUKTaxResident?: string;
  viewedWelcomePage?: string;
  viewedKYCSuccessPage?: string;
  hasAcceptedTerms?: string;
  hasSeenBilling?: string;
  referredByEmail?: string;
  amlScreening?: AmlScreeningResultEnum;
  skippedPortfolioCreation?: boolean;
};

export type CreateParticipantData = {
  appInstallInfo?: {
    createdAt: Date;
    platform: PlatformType;
  };
  anonymousId?: string;
  appsflyerId?: string;
  wlthd?: string;
  referrerEmail?: string;
  googleAdsMetadata?: GoogleAdsMetadataType;
  gaClientId?: string;
  pageUserLanded?: string;
  grsf?: string;
  financeAdsSessionId?: string;
  trackingSource?: TrackingSourceType;
  submissionTechClickId?: string;
};

export type CreateUserData = {
  email: string;
  emailVerified: string;
  role: string[];
  lastLogin: string;
  auth0: {
    id: string;
  };
};

export type CreateUserPartial = Pick<
  UserDocument,
  "email" | "emailVerified" | "auth0" | "role" | "lastLogin" | "lastLoginPlatform"
>;
