import { ExecutionWindowsType } from "../configs/executionWindowConfig";
import { FeesType } from "../models/Transaction";
import { ForeignCurrencyRatesType } from "currencies";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

type OrderPreviewType = {
  side: "buy" | "sell";
  quantity: number;
  money: number;
};

export type OrdersPreviewType = {
  [key in investmentUniverseConfig.AssetType]?: OrderPreviewType;
};

export interface DualExecutionValue<T> {
  express?: T;
  smart?: T;
}

export interface TransactionPreview {
  executionWindow?: DualExecutionValue<ExecutionWindowsType>;
  fees?: DualExecutionValue<FeesType>;
  orders: DualExecutionValue<OrdersPreviewType>;
  foreignCurrencyRates?: ForeignCurrencyRatesType;
  willSkipOrders?: boolean;
  cashback?: number;
  willResultInLowQuantityHolding?: boolean;
  hasETFOrders?: boolean;
}
