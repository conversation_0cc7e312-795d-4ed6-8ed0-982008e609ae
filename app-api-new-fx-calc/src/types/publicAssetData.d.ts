import {
  ETFAssetGeographyDistributionType,
  ETFAssetSectorDistributionType,
  StockAnalystViewsType
} from "../services/investmentProductService";
import { publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";

export type EtfDataType = {
  key: publicInvestmentUniverseConfig.PublicAssetType;
  about: AboutEtfType;
  basics: BasicsEtfType;
  advanced: {
    valuation: ValuationEtfType;
    growth: GrowthEtfType;
    price: PriceEtfType;
    technicals: TechnicalsEtfType;
  };
  regionalBreakdown: ETFAssetGeographyDistributionType;
  sectorBreakdown: ETFAssetSectorDistributionType;
  topHoldings: EtfHoldingType[];
};

type TechnicalsEtfType = {
  ytd: string;
  year1: string;
  year3: string;
  year5: string;
  year10: string;
  volatilityYear1: string;
  volatilityYear3: string;
  expectedReturnYear3: string;
  sharpRatio: string;
};

type GrowthEtfType = {
  longTermProtectedEarnings: string;
  historicalEarnings: string;
  sales: string;
  cashflow: string;
  bookValue: string;
};

type PriceEtfType = {
  week52High: string;
  week52Low: string;
  day50Ma: string;
  day200Ma: string;
};

export type ValuationEtfType = {
  priceToProspectiveEarnings: string;
  priceToBook: string;
  priceToSales: string;
  priceToCashflow: string;
};

export type AboutEtfType = {
  description: string;
  ticker: string;
  tradingOn: string;
  isin: string;
  tradedCurrency: string;
  baseCurrency: string;
  fundManager: string;
  fundManagerWebsite: string;
  website: string;
  name: string;
};

type BasicsEtfType = {
  fundSize: string;
  expenseRatio: string;
  income: string;
  dividendYieldFactor: string;
};

// type EsgType = {
//   totalEsg: number;
//   totalEsgPercentile: number;
//   enviromentalScore: number;
//   environmentScorePercentile: number;
//   socialScore: number;
//   socialScorePercentile: number;
//   governanceScore: number;
//   governanceScorePercentile: number;
// };

type DividendsStockType = {
  dividendPerShare: string;
  dividendYield: string;
  forwardDividendPerShare: string;
  forwardDividendYield: string;
  payoutRatio: string;
  exDividendDate: string;
  dividendDate: string;
};

export type SentimentType = {
  sentimentScore: number;
  chart: { score: number; date: number }[];
};

type EarningChartType = {
  date: string;
  epsActual?: number;
  epsEstimate: number;
  surprisePercent: number | null;
  displayDate: string;
};

type EarningTableType = {
  date: string;
  epsActual?: string;
  epsEstimate: string;
  surprisePercent?: string;
  displayDate: string;
};

export type EarningsType = {
  annual: {
    chart: EarningChartType[];
    table: EarningTableType[];
  };
  quarterly: {
    chart: EarningChartType[];
    table: EarningTableType[];
  };
};

type ValuationStockType = {
  peRatio: string;
  forwardPe: string;
  pegRatio: string;
  trailingPe: string;
  priceToSales: string;
  priceToBook: string;
};

type PriceVolumeStockType = {
  marketCap: string;
  averageDailyVolume: string;
};

type TechnicalsStockType = {
  beta: string;
  week52High: string;
  week52Low: string;
  day50Ma: string;
  day200Ma: string;
  shortRatio: string;
  shortPercent: string;
};

type ManagementEffectivenessStpclType = {
  roeTtm: string;
  roaTtm: string;
  profitMargin: string;
  grossProfit: string;
  operatingMargin: string;
};

type GrowthStockType = {
  quarterlyEarningsGrowthYoy: string;
  quarterlyRevenueGrowthYoy: string;
};

type SharesStatsStoclType = {
  outstandingShares: string;
  float: string;
  insiders: string;
  institutions: string;
};

type BasicsStockType = {
  marketCap: string;
  peRatio: string;
  eps: string;
  dividendYield: string;
  beta: string;
  forwardPeRatio: string;
  ebitda: string;
  exDividendDate: string;
};

type AboutStockType = {
  name: string;
  currency?: string;
  logoUrl: string;
  description: string;
  ticker: string;
  tradingOn: string;
  doesPayDividend: boolean;
  isin: string;
  industry: string;
  sector: string;
  ceo: string;
  headquarters: string;
  employees: number;
  website: string;
};

type EarningsMetricsStockType = {
  eps: string;
  epsEstimateCurrent: string;
  epsEstimateNext: string;
  ebitda: string;
  revenueTtm: string;
  revenuePerShareTtm: string;
};

export type EodGeneratedStockDataType = {
  about: AboutStockType;
  analystViews?: StockAnalystViewsType;
  basics: BasicsStockType;
  advanced: {
    priceVolume: PriceVolumeStockType;
    dividends: DividendsStockType;
    valuation: ValuationStockType;
    earnings: EarningsMetricsStockType;
    technicals: TechnicalsStockType;
    managementEffectiveness: ManagementEffectivenessStpclType;
    growth: GrowthStockType;
    sharesStats: SharesStatsStoclType;
  };
  earnings: EarningsType;
  financials: {
    incomeStatements: IncomeStatementsType;
    cashFlows: CashFlowsType;
    balanceSheets: BalanceSheetsType;
  };
};

export type StockDataType = EodGeneratedStockDataType & {
  key: publicInvestmentUniverseConfig.PublicAssetType;
};

export type IncomeStatementEodResponse = {
  netIncome: string;
  totalRevenue: string;
  ebitda: string;
  grossProfit: string;
  date: string;
  filing_date: string;
};

export type ProcessedIncomeStatement = {
  totalRevenue: number;
  netIncome: number;
  profitMargin: number | null; // caclulate using netIncome / totalRevenue
  ebitda: number;
  grossProfit: number;
  displayDate: string;
  fillingDate: string;
  totalRevenueGrowth: number | null;
  netIncomeGrowth: number | null;
  profitMarginGrowth: number | null;
  ebitdaGrowth: number | null;
  grossProfitGrowth: number | null;
};

type IncomeStatementChartType = {
  totalRevenue: number;
  netIncome: number;
  profitMargin: number | null; // caclulate using netIncome / totalRevenue
  ebitda: number;
  grossProfit: number;
  displayDate: string;
  fillingDate: string;
};

type IncomeStatementTableType = {
  totalRevenue: string;
  netIncome: string;
  ebitda: string;
  grossProfit: string;
  profitMargin: string; // caclulate using netIncome / totalRevenue
  displayDate: string;
  fillingDate: string;
};

export type IncomeStatementsType = {
  annual: {
    chart: IncomeStatementChartType[];
    table: IncomeStatementTableType[];
  };
  quarterly: {
    chart: IncomeStatementChartType[];
    table: IncomeStatementTableType[];
  };
};

export type BalanceSheetEodResponse = {
  totalAssets: string;
  totalLiab: string;
  date: string;
  filing_date: string;
};

export type ProcessedBalanceSheet = {
  totalAssets: number;
  totalLiabilities: number;
  debtToAssets: number | null;
  totalAssetsGrowth: number | null;
  totalLiabilitiesGrowth: number | null;
  debtToAssetsGrowth: number | null;
  displayDate: string;
  fillingDate: string;
};

type BalanceSheetChartType = {
  totalAssets: number;
  totalLiabilities: number;
  debtToAssets: number | null; // caclulate using totalAssets / totalLiabilities
  displayDate: string;
  fillingDate: string;
};

type BalanceSheetTableType = {
  totalAssets: string;
  totalLiabilities: string;
  debtToAssets: string; // caclulate using totalAssets / totalLiabilities
  displayDate: string;
  fillingDate: string;
};

export type BalanceSheetsType = {
  annual: {
    chart: BalanceSheetChartType[];
    table: BalanceSheetTableType[];
  };
  quarterly: {
    chart: BalanceSheetChartType[];
    table: BalanceSheetTableType[];
  };
};

export type CashFlowEodResponse = {
  totalCashFromOperatingActivities: string;
  investments: string;
  totalCashFromFinancingActivities: string;
  freeCashFlow: string;
  date: string;
  filing_date: string;
};

export type ProcessedCashFlow = {
  operatingActivities: number | null;
  investments: number | null;
  financingActivities: number;
  freeCashFlow: number;
  operatingActivitiesGrowth: number | null;
  investmentsGrowth: number | null;
  financingActivitiesGrowth: number | null;
  freeCashFlowGrowth: number | null;
  displayDate: string;
  fillingDate: string;
};

type CashFlowChartType = {
  operatingActivities: number | null;
  investments: number | null;
  financingActivities: number;
  freeCashFlow: number;
  displayDate: string;
  fillingDate: string;
};

type CashFlowTableType = {
  operatingActivities: string;
  investments: string;
  financingActivities: string;
  freeCashFlow: string;
  displayDate: string;
  fillingDate: string;
};

export type CashFlowsType = {
  annual: {
    chart: CashFlowChartType[];
    table: CashFlowTableType[];
  };
  quarterly: {
    chart: CashFlowChartType[];
    table: CashFlowTableType[];
  };
};

export type EtfHoldingType = { name: string; weight: string; logoUrl: string };
