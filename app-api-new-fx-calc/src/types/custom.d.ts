import { PortfolioDocument } from "../models/Portfolio";
import { PlatformType, UserDocument } from "../models/User";
import { Request, Response } from "express";
import { OrderDocument } from "../models/Order";
import { AppRatingDocument } from "../models/AppRating";
import { SubscriptionDocument } from "../models/Subscription";

declare namespace custom {
  interface Auth0AccessTokenPayload {
    roles: string[];
    iss: string;
    sub: string;
    aud: string[];
    iat: number; // timestamp in number
    exp: number; // timestamp in number
    azp: string; // hash
    scope: string; // space separated string (for multiple scopes)
  }
  interface Auth0IdentityTokenPayload {
    roles: string[];
    createdAt: Date;
    updated_at: Date;

    nickname: string;
    name: string;
    picture: string;
    email: string;
    email_verified: boolean;
    iss: string;
    sub: string;
    aud: string;
    iat: number; // timestamp in number
    exp: number; // timestamp in number
  }
  interface CustomRequest extends Request {
    file: any;
    accessTokenPayload?: Auth0AccessTokenPayload;
    identityTokenPayload?: Auth0IdentityTokenPayload;
    platform?: PlatformType;
    user?: UserDocument;
  }
  interface CustomResponse extends Response {
    locals: {
      portfolio?: PortfolioDocument;
      order?: OrderDocument;
      appRating?: AppRatingDocument;
      subscription?: SubscriptionDocument;
    };
  }
}

export = custom;
