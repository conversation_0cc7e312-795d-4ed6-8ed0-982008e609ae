import pako from "pako";
import { <PERSON><PERSON>ine, Redis, SetCommandOptions } from "@upstash/redis";
import { addBreadcrumb, captureException } from "@sentry/node";
import logger from "../external-services/loggerService";
import DateUtil from "../utils/dateUtil";
import ObjectUtil from "../utils/objectUtil";

const CACHE_UPLOAD_LIMIT_MB = 0.95;

export enum PIPELINE_WRAPPED_METHODS {
  MSET = "mset"
}

export class RedisClientService {
  private _redisClient: Redis;
  private static _instance: RedisClientService;

  constructor() {
    this._redisClient = new Redis({
      url: process.env.REDIS_URL,
      token: process.env.REDIS_TOKEN
    });
  }

  public static get Instance(): RedisClientService {
    return RedisClientService._instance || (RedisClientService._instance = new RedisClientService());
  }

  // ===============
  // PUBLIC METHODS
  // ===============

  /**
   * @description Atomically delete a key.
   */
  public async atomicDel(key: string): Promise<boolean> {
    logger.info("Atomically deleting cached data...", {
      module: "RedisClientService",
      method: "atomicDel",
      data: { key }
    });

    const script = `
      if redis.call('EXISTS', KEYS[1]) == 1 then
        redis.call('DEL', KEYS[1])
        return true
      else
        return false
      end
    `;

    try {
      return this._redisClient.eval(script, [key], []);
    } catch (err) {
      captureException(err);
      logger.error("Redis atomicDel failed", {
        module: "RedisClientService",
        method: "atomicDel",
        data: {
          error: err
        }
      });
    }
  }

  /**
   * @description Atomically set a key only if it doesn't exist.
   */
  public async atomicSetIfNotExists(
    key: string,
    value: any,
    setOptions: SetCommandOptions = {}
  ): Promise<boolean> {
    logger.info("Atomically setting cached data...", {
      module: "RedisClientService",
      method: "atomicSet",
      data: { key }
    });

    const script = `
      if redis.call('EXISTS', KEYS[1]) == 0 then
        redis.call('SET', KEYS[1], ARGV[1], 'EX', ARGV[2])
        return true
      else
        return false
      end
    `;

    try {
      const compressedData = RedisClientService._compress(value);

      if (RedisClientService._isRequestSizeAllowed(compressedData)) {
        const expirationInSeconds = setOptions.ex || DateUtil.convertDaysToSeconds(1); // defaults to 1d expiration
        return this._redisClient.eval(script, [key], [compressedData, expirationInSeconds]);
      } else {
        logger.error("Maximum request size exceeded", {
          module: "RedisClientService",
          method: "atomicSet",
          data: { key }
        });
      }
    } catch (err) {
      captureException(err);
      logger.error("Redis atomic set failed", {
        module: "RedisClientService",
        method: "atomicSet",
        data: {
          key,
          error: err
        }
      });
    }
  }

  public async del(key: string): Promise<void> {
    logger.info("Deleting cached data...", {
      module: "RedisClientService",
      method: "del",
      data: { key }
    });

    try {
      await this._redisClient.del(key);
      return;
    } catch (err) {
      captureException(err);
      logger.error("Redis del failed", {
        module: "RedisClientService",
        method: "del",
        data: {
          error: err
        }
      });
    }
  }

  /**
   * @description Returns the value of the specified key or null if the
   * key doesn’t exist.
   *
   * NOTE:
   * The value returned is not type string (as expected by value retrieval
   * on redis), but is parsed and is of type ValueType that is defined by
   * the caller of the method.
   */
  public async get<ValueType>(key: string): Promise<ValueType> {
    logger.info("Getting cached data...", {
      module: "RedisClientService",
      method: "get",
      data: { key }
    });

    try {
      const compressedCachedData: string = await this._redisClient.get(key);
      if (compressedCachedData) {
        return RedisClientService._decompress<ValueType>(compressedCachedData);
      } else return null;
    } catch (err) {
      addBreadcrumb({
        type: "default",
        category: "RedisClientService.get",
        level: "error",
        data: {
          key
        }
      });
      captureException(err);
      logger.error("Redis get failed", {
        module: "RedisClientService",
        method: "get",
        data: {
          key,
          error: err
        }
      });
    }
  }

  /**
   * @description Returns the value of the specified keys or null if the
   * key doesn’t exist.
   *
   * NOTE:
   * The value returned is not type string (as expected by value retrieval
   * on redis), but is parsed and is of type ValueType that is defined by
   * the caller of the method.
   */
  public async mGet<ValueType>(keys: string[]): Promise<ValueType[]> {
    logger.info("Getting multiple cached data...", {
      module: "RedisClientService",
      method: "mGet",
      data: { keys }
    });

    try {
      const compressedValues: string[] = await this._redisClient.mget(keys);
      return compressedValues.map((compressedValue) =>
        compressedValue ? RedisClientService._decompress<ValueType>(compressedValue) : null
      );
    } catch (err) {
      addBreadcrumb({
        type: "default",
        category: "RedisClientService.mGet",
        level: "error",
        data: {
          keys
        }
      });
      captureException(err);
      logger.error("Redis mGet failed", {
        module: "RedisClientService",
        method: "mGet",
        data: {
          keys,
          error: err
        }
      });
    }
  }

  public async mSet(keyValuePairs: Record<string, any>): Promise<void> {
    logger.info("Setting multiple key-value pairs...", {
      module: "RedisClientService",
      method: "mSet",
      data: { keys: Object.keys(keyValuePairs) }
    });

    try {
      if (Object.keys(keyValuePairs).length > 0) {
        const compressedKeyValuePairs = RedisClientService._compressKeyValuePairs(keyValuePairs);
        if (RedisClientService._isRequestSizeAllowed(compressedKeyValuePairs)) {
          await this._redisClient.mset(compressedKeyValuePairs);
        } else {
          logger.error("Maximum request size exceeded", {
            module: "RedisClientService",
            method: "mSet",
            data: { keys: Object.keys(keyValuePairs) }
          });
        }
      }
    } catch (err) {
      captureException(err);
      logger.error("Redis mSet failed", {
        module: "RedisClientService",
        method: "mSet",
        data: {
          keys: Object.keys(keyValuePairs),
          error: err
        }
      });
    }
  }

  public async set(key: string, value: any, setOptions: SetCommandOptions = {}): Promise<void> {
    logger.info("Setting cached data...", {
      module: "RedisClientService",
      method: "set",
      data: { key, setOptions }
    });

    try {
      const compressedData = RedisClientService._compress(value);
      if (RedisClientService._isRequestSizeAllowed(compressedData)) {
        await this._redisClient.set(key, compressedData, setOptions);
      } else {
        logger.error("Maximum request size exceeded", {
          module: "RedisClientService",
          method: "set",
          data: { key, setOptions }
        });
      }
    } catch (err) {
      captureException(err);
      logger.error("Redis set failed", {
        module: "RedisClientService",
        method: "set",
        data: {
          key,
          setOptions,
          error: err
        }
      });
    }
  }

  /**
   * @description Our pipeline method returns a pipeline instance similar to the one that
   * upstash redis returns, but with an extended mset method that checks if the cache data
   * is within the allowed size limits. To avoid any unintended usage, if a different pipeline
   * method is called, an error is thrown.
   *
   * Example:
   *
   * ```
   * pipeline.mset({ key1: "value1", key2: "value2" }); // this will work
   * pipeline.get("key1"); // This will throw an error
   * ```
   *
   */
  public pipeline(): Pipeline {
    const pipelineInstance = this._redisClient.pipeline();

    // Create a proxy to handle all method calls and restrict them to mset.
    const proxyPipeline = new Proxy(pipelineInstance, {
      get(target: Pipeline<[]>, propKey: string | symbol) {
        // Allow access to the mset method only and return a wrapped version of the mset method
        if (propKey === PIPELINE_WRAPPED_METHODS.MSET) {
          return (...args: Record<string, any>[]) => {
            // The arguments is an array with one element and this element
            // is the object that we want to pass to mset as an arg.
            const keyValuePairs = args[0];
            if (Object.keys(keyValuePairs).length > 0) {
              const compressedKeyValuePairs = RedisClientService._compressKeyValuePairs(keyValuePairs);
              if (RedisClientService._isRequestSizeAllowed(compressedKeyValuePairs)) {
                // Call the original mset method.
                target.mset(compressedKeyValuePairs);
              } else {
                logger.error("Maximum request size exceeded", {
                  module: "RedisClientService",
                  method: "pipeline",
                  data: { keys: Object.keys(keyValuePairs), method: "mset" }
                });
              }
            }
          };
        } else {
          return target[propKey as keyof typeof target];
        }
      }
    });

    return proxyPipeline;
  }

  // ===============
  // PRIVATE METHODS
  // ===============
  private static _compress(data: any): string {
    const stringifiedJSON = JSON.stringify(data);
    const compressed = pako.gzip(stringifiedJSON);
    const compressedBase64 = Buffer.from(compressed).toString("base64");
    return compressedBase64;
  }

  private static _compressKeyValuePairs(keyValuePairs: Record<string, any>): Record<string, string> {
    return Object.fromEntries(
      Object.entries(keyValuePairs).map(([key, value]) => [key, RedisClientService._compress(value)])
    );
  }

  private static _decompress<T>(compressedData: string): T {
    const decompressed = pako.ungzip(Buffer.from(compressedData, "base64"), { to: "string" });
    const originalObject = JSON.parse(decompressed);
    return originalObject;
  }

  private static _isRequestSizeAllowed(data: any): boolean {
    return ObjectUtil.calculateObjectSize(data) <= CACHE_UPLOAD_LIMIT_MB;
  }
}
