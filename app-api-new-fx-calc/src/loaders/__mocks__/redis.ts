import pako from "pako";
import { SetCommandOptions } from "@upstash/redis";

/**
 * Add keys to this array to blacklist them from being stored in the in-memory store.
 *
 * **Note:** This applies only to tests.
 */
const BLACKLIST_KEYS = ["investmentProducts", "investmentProductsWithTickers"];

export class RedisClientService {
  private IN_MEMORY_STORE: Map<string, string>;

  constructor() {
    this.IN_MEMORY_STORE = new Map();
  }

  public static get Instance(): RedisClientService {
    const globalWithRedis = global as typeof global & { RedisClientInstance: RedisClientService };
    if (!globalWithRedis.RedisClientInstance) {
      globalWithRedis.RedisClientInstance = new RedisClientService();
    }
    return globalWithRedis.RedisClientInstance;
  }

  public static isRequestSizeAllowed(data: any): boolean {
    return true;
  }

  public async atomicDel(key: string): Promise<boolean> {
    await this.del(key);
    return true;
  }

  public async atomicSet(key: string, value: any, setOptions: SetCommandOptions = {}): Promise<boolean> {
    await this.set(key, value, setOptions);
    return true;
  }

  public async del(key: string): Promise<void> {
    await Promise.resolve(this.IN_MEMORY_STORE.delete(key));
    return;
  }

  public async get(key: string): Promise<any> {
    const data = this.IN_MEMORY_STORE.get(key);
    // We do value decompression to have similar functionality to Upstash redis
    return Promise.resolve(data ? RedisClientService._decompress(data as string) : data) as Promise<any>;
  }

  public async mGet(keys: string[]): Promise<string[]> {
    const response = [] as string[];
    keys.forEach((key) => {
      const cachedValue = this.IN_MEMORY_STORE.get(key);
      if (cachedValue) {
        response.push(RedisClientService._decompress(cachedValue as string));
      }
    });
    return Promise.resolve(response) as Promise<string[]>;
  }

  public async mSet(keyValuePairs: Record<string, string>): Promise<void> {
    Object.entries(keyValuePairs).forEach(([key, value]) => {
      this.IN_MEMORY_STORE.set(key, RedisClientService._compress(value));
    });
  }

  public async set(key: string, value: string, setOptions: SetCommandOptions = {}): Promise<void> {
    if (BLACKLIST_KEYS.includes(key)) {
      return;
    }

    const compressedData = RedisClientService._compress(value);
    this.IN_MEMORY_STORE.set(key, compressedData);
  }

  public pipeline(): RedisClientService {
    return Object.assign(this, {
      mset: (keyValuePairs: Record<string, string>) => {
        return this.mSet(keyValuePairs);
      },
      async exec() {
        return;
      },
      length: () => Object.keys(this.IN_MEMORY_STORE).length
    });
  }

  private static _compress(data: any): string {
    const stringifiedJSON = JSON.stringify(data);
    const compressed = pako.gzip(stringifiedJSON);
    const compressedBase64 = Buffer.from(compressed).toString("base64");
    return compressedBase64;
  }

  private static _decompress<T>(compressedData: string): T {
    const decompressed = pako.ungzip(Buffer.from(compressedData, "base64"), { to: "string" });
    const originalObject = JSON.parse(decompressed);
    return originalObject;
  }
}
