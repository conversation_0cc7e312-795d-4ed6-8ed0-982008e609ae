import mongoose from "mongoose";
import { captureException } from "@sentry/node";
import logger from "../external-services/loggerService";
import { envIsDev } from "../utils/environmentUtil";
import { MongoReadPreferenceEnum } from "../utils/dbUtil";

// Import all of our models
import "../models/Account";
import "../models/Address";
import "../models/AssetNews";
import "../models/AppRating";
import "../models/Automation";
import "../models/BankAccount";
import "../models/CorporateEvent";
import "../models/Wallet";
import "../models/DailyTicker";
import "../models/Gift";
import "../models/IndexPrice";
import "../models/IntraDayTicker";
import "../models/InvestmentProduct";
import "../models/KycOperation";
import "../models/Mandate";
import "../models/Notification";
import "../models/NotificationSettings";
import "../models/Order";
import "../models/Participant";
import "../models/PaymentMethod";
import "../models/Portfolio";
import "../models/ReferralCode";
import "../models/Requisition";
import "../models/Reward";
import "../models/RewardInvitation";
import "../models/RiskAssessment";
import "../models/SavingsProduct";
import "../models/Subscription";
import "../models/SundownDigest";
import "../models/DailySummarySnapshot";
import "../models/Transaction";
import "../models/TransactionMonitor";
import "../models/User";
import "../models/UserDataRequest";

export default class MongoLoader {
  private _readPreference: MongoReadPreferenceEnum;

  constructor(readPreference: MongoReadPreferenceEnum) {
    this._readPreference = envIsDev() ? MongoReadPreferenceEnum.PRIMARY : readPreference;
  }

  public init(): void {
    this._initListeners();
    this._connectMongo();
  }

  private _connectMongo(): void {
    mongoose
      .set("strictQuery", false)
      .connect(process.env.DATABASE_URL, {
        readPreference: this._readPreference,
        maxPoolSize: 100
      })
      .catch((err) => {
        logger.error(`🙅 🚫 🙅 🚫 🙅 🚫 🙅 🚫 mongoose exception handling on connect → ${err.message}`, {
          module: "MongoLoader",
          method: "_connectMongo",
          data: { err }
        });
      });
  }

  private _initListeners(): void {
    mongoose.connection.on("error", (err) => {
      logger.error(`🙅 🚫 🙅 🚫 🙅 🚫 🙅 🚫 mongodb connection error → ${err.message}`, {
        module: "MongoLoader",
        method: "_initListeners",
        data: { err }
      });
      captureException(err);
      setTimeout(() => {
        this._connectMongo();
      }, 5000);
    });

    mongoose.connection.on("connected", function () {
      logger.info("✅ ✅ ✅ → mongodb is connected", {
        module: "MongoLoader",
        method: "_initListeners"
      });
    });

    mongoose.connection.on("disconnected", function () {
      logger.info("❌ ❌ ❌ → mongodb disconnected", {
        module: "MongoLoader",
        method: "_initListeners"
      });
    });
  }
}
