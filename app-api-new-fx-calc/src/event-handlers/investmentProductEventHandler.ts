import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import analytics, {
  TrackInvestmentProductStaleTickerPropertiesType
} from "../external-services/segmentAnalyticsService";
import { nanoid } from "nanoid";
import DateUtil from "../utils/dateUtil";

class InvestmentProductEventHandler {
  constructor() {
    eventEmitter.on(
      events.investmentProduct.staleTicker.eventId,
      this._handleInvestmentProductStaleTicker.bind(this)
    );
  }

  private _handleInvestmentProductStaleTicker(properties: TrackInvestmentProductStaleTickerPropertiesType): void {
    let message = "";
    if (properties.length > 20) {
      message = properties
        .slice(0, 20)
        .map(
          (staleTicker) =>
            `${staleTicker.assetName} - ${DateUtil.formatDateToDDMONYYYYHHMM(staleTicker.lastUpdateDate)} UTC\n`
        )
        .join("");
      message = message + `and other ${properties.length - 20} assets...`;
    } else {
      message = properties
        .map(
          (staleTicker) =>
            `${staleTicker.assetName} - ${DateUtil.formatDateToDDMONYYYYHHMM(staleTicker.lastUpdateDate)} UTC\n`
        )
        .join("");
    }

    analytics.rawTrack(
      {
        anonymousId: nanoid(),
        event: events.investmentProduct.staleTicker.name,
        properties: {
          message: message
        }
      },
      {
        All: false,
        SlackActions: true
      }
    );
  }
}

export default InvestmentProductEventHandler;
