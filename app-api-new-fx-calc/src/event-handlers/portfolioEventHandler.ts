import eventEmitter from "../loaders/eventEmitter";
import { UserDocument } from "../models/User";
import FacebookAppEventService from "../external-services/facebookAppEventService";
import GoogleAnalyticsService, { EventActionEnum } from "../external-services/googleAnalyticsService";
import analytics from "../external-services/segmentAnalyticsService";
import events from "./events";
import { AllocationCreationFlowEnum } from "../models/Portfolio";

class PortfolioEventHandler {
  constructor() {
    eventEmitter.on(events.portfolio.portfolioAllocation.eventId, this._handleVirtualPortfolioCreation.bind(this));
  }

  private async _handleVirtualPortfolioCreation(
    user: UserDocument,
    properties: { allocationCreationFlow: AllocationCreationFlowEnum }
  ): Promise<void> {
    analytics.track(
      user,
      events.portfolio.portfolioAllocation.name,
      {},
      { allocationCreationFlow: properties.allocationCreationFlow }
    );
    await Promise.allSettled([
      GoogleAnalyticsService.trackEventGA4({
        user,
        event: EventActionEnum.VIRTUAL_PORTFOLIO
      }),
      FacebookAppEventService.trackEvent(events.portfolio.portfolioAllocation.name, user.lastLoginPlatform, {
        email: user.email
      })
    ]);
  }
}

export default PortfolioEventHandler;
