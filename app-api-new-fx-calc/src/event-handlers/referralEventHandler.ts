import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import { UserDocument } from "../models/User";
import { TransactionalNotificationEventEnum } from "./notificationEvents";
import NotificationService from "../services/notificationService";

class ReferralEventHandler {
  constructor() {
    eventEmitter.on(events.referral.referralRewardCreation.eventId, this._handleReferralRewardCreation.bind(this));
    eventEmitter.on(events.referral.referrerRewardCreation.eventId, this._handleReferrerRewardCreation.bind(this));
    eventEmitter.on(events.referral.rewardSettled.eventId, this._handleRewardSettled.bind(this));
  }

  private async _handleReferralRewardCreation(user: UserDocument): Promise<void> {
    await NotificationService.createAppNotification(
      user.id,
      { notificationId: TransactionalNotificationEventEnum.REFERRAL_SUCCESS_2B },
      { sendImmediately: true }
    );
  }

  private async _handleReferrerRewardCreation(user: UserDocument): Promise<void> {
    await NotificationService.createAppNotification(
      user.id,
      { notificationId: TransactionalNotificationEventEnum.REFERRAL_SUCCESS_2A },
      { sendImmediately: true }
    );
  }

  private async _handleRewardSettled(user: UserDocument): Promise<void> {
    await NotificationService.createAppNotification(
      user.id,
      { notificationId: TransactionalNotificationEventEnum.REFERRAL_SUCCESS_3 },
      { sendImmediately: true }
    );
  }
}

export default ReferralEventHandler;
