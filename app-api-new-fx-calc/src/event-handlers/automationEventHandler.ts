import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import { UserDocument } from "../models/User";
import logger from "../external-services/loggerService";
import analytics, {
  TrackDepositPropertiesType,
  UserTraitType
} from "../external-services/segmentAnalyticsService";
import { currenciesConfig } from "@wealthyhood/shared-configs";

class AutomationEventHandler {
  constructor() {
    eventEmitter.on(
      events.automation.recurringInvestmentCreation.eventId,
      this._handleRecurringInvestmentCreation.bind(this)
    );
    eventEmitter.on(
      events.automation.recurringInvestmentCancellation.eventId,
      this._handleRecurringInvestmentCancellation.bind(this)
    );
    eventEmitter.on(
      events.automation.recurringSavingsCreation.eventId,
      this._handleRecurringSavingsCreation.bind(this)
    );
    eventEmitter.on(
      events.automation.recurringSavingsCancellation.eventId,
      this._handleRecurringSavingsCancellation.bind(this)
    );
    eventEmitter.on(
      events.automation.recurringRebalanceCreation.eventId,
      this._handleRecurringRebalanceCreation.bind(this)
    );
    eventEmitter.on(
      events.automation.recurringRebalanceCancellation.eventId,
      this._handleRecurringRebalanceCancellation.bind(this)
    );
  }

  private async _handleRecurringInvestmentCreation(
    user: UserDocument,
    properties: TrackDepositPropertiesType
  ): Promise<void> {
    logger.info(`Recurring investment created for user ${user.email}`, {
      module: "AutomationEventHandler",
      method: "_handleRecurringInvestmentCreation"
    });

    const traits: UserTraitType = {
      repInvestm: "True"
    };

    analytics.identify(user, traits, { All: false, MailChimp: true, Mixpanel: true, Intercom: true });
    analytics.track(
      user,
      events.automation.recurringInvestmentCreation.name,
      { All: false, Mixpanel: true, Intercom: true, Slack: true, SlackActions: true },
      { ...properties, displayCurrencySymbol: currenciesConfig.CURRENCY_SYMBOLS[properties.currency] }
    );
  }

  private async _handleRecurringInvestmentCancellation(user: UserDocument): Promise<void> {
    logger.info(`Recurring investment cancelled for user ${user.email}`, {
      module: "AutomationEventHandler",
      method: "_handleRecurringInvestmentCancellation"
    });

    const traits: UserTraitType = {
      repInvestm: "False"
    };

    analytics.identify(user, traits, { All: false, MailChimp: true, Mixpanel: true, Intercom: true });
    analytics.track(user, events.automation.recurringInvestmentCancellation.name, {
      All: false,
      Mixpanel: true,
      Intercom: true
    });
  }

  private async _handleRecurringSavingsCreation(
    user: UserDocument,
    properties: TrackDepositPropertiesType
  ): Promise<void> {
    logger.info(`Recurring savings created for user ${user.email}`, {
      module: "AutomationEventHandler",
      method: "_handleRecurringSavingsCreation"
    });

    const traits: UserTraitType = {
      repSavings: "True"
    };

    analytics.identify(user, traits, { All: false, MailChimp: true, Mixpanel: true, Intercom: true });
    analytics.track(
      user,
      events.automation.recurringSavingsCreation.name,
      { All: false, Mixpanel: true, Intercom: true, Slack: true, SlackActions: true },
      { ...properties, displayCurrencySymbol: currenciesConfig.CURRENCY_SYMBOLS[properties.currency] }
    );
  }

  private async _handleRecurringSavingsCancellation(user: UserDocument): Promise<void> {
    logger.info(`Recurring savings cancelled for user ${user.email}`, {
      module: "AutomationEventHandler",
      method: "_handleRecurringSavingsCancellation"
    });

    const traits: UserTraitType = {
      repSavings: "False"
    };

    analytics.identify(user, traits, { All: false, MailChimp: true, Mixpanel: true, Intercom: true });
    analytics.track(user, events.automation.recurringSavingsCancellation.name, {
      All: false,
      Mixpanel: true,
      Intercom: true
    });
  }

  private async _handleRecurringRebalanceCreation(user: UserDocument): Promise<void> {
    logger.info(`Recurring rebalance created for user ${user.email}`, {
      module: "AutomationEventHandler",
      method: "_handleRecurringRebalanceCreation"
    });

    const traits: UserTraitType = {
      autRebalan: "True"
    };

    analytics.identify(user, traits, { All: false, MailChimp: true, Mixpanel: true, Intercom: true });
    analytics.track(user, events.automation.recurringRebalanceCreation.name, {
      All: false,
      Mixpanel: true,
      Intercom: true,
      Slack: true,
      SlackActions: true
    });
  }

  private async _handleRecurringRebalanceCancellation(user: UserDocument): Promise<void> {
    logger.info(`Recurring rebalance cancelled for user ${user.email}`, {
      module: "AutomationEventHandler",
      method: "_handleRecurringRebalanceCancellation"
    });

    const traits: UserTraitType = {
      autRebalan: "False"
    };

    analytics.identify(user, traits, { All: false, MailChimp: true, Mixpanel: true, Intercom: true });
    analytics.track(user, events.automation.recurringRebalanceCancellation.name, {
      All: false,
      Mixpanel: true,
      Intercom: true
    });
  }
}

export default AutomationEventHandler;
