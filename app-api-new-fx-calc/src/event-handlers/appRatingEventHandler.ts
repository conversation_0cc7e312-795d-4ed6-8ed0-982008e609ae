import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import { UserDocument } from "../models/User";
import logger from "../external-services/loggerService";
import analytics, { TrackAppRatingPropertiesType } from "../external-services/segmentAnalyticsService";

class AppRatingEventHandler {
  constructor() {
    eventEmitter.on(events.appRating.appRatingSubmitted.eventId, this._handleAppRatingSubmitted.bind(this));
  }

  private _handleAppRatingSubmitted(user: UserDocument, properties: TrackAppRatingPropertiesType): void {
    logger.info(`User ${user.id} submitted app rating`, {
      module: "AppRatingEventHandler",
      method: "_handleAppRatingSubmitted"
    });

    analytics.track(
      user,
      events.appRating.appRatingSubmitted.name,
      {
        All: false,
        Mixpanel: true
      },
      properties
    );
  }
}

export default AppRatingEventHandler;
