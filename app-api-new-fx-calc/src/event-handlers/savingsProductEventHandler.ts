import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import analytics, {
  TrackSavingsProductDataUpdatePropertiesType
} from "../external-services/segmentAnalyticsService";
import { nanoid } from "nanoid";
import DateUtil from "../utils/dateUtil";

class SavingsProductEventHandler {
  constructor() {
    eventEmitter.on(
      events.savingsProduct.savingProductDataUpdate.eventId,
      this._handleSavingsProductDataUpdate.bind(this)
    );
  }

  private _handleSavingsProductDataUpdate(properties: TrackSavingsProductDataUpdatePropertiesType): void {
    const { savingsProductLabel, lastUpdateDate, dailyDistributionFactor, oneDayYield } = properties;
    analytics.rawTrack(
      {
        anonymousId: nanoid(),
        event: events.savingsProduct.savingProductDataUpdate.name,
        properties: {
          savingsProductLabel: savingsProductLabel,
          lastUpdateDate: DateUtil.formatDateToDDMONYYYYHHMM(lastUpdateDate),
          dailyDistributionFactor: dailyDistributionFactor,
          oneDayYield: oneDayYield
        }
      },
      {
        All: false,
        SlackActions: true
      }
    );
  }
}

export default SavingsProductEventHandler;
