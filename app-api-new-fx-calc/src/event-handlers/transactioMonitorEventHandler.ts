import analytics from "../external-services/segmentAnalyticsService";
import eventEmitter from "../loaders/eventEmitter";
import { UserDocument } from "../models/User";
import events from "./events";

class TransactionMonitorEventHandler {
  constructor() {
    eventEmitter.on(
      events.transactionMonitoring.aggregateAmountForHighRiskUser.eventId,
      this._handleAggregateAmountForHighRiskUser.bind(this)
    );
    eventEmitter.on(
      events.transactionMonitoring.aggregateAmountForLowMediumRiskUser.eventId,
      this._handleAggregateAmountForLowMediumRiskUser.bind(this)
    );
    eventEmitter.on(
      events.transactionMonitoring.withdrawalAfterDeposit.eventId,
      this._handleWithdrawalAfterDeposit.bind(this)
    );
    eventEmitter.on(
      events.transactionMonitoring.transactionAfterAccountInactivity.eventId,
      this._handleTransactionAfter1YearInactivity.bind(this)
    );
    eventEmitter.on(
      events.transactionMonitoring.highVolumeDeposits.eventId,
      this.handleHighVolumeDeposits.bind(this)
    );
    eventEmitter.on(
      events.transactionMonitoring.netAggregateAmountForHighRiskUser.eventId,
      this._handleNetAggregateAmountForHighRiskUser.bind(this)
    );
  }

  private _handleAggregateAmountForHighRiskUser(user: UserDocument): void {
    analytics.track(user, events.transactionMonitoring.aggregateAmountForHighRiskUser.name, {
      All: false,
      SlackActions: true
    });
  }

  private _handleAggregateAmountForLowMediumRiskUser(user: UserDocument): void {
    analytics.track(user, events.transactionMonitoring.aggregateAmountForLowMediumRiskUser.name, {
      All: false,
      SlackActions: true
    });
  }

  private _handleWithdrawalAfterDeposit(user: UserDocument): void {
    analytics.track(user, events.transactionMonitoring.withdrawalAfterDeposit.name, {
      All: false,
      SlackActions: true
    });
  }

  private _handleTransactionAfter1YearInactivity(user: UserDocument): void {
    analytics.track(user, events.transactionMonitoring.transactionAfterAccountInactivity.name, {
      All: false,
      SlackActions: true
    });
  }

  private handleHighVolumeDeposits(user: UserDocument): void {
    analytics.track(user, events.transactionMonitoring.highVolumeDeposits.name, {
      All: false,
      SlackActions: true
    });
  }

  private _handleNetAggregateAmountForHighRiskUser(user: UserDocument): void {
    analytics.track(user, events.transactionMonitoring.netAggregateAmountForHighRiskUser.name, {
      All: false,
      SlackActions: true
    });
  }
}

export default TransactionMonitorEventHandler;
