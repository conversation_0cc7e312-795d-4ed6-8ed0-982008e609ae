import eventEmitter from "../loaders/eventEmitter";
import { ParticipantDocument } from "../models/Participant";
import GoogleAnalyticsService, { EventActionEnum } from "../external-services/googleAnalyticsService";
import analytics from "../external-services/segmentAnalyticsService";
import { hashSHA256 } from "../utils/cryptoUtil";
import events from "./events";

class ParticipantEventHandler {
  constructor() {
    eventEmitter.on(events.participant.emailSubmitted.eventId, this._handleUserEmailSubmitted.bind(this));
  }

  private async _handleUserEmailSubmitted(participant: ParticipantDocument): Promise<void> {
    const { appInstallInfo, appsflyerId, email, wlthdId, grsfId, metadata, trackingSource, pageUserLanded } =
      participant;

    let mobileApp;
    let appDownld;
    if (appInstallInfo) {
      appDownld = appInstallInfo.createdAt;
      mobileApp = appInstallInfo.platform;
    }

    // Anonymous Id is by default the appsflyer id. However, if a user signs up through web,
    // this handler will be called with the appsflyer id empty (it's created on mobile), so in
    // that case we use the user's email address hash.
    let anonymousId = appsflyerId;
    if (!anonymousId) {
      anonymousId = hashSHA256(email);
    }

    analytics.rawIdentify(
      {
        anonymousId,
        traits: {
          appDownld,
          appsflyerId,
          email,
          mobileApp,
          wlthdId,
          grsfId,
          pageUserLanded,
          financeAdsId: metadata?.financeAds?.influencerId,
          source: trackingSource,
          sourceCampaign: metadata?.googleAds?.campaign
        }
      },
      { All: false, MailChimp: true, Mixpanel: true }
    );

    // In addition to the above, we also identify pageLanded to Mailchimp as it is the shortened version of
    // pageUserLanded that is set as the merge field in Mailchimp.
    analytics.rawIdentify(
      {
        anonymousId,
        traits: {
          pageLanded: pageUserLanded
        }
      },
      { All: false, MailChimp: true }
    );
    analytics.rawTrack(
      {
        anonymousId,
        event: events.participant.emailSubmitted.name,
        properties: {
          appsflyerId,
          email,
          wlthdId,
          grsfId,
          financeAdsId: metadata?.financeAds?.influencerId,
          platform: mobileApp
        }
      },
      { All: true, MailChimp: false }
    );
    await analytics.delay(2000);

    GoogleAnalyticsService.trackEventGA4({
      participant,
      event: EventActionEnum.EMAIL_SUBMITTED
    });
  }
}

export default ParticipantEventHandler;
