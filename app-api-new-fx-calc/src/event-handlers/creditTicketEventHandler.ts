import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import analytics, { TrackCreditTicketReportedPropertiesType } from "../external-services/segmentAnalyticsService";
import { nanoid } from "nanoid";
import CurrencyUtil from "../utils/currencyUtil";

class CreditTicketEventHandler {
  constructor() {
    eventEmitter.on(
      events.creditTickets.creditedAmountReported.eventId,
      this._handleCreditTicketReported.bind(this)
    );
  }

  private _handleCreditTicketReported(properties: TrackCreditTicketReportedPropertiesType): void {
    const messages = [];

    // 1. Report total credited amount
    messages.push(
      `We have currently ${CurrencyUtil.formatCurrency(properties.totalCreditedAmount, "EUR", "en")} in open credit tickets.`
    );

    // 2. Report old open tickets
    if (properties.oldOpenTickets > 0) {
      messages.push(
        `⚠️ There are ${properties.oldOpenTickets} credit tickets that have been open for more than 2 business days.`
      );
    } else {
      messages.push("✅️ There are no credit tickets that have been open for more than 2 business days.");
    }

    // 3. Report deposit vs credited amount match
    if (!properties.depositVsCreditedMatch) {
      messages.push(
        "⚠️ Mismatch between recent deposits and credited amounts:\n" +
          `- Total EU deposits: ${CurrencyUtil.formatCurrency(properties.totalRecentDeposits, "EUR", "en")}\n` +
          `- Total credited amount: ${CurrencyUtil.formatCurrency(properties.totalRecentCredited, "EUR", "en")}`
      );
    } else {
      messages.push("✅️ Recent deposits and credited amounts match!");
    }

    analytics.rawTrack(
      {
        anonymousId: nanoid(),
        event: events.creditTickets.creditedAmountReported.name,
        properties: {
          message: messages.join("\n")
        }
      },
      {
        All: false,
        SlackActions: true
      }
    );
  }
}

export default CreditTicketEventHandler;
