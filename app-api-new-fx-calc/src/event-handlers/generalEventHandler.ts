import { nanoid } from "nanoid";
import eventEmitter from "../loaders/eventEmitter";
import analytics from "../external-services/segmentAnalyticsService";
import { GoogleAdsMetadataType, TrackingSourceType } from "../models/Participant";
import events from "./events";
import { UserDocument } from "../models/User";

class GeneralEventHandler {
  constructor() {
    eventEmitter.on(events.general.install.eventId, this._handleAppInstall.bind(this));
    eventEmitter.on(
      events.general.appsflyerConversionSuccess.eventId,
      this._handleAppsflyerConversionSuccess.bind(this)
    );
    eventEmitter.on(events.general.appOpened.eventId, this._handleAppOpened.bind(this));
    eventEmitter.on(events.general.finimizeRefIdentified.eventId, this._handleFinimizeRefIdentified.bind(this));
  }

  private async _handleAppInstall(attributionParams: {
    appsflyerId: string;
    wlthdId?: string;
    grsfId?: string;
    pageUserLanded?: string;
    sId?: string;
    platform?: "ios" | "android";
  }): Promise<void> {
    const { appsflyerId, wlthdId, grsfId, sId, platform, pageUserLanded } = attributionParams;

    analytics.rawIdentify(
      {
        anonymousId: appsflyerId,
        traits: {
          appsflyerId,
          wlthdId,
          grsfId,
          pageUserLanded,
          sId,
          mobileApp: platform
        }
      },
      { All: false, Mixpanel: true }
    );
    analytics.rawTrack(
      {
        anonymousId: appsflyerId,
        event: events.general.install.name,
        properties: {
          appsflyerId,
          wlthdId,
          grsfId,
          sId,
          platform
        }
      },
      { All: true, MailChimp: false }
    );
    await analytics.delay(2000);
  }

  private async _handleAppsflyerConversionSuccess(attributionParams: {
    appsflyerId: string;
    googleAdsMetadata: GoogleAdsMetadataType;
    trackingSource: TrackingSourceType;
  }): Promise<void> {
    const { appsflyerId, googleAdsMetadata, trackingSource } = attributionParams;

    analytics.rawIdentify(
      {
        anonymousId: appsflyerId,
        traits: {
          appsflyerId,
          source: trackingSource,
          sourceCampaign: googleAdsMetadata?.campaign
        }
      },
      { All: false, Mixpanel: true }
    );
  }

  private async _handleAppOpened(user: UserDocument, platform: "ios" | "android" | "web"): Promise<void> {
    analytics.track(user, events.general.appOpened.name, { All: false, Mixpanel: true }, { platform });
  }

  private async _handleFinimizeRefIdentified(data: { contentPieceId: string; title: string }): Promise<void> {
    analytics.rawTrack(
      {
        anonymousId: nanoid(),
        event: events.general.finimizeRefIdentified.name,
        properties: {
          contentPieceId: data.contentPieceId,
          title: data.title
        }
      },
      {
        All: false,
        SlackActions: true
      }
    );
  }
}

export default GeneralEventHandler;
