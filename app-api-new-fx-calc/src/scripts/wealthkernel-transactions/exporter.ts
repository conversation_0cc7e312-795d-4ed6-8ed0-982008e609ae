import ScriptRunner from "../../jobs/services/scriptRunner";
import logger from "../../external-services/loggerService";
import fs from "fs";
import { TransactionType, WealthkernelService } from "../../external-services/wealthkernelService";

export class ExportTransactionsScriptRunner extends ScriptRunner {
  scriptName = "wealthkernel-transactions";
  private portfolioIds = ["prt-34uttpawa242so", "prt-35lcfvyle244ii"];

  async processFn(): Promise<void> {
    logger.info("Starting transaction export...", {
      module: `script:${this.scriptName}`
    });

    for (const portfolioId of this.portfolioIds) {
      const transactions: TransactionType[] = [];

      logger.info(`Fetching transactions for portfolio ${portfolioId}...`, {
        module: `script:${this.scriptName}`
      });

      await WealthkernelService.UKInstance.listTransactions(
        { portfolioId, startDate: "2025-01-01", endDate: "2025-03-31" },
        async (transaction: TransactionType) => {
          transactions.push(transaction);
          if (transactions.length % 20 === 0) {
            logger.info(`Retrieved ${transactions.length} transactions for ${portfolioId}...`, {
              module: `script:${this.scriptName}`
            });
          }
        }
      );

      this._saveDataToCSV(transactions, portfolioId);

      logger.info(`Completed export for portfolio ${portfolioId} with ${transactions.length} transactions`, {
        module: `script:${this.scriptName}`
      });
    }

    logger.info("Finished exporting all transactions", {
      module: `script:${this.scriptName}`
    });
  }

  private _saveDataToCSV(transactions: TransactionType[], portfolioId: string): void {
    const header = [
      "Transaction ID",
      "Type",
      "Status",
      "ISIN",
      "Quantity",
      "Price",
      "Price Currency",
      "Consideration Amount",
      "Consideration Currency",
      "Charges Amount",
      "Charges Currency",
      "Date",
      "Settled On",
      "Narrative"
    ];

    const csvRows = [];
    csvRows.push(header.join(","));

    transactions.forEach((transaction) => {
      const row = [
        transaction.id,
        transaction.type,
        transaction.status,
        transaction.isin || "",
        transaction.quantity || "",
        transaction.price?.amount || "",
        transaction.price?.currency || "",
        transaction.consideration.amount,
        transaction.consideration.currency,
        transaction.charges.amount,
        transaction.charges.currency,
        transaction.date,
        transaction.settledOn,
        (transaction.narrative || "").replace(/,/g, ";")
      ];

      csvRows.push(row.join(","));
    });

    const csvContent = csvRows.join("\n");
    const filename = `transactions-${portfolioId}-${new Date().toISOString()}.csv`;

    fs.writeFileSync(filename, csvContent);

    logger.info(`Saved transactions to ${filename}`, {
      module: `script:${this.scriptName}`
    });
  }
}

new ExportTransactionsScriptRunner().run();
