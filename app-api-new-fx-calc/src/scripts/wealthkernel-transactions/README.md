# Wealthkernel Transactions Exporter

This script exports transactions from Wealthkernel for our bonuses & fees portfolios into CSV files.

## Overview

The script fetches all transactions for our bonuses & fees portfolio IDs from the Wealthkernel API and saves them to CSV files. Each portfolio's transactions are saved in a separate file.

## Configuration

The script is configured with the following portfolio IDs:
- `prt-34uttpawa242so` - Bonuses Portfolio
- `prt-35lcfvyle244ii` - Fees Portfolio

Default date range for transaction fetching:
- Start Date: 2025-01-01
- End Date: 2025-04-01

Update `startDate` and `endDate` within the script to change the date range.

## Output

The script generates CSV files with the naming pattern:
```
transactions-{portfolioId}-{timestamp}.csv
```
