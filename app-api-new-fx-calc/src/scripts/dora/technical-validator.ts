import * as fs from "fs";
import * as path from "path";
import AdmZip from "adm-zip";
import * as os from "os";

/**
 * Interface for technical validation rules
 */
interface TechnicalRule {
  id: number;
  category: string;
  description: string;
}

/**
 * Interface for validation results
 */
interface TechnicalValidationResult {
  ruleCode: number;
  status: "passed" | "failed" | "skipped" | "error";
  category: string;
  description: string;
  details?: string;
}

/**
 * Interface for input data structure
 */
type InputData = Record<string, Record<string, any>[]>;

/**
 * Interface for CSV report data
 */
interface CsvReportData {
  parameters: Record<string, string>;
  tables: Record<string, any>;
  reportInfo: {
    fileName: string;
    tables: string[];
  };
}

/**
 * Interface for Filing Indicators data
 */
interface FilingIndicator {
  template: string;
  filed: boolean;
}

/**
 * Extract zip file to a temporary directory
 */
function extractZipFile(zipFilePath: string): string {
  try {
    const absoluteZipPath = path.resolve(zipFilePath);
    if (!fs.existsSync(absoluteZipPath)) {
      throw new Error(`Zip file not found: ${absoluteZipPath}`);
    }

    // Create a temp directory for extraction
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), "dora-validator-"));
    console.log(`Extracting zip to temporary directory: ${tempDir}`);

    // Extract the zip file
    const zip = new AdmZip(absoluteZipPath);

    // Log the zip content before extraction for debugging
    const zipEntries = zip.getEntries();
    console.log(`Zip file contains ${zipEntries.length} entries:`);
    zipEntries.forEach((entry, index) => {
      if (index < 20) {
        // Limit logging to first 20 entries to avoid flooding logs
        console.log(`- ${entry.entryName} (${entry.isDirectory ? "directory" : "file"})`);
      }
    });

    zip.extractAllTo(tempDir, true);

    // Log the extracted content
    console.log(`Files extracted to ${tempDir}:`);
    const listDirectoryContent = (dir: string, depth = 0): void => {
      if (depth > 3) return; // Limit recursion depth

      const entries = fs.readdirSync(dir);
      entries.forEach((entry) => {
        const fullPath = path.join(dir, entry);
        const stat = fs.statSync(fullPath);
        const isDir = stat.isDirectory();
        console.log(`${"  ".repeat(depth)}- ${entry}${isDir ? "/" : ""}`);

        if (isDir) {
          listDirectoryContent(fullPath, depth + 1);
        }
      });
    };

    listDirectoryContent(tempDir);

    return tempDir;
  } catch (error: any) {
    console.error(`Error extracting zip file: ${error.message}`);
    throw error;
  }
}

/**
 * Load technical validation rules from a JSON file
 */
function loadTechnicalRules(filePath: string): TechnicalRule[] {
  try {
    const absolutePath = path.resolve(filePath);
    if (!fs.existsSync(absolutePath)) {
      throw new Error(`Technical validation rules file not found: ${absolutePath}`);
    }
    const fileContent = fs.readFileSync(absolutePath, "utf-8");
    return JSON.parse(fileContent);
  } catch (error: any) {
    console.error(`Error loading technical validation rules from ${filePath}:`, error.message);
    throw error;
  }
}

/**
 * Helper function to check if a date string is in yyyy-mm-dd format
 */
function isValidDateFormat(value: string): boolean {
  return /^\d{4}-\d{2}-\d{2}$/.test(value);
}

/**
 * Helper function to check if a value is a boolean
 */
function isBoolean(value: any): boolean {
  if (typeof value === "boolean") return true;
  if (typeof value === "string") {
    const lowerValue = value.toLowerCase();
    return lowerValue === "true" || lowerValue === "false" || lowerValue === "1" || lowerValue === "0";
  }
  if (typeof value === "number") {
    return value === 1 || value === 0;
  }
  return false;
}

/**
 * Helper function to parse a filing indicator string to boolean
 */
function parseFilingIndicatorValue(value: string): boolean {
  if (typeof value === "boolean") return value;
  const lowerValue = value.toLowerCase();
  return lowerValue === "true" || lowerValue === "1";
}

/**
 * Parse the FilingIndicators.csv file
 */
function parseFilingIndicators(csvPath: string): FilingIndicator[] {
  try {
    if (!fs.existsSync(csvPath)) {
      console.warn(`FilingIndicators.csv not found at ${csvPath}`);
      return [];
    }

    let fileContent = fs.readFileSync(csvPath, "utf8");
    // Remove BOM if present
    if (fileContent.charCodeAt(0) === 0xfeff) {
      fileContent = fileContent.slice(1);
    }

    const lines = fileContent.split("\n").filter((line) => line.trim() !== "");

    // Check for header
    const header = lines[0].trim().split(",");
    if (header.length !== 2) {
      throw new Error(`FilingIndicators.csv has invalid header: expected 2 columns, got ${header.length}`);
    }

    const filingIndicators: FilingIndicator[] = [];

    // Skip header row
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      const parts = line.split(",");
      if (parts.length !== 2) {
        throw new Error(
          `Invalid format in FilingIndicators.csv line ${i + 1}: expected 2 columns, got ${parts.length}`
        );
      }

      const template = parts[0].trim();
      const filedValue = parts[1].trim();

      if (template === "" || filedValue === "") {
        throw new Error(`Empty values in FilingIndicators.csv line ${i + 1}`);
      }

      if (!isBoolean(filedValue)) {
        throw new Error(
          `Invalid filing indicator value '${filedValue}' in line ${i + 1}. Must be boolean (true/false/1/0)`
        );
      }

      filingIndicators.push({
        template: template,
        filed: parseFilingIndicatorValue(filedValue)
      });
    }

    return filingIndicators;
  } catch (error: any) {
    console.error(`Error parsing FilingIndicators.csv: ${error.message}`);
    return [];
  }
}

/**
 * Find the reports directory in an extracted zip
 */
function findReportsDirectory(baseDir: string): string {
  const reportsAtRoot = path.join(baseDir, "reports");

  // Check if reports directory exists at the root level
  if (fs.existsSync(reportsAtRoot) && fs.statSync(reportsAtRoot).isDirectory()) {
    console.log(`Found reports directory at root: ${reportsAtRoot}`);
    return reportsAtRoot;
  }

  // Otherwise, search recursively
  console.log(`Reports directory not found at ${reportsAtRoot}, searching for it...`);

  const findReportsDir = (dir: string): string | null => {
    const entries = fs.readdirSync(dir);

    // First check if "reports" is directly in this directory
    if (entries.includes("reports") && fs.statSync(path.join(dir, "reports")).isDirectory()) {
      return path.join(dir, "reports");
    }

    // Then check subdirectories
    for (const entry of entries) {
      const fullPath = path.join(dir, entry);
      if (fs.statSync(fullPath).isDirectory()) {
        const foundPath = findReportsDir(fullPath);
        if (foundPath) return foundPath;
      }
    }

    return null;
  };

  const foundReportsDir = findReportsDir(baseDir);
  if (foundReportsDir) {
    console.log(`Found reports directory at: ${foundReportsDir}`);
    return foundReportsDir;
  }

  // If not found, return the default location (even though it doesn't exist)
  console.log("Could not find reports directory in the extracted zip, using default path");
  return reportsAtRoot;
}

// Create an in-memory registry for unique filenames
const submittedFilenamesRegistry: Set<string> = new Set();

/**
 * Validate a specific technical rule
 */
function validateRule(
  rule: TechnicalRule,
  inputCsvDir: string,
  validationData: InputData,
  csvData: CsvReportData,
  zipFilePath: string
): TechnicalValidationResult {
  const baseResult: TechnicalValidationResult = {
    ruleCode: rule.id,
    status: "passed",
    category: rule.category,
    description: rule.description
  };

  try {
    switch (rule.id) {
      // Reception rules (101-115)
      case 101: {
        // Report filename extension must be zip
        const zipFileExt = path.extname(zipFilePath).toLowerCase();
        if (zipFileExt !== ".zip") {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid file extension: ${zipFileExt}. Expected: .zip`
          };
        }
        return baseResult;
      }

      case 102: {
        // SFTP report file size must not exceed 10GB
        const maxSizeBytes = 10 * 1024 * 1024 * 1024; // 10GB in bytes

        try {
          const stats = fs.statSync(zipFilePath);
          const fileSizeBytes = stats.size;

          if (fileSizeBytes > maxSizeBytes) {
            return {
              ...baseResult,
              status: "failed",
              details: `File size (${(fileSizeBytes / (1024 * 1024 * 1024)).toFixed(2)}GB) exceeds maximum allowed size of 10GB`
            };
          }

          return baseResult;
        } catch (error: any) {
          return {
            ...baseResult,
            status: "error",
            details: `Error checking file size: ${error.message}`
          };
        }
      }

      case 103: {
        // ZIP consistency (checksum, unzips correctly)
        try {
          // Try to read the zip file to validate its integrity
          const zip = new AdmZip(zipFilePath);

          // Check if the zip can be read properly
          zip.getEntries();

          // If we can get entries without error, the zip file is valid
          return baseResult;
        } catch (error: any) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid ZIP file: ${error.message}`
          };
        }
      }

      case 104: {
        // Filename allowed characters: alphanumeric plus hyphens ('-'), underscores ('_') and dots ('.')
        const filename = path.basename(zipFilePath);
        const validCharsPattern = /^[a-zA-Z0-9\-_.]+$/;

        if (!validCharsPattern.test(filename)) {
          const invalidChars = filename.replace(/[a-zA-Z0-9\-_.]/g, "");
          return {
            ...baseResult,
            status: "failed",
            details: `Filename '${filename}' contains invalid characters: '${invalidChars}'. Only alphanumeric characters, hyphens (-), underscores (_), and dots (.) are allowed.`
          };
        }

        return baseResult;
      }

      case 105: {
        // Filename structure for the example: 9845003AEDCED0EDA093.IND_GR_DORA010100_DORA_2025-04-14_20250414171844000.zip
        // Let's break it down into components and adjust our validation
        const filename = path.basename(zipFilePath);
        console.log(`Rule 105 - Analyzing filename: ${filename}`);

        // Split by the first dot to separate LEI code prefix
        const parts = filename.split(".");

        if (parts.length < 2) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid filename format '${filename}'. Expected format includes report identifier and components separated by dots and underscores.`
          };
        }

        // Get the main part after the first dot (excluding .zip extension)
        const mainPart = parts
          .slice(1)
          .join(".")
          .replace(/\.zip$/i, "");

        // Split the main part by underscores
        const components = mainPart.split("_");
        if (components.length < 6) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid filename format. Expected at least 6 components separated by underscores in the main part of the filename. Found: ${components.length} components: ${components.join(", ")}`
          };
        }

        // Extract the key components
        const [entityType, country, frameworkCode, module, referenceDate, timestamp] = components;
        console.log(`Rule 105 - Extracted components:
          Entity Type: ${entityType}
          Country: ${country}
          Framework Code: ${frameworkCode}
          Module: ${module}
          Reference Date: ${referenceDate}
          Timestamp: ${timestamp}
        `);

        // Validate entity type (check if it's not empty)
        if (!entityType || entityType.trim() === "") {
          return {
            ...baseResult,
            status: "failed",
            details: "Empty entity type in filename. Entity type must be provided."
          };
        }

        // Validate country code (should be 2 characters)
        if (country.length !== 2) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid country code '${country}' in filename. Should be a 2-character ISO country code.`
          };
        }

        // Check if module is one of the allowed values
        const allowedModules = ["DORA", "SUP", "REM", "RES", "PAY", "PILLAR3"];
        if (!allowedModules.includes(module)) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid module '${module}' in filename. Should be one of: ${allowedModules.join(", ")}.`
          };
        }

        // Validate reference date format (yyyy-mm-dd)
        if (!isValidDateFormat(referenceDate)) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid reference date format '${referenceDate}' in filename. Expected format: yyyy-mm-dd.`
          };
        }

        // Framework code should typically include the module prefix
        if (!frameworkCode.includes(module.substring(0, 4))) {
          return {
            ...baseResult,
            status: "failed",
            details: `Framework code '${frameworkCode}' should include the module prefix (${module.substring(0, 4)}).`
          };
        }

        // Validate timestamp (basic check for numeric value)
        if (!/^\d+$/.test(timestamp)) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid timestamp format '${timestamp}' in filename. Expected a numeric value.`
          };
        }

        return baseResult;
      }

      case 106: // Filename module inconsistency
      case 107: // Country code match
        return {
          ...baseResult,
          status: "skipped",
          details: "Reception rule not applicable in current context (applies to file upload/transfer)"
        };

      case 109: // Filename reference date is valid date
        if (!csvData.parameters.refPeriod) {
          return {
            ...baseResult,
            status: "failed",
            details: "Missing reference date parameter"
          };
        }

        if (!isValidDateFormat(csvData.parameters.refPeriod)) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid reference date format: ${csvData.parameters.refPeriod}. Expected yyyy-mm-dd`
          };
        }

        return baseResult;

      case 110: {
        // Each submitted report filename must be unique - duplicate filenames are not allowed
        try {
          const filename = path.basename(zipFilePath);

          // Check if filename already exists in the in-memory registry
          if (submittedFilenamesRegistry.has(filename)) {
            return {
              ...baseResult,
              status: "failed",
              details: `Duplicate filename detected: '${filename}'. A report with this filename has already been submitted.`
            };
          }

          // Add filename to in-memory registry
          submittedFilenamesRegistry.add(filename);

          return baseResult;
        } catch (error: any) {
          return {
            ...baseResult,
            status: "error",
            details: `Error checking filename uniqueness: ${error.message}`
          };
        }
      }

      case 111: {
        // Reference date not in future
        if (!csvData.parameters.refPeriod) {
          return {
            ...baseResult,
            status: "failed",
            details: "Missing reference date parameter"
          };
        }

        if (!isValidDateFormat(csvData.parameters.refPeriod)) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid reference date format: ${csvData.parameters.refPeriod}. Expected yyyy-mm-dd`
          };
        }

        // Check if reference date is in the future
        const refDate = new Date(csvData.parameters.refPeriod);
        const today = new Date();

        // Reset time part for comparison
        today.setHours(0, 0, 0, 0);

        if (refDate > today) {
          return {
            ...baseResult,
            status: "failed",
            details: `Reference date (${csvData.parameters.refPeriod}) is in the future. Today is ${today.toISOString().split("T")[0]}`
          };
        }

        return baseResult;
      }

      case 115: {
        // DORA submitted report must be zip containing plain-csv
        try {
          // First check if it's a valid zip file
          const zipFileExt = path.extname(zipFilePath).toLowerCase();
          if (zipFileExt !== ".zip") {
            return {
              ...baseResult,
              status: "failed",
              details: `Invalid file extension: ${zipFileExt}. Expected: .zip`
            };
          }

          // Try to read the zip file
          const zip = new AdmZip(zipFilePath);
          const zipEntries = zip.getEntries();

          // Check if the zip contains the reports directory
          const reportsEntries = zipEntries.filter(
            (entry) => entry.entryName.startsWith("reports/") || entry.entryName.includes("/reports/")
          );

          if (reportsEntries.length === 0) {
            return {
              ...baseResult,
              status: "failed",
              details: "Zip file does not contain a reports directory or reports files"
            };
          }

          // Check for required CSV files
          const csvFiles = reportsEntries.filter(
            (entry) => entry.entryName.endsWith(".csv") && !entry.isDirectory
          );

          if (csvFiles.length === 0) {
            return {
              ...baseResult,
              status: "failed",
              details: "No CSV files found in the reports directory of the zip file"
            };
          }

          // Check for required CSV files: FilingIndicators.csv and parameters.csv
          const hasFilingIndicators = reportsEntries.some((entry) =>
            entry.entryName.endsWith("FilingIndicators.csv")
          );

          const hasParameters = reportsEntries.some((entry) => entry.entryName.endsWith("parameters.csv"));

          if (!hasFilingIndicators || !hasParameters) {
            const missing: string[] = [];
            if (!hasFilingIndicators) missing.push("FilingIndicators.csv");
            if (!hasParameters) missing.push("parameters.csv");

            return {
              ...baseResult,
              status: "failed",
              details: `Missing required CSV files: ${missing.join(", ")}`
            };
          }

          // Check for non-CSV files in the reports directory (except report.json which is allowed)
          const nonCsvReportFiles = reportsEntries.filter(
            (entry) =>
              !entry.isDirectory && !entry.entryName.endsWith(".csv") && !entry.entryName.endsWith("report.json")
          );

          if (nonCsvReportFiles.length > 0) {
            const invalidFiles = nonCsvReportFiles.map((entry) => path.basename(entry.entryName));

            return {
              ...baseResult,
              status: "failed",
              details: `Non-CSV files found in reports directory: ${invalidFiles.join(", ")}. Only CSV and report.json are allowed.`
            };
          }

          return baseResult;
        } catch (error: any) {
          return {
            ...baseResult,
            status: "error",
            details: `Error validating ZIP contents: ${error.message}`
          };
        }
      }

      // CSV rules (701-720)
      case 701: {
        // Must contain required files
        console.log(`Checking for required files in ${inputCsvDir}`);

        // Find the reports directory (it might be in a subdirectory)
        const reportsPath = findReportsDirectory(inputCsvDir);

        console.log(`Using reports directory: ${reportsPath}`);

        const reportJsonPath = path.join(reportsPath, "report.json");
        const filingIndicatorsPath = path.join(reportsPath, "FilingIndicators.csv");
        const parametersPath = path.join(reportsPath, "parameters.csv");

        console.log(`Checking for: ${reportJsonPath}`);
        console.log(`Checking for: ${filingIndicatorsPath}`);
        console.log(`Checking for: ${parametersPath}`);

        const missingFiles: string[] = [];
        if (!fs.existsSync(reportJsonPath)) missingFiles.push("reports/report.json");
        if (!fs.existsSync(filingIndicatorsPath)) missingFiles.push("reports/FilingIndicators.csv");
        if (!fs.existsSync(parametersPath)) missingFiles.push("reports/parameters.csv");

        if (missingFiles.length > 0) {
          return {
            ...baseResult,
            status: "failed",
            details: `Missing required files: ${missingFiles.join(", ")}`
          };
        }

        // Check if files are empty
        const reportJsonContent = fs.readFileSync(reportJsonPath, "utf8");
        const filingIndicatorsContent = fs.readFileSync(filingIndicatorsPath, "utf8");
        const parametersContent = fs.readFileSync(parametersPath, "utf8");

        if (!reportJsonContent.trim()) missingFiles.push("reports/report.json (empty)");
        if (!filingIndicatorsContent.trim()) missingFiles.push("reports/FilingIndicators.csv (empty)");
        if (!parametersContent.trim()) missingFiles.push("reports/parameters.csv (empty)");

        if (missingFiles.length > 0) {
          return {
            ...baseResult,
            status: "failed",
            details: `Empty required files: ${missingFiles.join(", ")}`
          };
        }

        return baseResult;
      }

      case 702: {
        // Invalid declarations in FilingIndicators.csv - wrong columns
        const reportsPath = findReportsDirectory(inputCsvDir);
        const filingIndicatorsPath = path.join(reportsPath, "FilingIndicators.csv");

        if (!fs.existsSync(filingIndicatorsPath)) {
          return {
            ...baseResult,
            status: "failed",
            details: "FilingIndicators.csv not found"
          };
        }

        const fileContent = fs.readFileSync(filingIndicatorsPath, "utf8");
        const lines = fileContent.split("\n").filter((line) => line.trim() !== "");

        if (lines.length === 0) {
          return {
            ...baseResult,
            status: "failed",
            details: "FilingIndicators.csv is empty"
          };
        }

        // Check header row
        const headerColumns = lines[0].trim().split(",");
        if (headerColumns.length !== 2) {
          return {
            ...baseResult,
            status: "failed",
            details: `FilingIndicators.csv has wrong number of columns in header: found ${headerColumns.length}, expected 2`
          };
        }

        // Check data rows
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const columns = line.split(",");
          if (columns.length !== 2) {
            return {
              ...baseResult,
              status: "failed",
              details: `FilingIndicators.csv has wrong number of columns in row ${i + 1}: found ${columns.length}, expected 2`
            };
          }
        }

        return baseResult;
      }

      case 703: {
        // Each template can be declared only once
        const reportsPath = findReportsDirectory(inputCsvDir);
        const filingIndicatorsPath = path.join(reportsPath, "FilingIndicators.csv");

        if (!fs.existsSync(filingIndicatorsPath)) {
          return {
            ...baseResult,
            status: "skipped",
            details: "FilingIndicators.csv not found"
          };
        }

        const filingIndicators = parseFilingIndicators(filingIndicatorsPath);
        const templates = new Set<string>();
        const duplicates = new Set<string>();

        for (const indicator of filingIndicators) {
          if (templates.has(indicator.template)) {
            duplicates.add(indicator.template);
          }
          templates.add(indicator.template);
        }

        if (duplicates.size > 0) {
          return {
            ...baseResult,
            status: "failed",
            details: `Duplicate template declarations in FilingIndicators.csv: ${Array.from(duplicates).join(", ")}`
          };
        }

        return baseResult;
      }

      case 704: {
        // Only boolean values allowed in FilingIndicators
        const reportsPath = findReportsDirectory(inputCsvDir);
        const filingIndicatorsPath = path.join(reportsPath, "FilingIndicators.csv");

        if (!fs.existsSync(filingIndicatorsPath)) {
          return {
            ...baseResult,
            status: "skipped",
            details: "FilingIndicators.csv not found"
          };
        }

        let fileContent = fs.readFileSync(filingIndicatorsPath, "utf8");
        // Remove BOM if present
        if (fileContent.charCodeAt(0) === 0xfeff) {
          fileContent = fileContent.slice(1);
        }

        const lines = fileContent.split("\n").filter((line) => line.trim() !== "");
        const invalidLines: { line: number; value: string }[] = [];

        // Skip header
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const parts = line.split(",");
          if (parts.length !== 2) continue; // Already checked in rule 702

          const filedValue = parts[1].trim();
          if (!isBoolean(filedValue)) {
            invalidLines.push({ line: i + 1, value: filedValue });
          }
        }

        if (invalidLines.length > 0) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid boolean values in FilingIndicators.csv: ${invalidLines.map((il) => `Line ${il.line}: ${il.value}`).join(", ")}`
          };
        }

        return baseResult;
      }

      case 705: {
        // Missing filing indicator value
        const reportsPath = findReportsDirectory(inputCsvDir);
        const filingIndicatorsPath = path.join(reportsPath, "FilingIndicators.csv");

        if (!fs.existsSync(filingIndicatorsPath)) {
          return {
            ...baseResult,
            status: "skipped",
            details: "FilingIndicators.csv not found"
          };
        }

        let fileContent = fs.readFileSync(filingIndicatorsPath, "utf8");
        // Remove BOM if present
        if (fileContent.charCodeAt(0) === 0xfeff) {
          fileContent = fileContent.slice(1);
        }

        const lines = fileContent.split("\n").filter((line) => line.trim() !== "");
        const emptyValueLines: number[] = [];

        // Skip header
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const parts = line.split(",");
          if (parts.length !== 2) continue; // Already checked in rule 702

          const template = parts[0].trim();
          const filedValue = parts[1].trim();

          if (template === "" || filedValue === "") {
            emptyValueLines.push(i + 1);
          }
        }

        if (emptyValueLines.length > 0) {
          return {
            ...baseResult,
            status: "failed",
            details: `Missing template code or filing value in FilingIndicators.csv lines: ${emptyValueLines.join(", ")}`
          };
        }

        return baseResult;
      }

      case 714: {
        // entityID parameter must exist and match filename
        if (!csvData.parameters.entityID) {
          return {
            ...baseResult,
            status: "failed",
            details: "Missing entityID parameter"
          };
        }

        // We can't validate against filename since we don't have the original filename
        // in this context, but we can check if entityID exists
        return baseResult;
      }

      case 720: {
        // Valid table names must be used as CSV filenames
        const reportsPath = findReportsDirectory(inputCsvDir);

        if (!fs.existsSync(reportsPath)) {
          return {
            ...baseResult,
            status: "skipped",
            details: "Reports directory not found"
          };
        }

        const validTablePattern = /^b_\d{2}\.\d{2}\.csv$/i;
        const nonTableFiles = ["FilingIndicators.csv", "parameters.csv", "report.json"];

        const files = fs.readdirSync(reportsPath);
        const invalidTableFiles = files.filter((file) => {
          // Skip non-table files
          if (nonTableFiles.includes(file)) return false;

          // Skip directories
          if (fs.statSync(path.join(reportsPath, file)).isDirectory()) return false;

          // Check if file follows valid table name pattern
          return !validTablePattern.test(file);
        });

        if (invalidTableFiles.length > 0) {
          return {
            ...baseResult,
            status: "failed",
            details: `Invalid table filenames: ${invalidTableFiles.join(", ")}`
          };
        }

        return baseResult;
      }

      // Additional Plain-csv rules (801-808)
      case 801: // All header column codes must be defined in taxonomy
        return {
          ...baseResult,
          status: "skipped",
          details: "Cannot validate against DPM taxonomy in current context"
        };

      case 802: {
        // No column can be reported twice
        const duplicateColumns = new Map<string, string[]>();

        // Check each table
        for (const [tableName, tableData] of Object.entries(csvData.tables)) {
          if (!tableData.metadata || !tableData.metadata.headers) continue;

          const headers = tableData.metadata.headers;
          const uniqueHeaders = new Set<string>();
          const duplicates: string[] = [];

          for (const header of headers) {
            if (uniqueHeaders.has(header)) {
              duplicates.push(header);
            }
            uniqueHeaders.add(header);
          }

          if (duplicates.length > 0) {
            duplicateColumns.set(tableName, duplicates);
          }
        }

        if (duplicateColumns.size > 0) {
          const details = Array.from(duplicateColumns.entries())
            .map(([table, cols]) => `${table}: ${cols.join(", ")}`)
            .join("; ");

          return {
            ...baseResult,
            status: "failed",
            details: `Duplicate columns found: ${details}`
          };
        }

        return baseResult;
      }

      case 803: // For closed tables, all row identifiers must match DPM
      case 804: // For closed tables, no row identifier reported twice
        return {
          ...baseResult,
          status: "skipped",
          details: "Cannot validate against DPM taxonomy in current context"
        };

      case 805: {
        // For open tables, key columns cannot be empty
        // Assume c0010 is always the key column for open tables
        const keyColumn = "c0010";
        const tablesWithEmptyKeys: string[] = [];

        for (const [tableName, tableData] of Object.entries(validationData)) {
          for (const [rowIdx, row] of tableData.entries()) {
            if (!row[keyColumn] || row[keyColumn] === "") {
              tablesWithEmptyKeys.push(`${tableName} (row ${Number(rowIdx) + 1})`);
            }
          }
        }

        if (tablesWithEmptyKeys.length > 0) {
          return {
            ...baseResult,
            status: "failed",
            details: `Empty key columns found in: ${tablesWithEmptyKeys.join(", ")}`
          };
        }

        return baseResult;
      }

      case 807: // Report must follow key-foreign key constraints
        return {
          ...baseResult,
          status: "skipped",
          details: "Cannot validate against DPM constraints in current context"
        };

      case 808: {
        // All filing indicators must be true
        const reportsPath = findReportsDirectory(inputCsvDir);
        const filingIndicatorsPath = path.join(reportsPath, "FilingIndicators.csv");

        if (!fs.existsSync(filingIndicatorsPath)) {
          return {
            ...baseResult,
            status: "skipped",
            details: "FilingIndicators.csv not found"
          };
        }

        const filingIndicators = parseFilingIndicators(filingIndicatorsPath);
        const falseIndicators = filingIndicators.filter((fi) => !fi.filed).map((fi) => fi.template);

        if (falseIndicators.length > 0) {
          return {
            ...baseResult,
            status: "failed",
            details: `Filing indicators set to false: ${falseIndicators.join(", ")}`
          };
        }

        return baseResult;
      }

      default:
        return {
          ...baseResult,
          status: "skipped",
          details: `Rule ${rule.id} not implemented`
        };
    }
  } catch (error: any) {
    console.error(`Error validating rule ${rule.id}:`, error.message);
    return {
      ...baseResult,
      status: "error",
      details: `Error during validation: ${error.message}`
    };
  }
}

/**
 * Clean up temporary directories
 */
function cleanupTempDir(tempDir: string): void {
  try {
    // Recursively delete the temp directory
    if (fs.existsSync(tempDir)) {
      const files = fs.readdirSync(tempDir);
      for (const file of files) {
        const curPath = path.join(tempDir, file);
        if (fs.lstatSync(curPath).isDirectory()) {
          cleanupTempDir(curPath);
        } else {
          fs.unlinkSync(curPath);
        }
      }
      fs.rmdirSync(tempDir);
      console.log(`Cleaned up temporary directory: ${tempDir}`);
    }
  } catch (error: any) {
    console.error(`Error cleaning up temp directory: ${error.message}`);
  }
}

/**
 * Main function to validate technical rules
 */
export function validateTechnical(
  validationData: InputData,
  rulesFilePath: string,
  csvData: CsvReportData,
  zipFilePath?: string
): TechnicalValidationResult[] {
  console.log(`Starting technical validation using rules from: ${rulesFilePath}`);

  // Define the path to the zip file
  const zipPath = zipFilePath || path.join(process.cwd(), "deliverable.zip");

  if (!fs.existsSync(zipPath)) {
    throw new Error(`Zip file not found at: ${zipPath}`);
  }

  console.log(`Found zip file: ${zipPath}`);

  // Extract the zip to a temporary directory
  const extractedDir = extractZipFile(zipPath);

  try {
    // Load rules
    const rules = loadTechnicalRules(rulesFilePath);
    console.log(`Loaded ${rules.length} technical validation rules.`);

    const results: TechnicalValidationResult[] = [];

    // Apply each rule
    for (const rule of rules) {
      console.log(`\nProcessing technical rule: ${rule.id} (${rule.category})`);
      const result = validateRule(rule, extractedDir, validationData, csvData, zipPath);
      results.push(result);
    }

    console.log(`\nTechnical validation finished. Total results: ${results.length}`);

    // Report on issues
    const issues = results.filter((r) => r.status === "failed" || r.status === "error");
    console.log(`Found ${issues.length} technical issues.`);
    issues.forEach((r) => console.log(`- Rule ${r.ruleCode} (${r.status}): ${r.details || r.description}`));

    return results;
  } finally {
    // Clean up the temporary directory
    cleanupTempDir(extractedDir);
  }
}
