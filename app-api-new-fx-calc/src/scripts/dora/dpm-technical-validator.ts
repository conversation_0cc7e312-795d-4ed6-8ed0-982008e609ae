import * as fs from "fs";
import * as path from "path";

/**
 * Interface for technical validation rules
 */
interface TechnicalRule {
  ruleCode: number;
  category: string;
  ruleDescription: string;
}

/**
 * Interface for validation results
 */
interface TechnicalValidationResult {
  ruleCode: number;
  status: "passed" | "failed" | "skipped";
  category: string;
  description: string;
  details?: string;
}

/**
 * Interface for input data structure
 */
type InputData = Record<string, Record<string, any>[]>;

/**
 * Interface for CSV report data
 */
interface CsvReportData {
  parameters: Record<string, string>;
  tables: Record<string, any>;
  reportInfo: {
    fileName: string;
    tables: string[];
  };
}

/**
 * Rules to skip during validation
 */
const SKIP_RULES: number[] = [
  305, // Numeric fact must not be reported with a string value
  503,
  510,
  512
];

/**
 * Date Field mapping - nested Maps for efficient table/column lookup
 * First level: table name (with 't' prefix exactly as in data)
 * Second level: column name with field description
 */
const DATE_FIELDS = new Map([
  ["tB_01.01", new Map([["c0060", "Date of reporting"]])],
  [
    "tB_01.02",
    new Map([
      ["c0070", "Last date of record entry"],
      ["c0080", "Date of integration"],
      ["c0090", "Date of deletion"]
    ])
  ],
  [
    "tB_02.02",
    new Map([
      ["c0070", "Start date"],
      ["c0080", "End date"]
    ])
  ],
  ["tB_06.01", new Map([["c0070", "Date of last assessment of criticality or importance of the function"]])],
  ["tB_07.01", new Map([["c0070", "Date of the last audit"]])]
]);

/**
 * Boolean Field mapping - nested Maps for efficient table/column lookup
 * First level: table name (with 't' prefix exactly as in data)
 * Second level: column name with field description
 */
const BOOLEAN_FIELDS = new Map([
  ["tB_02.03", new Map([["c0030", "Link"]])],
  ["tB_03.01", new Map([["c0030", "Link"]])],
  ["tB_03.03", new Map([["c0031", "Link"]])]
]);

/**
 * Key Value Fields mapping - Maps for tables with key fields
 * First level: table name (with 't' prefix exactly as in data)
 * Second level: Set of column names that are key fields
 */
const KEY_FIELDS = new Map([
  ["tB_01.01", new Set(["c0010"])],
  ["tB_01.02", new Set(["c0010"])],
  ["tB_01.03", new Set(["c0010", "c0020"])],
  ["tB_02.01", new Set(["c0010"])],
  ["tB_02.02", new Set(["c0010", "c0020", "c0030", "c0050", "c0060", "c0130", "c0150", "c0160"])],
  ["tB_02.03", new Set(["c0010", "c0020"])],
  ["tB_03.01", new Set(["c0010", "c0020"])],
  ["tB_03.02", new Set(["c0010", "c0020"])],
  ["tB_03.03", new Set(["c0010", "c0020"])],
  ["tB_04.01", new Set(["c0010", "c0020", "c0040"])],
  ["tB_05.01", new Set(["c0010"])],
  ["tB_05.02", new Set(["c0010", "c0020", "c0030", "c0050", "c0060"])],
  ["tB_06.01", new Set(["c0010", "c0040"])],
  ["tB_07.01", new Set(["c0010", "c0020", "c0040"])]
]);

/**
 * Loads technical validation rules from a JSON file
 */
function loadTechnicalRules(filePath: string): TechnicalRule[] {
  try {
    const absolutePath = path.resolve(filePath);
    if (!fs.existsSync(absolutePath)) {
      throw new Error(`Technical validation rules file not found: ${absolutePath}`);
    }
    const fileContent = fs.readFileSync(absolutePath, "utf-8");
    return JSON.parse(fileContent);
  } catch (error: any) {
    console.error(`Error loading technical validation rules from ${filePath}:`, error.message);
    throw error;
  }
}

/**
 * Helper to check if a value is a valid date in yyyy-mm-dd format
 */
function isValidDateFormat(value: string): boolean {
  // Check if string is in format yyyy-mm-dd
  return /^\d{4}-\d{2}-\d{2}$/.test(value);
}

/**
 * Helper to check if a value is an integer
 */
function isInteger(value: any): boolean {
  if (typeof value === "number") return Number.isInteger(value);
  if (typeof value === "string") {
    const parsed = parseInt(value, 10);
    return !isNaN(parsed) && parsed.toString() === value;
  }
  return false;
}

/**
 * Helper to check if a value is a valid boolean
 */
function isValidBoolean(value: any): boolean {
  if (typeof value === "boolean") return true;
  if (typeof value === "number") return value === 0 || value === 1;
  if (typeof value === "string") {
    const lowerValue = value.toLowerCase();
    return lowerValue === "true" || lowerValue === "false" || lowerValue === "1" || lowerValue === "0";
  }
  return false;
}

/**
 * Validates data against technical rules
 */
function validateAgainstTechnicalRules(
  rule: TechnicalRule,
  validationData: InputData,
  csvData?: CsvReportData
): TechnicalValidationResult {
  const baseResult: TechnicalValidationResult = {
    ruleCode: rule.ruleCode,
    category: rule.category,
    description: rule.ruleDescription,
    status: "passed"
  };

  // Skip rules that are in the SKIP_RULES array
  if (SKIP_RULES.includes(rule.ruleCode)) {
    return {
      ...baseResult,
      status: "skipped",
      details: `Rule ${rule.ruleCode} is configured to be skipped`
    };
  }

  try {
    switch (rule.ruleCode) {
      // Rule 305: Numeric fact must not be reported with a string value
      case 305: {
        // For each table and each row, check if numeric columns contain string values
        for (const [tableName, tableData] of Object.entries(validationData)) {
          for (const [rowIndex, row] of tableData.entries()) {
            for (const [colName, value] of Object.entries(row)) {
              // Simple heuristic: if the column starts with 'c' followed by digits,
              // and is not one of known date/string columns, assume it should be numeric
              if (
                colName.match(/^c\d+$/) &&
                typeof value === "string" &&
                !isInteger(value) &&
                !isValidDateFormat(value) &&
                !colName.match(/^c00[1-3]0$/) // Exclude common string columns like c0010, c0020, c0030
              ) {
                return {
                  ...baseResult,
                  status: "failed",
                  details: `Table ${tableName}, row ${rowIndex + 1}, column ${colName} has non-numeric value: "${value}"`
                };
              }
            }
          }
        }
        return baseResult;
      }

      // Rule 306: XBRL instance documents must use UTF-8 encoding
      case 306: {
        // This is actually a check for the file encoding which we can't verify at this level
        // Assuming it's already in UTF-8 since we're processing it
        return baseResult;
      }

      // Rule 320: Reference date validation
      case 320: {
        if (!csvData || !csvData.parameters.refPeriod) {
          return {
            ...baseResult,
            status: "skipped",
            details: "No reference date parameter found in CSV data"
          };
        }

        const refPeriod = csvData.parameters.refPeriod;
        if (!isValidDateFormat(refPeriod)) {
          return {
            ...baseResult,
            status: "failed",
            details: `Reference date ${refPeriod} is not in required format yyyy-mm-dd`
          };
        }

        // Additional validations for reference date could be added here
        return baseResult;
      }

      // Rule 330: Date type metric must be reported with format 'yyyy-mm-dd'
      case 330: {
        for (const [tableName, tableData] of Object.entries(validationData)) {
          // Check if we have date fields for this table (using exact table name as key)
          const tableFields = DATE_FIELDS.get(tableName);
          if (!tableFields) continue; // No date fields in this table

          for (const [rowIndex, row] of tableData.entries()) {
            for (const [colName, value] of Object.entries(row)) {
              // Get field description if this is a date field
              const fieldDescription = tableFields.get(colName);

              // Only validate if this is a date field
              if (
                fieldDescription &&
                typeof value === "string" &&
                !isValidDateFormat(value) &&
                !value.startsWith("eba_") // Skip coded values
              ) {
                return {
                  ...baseResult,
                  status: "failed",
                  details: `Table ${tableName}, row ${rowIndex}, column ${colName} (${fieldDescription}) has invalid date format: "${value}"`
                };
              }
            }
          }
        }
        return baseResult;
      }

      // Rule 331: Integer type metric must be reported with an integer
      case 331: {
        // This is similar to rule 305, but more specific to integers
        for (const [tableName, tableData] of Object.entries(validationData)) {
          for (const [rowIndex, row] of tableData.entries()) {
            for (const [colName, value] of Object.entries(row)) {
              // If the value is numeric but not an integer
              if (
                value !== null &&
                value !== undefined &&
                value !== "" &&
                typeof value !== "boolean" &&
                !isInteger(value) &&
                typeof value !== "string" &&
                !colName.match(/^c00[1-3]0$/) // Exclude common string columns
              ) {
                return {
                  ...baseResult,
                  status: "failed",
                  details: `Table ${tableName}, row ${Number(rowIndex) + 1}, column ${colName} has non-integer value: "${value}"`
                };
              }
            }
          }
        }
        return baseResult;
      }

      // Rule 332: Boolean type metric must be reported with a boolean value
      case 332: {
        // Without metadata on which fields are boolean, we'll skip this check
        for (const [tableName, tableData] of Object.entries(validationData)) {
          // Check if we have boolean fields for this table
          const tableFields = BOOLEAN_FIELDS.get(tableName);
          if (!tableFields) continue; // No boolean fields in this table

          for (const [rowIndex, row] of tableData.entries()) {
            for (const [colName, value] of Object.entries(row)) {
              // Get field description if this is a boolean field
              const fieldDescription = tableFields.get(colName);

              // Only validate if this is a boolean field and value is not empty
              if (
                fieldDescription &&
                value !== null &&
                value !== undefined &&
                value !== "" &&
                !isValidBoolean(value)
              ) {
                return {
                  ...baseResult,
                  status: "failed",
                  details: `Table ${tableName}, row ${rowIndex}, column ${colName} (${fieldDescription}) has invalid boolean value: "${value}"`
                };
              }
            }
          }
        }
        return baseResult;
      }

      // Rule 333: trueItemType must be reported with true value
      case 333: {
        // Check boolean fields - they must be true if present
        for (const [tableName, tableData] of Object.entries(validationData)) {
          // Check if we have boolean fields for this table
          const tableFields = BOOLEAN_FIELDS.get(tableName);
          if (!tableFields) continue; // No boolean fields in this table

          for (const [rowIndex, row] of tableData.entries()) {
            for (const [colName, value] of Object.entries(row)) {
              // Get field description if this is a boolean field
              const fieldDescription = tableFields.get(colName);

              // Only validate if this is a boolean field and has a non-empty value
              if (fieldDescription && value !== null && value !== undefined && value !== "") {
                // Check if the value is explicitly true (not just any boolean)
                const isTrue =
                  value === true ||
                  value === 1 ||
                  value === "true" ||
                  value === "1" ||
                  (typeof value === "string" && value.toLowerCase() === "true");

                if (!isTrue) {
                  return {
                    ...baseResult,
                    status: "failed",
                    details: `Table ${tableName}, row ${rowIndex}, column ${colName} (${fieldDescription}) must be reported with true value, found: "${value}"`
                  };
                }
              }
            }
          }
        }
        return baseResult;
      }

      // Rule 503: Value restrictions for extensible properties
      case 503: {
        // Without a value hierarchy definition, we'll skip this check
        return {
          ...baseResult,
          status: "skipped",
          details: "Cannot validate against hierarchy without definitions"
        };
      }

      // Rule 505: Each report must contain a report.json
      case 505: {
        if (!csvData) {
          return {
            ...baseResult,
            status: "skipped",
            details: "No CSV data provided"
          };
        }

        // Check if the report.json file exists
        const reportJsonExists = csvData.reportInfo && csvData.reportInfo.fileName;
        if (!reportJsonExists) {
          return {
            ...baseResult,
            status: "failed",
            details: "No report.json information found in the CSV data"
          };
        }

        return baseResult;
      }

      // Rule 510: Filing indicator values must be valid
      case 510: {
        // Without filing indicator definitions, we'll skip this check
        return {
          ...baseResult,
          status: "skipped",
          details: "Cannot validate filing indicators without definitions"
        };
      }

      // Rule 512: Module reference validation
      case 512: {
        // Without module references, we'll skip this check
        return {
          ...baseResult,
          status: "skipped",
          details: "Cannot validate module references"
        };
      }

      // Rule 514: Entity type with qualifier validation
      case 514: {
        if (!csvData || !csvData.parameters.refPeriod) {
          return {
            ...baseResult,
            status: "skipped",
            details: "No reference date parameter found in CSV data"
          };
        }

        // Check if reference date is on or after 2022-12-31
        const refPeriod = csvData.parameters.refPeriod;
        const refDate = new Date(refPeriod);
        const checkDate = new Date("2022-12-31");

        if (refDate >= checkDate) {
          // Check if entity ID has a '.' qualifier
          const entityID = csvData.parameters.entityID;
          if (entityID && !entityID.includes(".")) {
            return {
              ...baseResult,
              status: "failed",
              details: `Entity ID ${entityID} requires a '.' qualifier for reference date ${refPeriod}`
            };
          }
        }

        return baseResult;
      }

      // Rule 806: No duplicate keys in open tables
      case 806: {
        // Check for duplicate key values in tables that have defined key fields
        for (const [tableName, tableData] of Object.entries(validationData)) {
          if (tableData.length <= 1) continue; // No duplicates possible with 0 or 1 rows

          // Get the key columns for this table
          const keyColumns = KEY_FIELDS.get(tableName);
          if (!keyColumns || keyColumns.size === 0) continue; // Skip tables without defined key fields

          // Set to track unique composite keys
          const keyValues = new Set<string>();

          for (const [rowIndex, row] of tableData.entries()) {
            // Create a composite key from all key fields
            // If there are multiple key columns, join them with a separator
            const keyParts: string[] = [];
            let hasAllKeyFields = true;

            // Build key parts from all defined key columns for this table
            for (const keyCol of keyColumns) {
              if (row[keyCol] !== undefined && row[keyCol] !== null) {
                keyParts.push(String(row[keyCol]));
              } else {
                hasAllKeyFields = false;
                break; // Skip this row if any key field is missing
              }
            }

            // Skip validation if any required key field is missing
            if (!hasAllKeyFields) continue;

            // Create composite key string
            const compositeKey = keyParts.join("::");

            // Check for duplicate
            if (keyValues.has(compositeKey)) {
              return {
                ...baseResult,
                status: "failed",
                details: `Table ${tableName} has duplicate key value(s) at row ${Number(rowIndex) + 1}. Composite key: "${compositeKey}"`
              };
            }

            // Add the key to the set for future duplicate checks
            keyValues.add(compositeKey);
          }
        }

        return baseResult;
      }

      default:
        return {
          ...baseResult,
          status: "skipped",
          details: `Technical rule ${rule.ruleCode} not implemented`
        };
    }
  } catch (error: any) {
    console.error(`Error validating rule ${rule.ruleCode}:`, error.message);
    return {
      ...baseResult,
      status: "failed",
      details: `Error during validation: ${error.message}`
    };
  }
}

/**
 * Main function to validate data against technical rules
 */
export function validateTechnical(
  validationData: InputData,
  rulesFilePath: string,
  csvData?: CsvReportData
): TechnicalValidationResult[] {
  console.log(`Starting technical validation using rules from: ${rulesFilePath}`);
  const rules = loadTechnicalRules(rulesFilePath);
  console.log(`Loaded ${rules.length} technical validation rules.`);

  const results: TechnicalValidationResult[] = [];

  for (const rule of rules) {
    console.log(`\nProcessing technical rule: ${rule.ruleCode} (${rule.category})`);
    const result = validateAgainstTechnicalRules(rule, validationData, csvData);
    results.push(result);
  }

  console.log(`\nTechnical validation finished. Total results: ${results.length}`);
  // Filter issues: exclude rules that are configured to be skipped
  const issues = results.filter(
    (r) => r.status === "failed" || (r.status === "skipped" && !r.details?.includes("configured to be skipped"))
  );
  console.log(`Found ${issues.length} technical issues.`);
  issues.forEach((r) => console.log(`- Rule ${r.ruleCode} (${r.status}): ${r.details || r.description}`));

  // Add success message with emoji if no issues found
  if (issues.length === 0) {
    console.log("\n🎉 Technical Validation completed successfully! All rules passed! 🎊");
  }

  return results;
}
