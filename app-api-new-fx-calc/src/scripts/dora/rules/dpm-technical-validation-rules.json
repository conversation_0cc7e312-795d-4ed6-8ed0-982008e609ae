[{"ruleCode": 305, "category": "DPM Technical Checks", "ruleDescription": "Numeric fact must not be reported with a string value"}, {"ruleCode": 306, "category": "DPM Technical Checks", "ruleDescription": "XBRL instance documents must use UTF-8 encoding."}, {"ruleCode": 320, "category": "DPM Technical Checks", "ruleDescription": "Reference date inside an instance as well as within filename must be valid (FR 2.13, FR 2.10)"}, {"ruleCode": 330, "category": "DPM Technical Checks", "ruleDescription": "Date type metric must be reported with format 'yyyy-mm-dd'"}, {"ruleCode": 331, "category": "DPM Technical Checks", "ruleDescription": "Integer type metric must be reported with an integer"}, {"ruleCode": 332, "category": "DPM Technical Checks", "ruleDescription": "Boolean type metric must be reported with a boolean value (true/false/1/0)"}, {"ruleCode": 333, "category": "DPM Technical Checks", "ruleDescription": "trueItemType must be reported with true value"}, {"ruleCode": 503, "category": "DPM Technical Checks", "ruleDescription": "Extensible main property and explicit key dimension must only be reported with one of the restricted value of a hierarchy"}, {"ruleCode": 505, "category": "DPM Technical Checks", "ruleDescription": "Each report must contain a report.json and this one must point to entrypoint json file for the module"}, {"ruleCode": 510, "category": "DPM Technical Checks", "ruleDescription": "The values of filing indicators MUST only be those given by the label resources with the role http://www.eurofiling.info/xbrl/role/filing-indicator-code (the values are also present in TemplateCode field of Template table in DPM) (FR 1.6.3)"}, {"ruleCode": 512, "category": "DPM Technical Checks", "ruleDescription": "The module referenced by the instance document must not refer to a taxonomy having FromDate = ToDate"}, {"ruleCode": 514, "category": "DPM Technical Checks", "ruleDescription": "Reporting subject must include entity type with '.' qualifier if the reference date is on or after 2022-12-31, and the module code does not contain CON or IND"}, {"ruleCode": 806, "category": "Additional Plain-csv", "ruleDescription": "For open tables, you cannot have multiple rows with identical key values"}]