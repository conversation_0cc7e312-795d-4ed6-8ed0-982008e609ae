[{"id": 101, "category": "Reception", "description": "Report filename extension must be zip"}, {"id": 102, "category": "Reception", "description": "SFTP report file size must not exceed 10GB"}, {"id": 103, "category": "Reception", "description": "ZIP consistency (checksum, unzips correctly)."}, {"id": 104, "category": "Reception", "description": "Filename allowed characters: alphanumeric plus hyphens ('-'), underscores ('_') and dots ('.')"}, {"id": 105, "category": "Reception", "description": "Filename structure (ReportSubject_Country_FrameworkCodeModuleVersion_Module_ReferenceDate_CreationTimestamp)"}, {"id": 106, "category": "Reception", "description": "Filename module inconsistency with the report category  (SUP, REM, RES,PAY,DORA,PILLAR3)"}, {"id": 107, "category": "Reception", "description": "For CA submitted reports, the submission path country must match the filename country code"}, {"id": 109, "category": "Reception", "description": "Filename reference date is valid date with format 'yyyy-mm-dd'"}, {"id": 110, "category": "Reception", "description": "Each submitted report filename must be unique - duplicate filenames are not allowed"}, {"id": 111, "category": "Reception", "description": "Reference date in the filename cannot be in the future"}, {"id": 115, "category": "Reception", "description": "DORA submitted report files must be a zip containing plain-csv "}, {"id": 701, "category": "CSV", "description": "CSV zip file must contain reports/report.json, reports/FilingIndicators.csv and reports/parameters.csv  files and must be non-empty"}, {"id": 702, "category": "CSV", "description": "Invalid declarations in FilingIndicators.csv - wrong number of columns reported"}, {"id": 703, "category": "CSV", "description": "Each template can be declared only once in FilingIndicators.csv"}, {"id": 704, "category": "CSV", "description": "Only boolean values (true, false, 1, 0) are allowed for any template code in FilingIndicators.csv"}, {"id": 705, "category": "CSV", "description": "Missing filing indicator value - the true/false value is missing - only template code is present"}, {"id": 714, "category": "CSV", "description": "The 'entityID' parameter must exist and its value must match filename"}, {"id": 720, "category": "CSV", "description": "Valid table names must be used as .csv filenames"}, {"id": 801, "category": "Additional Plain-csv", "description": "All the header column codes must be defined in the taxonomy json files (in DPM)"}, {"id": 802, "category": "Additional Plain-csv", "description": "No column can be reported twice"}, {"id": 803, "category": "Additional Plain-csv", "description": "For closed tables, all the row identifier must match those in the dpm"}, {"id": 804, "category": "Additional Plain-csv", "description": "For closed tables, No row identifier can be reported twice"}, {"id": 805, "category": "Additional Plain-csv", "description": "For open tables, the key columns values cannot be empty"}, {"id": 807, "category": "Additional Plain-csv", "description": "The reported data must follow the key-foreign key contraints defined in the DPM"}, {"id": 808, "category": "Additional Plain-csv", "description": "All filing indicators have to be sent as true - and the templates reported empty if no data is present"}]