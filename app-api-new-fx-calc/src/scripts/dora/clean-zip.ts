import * as fs from "fs";
import * as path from "path";
import { execSync } from "child_process";

/**
 * Cleans a zip file by removing .DS_Store files and their variants
 * @param zipFilePath Path to the original zip file
 * @param outputZipPath Path where the cleaned zip file will be saved (defaults to overwriting the original)
 * @returns String with the output zip path when done
 */
export function cleanZipFile(zipFilePath: string, outputZipPath: string = ""): string {
  try {
    console.log(`\n=== Cleaning zip file: ${zipFilePath} ===`);

    // If no output path is provided, use a temporary file and then overwrite the original
    const useOriginal = !outputZipPath;
    if (useOriginal) {
      const zipDir = path.dirname(zipFilePath);
      outputZipPath = path.join(zipDir, `_temp_${path.basename(zipFilePath)}`);
    }

    // Make sure the zip file exists
    if (!fs.existsSync(zipFilePath)) {
      throw new Error(`Zip file not found: ${zipFilePath}`);
    }

    // Create a temporary directory for extraction
    const tempDir = path.join(path.dirname(zipFilePath), "temp_extract");
    if (fs.existsSync(tempDir)) {
      // Clean up any existing temp dir
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    fs.mkdirSync(tempDir, { recursive: true });

    try {
      // Extract the zip file
      console.log(`Extracting zip to: ${tempDir}`);
      execSync(`unzip -q "${zipFilePath}" -d "${tempDir}"`);

      // Find and remove all .DS_Store files and their variants
      console.log("Removing macOS system files (.DS_Store, ._.DS_Store, etc.)...");
      let macFilesCount = 0;

      // Function to recursively search and remove macOS system files
      const removeMacSystemFiles = (dir: string): number => {
        let count = 0;
        const entries = fs.readdirSync(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory()) {
            // Recursively process subdirectories
            count += removeMacSystemFiles(fullPath);
          } else if (
            entry.name === ".DS_Store" ||
            entry.name === "._.DS_Store" ||
            entry.name.startsWith("._") || // Resource fork files
            entry.name.startsWith("._.") // Other variants
          ) {
            // Remove macOS system files
            fs.unlinkSync(fullPath);
            count++;
            console.log(`Removed: ${fullPath}`);
          }
        }

        return count;
      };

      macFilesCount = removeMacSystemFiles(tempDir);
      console.log(`Removed ${macFilesCount} macOS system files`);

      // Create a new zip file without the macOS system files
      console.log(`Creating cleaned zip file: ${outputZipPath}`);

      // Change to the temp directory to zip relative paths
      const currentDir = process.cwd();
      process.chdir(tempDir);

      try {
        execSync(`zip -r "${outputZipPath}" .`);
      } finally {
        // Change back to the original directory
        process.chdir(currentDir);
      }

      // If we're using the original filename, move the temp file to overwrite the original
      if (useOriginal) {
        // Replace the original file with the cleaned version
        fs.renameSync(outputZipPath, zipFilePath);
        console.log("Original zip file overwritten with cleaned version");
        outputZipPath = zipFilePath; // Set output path to the original for return value
      }

      console.log("Zip file cleaned successfully!");
      return outputZipPath;
    } finally {
      // Clean up the temporary directory
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
        console.log("Cleaned up temporary extraction directory");
      }
    }
  } catch (error: any) {
    console.error(`Error cleaning zip file: ${error.message}`);
    throw error;
  }
}

/**
 * Extract a zip file to a specified directory
 * @param zipFilePath Path to the zip file
 * @param extractDir Directory to extract to (will be created if it doesn't exist)
 */
export function extractZipFile(zipFilePath: string, extractDir: string): void {
  console.log(`\n=== Extracting zip file: ${zipFilePath} to ${extractDir} ===`);

  // Make sure the zip file exists
  if (!fs.existsSync(zipFilePath)) {
    throw new Error(`Zip file not found: ${zipFilePath}`);
  }

  // Create the extract directory if it doesn't exist
  if (!fs.existsSync(extractDir)) {
    fs.mkdirSync(extractDir, { recursive: true });
  }

  // Extract the zip file
  execSync(`unzip -q "${zipFilePath}" -d "${extractDir}"`);
  console.log("Extraction completed successfully!");
}

// If this script is run directly
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.length < 1) {
    console.error("Usage: ts-node clean-zip.ts <zipFilePath> [outputZipPath]");
    process.exit(1);
  }

  const zipFilePath = args[0];
  const outputZipPath = args[1] || "";

  try {
    const cleanedZipPath = cleanZipFile(zipFilePath, outputZipPath);
    console.log(`Cleaned zip file saved to: ${cleanedZipPath}`);
  } catch (error: any) {
    console.error(`\nError: ${error.message}`);
    process.exit(1);
  }
}
