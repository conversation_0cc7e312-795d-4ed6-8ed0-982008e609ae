import {
  <PERSON><PERSON><PERSON>er,
  ResponseEntrySummary,
  ResponseInvestmentProduct,
  ResponseLoanProduct,
  RESPONSE_SIZES,
  InvestmentProductJsonFields,
  LoanProductJsonFields
} from "../types/response-types";
import { UNIT_SEPARATOR, COMPANY } from "../constants";
import Encoder from "./encoder";
import * as fs from "fs";
import * as path from "path";

export class ResponseFormatter {
  /**
   * ==============
   * PUBLIC METHODS
   * ==============
   */

  /**
   * @description Generates and writes the response file
   * @param outputDir Directory where the file will be written
   * @param year Year for the filename
   * @param month Month for the filename
   * @param version Version number for the filename
   * @param data Response data to be written
   * @returns The full path of the generated file
   */
  public async generateFile(
    outputDir: string,
    year: number,
    month: number,
    version: number,
    data: {
      totalEntries: number;
      entries: Array<{
        summary: {
          afm: string;
          referenceDate: string;
          requestType: string;
          productRecordCount: string;
        };
        products: Array<InvestmentProductJsonFields | LoanProductJsonFields>;
      }>;
    }
  ): Promise<string> {
    // Generate the filename
    const filename = this._generateFilename(year, month, version);
    const filePath = path.join(outputDir, filename.replace(".gpg", "")); // Remove .gpg extension as it's added after encryption

    // Format the content
    const content = this.formatResponse(data);

    // Encode to ISO-8859-7
    const encodedContent = Encoder.encodeToISO8859_7(content);

    // Ensure the output directory exists
    await fs.promises.mkdir(outputDir, { recursive: true });

    // Write the file
    await fs.promises.writeFile(filePath, encodedContent);

    return filePath;
  }

  /**
   * @description Validates that a formatted record matches its expected size
   */
  public validateRecordSize(record: string, type: keyof typeof RESPONSE_SIZES): boolean {
    const expectedSize = RESPONSE_SIZES[type];
    const actualSize = Buffer.from(record, "utf8").length;

    if (actualSize !== expectedSize) {
      throw new Error(`Invalid record size for ${type}. Expected ${expectedSize} bytes, got ${actualSize} bytes`);
    }

    return true;
  }

  /**
   * @description Formats a complete response including header, entries and their products
   */
  public formatResponse(data: {
    totalEntries: number;
    entries: Array<{
      summary: {
        afm: string;
        referenceDate: string;
        requestType: string;
        productRecordCount: string;
      };
      products: Array<InvestmentProductJsonFields | LoanProductJsonFields>;
    }>;
  }): string {
    let output = this._formatHeader(data.totalEntries);

    for (const entry of data.entries) {
      // Add entry summary
      output += this._formatEntrySummary(entry.summary);

      // Add all products for this entry
      for (const product of entry.products) {
        if ("investmentType" in product) {
          output += this._formatInvestmentProduct(product);
        } else {
          output += this._formatLoanProduct(product);
        }
      }
    }

    return output;
  }

  /**
   * ==============
   * PRIVATE METHODS
   * ==============
   */

  /**
   * @description Generates the filename according to the specifications:
   * XXXXXXXXX-pothendata-YYYY-MM-vNNN.gpg where:
   * - XXXXXXXXX: Company AFM
   * - YYYY-MM: Monthly catalog period
   * - NNN: Version number (padded to 3 digits)
   */
  private _generateFilename(year: number, month: number, version: number): string {
    const yearStr = year.toString();
    const monthStr = month.toString().padStart(2, "0");
    const versionStr = version.toString().padStart(3, "0");

    return `${COMPANY.AFM}-pothendata-${yearStr}-${monthStr}-v${versionStr}.gpg`;
  }

  /**
   * @description Formats the header record
   */
  private _formatHeader(totalEntries: number): string {
    const record: ResponseHeader = {
      recordType: "F",
      afm: COMPANY.AFM,
      name: COMPANY.NAME,
      totalEntries
    };

    return (
      [
        record.recordType,
        this._formatString(record.afm, 9),
        this._formatString(record.name, 100),
        this._formatNumber(record.totalEntries, 7, 0)
      ].join("") + UNIT_SEPARATOR
    );
  }

  /**
   * @description Formats an entry summary record
   */
  private _formatEntrySummary(summary: Omit<ResponseEntrySummary, "recordType">): string {
    const record: ResponseEntrySummary = {
      recordType: "O",
      ...summary
    };

    return (
      [
        record.recordType,
        this._formatString(record.afm, 9),
        this._formatString(record.referenceDate, 8),
        record.requestType,
        this._formatString(record.productRecordCount, 3)
      ].join("") + UNIT_SEPARATOR
    );
  }

  /**
   * @description Formats an investment product record
   */
  private _formatInvestmentProduct(product: InvestmentProductJsonFields): string {
    const record: ResponseInvestmentProduct = {
      recordType: "I",
      internalCode: product.investmentInternalCode,
      investmentType: product.investmentType,
      isin: product.investmentIsin || null,
      title: product.investmentTitle,
      quantity: product.investmentQuantity,
      valuation: product.investmentValuation,
      currency: product.investmentCurrency
    };

    return (
      [
        record.recordType,
        this._formatString(record.internalCode, 60),
        this._formatEnumValue(record.investmentType),
        this._formatString(record.isin || "", 12),
        this._formatString(record.title, 100),
        this._formatNumber(record.quantity, 10, 6),
        this._formatNumber(record.valuation, 10, 2),
        this._formatString(record.currency, 3)
      ].join("") + UNIT_SEPARATOR
    );
  }

  /**
   * @description Formats a loan product record
   */
  private _formatLoanProduct(product: LoanProductJsonFields): string {
    const record: ResponseLoanProduct = {
      recordType: "L",
      loanType: product.loanType,
      internalCode: product.loanInternalCode,
      initialAmount: product.loanInitAmount,
      currency: product.loanCurrency,
      balance: product.loanBalance,
      balancePrefix: product.loanPrefix,
      accountStartDate: product.loanAccountStartDate,
      accountEndDate: product.loanAccountEndDate,
      chargesExpenses: product.loanChargesExpenses,
      chargesCurrency: product.loanChargesCurrency
    };

    return (
      [
        record.recordType,
        this._formatEnumValue(record.loanType),
        this._formatString(record.internalCode, 60),
        this._formatNumber(record.initialAmount, 10, 2),
        this._formatString(record.currency, 3),
        this._formatNumber(record.balance, 10, 2),
        record.balancePrefix,
        this._formatString(record.accountStartDate, 8),
        this._formatString(record.accountEndDate || "", 8),
        this._formatNumber(record.chargesExpenses, 10, 2),
        this._formatString(record.chargesCurrency, 3)
      ].join("") + UNIT_SEPARATOR
    );
  }

  /**
   *  @description Formats a number to a fixed-width string with padding
   */
  private _formatNumber(num: number, integerWidth: number, decimalPlaces: number): string {
    if (decimalPlaces === 0) {
      // No decimal places needed, just return the padded integer part
      return Math.round(num).toString().padStart(integerWidth, " ");
    } else {
      const str = num.toFixed(decimalPlaces);
      const [intPart, decPart = ""] = str.split(".");
      const paddedInt = intPart.padStart(integerWidth, " ");
      return `${paddedInt}.${decPart}`;
    }
  }

  /**
   *  @description Formats a string to a fixed width with space padding
   */
  private _formatString(str: string, width: number): string {
    return (str || "").padEnd(width, " ");
  }

  /**
   *  @description Formats an enum value as a 2-digit number with padding
   */
  private _formatEnumValue(value: number): string {
    return value.toString().padStart(2, "0");
  }
}
