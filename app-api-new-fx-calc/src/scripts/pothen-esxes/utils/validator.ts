import * as fs from "fs";
import { RESPONSE_SIZES } from "../types/response-types";
import { UNIT_SEPARATOR } from "../constants";
import Encoder from "./encoder";

export default class ResponseValidator {
  private _content: string;

  constructor(filePath: string) {
    const encodedContent = fs.readFileSync(filePath);
    // Decode from ISO-8859-7 to work with the content
    this._content = Encoder.decodeFromISO8859_7(encodedContent);
  }

  /**
   * ==============
   * PUBLIC METHODS
   * ==============
   */

  /**
   * @description Validates the file against all rules
   * @returns An object with validation results
   */
  public validate(): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    try {
      // Validate header size
      this._validateHeaderSize();

      // Validate that records are properly terminated with unit separators
      this._validateRecordTerminations();

      // Validate individual record sizes
      this._validateRecordSizes();

      // Validate header content
      this._validateHeaderContent();

      // Validate entry summaries
      this._validateEntrySummaries();

      // Check that totalEntries matches the actual number of entries
      this._validateEntryCount();
    } catch (error) {
      if (error instanceof Error) {
        errors.push(error.message);
      } else {
        errors.push(`Unknown error: ${error}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * ==============
   * PRIVATE METHODS
   * ==============
   */

  /**
   * @description Validates that the header has the correct size (117 bytes)
   */
  private _validateHeaderSize(): void {
    const headerEnd = this._content.indexOf(UNIT_SEPARATOR);
    if (headerEnd === -1) {
      throw new Error("Header not properly terminated with unit separator");
    }

    const headerContent = this._content.substring(0, headerEnd);
    // Use ISO-8859-7 encoding for size calculation instead of UTF-8
    const headerSize = Encoder.encodeToISO8859_7(headerContent).length;

    if (headerSize !== RESPONSE_SIZES.HEADER) {
      throw new Error(`Header size incorrect: expected ${RESPONSE_SIZES.HEADER} bytes, got ${headerSize} bytes`);
    }
  }

  /**
   * @description Validates that all records are properly terminated with unit separators
   */
  private _validateRecordTerminations(): void {
    const recordCount = (this._content.match(new RegExp(UNIT_SEPARATOR, "g")) || []).length;
    if (recordCount === 0) {
      throw new Error("No unit separators found in the file");
    }

    // Check the last character is a unit separator
    if (this._content.charAt(this._content.length - 1) !== UNIT_SEPARATOR) {
      throw new Error("File does not end with a unit separator");
    }
  }

  /**
   * @description Validates the sizes of each record in the file
   */
  private _validateRecordSizes(): void {
    const records = this._content.split(UNIT_SEPARATOR).filter((r) => r.length > 0);

    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const recordType = record.charAt(0);

      let expectedSize: number;
      switch (recordType) {
        case "F": // Header
          expectedSize = RESPONSE_SIZES.HEADER;
          break;
        case "O": // Entry summary
          expectedSize = RESPONSE_SIZES.ENTRY_SUMMARY;
          break;
        case "I": // Investment product
          expectedSize = RESPONSE_SIZES.INVESTMENT_PRODUCT;
          break;
        case "L": // Loan product
          expectedSize = RESPONSE_SIZES.LOAN_PRODUCT;
          break;
        default:
          throw new Error(`Unknown record type '${recordType}' at position ${i}`);
      }

      // Use ISO-8859-7 encoding for size calculation instead of UTF-8
      const actualSize = Encoder.encodeToISO8859_7(record).length;
      if (actualSize !== expectedSize) {
        throw new Error(
          `Record ${i} (type ${recordType}) has incorrect size: expected ${expectedSize} bytes, got ${actualSize} bytes`
        );
      }
    }
  }

  /**
   * @description Validates the header content
   */
  private _validateHeaderContent(): void {
    const headerEnd = this._content.indexOf(UNIT_SEPARATOR);
    const header = this._content.substring(0, headerEnd);

    // Check record type is 'F'
    if (header.charAt(0) !== "F") {
      throw new Error(`Header record type must be 'F', got '${header.charAt(0)}'`);
    }

    // Check that AFM is 9 characters
    const afm = header.substring(1, 10).trim();
    if (afm.length !== 9 || !/^\d+$/.test(afm)) {
      throw new Error(`Invalid AFM in header: '${afm}'`);
    }

    // Check that totalEntries is positive
    const totalEntries = parseInt(header.substring(110).trim(), 10);
    if (isNaN(totalEntries) || totalEntries <= 0) {
      throw new Error(`Total entries must be a positive number, got: '${header.substring(110).trim()}'`);
    }
  }

  /**
   * @description Validates the entry summaries in the file
   */
  private _validateEntrySummaries(): void {
    const records = this._content.split(UNIT_SEPARATOR).filter((r) => r.length > 0);

    for (let i = 1; i < records.length; i++) {
      // Start from 1 to skip header
      const record = records[i];
      const recordType = record.charAt(0);

      if (recordType === "O") {
        // Entry summary
        // Check AFM is 9 characters
        const afm = record.substring(1, 10).trim();
        if (afm.length !== 9 || !/^\d+$/.test(afm)) {
          throw new Error(`Invalid AFM in entry summary ${i}: '${afm}'`);
        }

        // Check that reference date is valid
        const referenceDate = record.substring(10, 18).trim();
        if (referenceDate.length !== 8 || !/^\d{8}$/.test(referenceDate)) {
          throw new Error(`Invalid reference date in entry summary ${i}: '${referenceDate}'`);
        }

        // Check that request type is valid
        const requestType = record.charAt(18);
        if (requestType !== "1" && requestType !== "2") {
          throw new Error(`Invalid request type in entry summary ${i}: '${requestType}'`);
        }

        // Check that product count is valid (>= -1)
        const productCountStr = record.substring(19).trim();
        const productCount = parseInt(productCountStr, 10);
        if (isNaN(productCount) || productCount < -1) {
          throw new Error(`Product count must be a number >= -1 in entry summary ${i}, got: '${productCountStr}'`);
        }
      }
    }
  }

  /**
   * @description Validates that the total entries in the header matches the actual number of entry summaries
   */
  private _validateEntryCount(): void {
    const records = this._content.split(UNIT_SEPARATOR).filter((r) => r.length > 0);

    // First record is the header
    const header = records[0];
    const totalEntries = parseInt(header.substring(110).trim(), 10);

    // Count actual entries (type 'O')
    const actualEntries = records.filter((r) => r.charAt(0) === "O").length;

    if (totalEntries !== actualEntries) {
      throw new Error(`Header specifies ${totalEntries} entries, but found ${actualEntries} entry summaries`);
    }
  }
}
