import { <PERSON>thenEsxesHeader, <PERSON>then<PERSON>xesEntry, ENTRY_RECORD_SIZE } from "../types/parser-types";
import { UNIT_SEPARATOR } from "../constants";
import iconv from "iconv-lite";
import { Buffer } from "buffer";

export class PothenEsxesParser {
  private buffer: string;
  private position: number = 0;

  constructor(fileContent: Buffer) {
    // Convert from ISO-8859-7 to UTF-8
    this.buffer = iconv.decode(fileContent, "iso-8859-7");
  }

  /**
   * ==============
   * PUBLIC METHODS
   * ==============
   */

  public parseContents(searchOptions: { birthDate?: string; afm?: string }): {
    header: PothenEsxesHeader;
    entries: PothenEsxesEntry[];
  } {
    const header = this._parseHeader();
    const entries: PothenEsxesEntry[] = [];

    const { birthDate, afm } = searchOptions ?? {};
    let entry: PothenEsxesEntry | null;
    while ((entry = this._parseEntry()) !== null) {
      if (!birthDate && !afm) {
        entries.push(entry);
        continue;
      } else if (entry.birthDate === birthDate || entry.afm === afm) {
        entries.push(entry);
      }
    }

    return { header, entries };
  }

  /**
   * ==============
   * PRIVATE METHODS
   * ==============
   */

  private _parseHeader(): PothenEsxesHeader {
    this.position = 0; // Reset position to start

    return {
      catalogType: this._readFixedString(1),
      catalogReference: this._readFixedString(6),
      fileIssueDate: this._readFixedString(8),
      totalEntries: this._readInteger(7)
    };
  }

  private _parseEntry(): PothenEsxesEntry | null {
    // Check if we've reached the end of the buffer
    if (this.position >= this.buffer.length) {
      return null;
    }

    // Check for unit separator and skip it
    if (this.buffer.charAt(this.position) === UNIT_SEPARATOR) {
      this.position++;
    }

    // Check if we still have enough data for an entry
    if (this.position + ENTRY_RECORD_SIZE > this.buffer.length) {
      return null;
    }

    return {
      afm: this._readFixedString(9),
      firstName: this._readFixedString(3),
      lastName: this._readFixedString(3),
      birthDate: this._readFixedString(8),
      referenceDate: this._readFixedString(8),
      requestType: this._readFixedString(1)
    };
  }

  private _readFixedString(length: number): string {
    const value = this.buffer
      .slice(this.position, this.position + length)
      .toString()
      .trim();
    this.position += length;
    return value;
  }

  private _readInteger(length: number): number {
    const value = parseInt(this._readFixedString(length), 10);
    return isNaN(value) ? 0 : value;
  }
}
