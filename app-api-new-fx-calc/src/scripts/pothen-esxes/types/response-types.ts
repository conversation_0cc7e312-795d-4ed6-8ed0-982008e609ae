// Investment Product Types
export enum InvestmentProductType {
  BOND = 1, // ΟΜΟΛΟΓΑ
  MUTUAL_FUND_SHARES = 3, // ΜΕΡΙΔΙΑ ΑΜΟΙΒΑΙΩΝ ΚΕΦΑΛΑΙΩΝ
  DERIVATIVES = 4, // ΠΑΡΑΓΩΓΑ ΧΡΗΜΑΤΟΟΙΚΟΝΟΜΙΚΑ
  INVESTMENT_INSURANCE = 5, // ΕΠΕΝΔΥΤΙΚΑ ΑΣΦΑΛΙΣΤΙΚΑ ΣΥΜΒΟΛΑΙΑ
  REPOS = 6, // REPOS
  STOCKS = 7, // ΜΕΤΟΧΕΣ
  OTHER = 28 // ΑΛΛΗ ΠΕΡΙΠΤΩΣΗ
}

// Loan Types
export enum LoanType {
  FOREIGN_CREDIT_INSTITUTION = 1, // ΔΑΝΕΙΑ ΥΠΟΧΡΕΟΥ ΣΕ ΔΗΛΩΣΗ ΠΕΡΙΟΥΣΙΑΚΗΣ ΚΑΤΑΣΤΑΣΗΣ ΑΠΟ ΑΛΛΟΔΑΠΑ ΠΙΣΤΩΤΙΚΑ ΙΔΡΥΜΑΤΑ
  OTHER_LEGAL_ENTITIES = 2, // ΔΑΝΕΙΑ ΥΠΟΧΡΕΟΥ ΣΕ ΔΗΛΩΣΗ ΠΕΡΙΟΥΣΙΑΚΗΣ ΚΑΤΑΣΤΑΣΗΣ ΑΠΟ ΛΟΙΠΑ ΝΟΜΙΚΑ ΠΡΟΣΩΠΑ ΔΗΜΟΣΙΟΥ ΚΑΙ ΙΔΙΩΤΙΚΟΥ ΔΙΚΑΙΟΥ
  INDIVIDUALS = 3, // ΔΑΝΕΙΑ ΥΠΟΧΡΕΟΥ ΣΕ ΔΗΛΩΣΗ ΠΕΡΙΟΥΣΙΑΚΗΣ ΚΑΤΑΣΤΑΣΗΣ ΠΡΟΣ ΦΥΣΙΚΑ ΠΡΟΣΩΠΑ
  ADMINISTRATIVE_FINES = 4, // ΔΙΟΙΚΗΤΙΚΑ ΠΡΟΣΤΙΜΑ
  MONETARY_PENALTIES = 5, // ΧΡΗΜΑΤΙΚΕΣ ΠΟΙΝΕΣ
  PUBLIC_TAXES = 6, // ΦΟΡΟΙ ΚΑΙ ΤΕΛΗ ΠΡΟΣ ΤΟ ΔΗΜΟΣΙΟ ΚΑΙ ΤΟΥΣ ΟΡΓΑΝΙΣΜΟΥΣ ΤΟΠΙΚΗΣ ΑΥΤΟΔΙΟΙΚΗΣΗΣ
  PUBLIC_ENTITY_FEES = 7, // ΤΕΛΗ ΠΡΟΣ ΝΟΜΙΚΑ ΠΡΟΣΩΠΑ ΔΗΜΟΣΙΟΥ ΔΙΚΑΙΟΥ
  INSURANCE_CONTRIBUTIONS = 8, // ΕΙΣΦΟΡΕΣ ΠΡΟΣ ΟΡΓΑΝΙΣΜΟΥΣ ΚΟΙΝΩΝΙΚΗΣ ΑΣΦΑΛΙΣΗΣ
  OTHER = 9, // ΑΛΛΟ
  HOUSING_RENOVATION_ENERGY = 10, // ΣΤΕΓΑΣΤΙΚΟ/ΕΠΙΣΚΕΥΑΣΤΙΚΟ/ΕΝΕΡΓΕΙΑΚΗΣ ΑΝΑΒΑΘΜΙΣΗΣ ΑΚΙΝΗΤΟΥ ΔΑΝΕΙΟ ΥΠΟΧΡΕΟΥ ΣΕ ΔΗΛΩΣΗ ΠΕΡΙΟΥΣΙΑΚΗΣ ΚΑΤΑΣΤΑΣΗΣ ΑΠΟ ΗΜΕΔΑΠΑ ΠΙΣΤΩΤΙΚΑ ΙΔΡΥΜΑΤΑ / ΧΡΗΜΑΤΟΠΙΣΤΩΤΙΚΟΥΣ ΟΡΓΑΝΙΣΜΟΥΣ
  BUSINESS = 11, // ΕΠΑΓΓΕΛΜΑΤΙΚΟ ΔΑΝΕΙΟ ΥΠΟΧΡΕΟΥ ΣΕ ΔΗΛΩΣΗ ΠΕΡΙΟΥΣΙΑΚΗΣ ΚΑΤΑΣΤΑΣΗΣ ΑΠΟ ΗΜΕΔΑΠΑ ΠΙΣΤΩΤΙΚΑ ΙΔΡΥΜΑΤΑ / ΧΡΗΜΑΤΟΠΙΣΤΩΤΙΚΟΥΣ ΟΡΓΑΝΙΣΜΟΥΣ
  CREDIT_CARD = 12, // ΠΙΣΤΩΤΙΚΗ ΚΑΡΤΑ ΥΠΟΧΡΕΟΥ ΣΕ ΔΗΛΩΣΗ ΠΕΡΙΟΥΣΙΑΚΗΣ ΚΑΤΑΣΤΑΣΗΣ ΑΠΟ ΗΜΕΔΑΠΑ ΠΙΣΤΩΤΙΚΑ ΙΔΡΥΜΑΤΑ / ΧΡΗΜΑΤΟΠΙΣΤΩΤΙΚΟΥΣ ΟΡΓΑΝΙΣΜΟΥΣ
  OTHER_DOMESTIC = 13, // ΑΛΛΟ ΔΑΝΕΙΟ ΥΠΟΧΡΕΟΥ ΣΕ ΔΗΛΩΣΗ ΠΕΡΙΟΥΣΙΑΚΗΣ ΚΑΤΑΣΤΑΣΗΣ ΑΠΟ ΗΜΕΔΑΠΑ ΠΙΣΤΩΤΙΚΑ ΙΔΡΥΜΑΤΑ / ΧΡΗΜΑΤΟΠΙΣΤΩΤΙΚΟΥΣ ΟΡΓΑΝΙΣΜΟΥΣ (π.χ. Υπερανάληψη, Παροχή πίστωσης για τη διενέργεια χρηματιστηριακών συναλλαγών-Margin Account, κ.λπ)
  CONSUMER = 14 // ΚΑΤΑΝΑΛΩΤΙΚΟ ΔΑΝΕΙΟ ΥΠΟΧΡΕΟΥ ΣΕ ΔΗΛΩΣΗ ΠΕΡΙΟΥΣΙΑΚΗΣ ΚΑΤΑΣΤΑΣΗΣ ΑΠΟ ΗΜΕΔΑΠΑ ΠΙΣΤΩΤΙΚΑ ΙΔΡΥΜΑΤΑ / ΧΡΗΜΑΤΟΠΙΣΤΩΤΙΚΟΥΣ ΟΡΓΑΝΙΣΜΟΥΣ
}

// Loan Balance Prefix Types
export enum LoanBalancePrefix {
  CREDIT = "Π", // Πιστωτικό υπόλοιπο
  DEBIT = "Χ", // Χρεωστικό υπόλοιπο
  ZERO = "0" // Μηδενικό υπόλοιπο
}

// Loan Record Types
export interface ResponseLoanProduct {
  recordType: "L"; // CHAR(1) - Always 'L'
  loanType: LoanType; // NUMBER(2) - Loan type code
  internalCode: string; // CHAR(60) - Internal reference code
  initialAmount: number; // FLOAT(10+1+2) - Initial loan amount
  currency: string; // CHAR(3) - Currency code
  balance: number; // FLOAT(10+1+2) - Current balance
  balancePrefix: LoanBalancePrefix; // CHAR(1) - Balance prefix (Π/Χ/0)
  accountStartDate: string; // NUMBER(8) - YYYYMMDD
  accountEndDate?: string; // NUMBER(8) - Optional YYYYMMDD
  chargesExpenses: number; // FLOAT(10+1+2) - Other charges/expenses
  chargesCurrency: string; // CHAR(3) - Charges currency code
}

// Header Record Types
export interface ResponseHeader {
  recordType: "F"; // CHAR(1) - Always 'F'
  afm: string; // CHAR(9) - XO-PI AFM
  name: string; // CHAR(100) - XO-PI Name
  totalEntries: number; // INT(7) - Total number of entries
}

// Entry Summary Types
export interface ResponseEntrySummary {
  recordType: "O"; // CHAR(1) - Always 'O' (Latin Capital)
  afm: string; // CHAR(9) - Entry AFM
  referenceDate: string; // DATE(8) - YYYYMMDD
  requestType: string; // CHAR(1) - 2: Without loans, 1: With loans
  productRecordCount: string; // CHAR(3) - Number of product records for the liable person that follow.
  // - If the liable person is a customer without products on the requested data reference date, this should be 0.
  // - If they are a customer with products on the requested data reference date, this should be greater than 0 (reflecting the actual number of products).
  // - If the liable person was not identified as a customer on the requested data reference date, this should be -1.
}

// Investment Product Record Types
export interface ResponseInvestmentProduct {
  recordType: "I"; // CHAR(1) - Always 'I' (Latin Capital)
  internalCode: string; // CHAR(60) - Internal reference code
  investmentType: InvestmentProductType; // NUMBER(2) - Investment type code
  isin: string | null; // CHAR(12) - Optional ISIN code
  title: string; // CHAR(100) - Investment product title
  quantity: number; // FLOAT(10+1+6) - Quantity with 6 decimal places
  valuation: number; // FLOAT(10+1+2) - Valuation with 2 decimal places
  currency: string; // CHAR(3) - Currency code
}

// Record Type Union
export type ResponseRecord =
  | ResponseHeader
  | ResponseEntrySummary
  | ResponseInvestmentProduct
  | ResponseLoanProduct;

// Constants for record sizes
export const RESPONSE_SIZES = {
  HEADER: 117, // bytes
  ENTRY_SUMMARY: 22, // bytes
  INVESTMENT_PRODUCT: 208, // bytes
  LOAN_PRODUCT: 125 // bytes
} as const;

// Utility type for JSON field mappings
export interface InvestmentProductJsonFields {
  investmentInternalCode: string;
  investmentType: InvestmentProductType;
  investmentIsin?: string;
  investmentTitle: string;
  investmentQuantity: number;
  investmentValuation: number;
  investmentCurrency: string;
}

export interface LoanProductJsonFields {
  loanType: LoanType;
  loanInternalCode: string;
  loanInitAmount: number;
  loanCurrency: string;
  loanBalance: number;
  loanPrefix: LoanBalancePrefix;
  loanAccountStartDate: string;
  loanAccountEndDate?: string;
  loanChargesExpenses: number;
  loanChargesCurrency: string;
}
