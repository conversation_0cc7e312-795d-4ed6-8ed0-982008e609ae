import fs from "fs";
import path from "path";
import { PothenEsxesParser } from "./utils/parser";
import { ResponseFormatter } from "./utils/response-formatter";
import {
  InvestmentProductType,
  LoanType,
  LoanBalancePrefix,
  InvestmentProductJsonFields,
  LoanProductJsonFields
} from "./types/response-types";
import ResponseValidator from "./utils/validator";

async function parseInputFile(filePath: string, searchOptions?: { birthDate?: string; afm?: string }) {
  try {
    if (!filePath) {
      throw new Error("Please provide a file path as an argument");
    }

    // Resolve input file path - check in input directory first
    const inputDir = path.join(__dirname, "input");
    let resolvedPath = path.join(inputDir, filePath);

    // If file doesn't exist in input directory, try as an absolute path or relative to current directory
    if (!fs.existsSync(resolvedPath)) {
      resolvedPath = filePath;
      console.warn(`File not found in input directory, trying direct path: ${resolvedPath}`);
    } else {
      console.log(`Reading file from input directory: ${resolvedPath}`);
    }

    const fileContent = await fs.promises.readFile(resolvedPath);
    const parser = new PothenEsxesParser(fileContent);

    // Parse the file
    const { header, entries } = parser.parseContents(searchOptions);

    // Print results
    console.log("Header:");
    console.log(JSON.stringify(header, null, 2));
    console.log("\nEntries:");
    console.log(JSON.stringify(entries, null, 2));

    console.log(`Number of entries found: ${entries.length}`);

    // Validate
    if (header.totalEntries !== entries.length) {
      console.warn(`Warning: Header specifies ${header.totalEntries} entries but found ${entries.length} entries`);
    }

    return { header, entries };
  } catch (error) {
    console.error("Error processing file:", error);
    throw error;
  }
}

async function generateResponse(inputFilename: string, parsedData: { header: any; entries: any[] }) {
  try {
    console.log(`Generating response from parsed data of: ${inputFilename}`);

    // Parse Year, Month, Version from inputFilename
    // Expected pattern: SOMETHING-YYYY-MM-vNNN.ext
    const baseFilename = path.basename(inputFilename, path.extname(inputFilename));
    const match = baseFilename.match(/.*-(\d{4})-(\d{2})-v(\d{3})$/);

    if (!match) {
      throw new Error(`Input filename "${baseFilename}" does not match expected pattern "...-YYYY-MM-vNNN"`);
    }
    const year = parseInt(match[1], 10);
    const month = parseInt(match[2], 10);
    const version = parseInt(match[3], 10);

    console.log(`Extracted for output: Year=${year}, Month=${month}, Version=${version}`);

    // Transform parsedData.entries for ResponseFormatter
    const formattedEntries = parsedData.entries.map((entry) => ({
      summary: {
        afm: entry.afm,
        referenceDate: entry.referenceDate,
        requestType: entry.requestType,
        productRecordCount: "-1" // FIXME: Assumption that all entries are non-customers
      },
      products: [] as (InvestmentProductJsonFields | LoanProductJsonFields)[] // FIXME: non-customers have no products
    }));

    const responseData = {
      totalEntries: parsedData.header.totalEntries, // Reflects total from original file
      entries: formattedEntries
    };

    const formatter = new ResponseFormatter();
    const outputDir = path.join(__dirname, "output");

    const encodedFilePath = await formatter.generateFile(outputDir, year, month, version, responseData);
    console.log(`Response file generated at: ${encodedFilePath}`);

    // Optionally, generate a readable version as well
    const readableContent = formatter.formatResponse(responseData);
    const readableFilePath = encodedFilePath + ".readable";
    await fs.promises.writeFile(readableFilePath, readableContent, "utf8");
    console.log(`Readable response file generated at: ${readableFilePath}`);
  } catch (error) {
    console.error("Error generating response file:", error);
    throw error; // Re-throw to be caught by main or caller
  }
}

async function generateResponseDemoFiles() {
  try {
    const formatter = new ResponseFormatter();
    const outputDir = path.join(__dirname, "output");

    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Sample response data based on the input structure
    const responseData = {
      totalEntries: 4,
      entries: [
        {
          summary: {
            afm: "000999925",
            referenceDate: "********",
            requestType: "2", // Without loans
            productRecordCount: "2"
          },
          products: [
            {
              investmentType: InvestmentProductType.STOCKS,
              investmentInternalCode: "STOCK001",
              investmentTitle: "ΑΛΦΑ ΤΡΑΠΕΖΑ Α.Ε.",
              investmentQuantity: 1000.0,
              investmentValuation: 15500.5,
              investmentCurrency: "EUR",
              investmentIsin: "GRS015003006"
            },
            {
              investmentType: InvestmentProductType.BOND,
              investmentInternalCode: "BOND001",
              investmentTitle: "ΕΛΛΗΝΙΚΟ ΔΗΜΟΣΙΟ 10ΕΤ",
              investmentQuantity: 50000.0,
              investmentValuation: 51250.75,
              investmentCurrency: "EUR",
              investmentIsin: "GR0124035693"
            }
          ]
        },
        {
          summary: {
            afm: "003444452",
            referenceDate: "********",
            requestType: "2", // Without loans
            productRecordCount: "1"
          },
          products: [
            {
              investmentType: InvestmentProductType.MUTUAL_FUND_SHARES,
              investmentInternalCode: "MF001",
              investmentTitle: "ALPHA TRUST ΟΜΟΛΟΓΙΩΝ ΕΣΩΤΕΡΙΚΟΥ",
              investmentQuantity: 2500.0,
              investmentValuation: 28750.25,
              investmentCurrency: "EUR",
              investmentIsin: "GRF000073006"
            }
          ]
        },
        {
          summary: {
            afm: "004444449",
            referenceDate: "********",
            requestType: "1", // With loans
            productRecordCount: "2"
          },
          products: [
            {
              investmentType: InvestmentProductType.STOCKS,
              investmentInternalCode: "STOCK002",
              investmentTitle: "ΕΘΝΙΚΗ ΤΡΑΠΕΖΑ Α.Ε.",
              investmentQuantity: 750.0,
              investmentValuation: 12350.0,
              investmentCurrency: "EUR",
              investmentIsin: "GRS003003019"
            },
            {
              loanType: LoanType.HOUSING_RENOVATION_ENERGY,
              loanInternalCode: "LOAN001",
              loanInitAmount: 150000.0,
              loanCurrency: "EUR",
              loanBalance: 125000.5,
              loanPrefix: LoanBalancePrefix.DEBIT,
              loanAccountStartDate: "********",
              loanChargesExpenses: 450.0,
              loanChargesCurrency: "EUR"
            }
          ]
        },
        {
          summary: {
            afm: "*********",
            referenceDate: "********",
            requestType: "1", // With loans
            productRecordCount: "1"
          },
          products: [
            {
              loanType: LoanType.CONSUMER,
              loanInternalCode: "LOAN002",
              loanInitAmount: 25000.0,
              loanCurrency: "EUR",
              loanBalance: 18500.75,
              loanPrefix: LoanBalancePrefix.DEBIT,
              loanAccountStartDate: "********",
              loanChargesExpenses: 250.0,
              loanChargesCurrency: "EUR"
            }
          ]
        }
      ]
    };

    // Generate the encoded file (ISO-8859-7)
    const encodedFilePath = await formatter.generateFile(
      outputDir,
      2024, // year
      7, // month
      1, // version
      responseData
    );
    console.log(`Encoded file generated at: ${encodedFilePath}`);

    // Generate a readable version (UTF-8)
    const readableContent = formatter.formatResponse(responseData);
    const readableFilePath = encodedFilePath + ".readable";
    await fs.promises.writeFile(readableFilePath, readableContent, "utf8");
    console.log(`Readable file generated at: ${readableFilePath}`);
  } catch (error) {
    console.error("Error generating response files:", error);
    throw error;
  }
}

function validateExistingFile() {
  try {
    const filePath = process.argv[3];
    if (!filePath) {
      console.error("Please provide a file path to validate as the second argument");
      throw new Error("Missing file path for validation");
    }

    const validator = new ResponseValidator(filePath);
    const results = validator.validate();

    if (results.isValid) {
      console.log("✓ File is valid! All checks passed.");
    } else {
      console.log("✗ File validation failed with the following errors:");
      results.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
  } catch (error) {
    console.error("Error validating file:", error);
    throw error;
  }
}

// Display available commands
function printHelp() {
  console.log("Usage:");
  console.log(
    "  npx ts-node cli.ts parse <input-file> [--birthDate YYYYMMDD] [--afm AFM_VALUE] - Parse input file from /input directory with optional filters"
  );
  console.log(
    "  npx ts-node cli.ts generate-response <input-file>                     - Parses the full input file and generates a response file. Output filename details (year, month, version) are derived from the input filename (expected pattern: ...-YYYY-MM-vNNN.ext). This command does not use --birthDate or --afm filters."
  );
  console.log(
    "  npx ts-node cli.ts generate-demo-response          - Generate demo response files in /output directory"
  );
  console.log("  npx ts-node cli.ts validate <file>                 - Validate an existing response file");
  console.log("  npx ts-node cli.ts help                            - Show this help message");
  console.log("");
  console.log("Options for 'parse' command:");
  console.log("  --birthDate YYYYMMDD  Filter entries by birth date (e.g., 20000828)");
  console.log("  --afm AFM_VALUE       Filter entries by AFM");
  console.log("");
  console.log("Directory Structure:");
  console.log("  /input  - Place input files here");
  console.log("  /output - Generated files are saved here");
}

// Run the appropriate function based on arguments
async function main() {
  try {
    const command = process.argv[2];

    if (!command) {
      printHelp();
      return;
    }

    // Get the input path for commands that need it
    const inputPath = process.argv[3];

    let birthDate: string;
    let afm: string;

    switch (command) {
      case "parse":
        // Parse input file only
        if (!inputPath) {
          throw new Error("Please provide an input file path");
        }
        // Parse optional arguments for 'parse' command
        for (let i = 4; i < process.argv.length; i++) {
          if (process.argv[i] === "--birthDate" && process.argv[i + 1]) {
            birthDate = process.argv[i + 1];
            console.log("searching by birth date " + birthDate);
            i++; // Skip next argument as it's the value
          } else if (process.argv[i] === "--afm" && process.argv[i + 1]) {
            afm = process.argv[i + 1];
            i++; // Skip next argument as it's the value
          }
        }
        await parseInputFile(inputPath, { birthDate, afm });
        break;

      case "generate-response": {
        const parsedFileContents = await parseInputFile(inputPath);
        const inputFileBaseName = path.basename(inputPath);
        await generateResponse(inputFileBaseName, parsedFileContents);
        break;
      }

      case "generate-demo-response":
        // Generate new demo files only
        await generateResponseDemoFiles();
        break;

      case "validate":
        // Validate existing file
        validateExistingFile();
        break;

      case "help":
        // Show help message
        printHelp();
        break;

      default:
        throw new Error(`Unsupported option: ${command}`);
    }
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
}

main();
