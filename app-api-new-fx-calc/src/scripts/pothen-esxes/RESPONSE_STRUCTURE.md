# Κανόνες Γραμμογράφησης Αρχείων

Γραμμογράφηση Αρχείων

Γενικές παρατηρήσεις:
• ΠΡΟΣΟΧΗ: Η κωδικοποίηση όλων των αρχείων είναι η ISO-8859-7.
• Κάθε εγγραφή τερματίζεται με τον ISO-8859-7 χαρακτήρα 0x1F (UNIT SEPARATOR) και απαγορεύεται η χρήση του συγκεκριμένου χαρακτήρα σε οποιοδήποτε πεδίο δεδομένων.
• Στα πεδία τύπου FLOAT χρησιμοποιείται ο χαρακτήρας τελεία ‘.’ αντί για τον χαρακτήρα υποδιαστολή ‘,’. Τα δεκαδικά ψηφία είναι υποχρεωτικά. Το μέγεθος είναι το μήκος του ακέραιου μέρους, συν 1 χαρακτήρα της τελείας, συν το μέγεθος του δεκαδικού μέρους. Για χρηματικά ποσά το μήκος του δεκαδικού μέρους είναι πάντα 2.
• Οι εγγραφές απάντησης θα έχουν μεταβλητό μήκος (σε byte) ανάλογα με τον τύπο της εγγραφής αλλά σταθερό για κάθε τύπο εγγραφής. Το ίδιο το μήκος της εγγραφής αποτελεί στοιχείο ελέγχου για την ορθότητα της.
• Ο διαχωρισμός μεταξύ των πεδίων σε κάθε εγγραφή ΔΕΝ γίνεται με delimiter, αλλά κάθε πεδίο έχει συγκεκριμένο μήκος (σε byte) ανάλογα με τον τύπο του.
• Όπου γίνεται συμπλήρωση του ΑΦΜ θα γίνεται με 9 αλφαριθμητικά σύμβολα (τύπος string), συμπεριλαμβανομένου και τυχόν αρχικού ή αρχικών «0».
• Στην στήλη «Υποχρ.» των πινάκων που περιγράφουν τη γραμμογράφηση στη συνέχεια σημειώνεται «✔️» όταν το πεδίο είναι υποχρεωτικό, ενώ «Χ» όταν το πεδίο είναι προαιρετικό.
• Προκειμένου να επιτευχθεί το απαιτούμενο μέγεθος (σε byte) των διαφόρων πεδίων, προστίθενται, όπου είναι απαραίτητο, χαρακτήρες κενού είτε δεξιά είτε αριστερά του απαντητικού δεδομένου, π.χ. για πεδίο τύπου FLOAT(10+1+6) ο αριθμός 100.1 μπορεί να γραφεί στο αρχείο στο αντίστοιχο πεδίο bbbbbbb100.1bbbbb όπου b είναι ο κενός χαρακτήρας ‘ ‘. Χρειάζεται προσοχή, διότι υπάρχουν δύο τύποι FLOAT(10+1+6) και FLOAT(10+1+2).
• Στην περίπτωση προαιρετικών πεδίων (π.χ. ISIN, Ημ/νία Λήξης Δανειακής Υποχρέωσης), εφόσον η αντίστοιχη τιμή δεν είναι διαθέσιμη, θα πρέπει να χρησιμοποιηθούν χαρακτήρες κενού ίσου πλήθους με το απαιτούμενο μήκος του κάθε πεδίου.


# Κανόνες Γραμμογράφησης Αρχείων – Αναλυτικός Οδηγός

Αυτό το αρχείο περιλαμβάνει τόσο τους τεχνικούς κανόνες για τη δημιουργία αρχείων σύμφωνα με τις προδιαγραφές του ΟΠΣ Πόθεν Έσχες, όσο και πρακτικές οδηγίες βασισμένες στις πιο συχνές ερωτήσεις και λάθη.

## Γενικές Τεχνικές Προδιαγραφές
- Η **κωδικοποίηση** των αρχείων πρέπει να είναι **ISO-8859-7**.
- Κάθε εγγραφή **τερματίζεται με τον χαρακτήρα 0x1F (UNIT SEPARATOR)**. Δεν επιτρέπεται η χρήση του χαρακτήρα αυτού μέσα στα δεδομένα.
- Τα **δεκαδικά** γράφονται με τελεία (.) και όχι με κόμμα. Π.χ. `100.25`.
- Δεν χρησιμοποιούνται delimiters (π.χ. κόμματα). Αντίθετα, κάθε πεδίο έχει **σταθερό μήκος**, το οποίο πρέπει να συμπληρώνεται με **κενά** αν η τιμή είναι μικρότερη.
- Για προαιρετικά πεδία (όπως ISIN ή Ημερομηνία Λήξης Δανείου), εάν δεν υπάρχει τιμή, πρέπει να **συμπληρώνεται με κενά** και όχι με μηδενικά ή άλλες αυθαίρετες τιμές.
- Όλα τα πεδία **ημερομηνίας** πρέπει να είναι στη μορφή `YYYYMMDD`. Δεν επιτρέπεται η χρήση τιμής `00000000` ή ημερομηνιών όπως `01/01/2100`.
- Οι ΑΦΜ γράφονται **με 9 χαρακτήρες**, περιλαμβάνοντας τυχόν αρχικά μηδενικά.

## Οδηγίες ανά Τύπο Εγγραφής

### Εγγραφή Υπόχρεου (Τύπος "O")
- Χρησιμοποιείται για κάθε υπόχρεο-πελάτη.
- Το πεδίο **"Πλήθος εγγραφών προϊόντων"** έχει:
  - `0` αν ο πελάτης υπάρχει αλλά δεν έχει προϊόντα.
  - `-1` αν δεν ταυτοποιείται καθόλου (δεν είναι πελάτης).

### Επενδυτικά Προϊόντα (Τύπος "I")
- Συμπληρώνονται για κάθε προϊόν, με είδος από παραμετρικό πίνακα (π.χ. `07` για μετοχές).
- Το πεδίο `ISIN` είναι **προαιρετικό**.
- Το ποσό και η αποτίμηση πρέπει να είναι FLOAT με αντίστοιχη ακρίβεια (10+1+6 και 10+1+2).

### Καταθετικοί Λογαριασμοί (Τύπος "D")
- Πρέπει να αναφέρονται **τα 4 τελευταία ψηφία** του IBAN.
- Το πεδίο `Πρόσημο` μπορεί να είναι `Π` (πιστωτικό), `Χ` (χρεωστικό, **προσοχή: ελληνικό Χ**), ή `0`.

### Δανειακές Υποχρεώσεις (Τύπος "L")
- Περιλαμβάνει και τις επιπλέον χρεώσεις (π.χ. `loanChargesExpenses`).
- Η `Ημερομηνία Λήξης` είναι **προαιρετική** και, αν λείπει, πρέπει να έχει **8 κενά**.

### Θυρίδες (Τύπος "S") και Μέταλλα (Τύπος "M")
- Χρησιμοποιούνται ανάλογα με τη φύλαξη ή μεταλλικά στοιχεία.
- Οι ποσότητες είναι επίσης FLOAT και χρησιμοποιούν κωδικούς μονάδων μέτρησης.

## Ονοματολογία Αρχείων
- Το όνομα του αρχείου πρέπει να περιέχει:
  - τον ΑΦΜ (9 ψηφία)
  - την ημερομηνία αναφοράς (π.χ. `2025-03`)
  - και την έκδοση (π.χ. `v001`)
- Παράδειγμα: `123456789-pothendata-2025-03-v001.gpg`
- Για νέα υποβολή του ίδιου μήνα, αλλάζει μόνο το `v001` σε `v002`, κ.ο.κ.

## Συχνά Λάθη
- Λανθασμένος χαρακτήρας στο πεδίο “Πρόσημο” (χρήση λατινικού Χ αντί του ελληνικού Χ).
- Πεδία με λάθος αριθμό χαρακτήρων (π.χ. IBAN λιγότερο από 4 ψηφία).
- Αποστολή αρχείου κρυπτογραφημένου **με το δικό σας public key αντί για του Πόθεν Έσχες**.
- Ημερομηνίες σε μη έγκυρες μορφές ή με μη αποδεκτές default τιμές.

## Τελική Υπενθύμιση
Το αρχείο πρέπει πάντα να **κρυπτογραφείται με το public key του Πόθεν Έσχες**, να ανεβαίνει στον κατάλογο `pothen` του FTPS και να έχει τη σωστή μορφή ονόματος αρχείου.

