import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { SUBMISSION_HOURS_CONFIG } from "./submissionWindowConfig";

export enum ExecutionTypeEnum {
  MARKET_HOURS = "MARKET_HOURS",
  REALTIME = "REALTIME"
}

export type ExecutionWindowsType = {
  stocks?: ExecutionWindowType;
  etfs?: ExecutionWindowType;
};

export type ExecutionWindowType = AggregateExecutionWindowType | RealTimeExecutionWindowType;

export type AggregateExecutionWindowType = {
  executionType: ExecutionTypeEnum.MARKET_HOURS;
  start: Date;
  end: Date;
};

export type RealTimeExecutionWindowType = {
  executionType: ExecutionTypeEnum.REALTIME;
};

type ExecutionTimeConfigType = {
  start: {
    HOUR: number;
    MINUTE: number;
  };
  end: {
    HOUR: number;
    MINUTE: number;
  };
  timeZone: string;
};

type ExecutionHoursConfigType = {
  realtime: ExecutionTimeConfigType;
  aggregate?: ExecutionTimeConfigType;
};

/**
 * Execution window configuration describes what we communicate to the users regarding **execution** times for either
 * stocks or ETFs.
 *
 * For ETFs, depending on how the order is submitted, the execution window is different:
 * - For realtime orders, the execution window is the same as the submission window.
 * - For aggregate orders, the execution window is the one provided by WK.
 */
export const EXECUTION_WINDOW_HOURS_CONFIG: Record<
  investmentUniverseConfig.AssetCategoryType,
  ExecutionHoursConfigType
> = {
  stock: {
    realtime: SUBMISSION_HOURS_CONFIG.stock.realtime,
    aggregate: {
      start: {
        HOUR: 9,
        MINUTE: 30
      },
      end: {
        HOUR: 16,
        MINUTE: 0
      },
      timeZone: "America/New_York"
    }
  },
  etf: {
    realtime: SUBMISSION_HOURS_CONFIG.etf.realtime,
    aggregate: {
      start: {
        HOUR: 13,
        MINUTE: 0
      },
      end: {
        HOUR: 18,
        MINUTE: 0
      },
      timeZone: "Europe/London"
    }
  }
};
