import { EmploymentStatusType, IndustryType, SourceOfWealthType } from "../external-services/wealthkernelService";

/**
 * Warning:
 * If you wish to modify an incomeRange, you HAVE to create a new one.
 * Id field exists for backwards compatibility.
 */
export const INCOME_RANGE_CONFIG: {
  id: number;
  minIncome: number;
  maxIncome: number | null;
  annualIncomeToSubmit: number;
}[] = [
  { id: 1, minIncome: 0, maxIncome: 10000, annualIncomeToSubmit: 5000 },
  { id: 2, minIncome: 10001, maxIncome: 20000, annualIncomeToSubmit: 15000 },
  { id: 3, minIncome: 20001, maxIncome: 30000, annualIncomeToSubmit: 25000 },
  { id: 4, minIncome: 30001, maxIncome: 50000, annualIncomeToSubmit: 40000 },
  { id: 5, minIncome: 50001, maxIncome: 75000, annualIncomeToSubmit: 62500 },
  { id: 6, minIncome: 75001, maxIncome: 100000, annualIncomeToSubmit: 87500 },
  { id: 7, minIncome: 100001, maxIncome: 150000, annualIncomeToSubmit: 125000 },
  { id: 8, minIncome: 150001, maxIncome: 250000, annualIncomeToSubmit: 200000 },
  { id: 9, minIncome: 250001, maxIncome: 500000, annualIncomeToSubmit: 375000 },
  { id: 10, minIncome: 500001, maxIncome: null, annualIncomeToSubmit: 500000 } // or use a very high number instead of null
];

export type EmploymentInfoConfigurationType = {
  employmentStatuses: {
    id: EmploymentStatusType;
    label: string;
  }[];
  industries: {
    id: IndustryType;
    label: string;
  }[];
  sourcesOfWealth: {
    id: SourceOfWealthType;
    label: string;
  }[];
  incomeRanges: {
    id: string;
    label: string;
  }[];
  employmentStatusThatRequireIndustry: EmploymentStatusType[];
};
