import { PlatformCategoryEnum } from "./platformConfig";
import { PartialRecord } from "utils";

export enum DepositActionEnum {
  JUST_PAY = "JUST_PAY",
  DEPOSIT_AND_INVEST = "DEPOSIT_AND_INVEST",
  DEPOSIT_AND_SAVE = "DEPOSIT_AND_SAVE",
  LIFETIME_CHARGE = "LIFETIME_CHARGE"
}

export const TRUELAYER_REDIRECT_URI_CONFIG: Record<PlatformCategoryEnum, Record<DepositActionEnum, string>> = {
  [PlatformCategoryEnum.MOBILE]: {
    [DepositActionEnum.JUST_PAY]: `${process.env.DOMAIN_URL}/truelayer-callback/just-pay`,
    [DepositActionEnum.DEPOSIT_AND_INVEST]: `${process.env.DOMAIN_URL}/truelayer-callback/pay-and-invest`,
    [DepositActionEnum.LIFETIME_CHARGE]: `${process.env.DOMAIN_URL}/truelayer-callback/pay-lifetime-subscription`,
    [DepositActionEnum.DEPOSIT_AND_SAVE]: `${process.env.DOMAIN_URL}/truelayer-callback/pay-and-save`
  },
  [PlatformCategoryEnum.WEB]: {
    [DepositActionEnum.JUST_PAY]: `${process.env.CLIENT_DOMAIN_URL}/investor/truelayer-pay-callback/just-pay`,
    [DepositActionEnum.DEPOSIT_AND_INVEST]: `${process.env.CLIENT_DOMAIN_URL}/investor/truelayer-pay-callback/pay-and-invest`,
    [DepositActionEnum.LIFETIME_CHARGE]: `${process.env.CLIENT_DOMAIN_URL}/investor/truelayer-pay-callback/pay-lifetime-subscription`,
    [DepositActionEnum.DEPOSIT_AND_SAVE]: `${process.env.CLIENT_DOMAIN_URL}/investor/truelayer-pay-callback/pay-and-save`
  }
};

export const SALTEDGE_REDIRECT_URI_CONFIG: Record<
  PlatformCategoryEnum,
  PartialRecord<DepositActionEnum, string>
> = {
  [PlatformCategoryEnum.MOBILE]: {
    [DepositActionEnum.JUST_PAY]: `${process.env.DOMAIN_URL}/saltedge-pay-callback/just-pay`
  },
  [PlatformCategoryEnum.WEB]: {
    [DepositActionEnum.JUST_PAY]: `${process.env.CLIENT_DOMAIN_URL}/investor/saltedge-pay-callback/just-pay`
  }
};
