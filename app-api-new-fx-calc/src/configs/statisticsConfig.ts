export default class StatisticsConfig {
  public static readonly DCA_DEFAULT_INITIAL_INVESTMENT = 150;
  public static readonly DCA_DEFAULT_MONTHLY_INVESTMENT = 150;

  public static readonly FUTURE_DEFAULT_INITIAL_INVESTMENT = 150;
  public static readonly FUTURE_DEFAULT_MONTHLY_INVESTMENT = 150;

  public static readonly URLS = {
    optimalAllocationUrlV3: `${process.env.STATISTICS_SERVICE_URL}/v3/portfolios/optimal-allocation`,
    indexStatsV3: `${process.env.STATISTICS_SERVICE_URL}/v3/index-stats`
  };
}
