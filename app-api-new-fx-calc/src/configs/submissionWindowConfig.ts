import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

type SubmissionTimeConfigType = {
  start: {
    HOUR: number;
    MINUTE: number;
  };
  end: {
    HOUR: number;
    MINUTE: number;
  };
  timeZone: string;
};

type SubmissionHoursConfigType = {
  realtime: SubmissionTimeConfigType;
  aggregate: SubmissionTimeConfigType;
};

export const SUBMISSION_HOURS_CONFIG: Record<
  investmentUniverseConfig.AssetCategoryType,
  SubmissionHoursConfigType
> = {
  stock: {
    // Our stock realtime submission window is 9:30 to 16:00 NY time (US market hours)
    realtime: {
      start: {
        HOUR: 9,
        MINUTE: 30
      },
      end: {
        HOUR: 16,
        MINUTE: 0
      },
      timeZone: "America/New_York"
    },
    // Our stock aggregate submission window is a 45 minutes window before US market opens
    aggregate: {
      start: {
        HOUR: 8,
        MINUTE: 45
      },
      end: {
        HOUR: 9,
        MINUTE: 30
      },
      timeZone: "America/New_York"
    }
  },
  etf: {
    // Our ETF realtime submission window is 8:00 - 16.30 UK time
    realtime: {
      start: {
        HOUR: 8,
        MINUTE: 0
      },
      end: {
        HOUR: 16,
        MINUTE: 30
      },
      timeZone: "Europe/London"
    },
    // Our ETF aggregate submission window is 12.15 - 13.00 UK time
    aggregate: {
      start: {
        HOUR: 12,
        MINUTE: 15
      },
      end: {
        HOUR: 13,
        MINUTE: 15 // Even though Wealthkernel have a 13.00 window end, they do allow order submitted shortly after that.
      },
      timeZone: "Europe/London"
    }
  }
};

// Our MMF order submission window is 09:30 - 10:45 UK time
export const MMF_SUBMISSION_HOURS_CONFIG: SubmissionTimeConfigType = {
  start: {
    HOUR: 9,
    MINUTE: 30
  },
  end: {
    HOUR: 10,
    MINUTE: 45
  },
  timeZone: "Europe/London"
};
