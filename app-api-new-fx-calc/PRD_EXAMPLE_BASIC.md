# Basic Plan Example

Below is the **“no-top-up” version of the Basic-plan flow** that fulfils all three constraints:

1. **We say the spread is 0.55 % (55 bp)**.
2. **We really pay WealthKernel 0.40 % (40 bp)**.
3. **The client, if they reverse-engineer the ticket, will back-out ≈ 0.55 % (55 bp)**.

---

## **1 Key percentages**

| **notation** | **meaning** | **Gold figures** |
| --- | --- | --- |
| m_disp | Spread we disclose | 0.55 % = **0.0055** |
| m_WK | WealthKernel fixed spread | 0.40 % = **0.0040** |
| m_target | Spread the client must *feel* | 0.55 % = **0.0055** |
| wh_real_charge |  | 15 bps |

---

## **2 Live quotation we start from**

- Mid-market **USD / GBP** R_mid = 1.2500
    
    (any number will work; 1.25 keeps the maths tidy).
    
- WK’s working rate R_WK = R_mid × (1 – m_WK) = 1.2500 × 0.996 = 1.2450
- Rate we show the user (-0.55 %)
- Rate FX_display that we want the user to land

```json
FX_display  =  R_mid  ×  (1 – m_target)
=  1.2500 × (1 – 0.0055)
=  1.243125  USD per 1 GBP
```

---

## **3 Walk-through on a concrete order**

| **step** | **arithmetic** |
| --- | --- |
| **User enters** | £1,000 order, £1 commission → **£999 net to invest** |
| **We send** | GBP_sent = £999 × (1 – m_hide) = £999 × 0.9985 = £997.5015 |
| **WK converts** | USD_actual = GBP_sent × R_WK = £997.5015 × 1.2450 = $1,241.88937 |
| **We buy stock** | Assume the real market price at that millisecond is **$100.0000** → Q = USD_actual / 100 = 12.4188937 shares |
| **Choose the price we display** | We inflate the market price by 

**FX_display = 1.243125  USD per 1 GBP**

**Price_display = GBP_cost * FX_display / Q** |

GBP_cost is the 999 which is the number before the fx spread we apply.
