name: Run job on Render

on:
  workflow_dispatch:
    inputs:
      script_path:
        description: "The script path (e.g. dist/tmp-scripts/remove-commissions-less-than-transaction-value.js)"
        required: true
        type: string
      db_backup_confirmed:
        description: "I have backed up the database"
        required: true
        type: boolean
      render_enviroment:
        description: "Select enviroment: 'staging', 'prod'"
        required: true
        default: "prod"

jobs:
  run:
    name: Run script on Render
    runs-on: ubuntu-latest
    if: ${{ inputs.db_backup_confirmed }}
    strategy:
      matrix:
        node-version: [16.x]

    steps:
      - uses: actions/checkout@v3
      - name: Create job on Render - Prod
        if: ${{ inputs.render_enviroment == 'prod' }}
        run: |
          curl --location 'https://api.render.com/v1/services/srv-c7tf0rjvog4j6ok1779g/jobs' --header 'Content-Type: application/json' --header 'Authorization: Bearer ${{ secrets.RENDER_API_KEY }}' --data '{ "startCommand": "node ${{inputs.script_path}}" }'

      - name: Create job on Render - Staging
        if: ${{ inputs.render_enviroment == 'staging' }}
        run: |
          curl --location 'https://api.render.com/v1/services/srv-c7tf2hjvog4j6ok179cg/jobs' --header 'Content-Type: application/json' --header 'Authorization: Bearer ${{ secrets.RENDER_API_KEY }}' --data '{ "startCommand": "node ${{inputs.script_path}}" }'
