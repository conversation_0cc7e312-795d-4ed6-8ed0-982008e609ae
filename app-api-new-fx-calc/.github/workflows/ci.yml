# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

# Το SSH into a Ubicloud machine, a step 'Deploy SSH key' must be added where we do the following:
# mkdir -p ~/.ssh
# echo "YOUR_PUBLIC_SSH_KEY" >> ~/.ssh/authorized_keys
# Then, you can SSH by doing ssh runner@ip where IP is the output of step 'IPv4 Address'

name: CI

on:
  pull_request:
    branches: [master]

jobs:
  test:
    name: Build & Test
    runs-on: ubicloud-standard-16
    strategy:
      matrix:
        node-version: [20.11.1]

    steps:
      - name: IPv4 Address
        run: curl -sL --ipv4 ifconfig.me
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          always-auth: true
          node-version: ${{ matrix.node-version }}
          cache: "npm"
          registry-url: https://registry.npmjs.org
          scope: "@wealthyhood"
          token: ${{secrets.NPM_TOKEN}}
      - name: Install npm dependencies
        run: npm install --production=false
        env:
          NPM_TOKEN: ${{secrets.NPM_TOKEN}}
      - name: Build Project
        run: npm run build
      - name: Setup env file for testing
        run: echo "${{secrets.APP_API_ENV_TEST }}" > dev.env
      - name: Test
        run: npm run test-ci
        env:
          NODE_ENV: "development"
          NODE_OPTIONS: "--max_old_space_size=4096"
