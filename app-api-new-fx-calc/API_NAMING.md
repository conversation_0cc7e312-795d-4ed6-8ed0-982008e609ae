# API Guidelines

## 1. Naming

### 1.1 Use nouns to represent resources

The URI should refer to a resource that is a thing (noun) instead of an action (verb). Example of resource: User Accounts.

```
http://api.example.com/user-management/users/{id}
http://api.example.com/user-management/users/
```

The resource archetypes can be divided into four categories:

- **document**
A document resource is a singular concept that is akin to an object instance or database record.

Use "singular" name to denote document resource archetype.

```
http://api.example.com/portfolio-management/managed-portfolios/{portfolio-id}
http://api.example.com/user-management/users/{id}
http://api.example.com/user-management/users/admin
```

- **collection**
A collection resource is a server-managed directory of resources.
Use the "plural" name to denote the collection resource archetype.

```
http://api.example.com/user-management/users
http://api.example.com/user-management/users/{id}/accounts
```

- **store**
A store is a client-managed resource repository. A store resource lets an API client put resources in, get them back out, and decide when to delete them.

Use "plural" name to denote store resource archetype.

```
http://api.example.com/portfolio-management/users/{id}/portfolios
```

- **controller**
A controller resource models a procedural concept. Controller resources are like executable functions, with parameters and return values, inputs, and outputs.

Use "verb" to denote controller archetype.

```
http://api.example.com/cart-management/users/{id}/cart/checkout
http://api.example.com/song-management/users/{id}/playlist/play
```

Note:
**In the current API design we'll avoid use of document archetypes.**

----------

Once you have your resources defined, you need to identify what actions apply to them and how those would map to your API. RESTful principles provide strategies to handle CRUD actions using HTTP methods mapped as follows:

```
GET /users - Retrieves a list of users
GET /users/12 - Retrieves a specific user
POST /users - Creates a new user
POST /users/12 - Updates user with id #12
```

In the above examples there are no method naming conventions to follow and the URL structure is clean & clear.

**Note**
We'll avoid using PUT, PATCH & DELETE. We'll only use POST & GET. Both update & creation will be handled through POST requests.

### 1.2 Name collections with plural nouns

### 1.3 Forward slashes

- Use forward slash (/) to indicate hierarchical relationships
- Do not use trailing forward slash (/) in URIs

### 1.4 Use hyphens (-) to improve the readability of URIs

### 1.5 Do not use underscores ( _ )

### 1.6 Use lowercase letters in URIs

### 1.7 Never use CRUD function names in URIs

### 1.8 Use query component to filter URI collection

### 1.9 Use logical nesting on endpoints

When designing endpoints, it makes sense to group those that contain associated information. That is, if one object can contain another object, you should design the endpoint to reflect that. This is good practice regardless of whether your data is structured like this in your database.

For example, if we want an endpoint to get the portfolios for a user, we should append the /portfolios path to the end of the /users path. We can do that with the following code in Express:

```
const express = require('express');
const bodyParser = require('body-parser');
const app = express();

app.use(bodyParser.json());

app.get('/users/:userId/portfolios', (req, res) => {
  const { userId } = req.params;
  const portfolios = [];
  // code to get comments by portfolioId
  res.json(portfolios);
});

app.listen(3000, () => console.log('server started'));
```

Examples of nesting in resources with relations
```
GET /users/12/portfolios - Retrieves list of portfolios for user #12
GET /users/12/portfolios/5 - Retrieves portfolio #5 for user #12
POST /users/12/portfolios - Creates a new portfolio in user #12
POST /users/12/portfolios/5 - Updates portfolio #5 for user #12
```

### 1.10 Actions that don't fit into the world of CRUD operations

There are a number of approaches:

- **Suggested: Treat it like a sub-resource with RESTful principles. For example, GitHub's API lets you star a gist with PUT /gists/:id/star and unstar with DELETE /gists/:id/star.**
- You may have no way to map the action to a sensible RESTful structure. For example, a multi-resource search doesn't really make sense to be applied to a specific resource's endpoint. In this case, /search would make the most sense even though it isn't a resource. This is OK - just do what's right from the perspective of the API consumer and make sure it's documented clearly to avoid confusion.
- Sometimes it may make sense to restructure the action to appear like a field of a resource. This works if the action doesn't take parameters. For example an activate action could be mapped to a boolean activated field and updated via a PUT to the resource.


## 2. Filtering, Sorting & Searching

Complex result filters, sorting requirements and advanced searching (when restricted to a single type of resource) can all be easily implemented as query parameters on top of the base URL.

### 2.1 Filtering

Use a unique query parameter for each field that implements filtering. For example, when requesting a list of users from the /users endpoint, you may want to limit these to only those that are kyc'd. This could be accomplished with a request like GET /users?kycPassed=true. Here, kycPassed is a query parameter that implements a filter.

### 2.2 Sorting

Similar to filtering, a generic parameter sort can be used to describe sorting rules. Accommodate complex sorting requirements by letting the sort parameter take in list of comma separated fields, each with a possible unary negative to imply descending sort order. Examples:

```
GET /users?sort=-createdAt - Retrieves a list of users in descending order of creation date
GET /users?sort=-createdAt,dateOfBirth - Retrieves a list of users in descending order of creation date. Within a specific creation date, older users are ordered first
```

### 2.3 Searching

Sometimes basic filters aren't enough and you need the power of full text search. This is useful when we'll be using ElasticSearch or another Lucene based search technology. When full text search is used as a mechanism of retrieving resource instances for a specific type of resource, it can be exposed on the API as a query parameter on the resource's endpoint. Let's say q. Search queries should be passed straight to the search engine and API output should be in the same format as a normal list result.

Combining these together, we can build queries like:
```
GET /orders?sort=-updated_at - Retrieve recently updated orders
GET /orders?status=settled&sort=-updated_at - Retrieve recently closed orders
GET /orders?q=return&status=settled&sort=-priority,created_at - Retrieve the highest priority open orders mentioning the word 'return'
```

### 2.4 Aliases for common queries

To make the API experience more pleasant for the average consumer, consider packaging up sets of conditions into easily accessible RESTful paths. For example, the recently settled orders query above could be packaged up as GET /orders/recently-settled.

### 2.5 Pagination

TODO:


## 3. HTTP Status Codes

We'll be using the following HTTP status codes:

```
200 OK - Response to a successful GET. Can also be used for a POST that doesn't result in a creation.
201 Created - Response to a POST that results in a creation. Should be combined with a Location header pointing to the location of the new resource
204 No Content - Response to a successful request that won't be returning a body (like a DELETE request)
304 Not Modified - Used when HTTP caching headers are in play
400 Bad Request - The request is malformed, such as if the body does not parse
401 Unauthorized - When no or invalid authentication details are provided. Also useful to trigger an auth popup if the API is used from a browser
403 Forbidden - When authentication succeeded but authenticated user doesn't have access to the resource
404 Not Found - When a non-existent resource is requested
405 Method Not Allowed - When an HTTP method is being requested that isn't allowed for the authenticated user
422 Unprocessable Entity - Used for validation errors
500 Internal server error – This is a generic server error. It probably shouldn’t be thrown explicitly.
502 Bad Gateway – This indicates an invalid response from an upstream server.
503 Service Unavailable – This indicates that something unexpected happened on server side (It can be anything like server overload, some parts of the system failed, etc.).
```

## 4. Errors

For API error we'll use a simple but descriptive response. That response should have a 4xx or 5xx HTTP status and a consumable JSON error representation with the following format:
```
{
    "message": "something bad happened",
    "description": "more details about the error here"
}
```

In the future we may also have to add a `code` field, but given that this is an internal API, it doesn't make much sense at the moment.

## 5. Authentication
TODO:


# References
- https://github.com/RootSoft/API-Naming-Convention
- https://restfulapi.net/resource-naming/
- https://stackoverflow.blog/2020/03/02/best-practices-for-rest-api-design/