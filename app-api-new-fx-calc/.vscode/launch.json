{"version": "0.2.0", "configurations": [{"name": "Docker: Attach to Node", "type": "node", "request": "attach", "restart": false, "port": 9228, "address": "localhost", "localRoot": "${workspaceFolder}", "remoteRoot": "/workdir", "smartStep": true}, {"name": "docker-jest", "type": "node", "request": "attach", "address": "0.0.0.0", "port": 9227, "localRoot": "${workspaceFolder}", "remoteRoot": "/workdir", "internalConsoleOptions": "neverOpen", "presentation": {"reveal": "silent"}}]}