# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# macOS system files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Typescript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
*.env

# Jest reports
jest_*

# Ignore built ts files
dist/**/*
public/dist/*.bundle.js
public/dist/*.bundle.js.map

# Nodechef deployment
__ncbundle__.zip

# Docker compose file
docker-compose.database.yml

# Cypress
cypress.env.json

# Ides
.idea/

# Certficate
cert.pem
key.pem

# Sentry Config File
.sentryclirc

# Pothen Esxes output files
src/scripts/pothen-esxes/output/

# Pothen Esxes input files
src/scripts/pothen-esxes/input/

# DORA processing directories
/src/scripts/dora/reports/
/src/scripts/dora/deliverable.zip
/src/scripts/dora/*.zip
