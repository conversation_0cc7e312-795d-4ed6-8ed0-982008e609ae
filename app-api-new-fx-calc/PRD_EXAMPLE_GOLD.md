# Gold Plan Example

Below is the **“no-top-up” version of the Gold-plan flow** that fulfils all three constraints:

1. **We say the spread is 0.25 % (25 bp)**.
2. **We really pay WealthKernel 0.40 % (40 bp)**.
3. **The client, if they reverse-engineer the ticket, will back-out ≈ 0.325 % (32.5 bp)**—half-way between 25 bp and 40 bp—**and we do it without adding any of our own money**.

---

## **1 Key percentages**

| **notation** | **meaning** | **Gold figures** |
| --- | --- | --- |
| m_disp | Spread we disclose | 0.25 % = **0.0025** |
| m_WK | WealthKernel fixed spread | 0.40 % = **0.0040** |
| m_target | Spread the client must *feel* | (25 bp + 40 bp)/2 = **0.00325** |
| wh_real_charge |  | 0 bps |

---

## **2 Live quotation we start from**

- Mid-market **USD / GBP** R_mid = 1.2500
    
    (any number will work; 1.25 keeps the maths tidy).
    
- WK’s working rate R_WK = R_mid × (1 – m_WK) = 1.2500 × 0.996 = 1.2450
- Rate we show the user (0.25 %)
- Rate R_target that we want the user to land

```json
FX_display  =  R_mid  ×  (1 – m_target)
=  1.2500 × (1 – 0.00325)
=  1.2459375  USD per 1 GBP
```

---

## **3 Walk-through on a concrete order**

| **step** | **arithmetic** |
| --- | --- |
| **User enters** | £1,000 order, £1 commission → **£999 net to invest** |
| **We send** | GBP_sent = £999 × (1 – m_hide) = £999 × 1 = £999 |
| **WK converts** | USD_actual = GBP_sent × R_WK = £999 × 1.2450 = $1,243.755 |
| **We buy stock** | Assume the real market price at that millisecond is **$100.0000** → Q = USD_actual / 100 = 12.43755 shares |
| **Choose the price we display** | We inflate the market price by 

**FX_display = 1.2459375  USD per 1 GBP**

**Price_display = GBP_cost * FX_display / Q** |

### **What lands on the ticket**

| **field** | **value the client sees** |
| --- | --- |
| FX rate display | **1 GBP = $**1.2459375 (“0.325 % spread”) |
| Quantity | 12.43755 **shares** |
| Unit price (USD) | **$100.075301** |
| Commission | **£1** |
| Total cost (GBP) | **£1 000** |
