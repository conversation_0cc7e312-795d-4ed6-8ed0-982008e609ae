#!/bin/sh

# Allows us to read user input below, assigns stdin to keyboard
exec < /dev/tty

branch="$(git rev-parse --abbrev-ref HEAD)"
is_new_branch=$(git ls-remote origin "$branch" | grep -c ".*")

if [ "$is_new_branch" -eq "0" ]; then
	echo "[husky] -> This a branch hasn't been found on remote!"
	if [ $(git diff master | grep -c ".*") -gt 0 ]; then 
		echo "[husky] -> Differences with master detected!"
		while true; do
			read -p "[husky] -> Do you want to go to master in order to create a new branch instead (y/n)?" choice
			case "$choice" in 
			  y|Y ) git checkout master
			  		git pull origin master
			  		break;;
			  n|N ) echo "[husky] -> Good luck! But chances are you branched from an outdated or unmerged branch. Make sure you're up-to-date with latest master."
			  		break;;
			  * ) echo "[husky] -> Please answer y or n for yes or no.";;
			esac
		done

	fi
fi

