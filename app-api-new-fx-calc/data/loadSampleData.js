const fs = require("fs");
const mongoose = require("mongoose");

if (process.env.NODE_ENV === "production") {
  require("dotenv").config({ path: "prod.env" });
} else if (process.env.NODE_ENV === "staging") {
  require("dotenv").config({ path: "stage.env" });
} else if (process.env.NODE_ENV === "development") {
  require("dotenv").config({ path: "dev.env" });
}

mongoose.connect(process.env.DATABASE_URL);

// import all of our models - they need to be imported only once
require("../dist/models/Account");
require("../dist/models/Address");
require("../dist/models/BankAccount");
require("../dist/models/IntraDayTicker");
require("../dist/models/DailyTicker");
require("../dist/models/InvestmentProduct");
require("../dist/models/SavingsProduct");
require("../dist/models/Order");
require("../dist/models/Portfolio");
require("../dist/models/Transaction");
require("../dist/models/Subscription");
require("../dist/models/User");
require("../dist/models/AssetNews");
require("../dist/models/ContentEntry");
require("../dist/models/SundownDigest");
require("../dist/models/IndexPrice");

const Account = mongoose.model("Account");
const Address = mongoose.model("Address");
const BankAccount = mongoose.model("BankAccount");
const DailySavingsProductTicker = mongoose.model("DailySavingsProductTicker");
const IntraDayPortfolioTicker = mongoose.model("IntraDayPortfolioTicker");
const IntraDayAssetTicker = mongoose.model("IntraDayAssetTicker");
const DailyPortfolioTicker = mongoose.model("DailyPortfolioTicker");
const DailyPortfolioSavingsTicker = mongoose.model("DailyPortfolioSavingsTicker");
const User = mongoose.model("User");
const Order = mongoose.model("Order");
const Portfolio = mongoose.model("Portfolio");
const Subscription = mongoose.model("Subscription");
const InvestmentProduct = mongoose.model("InvestmentProduct");
const SavingsProduct = mongoose.model("SavingsProduct");
const DepositCashTransaction = mongoose.model("DepositCashTransaction");
const WithdrawalCashTransaction = mongoose.model("WithdrawalCashTransaction");
const AssetTransaction = mongoose.model("AssetTransaction");
const DividendTransaction = mongoose.model("DividendTransaction");
const AssetNews = mongoose.model("AssetNews");
const ContentEntry = mongoose.model("ContentEntry");
const SundownDigest = mongoose.model("SundownDigest");
const IndexPrice = mongoose.model("IndexPrice");
const DailySummarySnapshot = mongoose.model("DailySummarySnapshot");

async function deleteData() {
  console.log("😢😢 Goodbye Data...");

  await Promise.all([
    Account.deleteMany(),
    Address.deleteMany(),
    BankAccount.deleteMany(),
    IntraDayPortfolioTicker.deleteMany(),
    IntraDayAssetTicker.deleteMany(),
    DailyPortfolioTicker.deleteMany(),
    DailyPortfolioSavingsTicker.deleteMany(),
    DailySavingsProductTicker.deleteMany(),
    User.deleteMany(),
    Order.deleteMany(),
    Portfolio.deleteMany(),
    Subscription.deleteMany(),
    InvestmentProduct.deleteMany(),
    SavingsProduct.deleteMany(),
    DepositCashTransaction.deleteMany(),
    WithdrawalCashTransaction.deleteMany(),
    AssetTransaction.deleteMany(),
    DividendTransaction.deleteMany(),
    AssetNews.deleteMany(),
    ContentEntry.deleteMany(),
    SundownDigest.deleteMany(),
    IndexPrice.deleteMany(),
    DailySummarySnapshot.deleteMany()
  ]);

  console.log("Data Deleted. To load sample data, run\n\n\t npm run sample\n\n");
  process.exit();
}

async function loadData() {
  try {
    const accounts = JSON.parse(fs.readFileSync(__dirname + "/accounts.json", "utf-8"));
    const addresses = JSON.parse(fs.readFileSync(__dirname + "/addresses.json", "utf-8"));
    const bankAccounts = JSON.parse(fs.readFileSync(__dirname + "/bankAccounts.json", "utf-8"));
    const assetTransactions = JSON.parse(fs.readFileSync(__dirname + "/assetTransactions.json", "utf-8"));
    const intraDayPortfolioTickers = JSON.parse(
      fs.readFileSync(__dirname + "/intraDayPortfolioTickers.json", "utf-8")
    );
    const intraDayAssetTickers = JSON.parse(fs.readFileSync(__dirname + "/intraDayAssetTickers.json", "utf-8"));
    const dailyPortfolioTickers = JSON.parse(fs.readFileSync(__dirname + "/dailyPortfolioTickers.json", "utf-8"));
    const dailySavingsProductTickers = JSON.parse(
      fs.readFileSync(__dirname + "/dailySavingsProductTickers.json", "utf-8")
    );
    const dailyPortfolioSavingsTickers = JSON.parse(
      fs.readFileSync(__dirname + "/dailyPortfolioSavingsTickers.json", "utf-8")
    );
    const depositCashTransactions = JSON.parse(
      fs.readFileSync(__dirname + "/depositCashTransactions.json", "utf-8")
    );
    const users = JSON.parse(fs.readFileSync(__dirname + "/users.json", "utf-8"));
    const orders = JSON.parse(fs.readFileSync(__dirname + "/orders.json", "utf-8"));
    const portfolios = JSON.parse(fs.readFileSync(__dirname + "/portfolios.json", "utf-8"));
    const subscriptions = JSON.parse(fs.readFileSync(__dirname + "/subscriptions.json", "utf-8"));
    const investmentProducts = JSON.parse(fs.readFileSync(__dirname + "/investmentProducts.json", "utf-8"));
    const savingsProducts = JSON.parse(fs.readFileSync(__dirname + "/savingsProducts.json", "utf-8"));
    const withdrawalCashTransactions = JSON.parse(
      fs.readFileSync(__dirname + "/withdrawalCashTransactions.json", "utf-8")
    );
    const dividendTransactions = JSON.parse(fs.readFileSync(__dirname + "/dividendTransactions.json", "utf-8"));
    const assetNews = JSON.parse(fs.readFileSync(__dirname + "/assetNews.json", "utf-8"));
    const contentEntries = JSON.parse(fs.readFileSync(__dirname + "/contentEntries.json", "utf-8"));
    const sundownDigests = JSON.parse(fs.readFileSync(__dirname + "/sundownDigests.json", "utf-8"));
    const indexPrices = JSON.parse(fs.readFileSync(__dirname + "/indexPrices.json", "utf-8"));
    const dailySummarySnapshots = JSON.parse(fs.readFileSync(__dirname + "/dailySummarySnapshots.json", "utf-8"));

    console.log("Inserting data... 🚚🚚");

    await Promise.all([
      Account.insertMany(accounts),
      Address.insertMany(addresses),
      BankAccount.insertMany(bankAccounts),
      IntraDayPortfolioTicker.insertMany(intraDayPortfolioTickers),
      IntraDayAssetTicker.insertMany(intraDayAssetTickers),
      DailyPortfolioTicker.insertMany(dailyPortfolioTickers),
      DailySavingsProductTicker.insertMany(dailySavingsProductTickers),
      DailyPortfolioSavingsTicker.insertMany(dailyPortfolioSavingsTickers),
      User.insertMany(users),
      Order.insertMany(orders),
      Portfolio.insertMany(portfolios),
      Subscription.insertMany(subscriptions),
      InvestmentProduct.insertMany(investmentProducts),
      SavingsProduct.insertMany(savingsProducts),
      DepositCashTransaction.insertMany(depositCashTransactions),
      WithdrawalCashTransaction.insertMany(withdrawalCashTransactions),
      AssetTransaction.insertMany(assetTransactions),
      DividendTransaction.insertMany(dividendTransactions),
      AssetNews.insertMany(assetNews),
      ContentEntry.insertMany(contentEntries),
      SundownDigest.insertMany(sundownDigests),
      IndexPrice.insertMany(indexPrices),
      DailySummarySnapshot.insertMany(dailySummarySnapshots)
    ]);

    console.log("👍👍👍👍👍👍👍👍 Done!");
    process.exit();
  } catch (e) {
    console.log(
      "\n👎👎👎👎👎👎👎👎 Error! The Error info is below but if you are importing sample data make sure to drop the existing database first with.\n\n\t npm run blowitallaway\n\n\n"
    );
    console.log(e);
    process.exit();
  }
}

if (process.argv.includes("--delete")) {
  deleteData();
} else {
  loadData();
}
