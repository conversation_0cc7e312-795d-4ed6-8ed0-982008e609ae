openapi: "3.0.2"
info:
  title: Wealthyhood API
  version: "1.0"
  description: |
    Wealthyhood api is protected and requires a bearer jwt token to be accessed. A common flow is to retrieve an access token either by autorization code (apps) flow or client credentials (m2m) flow and provide it.


    The following scopes are required in order to access api


    - **api:user** (example)

    - **api:admin** (example)


    ## How to retrieve access tokens
    ### client credentials m2m flow
    ```
    curl --location --request POST 'https://dev-inrhgw7y.eu.auth0.com/oauth/token' \
    --header 'Content-Type: application/x-www-form-urlencoded' \
    --data-urlencode 'grant_type=client_credentials' \
    --data-urlencode 'client_id=<client_id>' \
    --data-urlencode 'client_secret=<client_secret>' \
    --data-urlencode 'audience=https://dev-inrhgw7y.eu.auth0.com/api/v2/'
    ```

    ### code user flow 

    Through a browser login to 

    ```https://dev-inrhgw7y.eu.auth0.com/authorize?response_type=code&client_id=hGNDA4kFSG9I4xztWEjwR8Muj0JwrpPh&redirect_uri=http://localhost:3000&scope=email%20openid%20profile&audience=https://dev-inrhgw7y.eu.auth0.com/api/v2/&state=test```

    Then copy code from redirected url and provide it to the request
    ```
    curl --location --request POST 'https://dev-inrhgw7y.eu.auth0.com/oauth/token' \
    --header 'Content-Type: application/x-www-form-urlencoded' \
    --header 'Cookie: did=s%3Av0%3Afa9fe1b0-7e9d-11ec-a2a1-af332d7e0eb6.W01px0jWPpB8s13qonV%2F23iAMEbGnDvCdpeE3b27Rxw; did_compat=s%3Av0%3Afa9fe1b0-7e9d-11ec-a2a1-af332d7e0eb6.W01px0jWPpB8s13qonV%2F23iAMEbGnDvCdpeE3b27Rxw' \
    --data-urlencode 'grant_type=authorization_code' \
    --data-urlencode 'client_id=<client_id>' \
    --data-urlencode 'client_secret=<client_secret>' \
    --data-urlencode 'code=<code>' \
    --data-urlencode 'redirect_uri=http://localhost:3000'
    ```

servers:
  - url: http://localhost:2000/api/c2m/
  - url: http://localhost:2000/api/m2m/
tags:
  - name: "portfolios"
    description: "Operations regarding portfolios"
  - name: "transactions"
    description: "Operations regarding transactions"
  - name: "users"
    description: "Operations regarding users"
  - name: "rewards"
    description: "Operations regarding rewards"
  - name: "bank-accounts"
    description: "Operations regarding bank accounts"
  - name: statistics
    description: "Operations regarding statistics"

security:
  - bearerAuth: []

paths:
  /portfolios:
    get:
      description: "Retrieves authenticated user's portfolios"
      tags:
        - portfolios
      parameters:
        - in: query
          name: mode
          schema:
            $ref: "#/components/schemas/PortfolioModeEnum"
          required: false
          description: portfolio's mode
        - in: query
          name: populateTicker
          schema:
            type: boolean
          required: false
          description: determines if tickers should be populated
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
    post:
      description: "Creates a portfolio for user"
      tags:
        - portfolios
      parameters:
        - in: query
          name: mode
          schema:
            $ref: "#/components/schemas/PortfolioModeEnum"
          required: true
          description: portfolio's mode
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Portfolio"
        204:
          description: Empty response (Nothing happened)
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}:
    get:
      description: "Retrieves authenticated user's portfolio"
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
    post:
      description: "Updates authenticated user's portfolio by creating pending orders provided"
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                pendingOrders:
                  description: "For a given entry one of properties 'quantity', 'money' should be provided"
                  type: "object"
                  additionalProperties:
                    type: object
                    properties:
                      side:
                        type: string
                        enum:
                          - buy
                          - sell
                      quantity:
                        type: number
                        nullable: true
                      money:
                        type: number
                        nullable: true
                  example:
                    equities_us:
                      side: buy
                      money: 10.5
                    equities_global:
                      side: sell
                      quantity: 0.03

      responses:
        200:
          $ref: "#/components/responses/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/with-returns:
    get:
      description: "Retrieves authenticated user's portfolio"
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PortfolioWithReturns"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/dca:
    get:
      description: "Retrieves data for dashboard chart assuming that user invests initially 150 £ and 150 £ monthly"
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
        - name: initial
          in: query
          description: "Initial investment amount"
          required: false
          schema:
            type: number
            format: double
        - name: monthly
          in: query
          description: "Monthly investment amount "
          required: false
          schema:
            type: number
            format: double
        - name: weeklyResample
          in: query
          description: "Determines if results are calculated with weekly frequency"
          required: false
          schema:
            type: boolean
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  dcaAll:
                    type: object
                    properties:
                      10y:
                        type: array
                        items:
                          type: object
                          additionalProperties:
                            type: integer
                          example:
                            "1342137600000": 10000,
                            "1342396800000": 9965.686275,
                      5y:
                        type: array
                        items:
                          type: object
                          additionalProperties:
                            type: integer
                          example:
                            "1342137600000": 10000,
                            "1342396800000": 9965.686275,
                      3y:
                        type: array
                        items:
                          type: object
                          additionalProperties:
                            type: integer
                          example:
                            "1342137600000": 10000,
                            "1342396800000": 9965.686275,
                      2y:
                        type: array
                        items:
                          type: object
                          additionalProperties:
                            type: integer
                          example:
                            "1342137600000": 10000,
                            "1342396800000": 9965.686275,
                      1y:
                        type: array
                        items:
                          type: object
                          additionalProperties:
                            type: integer
                          example:
                            "1342137600000": 10000,
                            "1342396800000": 9965.686275,
                      6m:
                        type: array
                        items:
                          type: object
                          additionalProperties:
                            type: integer
                          example:
                            "1342137600000": 10000,
                            "1342396800000": 9965.686275,
                      1m:
                        type: array
                        items:
                          type: object
                          additionalProperties:
                            type: integer
                          example:
                            "1342137600000": 10000,
                            "1342396800000": 9965.686275,

        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/personalisation-preferences:
    post:
      description: "Saves the investor's personalisation preferences to his/hers virtual portfolio and creates a real portfolio with these preferences."
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PersonalisationType"

      responses:
        200:
          $ref: "#/components/responses/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/buy:
    post:
      description: "Handles cash-to-portfolio conversion during portfolio top-up."
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                orderAmount:
                  type: "number"
                  format: "double"
      responses:
        200:
          $ref: "#/components/responses/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/invest-pending-deposit:
    post:
      description: "Handles top-up of portfolio during 1-step deposit & invest flow."
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                pendingDepositId:
                  type: "string"
                orderAmount:
                  type: "number"
                  format: "hash"
      responses:
        200:
          $ref: "#/components/responses/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/buy-asset-pending-deposit:
    post:
      description: "Handles buying a single asset in the 1-step deposit & invest flow."
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                pendingDepositId:
                  type: "string"
                pendingOrders:
                  type: "object"
                  additionalProperties:
                    type: object
                    properties:
                      side:
                        type: string
                        enum:
                          - buy
                      money:
                        type: number
                        nullable: true
                  example:
                    equities_us:
                      side: buy
                      money: 10.5
      responses:
        200:
          $ref: "#/components/responses/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/sell:
    post:
      description: "Handles portfolio-to-cash conversion during portfolio selling."
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                orderAmount:
                  type: "number"
                  format: "double"
      responses:
        200:
          $ref: "#/components/responses/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/convert:
    post:
      description: "Handles the transition from simulated to real portfolio mode and performs actual execution of investments."
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                orderAmount:
                  type: "number"
                  format: "double"
      responses:
        200:
          $ref: "#/components/responses/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/withdraw:
    post:
      description: "Withdraws an amount from the user's portfolio's available cash"
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                amount:
                  type: "number"
                  format: "double"
      responses:
        200:
          $ref: "#/components/responses/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/{id}/allocation:
    post:
      description: "Creates asset allocation for virtual portfolio"
      tags:
        - portfolios
      parameters:
        - name: id
          in: path
          description: "Portfolio id (a hashed string)"
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                allocation:
                  description: "A dict of asset ids along with allocation number"
                  type: object
                  example:
                    equities-global: 21.2
                    equities-us: 34.5
                  additionalProperties:
                    type: number
                investmentAmount:
                  type: "number"
                  format: "double"
      responses:
        200:
          $ref: "#/components/responses/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/sync-wealthkenrel:
    post:
      description: "Creates or syncs user's portfolios to wealthkernel"
      tags:
        - portfolios
      responses:
        204:
          description: Empty response (Nothing happened)
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions:
    get:
      description: "Retrieves transactions. Pagination can be applied to retrieved data by using parameters pageSize, page"
      tags:
        - transactions
      parameters:
        - in: query
          name: portfolio
          schema:
            type: string
            format: hash
          required: false
          description: portfolio's id
        - in: query
          name: truelayerStatus
          explode: true
          schema:
            type: array
            items:
              $ref: "#/components/schemas/PaymentStatusEnum"
          required: false
          description: truelayer status of transaction
        - in: query
          name: status
          explode: true
          schema:
            type: array
            items:
              $ref: "#/components/schemas/TrasnactionStatusEnum"
          required: false
          description: calculated status of transaction
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
        - in: query
          name: populateOwner
          schema:
            type: boolean
          required: false
          description: determines if owner should be populated
        - in: query
          name: populate
          schema:
            type: string
          required: false
          description: comma-separated list of fields to be populated
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          required: false
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          required: false
          description: determines which page to retrieve (starting from 1) based on page size

      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                anyOf:
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          anyOf:
                            - $ref: "#/components/schemas/Transaction"
                            - $ref: "#/components/schemas/AssetTransaction"
                            - $ref: "#/components/schemas/DepositCashTransaction"
                            - $ref: "#/components/schemas/WithdrawalCashTransaction"
                  - type: object
                    properties:
                      pagination:
                        $ref: "#/components/schemas/Pagination"
                      transactions:
                        type: array
                        items:
                          anyOf:
                            - $ref: "#/components/schemas/Transaction"
                            - $ref: "#/components/schemas/AssetTransaction"
                            - $ref: "#/components/schemas/DepositCashTransaction"
                            - $ref: "#/components/schemas/WithdrawalCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/{id}:
    get:
      description: "Retrieves a transaction by id"
      tags:
        - transactions
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                anyOf:
                  - $ref: "#/components/schemas/Transaction"
                  - $ref: "#/components/schemas/AssetTransaction"
                  - $ref: "#/components/schemas/DepositCashTransaction"
                  - $ref: "#/components/schemas/WithdrawalCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/deposits:
    get:
      description: "Retrieves deposit cash transactions. Pagination can be applied to retrieved data by using parameters pageSize, page"
      tags:
        - transactions
      parameters:
        - in: query
          name: portfolio
          schema:
            type: string
            format: hash
          required: false
          description: portfolio's id
        - in: query
          name: truelayerStatus
          explode: true
          schema:
            type: array
            items:
              $ref: "#/components/schemas/PaymentStatusEnum"
          required: false
          description: truelayer status of transaction
        - in: query
          name: status
          explode: true
          schema:
            type: array
            items:
              $ref: "#/components/schemas/TrasnactionStatusEnum"
          required: false
          description: calculated status of transaction
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
        - in: query
          name: populateOwner
          schema:
            type: boolean
          required: false
          description: determines if owner should be populated
        - in: query
          name: populatePendingDeposit
          schema:
            type: boolean
          required: false
          description: determines if pending deposit should be populated
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          required: false
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          required: false
          description: determines which page to retrieve (starting from 1) based on page size

      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                anyOf:
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/DepositCashTransaction"
                  - type: object
                    properties:
                      pagination:
                        $ref: "#/components/schemas/Pagination"
                      transactions:
                        type: array
                        items:
                          $ref: "#/components/schemas/DepositCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
    post:
      description: Creates a payment to truelayer
      tags:
        - transactions
      parameters:
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                paymentAmount:
                  type: number
                bankAccountId:
                  type: string
                depositAndInvest:
                  type: boolean
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      paymentUri:
                        type: string
                      depositId:
                        type: string
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/deposits/{id}:
    get:
      description: "Retrieves a deposit transaction by id"
      tags:
        - transactions
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DepositCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/deposits/{id}/sync-truelayer:
    post:
      description: "Sync deposit's truelayer status with truelayer api"
      tags:
        - transactions
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/DepositCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/deposits/sync-truelayer:
    post:
      description: "Sync deposit (with corresponding truelayer id) status with truelayer api"
      tags:
        - transactions
      parameters:
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
        - in: query
          name: truelayerId
          schema:
            type: string
          description: "The deposit truelayer id"
          required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/DepositCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/assets:
    get:
      description: "Retrieves asset transactions. Pagination can be applied to retrieved data by using parameters pageSize, page"
      tags:
        - transactions
      parameters:
        - in: query
          name: portfolio
          schema:
            type: string
            format: hash
          required: false
          description: portfolio's id
        - in: query
          name: truelayerStatus
          explode: true
          schema:
            type: array
            items:
              $ref: "#/components/schemas/PaymentStatusEnum"
          required: false
          description: truelayer status of transaction
        - in: query
          name: status
          explode: true
          schema:
            type: array
            items:
              $ref: "#/components/schemas/TrasnactionStatusEnum"
          required: false
          description: calculated status of transaction
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
        - in: query
          name: populateOwner
          schema:
            type: boolean
          required: false
          description: determines if owner should be populated
        - in: query
          name: populateOrders
          schema:
            type: boolean
          required: false
          description: determines if transaction's orders should be populated
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          required: false
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          required: false
          description: determines which page to retrieve (starting from 1) based on page size

      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                anyOf:
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/AssetTransaction"
                  - type: object
                    properties:
                      pagination:
                        $ref: "#/components/schemas/Pagination"
                      transactions:
                        type: array
                        items:
                          $ref: "#/components/schemas/AssetTransaction"

        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/assets/{id}:
    get:
      description: "Retrieves an asset transaction by id"
      tags:
        - transactions
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
        - in: query
          name: populateOrders
          schema:
            type: boolean
          required: false
          description: determines if orders of transaction should be populated
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AssetTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /users/verify:
    post:
      description: "Verify user passed in external-user-id header. The endpoint has been built so that it works in a polling fashion - there is no guarantee that the endpoint will verify a user in a single endpoint hit."
      tags:
        - users
      parameters:
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: "#/components/schemas/VerificationStatusEnum"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /daily-portfolio-tickers:
    get:
      description: "Retrieves portfolio tickers for given portfolio ID. Pagination and sorting can be applied to retrieve data by using parameters pageSize/page and sort respectively"
      tags:
        - tickers
      parameters:
        - in: query
          name: portfolio
          schema:
            type: string
          required: true
          description: portfolio ID for which to retrieve daily-tickers
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          description: determines which page to retrieve (starting from 1)  based on page size
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-date (desc order)
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: "#/components/schemas/Pagination"
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/DailyPortfolioTicker"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /addresses:
    post:
      description: "Creates or updates address of given ownerId"
      tags:
        - addresses
      parameters:
        - name: owner
          in: query
          required: true
          schema:
            type: string
            format: hash
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                owner:
                  description: "Owner ID, must match the one given in headers"
                  type: string
                line1:
                  description: "First line of address"
                  type: string
                line2:
                  description: "Second line of address"
                  type: string
                postalCode:
                  type: string
                city:
                  type: string
                countryCode:
                  type: string
      responses:
        204:
          description: "OK"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
  /users/{id}:
    post:
      description: Updates a user
      tags:
        - users
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
          description: User's id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                lastName:
                  type: string
                dateOfBirth:
                  type: string
                  format: date
                  description: date in ISO format 'YYYY-MM-DDTHH:MM:SSZ'
                nationality:
                  type: string
                taxResidencyCountryCode:
                  type: string
                taxResidencyProofType:
                  type: string
                taxResidencyProofValue:
                  type: string
                taxResidencyIsUK:
                  type: string
                hasAcceptedTerms:
                  type: boolean
                viewedWelcomePage:
                  type: boolean
                uKResidentStatus:
                  type: boolean
                submittedRequiredInfoAt:
                  type: string
                  format: date
                  description: date in ISO format 'YYYY-MM-DDTHH:MM:SSZ'
      responses:
        200:
          $ref: "#/components/schemas/User"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /users/{id}/linked-bank-account:
    get:
      description: Retrieves user's (request user) linked bank account
      tags:
        - users
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
          description: User's id
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/LinkedBankAccount"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"

  /rewards:
    get:
      description: "Retrieves rewards. Pagination can be applied to retrieved data by using parameters pageSize, page"
      tags:
        - rewards
      parameters:
        - in: query
          name: populatePortfolio
          schema:
            type: boolean
          required: false
          description: Determines if users' portfolios should be populated
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          required: false
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          required: false
          description: determines which page to retrieve (starting from 1) based on page size
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                anyOf:
                  - type: object
                    properties:
                      pagination:
                        $ref: "#/components/schemas/Pagination"
                      rewards:
                        type: array
                        items:
                          $ref: "#/components/schemas/Reward"
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/Reward"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /rewards/{id}:
    post:
      tags:
        - rewards
      description: Updates a reward by id
      parameters:
        - in: path
          name: id
          schema:
            type: string
          description: Reward's id
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                hasViewedAppModal:
                  type: boolean
      responses:
        204:
          description: OK
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /bank-accounts:
    post:
      tags:
        - bank-accounts
      description: Creates a bank account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                name:
                  type: string
                number:
                  type: string
                sortCode:
                  type: string
                truelayerProviderId:
                  type: string
      responses:
        204:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/BankAccount"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"

  /investment-products:
    get:
      description: "Retrieves all investment products available on the platform."
      tags:
        - investment-products
      parameters:
        - in: query
          name: populateTicker
          schema:
            type: boolean
          required: false
          description: Determines if the investment products' ticker should be populated
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/InvestmentProduct"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /investment-products/etf-data:
    get:
      description: "Retrieves the ETF data for the investment product specified in the query param."
      tags:
        - investment-products
      parameters:
        - in: query
          name: asset
          schema:
            type: string
          required: true
          description: Specifies the asset id of the investment products that we'll retrieve data for.
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  currentPrice:
                    type: number
                    example: 82.96
                  topHoldings:
                    type: array
                    example:
                      - name: "Apple Inc"
                        weight: "3.73%"
                        logoUrl: "https://eodhistoricaldata.com/img/logos/US/aapl.png"
                  expenseRatio:
                    type: string
                    example: "0.2200"
                  indexStats:
                    type: object
                    example:
                      expectedReturn: "8.8%"
                      annualRisk: "16.6%"
                      fpEarnings: "13.88"
                      dividendYield: "2.46%"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /investment-products/prices:
    get:
      description: "Retrieves all investment products available on the platform."
      tags:
        - investment-products
      parameters:
        - in: query
          name: populateTicker
          schema:
            type: boolean
          required: false
          description: Determines if the investment products' ticker should be populated
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                example:
                  - date: "2021-01-24"
                    close: 1
                  - date: "2021-01-25"
                    close: 2
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"

  /statistics/past-performance:
    get:
      description: "Retrieves data for past performance chart"
      tags:
        - statistics
      parameters:
        - in: query
          name: inital
          description: "Simulated invested amount"
          required: true
          schema:
            type: number
            format: double
        - in: query
          name: asset
          description: "This parameter could be any valid asset key"
          style: form
          explode: true
          example:
            Equities-UK: 45
            Equities-US: 55
          schema:
            type: object
            additionalProperties:
              type: number
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  total_past_performance_all:
                    $ref: "#/components/schemas/Performance"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /statistics/future-performance:
    get:
      description: "Retrieves data for future performance chart"
      tags:
        - statistics
      parameters:
        - in: query
          name: inital
          description: "Simulated invested amount"
          required: true
          schema:
            type: number
            format: double
        - in: query
          name: asset
          description: "This parameter could be any valid asset key"
          style: form
          explode: true
          example:
            Equities-UK: 45
            Equities-US: 55
          schema:
            type: object
            additionalProperties:
              type: number
        - in: header
          name: external-user-id
          schema:
            type: string
          description: "This header is required for m2m access in order to run on behalf of user"
          required: false
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  total_past_performance_all:
                    $ref: "#/components/schemas/Performance"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
components:
  responses:
    Portfolio:
      description: "Operation was successful"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Portfolio"
    NotFound:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApiErrorResponse"
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApiErrorResponse"
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApiErrorResponse"
    BadRequest:
      description: "Bad Request"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApiErrorResponse"
  schemas:
    TransactionCategoryEnum:
      type: string
      enum:
        - DepositCashTransaction
        - WithdrawalCashTransaction
        - AssetTransaction
    CurrencyEnum:
      type: string
      enum:
        - EUR
        - GBP
        - USD
    PortfolioModeEnum:
      type: string
      enum:
        - REAL
        - VIRTUAL

    VerificationStatusEnum:
      type: string
      enum:
        - verified
        - verifying

    WrapperTypeEnum:
      type: string
      enum:
        - GIA
        - ISA

    CashType:
      type: object
      properties:
        available:
          type: number
          format: double
        reserved:
          type: number
          format: double

    PersonalisationType:
      type: object
      properties:
        assetClasses:
          type: array
          items:
            type: string
        geography:
          type: string
        risk:
          type: number
        sectors:
          type: array
          items:
            type: string

    InitialHoldingsAllocationType:
      type: object
      properties:
        asset:
          $ref: "#/components/schemas/InvestmentProduct"
          nullable: true
        assetCommonId:
          type: string
        percentage:
          type: number
          format: double

    ApiErrorResponse:
      type: object
      properties:
        status:
          type: number
          format: integer
        error:
          type: object
          properties:
            message:
              type: string
            description:
              type: string
              nullable: true
        responseId:
          type: string
          format: "uuid"

    HoldingsType:
      type: object
      properties:
        asset:
          $ref: "#/components/schemas/InvestmentProduct"
          nullable: true
        assetCommonId:
          type: string
          description: "A valid asset id from supported assets"
        quantity:
          type: number
          format: double
    Portfolio:
      type: object
      properties:
        cash:
          $ref: "#/components/schemas/CashType"
        createdAt:
          type: string
          format: date-time
        currency:
          $ref: "#/components/schemas/CurrencyEnum"
        currentTicker:
          $ref: "#/components/schemas/DailyPortfolioTicker"
        holdings:
          type: array
          items:
            $ref: "#/components/schemas/HoldingsType"
        initialHoldingsAllocation:
          $ref: "#/components/schemas/InitialHoldingsAllocationType"
        lastUpdated:
          type: string
          format: date-time
        mode:
          $ref: "#/components/schemas/PortfolioModeEnum"
        name:
          type: string
          nullable: true
        owner:
          type: object
        personalisationPreferences:
          $ref: "#/components/schemas/PersonalisationType"
        totalDepositedAmount:
          type: number
          format: double
        wealthkernel:
          type: object
          properties:
            portfolioId:
              type: string
        wrapperType:
          $ref: "#/components/schemas/WrapperTypeEnum"

    PortfolioWithReturns:
      allOf:
        - $ref: "#/components/schemas/Portfolio"
        - type: object
          required:
            - rootCause
          properties:
            returnsValue:
              type: number
              format: double
            upByValue:
              type: number
              format: double
            holdings:
              type: array
              items:
                allOf:
                  - type: object
                    properties:
                      asset:
                        $ref: "#/components/schemas/InvestmentProduct"
                        nullable: true
                      assetCommonId:
                        type: string
                        description: "A valid asset id from supported assets"
                      quantity:
                        type: number
                        format: double
                      sinceBuyReturns:
                        type: number
                        format: double

    InvestmentProduct:
      type: object
      properties:
        assetClass:
          type: string
          enum:
            - EQUITIES
            - BONDS
            - COMMODITTIES
            - REAL_ESTATE
        currentTicker:
          type: object
        description:
          type: string
          nullable: true
        commonId:
          type: string
          description: "A valid asset id from supported assets"
        isin:
          type: string
        listed:
          type: boolean
          nullable: true
        name:
          type: string
        officialTickerName:
          type: string
        officialExchange:
          type: string
        wealthkernelPortfolioId:
          type: string
          nullable: true
    DailyPortfolioTicker:
      properties:
        currency:
          $ref: "#/components/schemas/CurrencyEnum"
        date:
          type: string
          format: date-time
        price:
          type: number
          format: double
        openingPrice:
          type: number
          format: double
          nullable: true
        closingPrice:
          type: number
          format: double
          nullable: true
        returnPercentage:
          type: number
          format: double
        dateLabel:
          type: string
          format: date-time
        portfolio:
          type: string
          description: "Portfolio's id"
        wealthkernelValue:
          type: number
          format: double
    PortfolioData:
      type: object
      properties:
        cash:
          type: object
          properties:
            GBP:
              $ref: "#/components/schemas/CashType"
        holdings:
          type: array
          items:
            $ref: "#/components/schemas/HoldingsType"
        initialHoldingAllocation:
          $ref: "#/components/schemas/InitialHoldingsAllocationType"
        mode:
          $ref: "#/components/schemas/PortfolioModeEnum"
        owner:
          type: string
        wrapperType:
          $ref: "#/components/schemas/WrapperTypeEnum"
    TrasnactionStatusEnum:
      type: string
      enum:
        - Pending
        - Cancelled
        - Rejected
        - Settled
    Transaction:
      type: object
      properties:
        owner:
          anyOf:
            - type: string
            - type: object
        portfolio:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/Portfolio"
        wealthkernelPortfolioId:
          type: string
        consideration:
          type: object
          properties:
            currency:
              $ref: "#/components/schemas/CurrencyEnum"
            amount:
              type: number
              format: double
        category:
          $ref: "#/components/schemas/TransactionCategoryEnum"
        createdAt:
          type: string
          format: date-time
        status:
          $ref: "#/components/schemas/TrasnactionStatusEnum"
    PortfolioTransactionCategoryEnum:
      type: string
      enum:
        - buy
        - sell
        - update
    AssetTransaction:
      allOf:
        - $ref: "#/components/schemas/Transaction"
        - type: object
          required:
            - rootCause
          properties:
            orders:
              anyOf:
                - type: array
                  items:
                    type: string
                - type: array
                  items:
                    type: object
                    description: "Order schema to be implemented"
            originalInvestmentAmount:
              type: number
              format: double
    PaymentStatusEnum:
      type: string
      enum:
        - new
        - authorised
        - cancelled
        - failed
        - rejected
        - submitted
        - executed
    DepositStatusEnum:
      type: string
      enum:
        - Created
        - Active
        - Settled
        - Cancelled
        - Rejected
    DepositCashTransaction:
      allOf:
        - $ref: "#/components/schemas/Transaction"
        - type: object
          required:
            - rootCause
          properties:
            wealthkernel:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/DepositStatusEnum"
    WithdrawalStatusEnum:
      type: string
      enum:
        - Pending
        - Active
        - Settled
        - Cancelling
        - Cancelled
        - Rejected
    WithdrawalCashTransaction:
      allOf:
        - $ref: "#/components/schemas/Transaction"
        - type: object
          required:
            - rootCause
          properties:
            bankReference:
              type: string
            truelayer:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/PaymentStatusEnum"
            wealthkernel:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/WithdrawalStatusEnum"
    Pagination:
      type: object
      properties:
        page:
          type: number
          format: integer
        pageSize:
          type: number
          format: integer
        pages:
          type: number
          format: integer
        count:
          type: number
          format: integer
    UserTypeEnum:
      type: string
      enum:
        - INVESTOR
        - ADMIN
        - TEST_ACCOUNT
    User:
      type: object
      properties:
        auth0:
          type: object
          properties:
            id:
              type: string
        bankAccounts:
          type: array
          items:
            anyOf:
              - type: string
              - $ref: "#/components/schemas/BankAccount"
        portfolios:
          type: array
          items:
            anyOf:
              - type: string
              - $ref: "#/components/schemas/Portfolio"
        addresses:
          type: array
          items:
            type: object
        createdAt:
          type: string
          format: date
        currency:
          type: string
        dateOfBirth:
          type: string
          format: date
        email:
          type: string
        emailDisposable:
          type: string
        emailVerified:
          type: string
        firstName:
          type: string
        isUKTaxResident:
          type: boolean
        img:
          type: string
        initialInvestment:
          type: number
          format: double
        kycPassed:
          type: boolean
        lastName:
          type: string
        lastLogin:
          type: string
          format: date
        nationalities:
          type: array
          items:
            type: string
        role:
          type: array
          items:
            $ref: "#/components/schemas/UserTypeEnum"
        passports:
          type: array
          items:
            type: string
        monthlyInvestment:
          type: number
          format: double
        portfolioConversionStatus:
          type: string
          enum:
            - notStarted
            - inProgress
            - completed
        taxResidency:
          type: object
          properties:
            countryCode:
              type: string
            proofType:
              type: string
            value:
              type: string
        UKResidentStatus:
          type: string
          enum:
            - uk
            - non-uk
            -
        viewedWelcomePage:
          type: boolean
        virtualPortfolio:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/Portfolio"
        wealthkernel:
          type: object
          properties:
            partyId:
              type: string
    BankAccount:
      type: object
      properties:
        owner:
          type: string
        name:
          type: string
        number:
          type: string
        sortCode:
          type: string
        truelayerProviderId:
          type: string
        wealthkernel?:
          type: object
          properties:
            id:
              type: string
            status:
              type: string
              enum:
                - Active
                - Inactive
    Provider:
      type: object
      properties:
        id:
          type: string
        logo:
          type: string
        icon:
          type: string
        displayable_name:
          type: string
        main_bg_color:
          type: string
        supports_app_to_app:
          type: boolean
        country_code:
          type: string
        divisions:
          type: array
          items:
            type: string
    LinkedBankAccount:
      allOf:
        - $ref: "#/components/schemas/BankAccount"
        - type: object
          properties:
            id:
              type: string
            provider:
              $ref: "#/components/schemas/Provider"
    OrderStatusEnum:
      type: string
      enum:
        - Pending
        - Open
        - Matched
        - Rejected
        - Cancelling
        - Cancelled
    Reward:
      type: object
      properties:
        asset:
          type: string
        consideration:
          type: object
          properties:
            currency:
              $ref: "#/components/schemas/CurrencyEnum"
            amount:
              type: number
              format: double
        quantity:
          type: number
          format: double
        referrer:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/User"
        referral:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/User"
        targetUser:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/User"
        updatedAt:
          type: string
          format: date
        deposit:
          type: object
          properties:
            wealthkernel:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/DepositStatusEnum"
        order:
          type: object
          properties:
            wealthkernel:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/OrderStatusEnum"
    Performance:
      type: object
      properties:
        10y:
          type: array
          items:
            type: object
            additionalProperties:
              type: integer
            example:
              "1342137600000": 10000,
              "1342396800000": 9965.686275,
        5y:
          type: array
          items:
            type: object
            additionalProperties:
              type: integer
            example:
              "1342137600000": 10000,
              "1342396800000": 9965.686275,
        3y:
          type: array
          items:
            type: object
            additionalProperties:
              type: integer
            example:
              "1342137600000": 10000,
              "1342396800000": 9965.686275,
        2y:
          type: array
          items:
            type: object
            additionalProperties:
              type: integer
            example:
              "1342137600000": 10000,
              "1342396800000": 9965.686275,
        1y:
          type: array
          items:
            type: object
            additionalProperties:
              type: integer
            example:
              "1342137600000": 10000,
              "1342396800000": 9965.686275,
        6m:
          type: array
          items:
            type: object
            additionalProperties:
              type: integer
            example:
              "1342137600000": 10000,
              "1342396800000": 9965.686275,
        1m:
          type: array
          items:
            type: object
            additionalProperties:
              type: integer
            example:
              "1342137600000": 10000,
              "1342396800000": 9965.686275,
  securitySchemes:
    bearerAuth: # arbitrary name for the security scheme
      type: http
      scheme: bearer
      bearerFormat: JWT # optional, arbitrary value for documentation purposes
